// Test frontend streaming functionality
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testFrontendStream() {
  try {
    console.log('🧪 Testing frontend streaming functionality...');
    
    // Test 1: Direct backend stream API
    console.log('\n1️⃣ Testing backend stream API directly...');
    const response = await fetch('http://localhost:8000/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message: '你好' }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ Backend stream API is accessible');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let messageCount = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            messageCount++;
            console.log(`📡 Message ${messageCount}:`, data.type, data.content ? data.content.substring(0, 50) + '...' : '');
          } catch (e) {
            console.log('📄 Raw line:', line);
          }
        }
      }
    }

    console.log(`✅ Received ${messageCount} streaming messages from backend`);
    
    // Test 2: Frontend accessibility
    console.log('\n2️⃣ Testing frontend accessibility...');
    const frontendResponse = await fetch('http://localhost:3000');
    
    if (frontendResponse.ok) {
      console.log('✅ Frontend is accessible on port 3000');
    } else {
      console.log('❌ Frontend is not accessible');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFrontendStream(); 