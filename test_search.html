<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票搜索测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .search-box {
            position: relative;
            margin-bottom: 20px;
        }
        input {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            outline: none;
            transition: border-color 0.3s;
        }
        input:focus {
            border-color: #007bff;
        }
        .suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .suggestion-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .symbol {
            font-weight: bold;
            color: #007bff;
        }
        .name {
            color: #333;
            margin-left: 10px;
        }
        .market {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
        }
        .industry {
            color: #888;
            font-size: 12px;
            margin-left: 10px;
        }
        .results {
            margin-top: 20px;
        }
        .result-count {
            color: #666;
            margin-bottom: 15px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 股票搜索功能测试 - 已集成AkShare美股数据</h1>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
            <h3 style="margin: 0 0 10px 0; color: #155724;">✅ 功能改进完成</h3>
            <ul style="margin: 0; color: #155724;">
                <li><strong>Tushare API集成</strong>：获取A股和港股数据（5,418只A股 + 200只港股）</li>
                <li><strong>AkShare美股数据</strong>：补充20只热门美股（AAPL、TSLA、MSFT等）</li>
                <li><strong>智能缓存机制</strong>：1小时缓存，避免重复API调用</li>
                <li><strong>多级数据源</strong>：API失败时自动降级到硬编码数据</li>
                <li><strong>智能搜索算法</strong>：支持代码、公司名称、行业匹配</li>
            </ul>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testSearch('AAPL')">测试 "AAPL"</button>
            <button class="test-btn" onclick="testSearch('apple')">测试 "apple"</button>
            <button class="test-btn" onclick="testSearch('tesla')">测试 "tesla"</button>
            <button class="test-btn" onclick="testSearch('microsoft')">测试 "microsoft"</button>
            <button class="test-btn" onclick="testSearch('腾讯')">测试 "腾讯"</button>
            <button class="test-btn" onclick="testSearch('茅台')">测试 "茅台"</button>
            <button class="test-btn" onclick="testSearch('600519')">测试 "600519"</button>
            <button class="test-btn" onclick="testSearch('nvidia')">测试 "nvidia"</button>
        </div>
        
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="输入股票代码或名称，如 AAPL、腾讯、茅台..." 
                   oninput="handleSearch()" onkeypress="handleKeyPress(event)">
            <div class="suggestions" id="suggestions"></div>
        </div>
        
        <div class="results" id="results"></div>
    </div>

    <script>
        let searchTimeout;
        
        function handleSearch() {
            const query = document.getElementById('searchInput').value.trim();
            
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            if (query.length === 0) {
                hideSuggestions();
                return;
            }
            
            // 防抖：延迟300ms执行搜索
            searchTimeout = setTimeout(() => {
                searchStocks(query);
            }, 300);
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                const query = document.getElementById('searchInput').value.trim();
                if (query) {
                    searchStocks(query);
                }
            }
        }
        
        function testSearch(query) {
            document.getElementById('searchInput').value = query;
            searchStocks(query);
        }
        
        async function searchStocks(query) {
            const resultsDiv = document.getElementById('results');
            const suggestionsDiv = document.getElementById('suggestions');
            
            resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
            
            try {
                const response = await fetch(`http://localhost:8000/stocks/search/${encodeURIComponent(query)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data, query);
                    displaySuggestions(data.suggestions);
                } else {
                    resultsDiv.innerHTML = '<div class="error">搜索失败：' + (data.message || '未知错误') + '</div>';
                }
                
            } catch (error) {
                console.error('搜索错误:', error);
                resultsDiv.innerHTML = '<div class="error">搜索失败：' + error.message + '</div>';
                hideSuggestions();
            }
        }
        
        function displayResults(data, query) {
            const resultsDiv = document.getElementById('results');
            
            let html = `<div class="result-count">搜索 "${query}" 找到 ${data.count} 个结果：</div>`;
            
            if (data.suggestions && data.suggestions.length > 0) {
                data.suggestions.forEach(stock => {
                    html += `
                        <div class="suggestion-item">
                            <span class="symbol">${stock.symbol}</span>
                            <span class="name">${stock.name}</span>
                            <span class="market">[${stock.market}]</span>
                            <span class="industry">${stock.industry}</span>
                        </div>
                    `;
                });
            } else {
                html += '<div>没有找到匹配的股票</div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function displaySuggestions(suggestions) {
            const suggestionsDiv = document.getElementById('suggestions');
            
            if (!suggestions || suggestions.length === 0) {
                hideSuggestions();
                return;
            }
            
            let html = '';
            suggestions.slice(0, 8).forEach(stock => {  // 只显示前8个建议
                html += `
                    <div class="suggestion-item" onclick="selectStock('${stock.symbol}', '${stock.name}')">
                        <span class="symbol">${stock.symbol}</span>
                        <span class="name">${stock.name}</span>
                        <span class="market">[${stock.market}]</span>
                    </div>
                `;
            });
            
            suggestionsDiv.innerHTML = html;
            suggestionsDiv.style.display = 'block';
        }
        
        function hideSuggestions() {
            document.getElementById('suggestions').style.display = 'none';
        }
        
        function selectStock(symbol, name) {
            document.getElementById('searchInput').value = symbol;
            hideSuggestions();
            searchStocks(symbol);
        }
        
        // 点击页面其他地方时隐藏建议
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-box')) {
                hideSuggestions();
            }
        });
    </script>
</body>
</html>
