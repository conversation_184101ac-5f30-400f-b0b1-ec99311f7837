#!/usr/bin/env python3
"""
Comprehensive Frontend-Backend Connectivity Test
Tests all aspects of the connection between frontend and backend
"""

import requests
import time
import json
from datetime import datetime

def test_backend_health():
    """Test backend health endpoint"""
    print("🔍 Testing Backend Health Endpoint...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data.get('status', 'unknown')}")
            print(f"   Service: {data.get('service', 'N/A')}")
            print(f"   Version: {data.get('version', 'N/A')}")
            return True
        else:
            print(f"❌ Backend Health Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Health Error: {e}")
        return False

def test_frontend_health():
    """Test frontend accessibility"""
    print("\n🔍 Testing Frontend Accessibility...")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            if "金融投资助手" in response.text:
                print("✅ Frontend Accessible")
                return True
            else:
                print("⚠️  Frontend responding but content may be wrong")
                return False
        else:
            print(f"❌ Frontend Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend Error: {e}")
        return False

def test_cors_preflight():
    """Test CORS preflight requests"""
    print("\n🔍 Testing CORS Preflight...")
    try:
        # Simulate preflight request
        response = requests.options(
            "http://localhost:8000/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        if response.status_code in [200, 204]:
            print("✅ CORS Preflight OK")
            return True
        else:
            print(f"❌ CORS Preflight Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS Preflight Error: {e}")
        return False

def test_api_endpoints():
    """Test key API endpoints"""
    print("\n🔍 Testing Key API Endpoints...")
    endpoints = [
        "/factors",
        "/factors/list", 
        "/factors/health",
        "/divergence/markets"
    ]
    
    success_count = 0
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint}")
                success_count += 1
            else:
                print(f"❌ {endpoint}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    print(f"📊 API Endpoints: {success_count}/{len(endpoints)} working")
    return success_count == len(endpoints)

def test_javascript_fetch():
    """Test simulated browser fetch request"""
    print("\n🔍 Testing Browser-like Fetch Request...")
    try:
        response = requests.get(
            "http://localhost:8000/health",
            headers={
                "Origin": "http://localhost:3000",
                "Referer": "http://localhost:3000/",
                "User-Agent": "Mozilla/5.0 (Frontend Test)",
                "Accept": "application/json",
                "Content-Type": "application/json"
            },
            timeout=5
        )
        if response.status_code == 200:
            print("✅ Browser-like Request OK")
            return True
        else:
            print(f"❌ Browser-like Request Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Browser-like Request Error: {e}")
        return False

def test_environment_config():
    """Test environment configuration"""
    print("\n🔍 Testing Environment Configuration...")
    
    # Check if .env.local exists
    try:
        with open("frontend/.env.local", "r") as f:
            content = f.read()
            if "NEXT_PUBLIC_API_URL=http://localhost:8000" in content:
                print("✅ Frontend .env.local configured correctly")
                return True
            else:
                print("⚠️  Frontend .env.local exists but may be misconfigured")
                print(f"   Content: {content}")
                return False
    except FileNotFoundError:
        print("❌ Frontend .env.local not found")
        return False
    except Exception as e:
        print(f"❌ Environment config error: {e}")
        return False

def main():
    """Run all connectivity tests"""
    print("🏦 Comprehensive Frontend-Backend Connectivity Test")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Frontend Health", test_frontend_health),
        ("CORS Preflight", test_cors_preflight),
        ("API Endpoints", test_api_endpoints),
        ("Browser-like Request", test_javascript_fetch),
        ("Environment Config", test_environment_config)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
        time.sleep(0.5)  # Brief pause between tests
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<25} {status}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All connectivity tests PASSED! Frontend-Backend integration is working correctly.")
    else:
        print("⚠️  Some tests FAILED. Please check the specific failures above.")
        print("\n🔧 Suggested fixes:")
        if not results.get("Backend Health"):
            print("   - Start backend server: cd /path/to/cash-flow && python -m uvicorn backend.server:app --host 0.0.0.0 --port 8000 --reload")
        if not results.get("Frontend Health"):
            print("   - Start frontend server: cd frontend && npm run dev")
        if not results.get("Environment Config"):
            print("   - Create frontend/.env.local with: NEXT_PUBLIC_API_URL=http://localhost:8000")
        if not results.get("CORS Preflight"):
            print("   - Check backend CORS configuration in backend/server.py")
    
    return passed == total

if __name__ == "__main__":
    main() 