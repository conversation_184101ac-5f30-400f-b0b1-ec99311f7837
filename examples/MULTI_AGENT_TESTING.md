# 多智能体系统测试指南

本文档说明如何使用提供的测试脚本来验证 AI 智能分析系统的多智能体协作功能。

## 📁 测试脚本概览

### 1. `test_multi_agent_system.py` - 全面测试脚本
- **用途**: 完整的系统功能测试，包含详细的测试报告
- **特点**: 
  - 测试所有核心组件（LLM、智能体、工具、工作流）
  - 端到端工作流验证
  - 生成详细的 JSON 测试报告
  - 支持日志记录和错误追踪

### 2. `quick_agent_test.py` - 快速验证脚本  
- **用途**: 快速检查系统基础功能是否正常
- **特点**:
  - 轻量级测试，运行速度快
  - 专注于核心功能验证
  - 简洁的输出格式
  - 适合日常开发验证

### 3. `test_langgraph_workflow.py` - 工作流专项测试
- **用途**: 专门测试 LangGraph 多智能体协作逻辑
- **特点**:
  - 验证节点转换逻辑
  - 测试智能体工具调用
  - 步骤执行流程验证
  - 工作流路径测试

## 🚀 使用方法

### 前置条件

1. **环境配置**
   ```bash
   # 确保已安装必要的依赖
   pip install -r requirements.txt
   
   # 配置环境变量（可选）
   export GEMINI_API_KEY="your-api-key"
   export SILICONFLOW_API_KEY="your-siliconflow-key"
   ```

2. **项目结构**
   ```
   cash-flow/
   ├── backend/ai/          # AI 系统核心代码
   ├── test_multi_agent_system.py
   ├── quick_agent_test.py
   ├── test_langgraph_workflow.py
   └── MULTI_AGENT_TESTING.md
   ```

### 运行测试

#### 1. 快速验证（推荐先运行）
```bash
python quick_agent_test.py
```

**输出示例:**
```
[10:30:15] ===============================================
[10:30:15] 开始快速多智能体系统测试
[10:30:15] ===============================================

[10:30:15] --- 基础组件 ---
[10:30:15] 测试基础组件导入...
[10:30:15] ✓ 基础组件导入成功

[10:30:16] --- LLM基础功能 ---
[10:30:16] 测试 LLM 基础功能...
[10:30:18] ✓ LLM 响应正常 (长度: 95)

...

[10:30:25] ===============================================
[10:30:25] 测试结果汇总
[10:30:25] ===============================================
[10:30:25] 总测试数: 6
[10:30:25] 通过: 6
[10:30:25] 失败: 0
[10:30:25] 🎉 所有测试通过！
```

#### 2. 全面测试
```bash
python test_multi_agent_system.py
```

**特点:**
- 运行时间较长（5-10分钟）
- 详细的测试过程日志
- 生成 `multi_agent_test_report.json` 报告文件
- 创建 `test_multi_agent.log` 日志文件

#### 3. 工作流专项测试
```bash
python test_langgraph_workflow.py
```

**专门测试:**
- LangGraph 节点转换
- 智能体协作逻辑
- 工具调用机制
- 计划执行流程

## 📊 测试报告解读

### 快速测试输出
- ✓ 表示测试通过
- ✗ 表示测试失败
- ⚠ 表示警告（功能可用但有问题）

### 全面测试报告 (`multi_agent_test_report.json`)
```json
{
  "timestamp": "2024-01-15 10:30:25",
  "summary": {
    "total": 6,
    "passed": 5,
    "failed": 1,
    "success_rate": 83.3
  },
  "results": [
    {
      "test": "LLM管理器测试",
      "status": "PASS",
      "details": "LLM响应长度: 95, 意图分析: 技术分析"
    }
  ]
}
```

## 🔧 故障排除

### 常见问题

#### 1. 导入错误
```
ImportError: No module named 'backend.ai'
```
**解决方案**: 确保在项目根目录运行测试脚本

#### 2. LLM 连接失败
```
✗ LLM 测试失败: GEMINI_API_KEY环境变量未设置，启用模拟模式
```
**解决方案**: 
- 设置 `GEMINI_API_KEY` 环境变量
- 或者使用模拟模式（系统会自动回退）

#### 3. 工具不可用
```
✗ AkShare工具不可用
```
**解决方案**: 
- 检查 `akshare` 库是否正确安装
- 确认网络连接正常

#### 4. 工作流超时
```
工作流测试失败: asyncio timeout
```
**解决方案**:
- 检查网络连接
- 降低测试复杂度（减少 `max_step_num`）

### 调试模式

在脚本中启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

或者设置环境变量：
```bash
export PYTHONPATH="."
export DEBUG_MODE="true"
python test_multi_agent_system.py
```

## 📈 性能基准

### 预期运行时间
- **快速测试**: 30-60秒
- **全面测试**: 5-10分钟  
- **工作流测试**: 2-5分钟

### 资源使用
- **内存**: 通常 < 1GB
- **网络**: 需要访问 LLM API 和数据源
- **存储**: 生成日志和报告文件约 1-5MB

## 🎯 测试覆盖范围

### 已覆盖功能
- ✅ LLM 管理器（Gemini API、故障转移）
- ✅ 智能体管理器（工具初始化、智能体创建）
- ✅ 工作流管理器（查询处理、意图分析）
- ✅ LangGraph 图结构（节点、边、状态转换）
- ✅ 工具集成（AkShare、技术分析、搜索等）
- ✅ 端到端工作流（完整的分析流程）

### 待扩展测试
- 🔄 负载测试（并发请求）
- 🔄 错误恢复测试
- 🔄 数据质量验证
- 🔄 安全性测试

## 💡 最佳实践

1. **定期运行**: 建议在代码更改后运行快速测试
2. **完整测试**: 在发布前运行全面测试
3. **监控指标**: 关注测试成功率和执行时间
4. **日志分析**: 定期检查测试日志以发现潜在问题
5. **环境隔离**: 在独立环境中运行测试

## 📞 支持

如果遇到测试问题：

1. 检查本文档的故障排除部分
2. 查看生成的日志文件
3. 确认环境配置正确
4. 验证网络连接和 API 密钥

---

**注意**: 这些测试脚本设计用于验证系统功能，在生产环境中使用时请注意 API 调用限制和成本控制。 