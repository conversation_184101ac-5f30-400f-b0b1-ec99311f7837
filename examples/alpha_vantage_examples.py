#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Alpha Vantage 工具使用示例

本文件演示了如何使用Alpha Vantage工具获取各种金融数据。
使用前请确保已设置 ALPHA_VANTAGE_API_KEY 环境变量。

注册免费API密钥：https://www.alphavantage.co/support/#api-key
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.tools.alpha_vantage import alpha_vantage_tool


def example_stock_daily_data():
    """示例1：获取股票日线数据"""
    print("=== 示例1：获取苹果(AAPL)股票日线数据 ===")
    result = alpha_vantage_tool.invoke({
        "function": "TIME_SERIES_DAILY",
        "symbol": "AAPL",
        "outputsize": "compact"  # 获取最近100个交易日的数据
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_global_quote():
    """示例2：获取实时股价"""
    print("=== 示例2：获取微软(MSFT)实时股价 ===")
    result = alpha_vantage_tool.invoke({
        "function": "GLOBAL_QUOTE",
        "symbol": "MSFT"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_symbol_search():
    """示例3：搜索股票代码"""
    print("=== 示例3：搜索Tesla相关股票 ===")
    result = alpha_vantage_tool.invoke({
        "function": "SYMBOL_SEARCH",
        "keywords": "Tesla"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_news_sentiment():
    """示例4：新闻情感分析"""
    print("=== 示例4：获取AAPL和MSFT的新闻情感分析 ===")
    result = alpha_vantage_tool.invoke({
        "function": "NEWS_SENTIMENT",
        "tickers": "AAPL,MSFT"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_technical_indicators():
    """示例5：技术指标 - 简单移动平均线(SMA)"""
    print("=== 示例5：获取AAPL的20日简单移动平均线 ===")
    result = alpha_vantage_tool.invoke({
        "function": "SMA",
        "symbol": "AAPL",
        "interval": "daily",
        "time_period": 20,
        "series_type": "close"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_company_overview():
    """示例6：公司基本面信息"""
    print("=== 示例6：获取苹果公司基本面信息 ===")
    result = alpha_vantage_tool.invoke({
        "function": "OVERVIEW",
        "symbol": "AAPL"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_currency_exchange():
    """示例7：货币汇率"""
    print("=== 示例7：获取USD/CNY汇率 ===")
    result = alpha_vantage_tool.invoke({
        "function": "CURRENCY_EXCHANGE_RATE",
        "additional_params": {
            "from_currency": "USD",
            "to_currency": "CNY"
        }
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_economic_indicators():
    """示例8：经济指标 - GDP"""
    print("=== 示例8：获取美国实际GDP数据 ===")
    result = alpha_vantage_tool.invoke({
        "function": "REAL_GDP",
        "additional_params": {
            "interval": "quarterly"
        }
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_intraday_data():
    """示例9：日内分钟级数据"""
    print("=== 示例9：获取TSLA的5分钟级日内数据 ===")
    result = alpha_vantage_tool.invoke({
        "function": "TIME_SERIES_INTRADAY",
        "symbol": "TSLA",
        "interval": "5min",
        "outputsize": "compact"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def example_rsi_indicator():
    """示例10：技术指标 - RSI相对强弱指数"""
    print("=== 示例10：获取AAPL的14日RSI指标 ===")
    result = alpha_vantage_tool.invoke({
        "function": "RSI",
        "symbol": "AAPL",
        "interval": "daily",
        "time_period": 14,
        "series_type": "close"
    })
    print(result)
    print("\n" + "="*50 + "\n")


def main():
    """运行所有示例"""
    print("Alpha Vantage 工具使用示例")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv("ALPHA_VANTAGE_API_KEY"):
        print("❌ 错误：未设置 ALPHA_VANTAGE_API_KEY 环境变量")
        print("请在 .env 文件中添加：")
        print("ALPHA_VANTAGE_API_KEY=your_api_key_here")
        print("\n免费获取API密钥：https://www.alphavantage.co/support/#api-key")
        return
    
    print("✅ API密钥已设置，开始运行示例...\n")
    
    # 运行示例（注意：免费API有调用频率限制，可能需要间隔运行）
    examples = [
        example_stock_daily_data,
        example_global_quote,
        example_symbol_search,
        # example_news_sentiment,      # 需要Premium API
        # example_technical_indicators,  # 部分需要Premium API
        # example_company_overview,     # 需要Premium API
        # example_currency_exchange,    # 部分功能需要Premium API
        # example_economic_indicators,  # 需要Premium API
        # example_intraday_data,        # 部分需要Premium API
        # example_rsi_indicator,        # 需要Premium API
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            print(f"运行示例 {i}...")
            example_func()
            # 添加延迟以避免API限制
            import time
            time.sleep(12)  # 免费API每分钟限制5次调用
        except Exception as e:
            print(f"示例 {i} 运行失败: {e}")
            continue


if __name__ == "__main__":
    main() 