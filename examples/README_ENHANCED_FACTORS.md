# 增强型多维度技术因子计算与管理

本模块为金融投资助手系统提供了全面的技术因子计算能力，支持29个不同类型的技术指标，涵盖动量、趋势、波动率和成交量四大类别。

## 🚀 核心功能

### 📊 技术因子分类

**动量因子 (9个):**
- RSI相对强弱指标 (14日、6日、24日)
- 随机指标 (KDJ)
- 威廉指标 (Williams %R)  
- 商品通道指数 (CCI)
- 动量指标 (Momentum)
- 变动率指标 (ROC)

**趋势因子 (10个):**
- MACD系列 (MACD线、信号线、柱状图)
- 简单移动平均线 (SMA 5/10/20/50/200日)
- 指数移动平均线 (EMA 12/26日)

**波动率因子 (7个):**
- 布林带系列 (上轨、中轨、下轨、位置、宽度)
- 平均真实波动率 (ATR)
- 价格波动率

**成交量因子 (3个):**
- 成交量移动平均线
- 量比  
- 能量潮指标 (OBV)

### 🛠️ 核心特性

- **高性能计算**: 基于NumPy和Pandas的向量化计算
- **RESTful API**: 完整的HTTP API接口支持
- **批量处理**: 支持多股票并行因子计算
- **灵活配置**: 可配置的因子参数和启用状态
- **错误处理**: 完善的异常处理和数据验证
- **扩展性**: 易于添加新的技术指标

## 📋 文件结构

```
backend/
├── enhanced_factors.py     # 核心因子计算器
├── factor_api.py          # RESTful API接口
└── factors.py             # 原有因子模块 (兼容)

examples/
└── enhanced_factors_demo.py # 功能演示脚本

test_enhanced_factors.py    # 功能测试脚本
FACTOR_ANALYSIS_TASKS.md    # 任务跟踪文档
```

## 🔧 使用方法

### 1. Python模块调用

```python
from backend.enhanced_factors import get_enhanced_factor_calculator, FactorManager
from backend.data_manager import init_data_manager

# 初始化
data_manager = init_data_manager("your_tushare_token")
factor_manager = FactorManager(data_manager)

# 计算单只股票的所有因子
factors = factor_manager.calculate_factors_for_stock("AAPL")

# 计算指定因子
selected_factors = factor_manager.calculate_factors_for_stock(
    "AAPL", 
    factor_names=["rsi", "macd", "bollinger_position"]
)

# 批量计算
batch_results = factor_manager.batch_calculate_factors(
    symbols=["AAPL", "MSFT", "GOOGL"],
    factor_names=["rsi", "macd", "sma_20"]
)
```

### 2. REST API调用

#### 获取因子列表
```bash
curl "http://localhost:8000/factors/list"
```

#### 计算单只股票因子
```bash
curl -X POST "http://localhost:8000/factors/calculate" \
-H "Content-Type: application/json" \
-d '{
  "symbol": "AAPL",
  "factor_names": ["rsi", "macd", "sma_20"]
}'
```

#### 批量计算
```bash
curl -X POST "http://localhost:8000/factors/calculate/batch" \
-H "Content-Type: application/json" \
-d '{
  "symbols": ["AAPL", "MSFT", "GOOGL"],
  "factor_names": ["rsi", "macd", "bollinger_position"]
}'
```

#### URL参数方式
```bash
curl "http://localhost:8000/factors/calculate/AAPL?factor_names=rsi,macd,sma_20"
```

## 🧪 测试与验证

### 运行演示程序
```bash
# 显示所有可用因子
python examples/enhanced_factors_demo.py --demo list

# 完整演示
python examples/enhanced_factors_demo.py --demo full

# 分析特定股票
python examples/enhanced_factors_demo.py --demo analysis --symbol AAPL
```

### 运行测试脚本
```bash
# 测试所有功能
python test_enhanced_factors.py

# 测试特定功能
python test_enhanced_factors.py --test api
python test_enhanced_factors.py --test calculator
python test_enhanced_factors.py --test performance
```

### 系统健康检查
```bash
python system_health_check.py
```

## 📈 API接口文档

### 主要端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/factors/list` | GET | 获取所有可用因子列表 |
| `/factors/categories` | GET | 获取因子分类信息 |
| `/factors/calculate` | POST | 计算单只股票的因子 |
| `/factors/calculate/batch` | POST | 批量计算多只股票的因子 |
| `/factors/calculate/{symbol}` | GET | 通过URL参数计算因子 |
| `/factors/info/{factor_name}` | GET | 获取单个因子的详细信息 |
| `/factors/health` | GET | 因子服务健康检查 |

### 响应格式

#### 因子计算响应
```json
{
  "success": true,
  "symbol": "AAPL",
  "factors": {
    "rsi": 65.42,
    "macd": 0.0023,
    "sma_20": 150.25,
    "bollinger_position": 0.73
  },
  "calculation_time": "2024-12-06T17:21:11.123456",
  "message": "成功计算4个因子"
}
```

#### 批量计算响应
```json
{
  "success": true,
  "results": {
    "AAPL": {"rsi": 65.42, "macd": 0.0023},
    "MSFT": {"rsi": 58.21, "macd": 0.0015},
    "GOOGL": {"rsi": 72.14, "macd": 0.0031}
  },
  "total_symbols": 3,
  "successful_symbols": 3,
  "calculation_time": "2024-12-06T17:21:11.123456"
}
```

## 🎯 性能指标

基于测试结果：
- **总因子数**: 29个
- **因子分类**: 4个类别
- **单次计算时间**: < 1秒 (所有因子)
- **批量计算**: 支持最多50只股票
- **API响应时间**: < 30秒 (单股票), < 60秒 (批量)

## 🔮 后续计划

### 阶段三: 因子筛选与分析
- [ ] 基于因子的股票筛选功能
- [ ] 因子相关性分析
- [ ] 因子有效性评估
- [ ] 动态阈值设置

### 阶段四: 回测框架
- [ ] 基于因子的策略回测
- [ ] 收益率和风险评估
- [ ] 多因子组合优化
- [ ] 实时监控和预警

## 🛡️ 错误处理

系统包含完善的错误处理机制：
- 数据验证和清理
- 网络超时处理
- 计算异常捕获
- 详细的错误日志记录

## 📚 相关文档

- [任务跟踪文档](FACTOR_ANALYSIS_TASKS.md) - 详细的开发进度和计划
- [API文档](http://localhost:8000/docs) - 完整的API交互文档
- [因子算法说明] - 各技术指标的计算方法和参数说明

## 🤝 贡献指南

1. 添加新因子: 继承 `FactorCalculator` 类并实现计算方法
2. 扩展API: 在 `factor_api.py` 中添加新的路由
3. 完善测试: 在 `test_enhanced_factors.py` 中添加相应测试
4. 更新文档: 更新相关文档和示例代码

---

**注意**: 本模块需要有效的数据源配置 (如Tushare token) 才能正常工作。请确保在使用前正确配置数据管理器。 