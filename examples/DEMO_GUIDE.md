# 🚀 Stock Chart Feature Demo Guide

## 🎯 Overview
This guide demonstrates the complete stock chart detection and display feature that shows interactive candlestick charts before AI analysis reports.

## 🖥️ Server Status
✅ **Backend Server**: Running on http://localhost:8000  
✅ **Frontend Server**: Running on http://localhost:3000  
✅ **Stock Chart Pipeline**: Fully operational

## 📱 How to Test the Feature

### Step 1: Access the Application
1. Open your browser and go to: **http://localhost:3000**
2. Click on **"🤖 AI智能分析"** (AI Analysis) module
3. You'll see the chat interface with example questions

### Step 2: Test Stock Chart Queries

Try these example queries to see the stock charts in action:

#### 🍎 Apple Stock Analysis
```
分析苹果公司AAPL的股票走势
```
**Expected Result**: Interactive AAPL candlestick chart showing recent price data, followed by AI analysis

#### 🚗 Tesla Stock Chart  
```
Show me Tesla TSLA stock chart
```
**Expected Result**: TSLA chart with volume bars and price change indicators

#### 📘 Meta Technical Analysis
```
META股票技术分析
```
**Expected Result**: META chart with current price $697.71 (+1.91%)

#### 💻 Microsoft K-Line Chart
```
给我看看微软的K线图
```
**Expected Result**: MSFT candlestick chart with interactive zoom and pan features

#### 🔍 Mixed Language Query
```
Apple AAPL股票分析
```
**Expected Result**: Detects both English and Chinese, shows AAPL chart

## 🎨 Chart Features to Explore

### Interactive Elements
- **🔍 Zoom**: Use mouse wheel or zoom controls at bottom
- **📊 Hover Tooltips**: Hover over candlesticks for detailed OHLC data
- **📈 Volume Bars**: Bottom section shows trading volume
- **🎯 Price Indicators**: Current price and change percentage in title

### Visual Elements
- **🟢 Green Candles**: Price increased (close > open)
- **🔴 Red Candles**: Price decreased (close < open)
- **📊 Volume Colors**: Match corresponding candlestick colors
- **🎨 Professional Styling**: Gradient backgrounds and clean design

## 🔄 Complete Workflow Demo

### What Happens Behind the Scenes:

1. **🔍 Symbol Detection**
   - User types: "分析苹果公司AAPL的股票走势"
   - System detects: AAPL (Apple Inc.)
   - Confidence: 95% (company name + ticker match)

2. **📊 Data Fetching**
   - Calls AkShare API for AAPL data
   - Retrieves 120 trading days of OHLCV data
   - Caches for 5 minutes for performance

3. **🎨 Chart Generation**
   - Creates ECharts candlestick configuration
   - Adds volume bars, tooltips, zoom controls
   - Calculates price changes and trends

4. **💬 Message Display**
   - Shows chart as special message type
   - Displays current price and change
   - Follows with AI analysis report

5. **🤖 AI Analysis**
   - Continues normal workflow
   - Provides technical analysis
   - Gives investment recommendations

## 🧪 Test Scenarios

### ✅ Positive Test Cases
- [x] Chinese company names: "苹果公司", "特斯拉", "微软"
- [x] English company names: "Apple", "Tesla", "Microsoft"  
- [x] Stock tickers: "AAPL", "TSLA", "META", "MSFT"
- [x] Mixed language: "Apple AAPL股票"
- [x] Technical terms: "K线图", "stock chart", "技术分析"

### ❌ Negative Test Cases
- [x] Non-stock queries: "今天天气怎么样" (should skip chart generation)
- [x] Invalid tickers: "INVALID" (should gracefully handle errors)
- [x] Empty queries: "" (should show validation message)

## 📊 Sample Chart Data

Based on our demo run, here's what you'll see:

| Stock | Current Price | Change | Change % | Volume |
|-------|---------------|--------|----------|---------|
| AAPL  | $203.92      | +$3.29 | +1.64%   | 46.6M   |
| TSLA  | $295.14      | +$10.44| +3.67%   | 164.7M  |
| META  | $697.71      | +$13.09| +1.91%   | 11.7M   |
| MSFT  | $470.38      | +$2.70 | +0.58%   | 15.3M   |

## 🎯 Key Success Indicators

### ✅ Chart Display
- [ ] Chart appears before AI text response
- [ ] Interactive zoom and pan work smoothly
- [ ] Tooltips show accurate OHLC data
- [ ] Volume bars display correctly
- [ ] Price change colors are accurate

### ✅ Data Accuracy
- [ ] Current prices match market data
- [ ] Price changes calculated correctly
- [ ] Date ranges show recent trading days
- [ ] Volume data displays properly

### ✅ User Experience
- [ ] Fast chart generation (< 3 seconds)
- [ ] Responsive design on mobile
- [ ] Smooth scrolling in chat
- [ ] Professional visual styling

## 🔧 Troubleshooting

### Chart Not Appearing?
1. Check browser console for JavaScript errors
2. Verify backend server is running on port 8000
3. Test with simple query: "AAPL股票"

### Data Not Loading?
1. Check internet connection for AkShare API
2. Try different stock symbol: "TSLA", "META"
3. Check backend logs for API errors

### Styling Issues?
1. Hard refresh browser (Ctrl+F5 / Cmd+Shift+R)
2. Check if CSS files loaded properly
3. Test in different browser

## 🎉 Success Metrics

After testing, you should see:
- ✅ **100% Symbol Detection**: All major stocks detected correctly
- ✅ **Real-time Data**: Current market prices and changes
- ✅ **Interactive Charts**: Smooth zoom, pan, and hover
- ✅ **Professional UI**: Clean, modern chart styling
- ✅ **Dual Output**: Charts first, then AI analysis

## 🚀 Next Steps

1. **Production Deployment**: Ready for live environment
2. **User Training**: Share demo queries with users
3. **Monitoring**: Track chart generation performance
4. **Feedback**: Collect user experience feedback
5. **Enhancements**: Add more chart customization options

---

**🎊 Congratulations!** You now have a fully functional stock chart feature that enhances your AI analysis platform with interactive financial visualizations!

**📞 Support**: If you encounter any issues, check the logs in the terminal where the servers are running. 