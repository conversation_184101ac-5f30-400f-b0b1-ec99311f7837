#!/usr/bin/env python3
"""
增强型因子计算演示脚本
展示如何使用新的多维度技术因子计算功能
"""

import sys
import os
import asyncio
import pandas as pd
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.enhanced_factors import (
    EnhancedFactorCalculator, 
    FactorManager, 
    get_enhanced_factor_calculator,
    list_all_available_factors,
    get_factor_descriptions
)
from backend.data_manager import init_data_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedFactorsDemo:
    """增强型因子计算演示类"""
    
    def __init__(self, tushare_token: str = "your_token_here"):
        self.tushare_token = tushare_token
        self.data_manager = None
        self.factor_manager = None
        self.calculator = None
        
    def initialize(self):
        """初始化数据管理器和因子计算器"""
        try:
            print("🔄 正在初始化数据管理器和因子计算器...")
            
            # 初始化数据管理器
            self.data_manager = init_data_manager(self.tushare_token)
            
            # 初始化因子管理器
            self.factor_manager = FactorManager(self.data_manager)
            
            # 初始化因子计算器
            self.calculator = get_enhanced_factor_calculator()
            
            print("✅ 初始化完成！")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def show_available_factors(self):
        """显示所有可用的因子"""
        print("\n" + "="*80)
        print("📊 可用的增强型因子列表")
        print("="*80)
        
        try:
            # 获取因子分类
            categories = list_all_available_factors()
            descriptions = get_factor_descriptions()
            
            total_factors = 0
            for category, factors in categories.items():
                print(f"\n📈 {category.upper()} 因子 ({len(factors)}个):")
                print("-" * 60)
                
                for factor in factors:
                    desc = descriptions.get(factor, "无描述")
                    print(f"  • {factor}: {desc}")
                    total_factors += 1
            
            print(f"\n📊 总计: {total_factors} 个因子")
            
        except Exception as e:
            print(f"❌ 获取因子列表失败: {e}")
    
    def demo_single_stock_calculation(self, symbol: str = "AAPL"):
        """演示单只股票的因子计算"""
        print(f"\n" + "="*80)
        print(f"🔍 演示: 计算 {symbol} 的所有因子")
        print("="*80)
        
        try:
            # 计算所有因子
            print(f"📊 正在计算 {symbol} 的因子...")
            factors = self.factor_manager.calculate_factors_for_stock(symbol)
            
            if not factors:
                print(f"❌ 未能获取 {symbol} 的因子数据，可能是数据不可用")
                return
            
            print(f"✅ 成功计算 {len(factors)} 个因子")
            
            # 按分类显示结果
            categories = self.calculator.get_factor_categories()
            
            for category, factor_names in categories.items():
                category_factors = {name: factors[name] for name in factor_names if name in factors}
                
                if category_factors:
                    print(f"\n📈 {category.upper()} 因子:")
                    print("-" * 50)
                    
                    for factor_name, value in category_factors.items():
                        print(f"  {factor_name}: {value:.4f}")
            
        except Exception as e:
            print(f"❌ 计算失败: {e}")
    
    def demo_selected_factors_calculation(self, symbol: str = "AAPL"):
        """演示计算指定因子"""
        print(f"\n" + "="*80)
        print(f"🎯 演示: 计算 {symbol} 的指定因子")
        print("="*80)
        
        # 选择一些关键因子
        selected_factors = [
            'rsi', 'macd', 'bollinger_position', 
            'sma_20', 'ema_12', 'stoch_k', 'atr'
        ]
        
        try:
            print(f"🔍 正在计算以下因子: {', '.join(selected_factors)}")
            
            factors = self.factor_manager.calculate_factors_for_stock(
                symbol=symbol,
                factor_names=selected_factors
            )
            
            if not factors:
                print(f"❌ 未能获取 {symbol} 的因子数据")
                return
            
            print(f"\n✅ 成功计算 {len(factors)} 个指定因子:")
            print("-" * 50)
            
            for factor_name, value in factors.items():
                desc = get_factor_descriptions().get(factor_name, "")
                print(f"  {factor_name}: {value:.4f} ({desc})")
            
            # 简单的因子解读
            print(f"\n📋 简单解读:")
            self._interpret_factors(factors)
            
        except Exception as e:
            print(f"❌ 计算失败: {e}")
    
    def demo_batch_calculation(self, symbols: list = None):
        """演示批量计算"""
        if symbols is None:
            symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
        
        print(f"\n" + "="*80)
        print(f"🚀 演示: 批量计算多只股票的因子")
        print("="*80)
        
        # 选择几个关键因子进行批量计算
        key_factors = ['rsi', 'macd', 'bollinger_position', 'sma_20']
        
        try:
            print(f"📊 正在批量计算 {len(symbols)} 只股票的因子...")
            print(f"📈 股票列表: {', '.join(symbols)}")
            print(f"🎯 因子列表: {', '.join(key_factors)}")
            
            results = self.factor_manager.batch_calculate_factors(
                symbols=symbols,
                factor_names=key_factors
            )
            
            print(f"\n✅ 批量计算完成，成功计算 {len(results)} 只股票")
            
            # 显示结果表格
            self._display_batch_results(results, key_factors)
            
        except Exception as e:
            print(f"❌ 批量计算失败: {e}")
    
    def demo_factor_analysis(self, symbol: str = "AAPL"):
        """演示因子分析"""
        print(f"\n" + "="*80)
        print(f"📈 演示: {symbol} 因子分析报告")
        print("="*80)
        
        try:
            # 计算所有因子
            factors = self.factor_manager.calculate_factors_for_stock(symbol)
            
            if not factors:
                print(f"❌ 未能获取 {symbol} 的数据")
                return
            
            # 生成分析报告
            print(f"📊 {symbol} 技术因子分析报告")
            print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 60)
            
            # 动量分析
            print("\n🚀 动量分析:")
            momentum_factors = ['rsi', 'rsi_6', 'rsi_24', 'momentum', 'roc']
            for factor in momentum_factors:
                if factor in factors:
                    value = factors[factor]
                    interpretation = self._interpret_momentum_factor(factor, value)
                    print(f"  {factor}: {value:.2f} - {interpretation}")
            
            # 趋势分析
            print("\n📈 趋势分析:")
            trend_factors = ['macd', 'sma_20', 'sma_50', 'ema_12', 'ema_26']
            for factor in trend_factors:
                if factor in factors:
                    value = factors[factor]
                    print(f"  {factor}: {value:.2f}")
            
            # 波动率分析
            print("\n📊 波动率分析:")
            volatility_factors = ['atr', 'bollinger_width', 'price_volatility']
            for factor in volatility_factors:
                if factor in factors:
                    value = factors[factor]
                    print(f"  {factor}: {value:.4f}")
            
            # 综合评分
            self._generate_overall_score(factors)
            
        except Exception as e:
            print(f"❌ 因子分析失败: {e}")
    
    def _interpret_factors(self, factors: dict):
        """简单的因子解读"""
        try:
            rsi = factors.get('rsi', 50)
            if rsi > 70:
                print(f"  📊 RSI = {rsi:.1f}: 可能超买，考虑卖出信号")
            elif rsi < 30:
                print(f"  📊 RSI = {rsi:.1f}: 可能超卖，考虑买入信号")
            else:
                print(f"  📊 RSI = {rsi:.1f}: 处于正常区间")
            
            bollinger_pos = factors.get('bollinger_position', 0.5)
            if bollinger_pos > 0.8:
                print(f"  📊 布林带位置 = {bollinger_pos:.2f}: 价格接近上轨，可能回调")
            elif bollinger_pos < 0.2:
                print(f"  📊 布林带位置 = {bollinger_pos:.2f}: 价格接近下轨，可能反弹")
            else:
                print(f"  📊 布林带位置 = {bollinger_pos:.2f}: 价格在正常区间")
            
            macd = factors.get('macd', 0)
            if macd > 0:
                print(f"  📊 MACD = {macd:.4f}: 多头信号")
            else:
                print(f"  📊 MACD = {macd:.4f}: 空头信号")
                
        except Exception as e:
            print(f"  ❌ 因子解读失败: {e}")
    
    def _interpret_momentum_factor(self, factor_name: str, value: float) -> str:
        """解读动量因子"""
        if 'rsi' in factor_name:
            if value > 70:
                return "超买"
            elif value < 30:
                return "超卖"
            else:
                return "正常"
        elif factor_name == 'momentum':
            return "上涨动量" if value > 0 else "下跌动量"
        elif factor_name == 'roc':
            return f"{'上涨' if value > 0 else '下跌'} {abs(value):.1f}%"
        else:
            return "正常"
    
    def _display_batch_results(self, results: dict, factors: list):
        """显示批量计算结果"""
        if not results:
            print("❌ 没有计算结果")
            return
        
        print(f"\n📊 批量计算结果表格:")
        print("-" * 80)
        
        # 表头
        header = f"{'股票':<8}"
        for factor in factors:
            header += f"{factor:<12}"
        print(header)
        print("-" * 80)
        
        # 数据行
        for symbol, factor_values in results.items():
            row = f"{symbol:<8}"
            for factor in factors:
                value = factor_values.get(factor, 0.0)
                row += f"{value:<12.2f}"
            print(row)
    
    def _generate_overall_score(self, factors: dict):
        """生成综合评分"""
        print(f"\n🎯 综合技术评分:")
        print("-" * 30)
        
        try:
            score = 0
            total_weight = 0
            
            # RSI 评分 (权重: 20%)
            rsi = factors.get('rsi', 50)
            if 30 <= rsi <= 70:
                rsi_score = 80
            elif 20 <= rsi < 30 or 70 < rsi <= 80:
                rsi_score = 60
            else:
                rsi_score = 30
            score += rsi_score * 0.2
            total_weight += 0.2
            
            # MACD 评分 (权重: 25%)
            macd = factors.get('macd', 0)
            macd_histogram = factors.get('macd_histogram', 0)
            if macd > 0 and macd_histogram > 0:
                macd_score = 85
            elif macd > 0 or macd_histogram > 0:
                macd_score = 60
            else:
                macd_score = 35
            score += macd_score * 0.25
            total_weight += 0.25
            
            # 布林带评分 (权重: 15%)
            bollinger_pos = factors.get('bollinger_position', 0.5)
            if 0.2 <= bollinger_pos <= 0.8:
                bollinger_score = 75
            else:
                bollinger_score = 45
            score += bollinger_score * 0.15
            total_weight += 0.15
            
            # 趋势评分 (权重: 25%)
            sma_20 = factors.get('sma_20', 0)
            current_price = factors.get('close', sma_20)  # 假设当前价格
            if current_price > sma_20:
                trend_score = 75
            else:
                trend_score = 45
            score += trend_score * 0.25
            total_weight += 0.25
            
            # 波动率评分 (权重: 15%)
            atr = factors.get('atr', 0)
            volatility_score = min(80, max(40, 80 - atr * 10))  # 简化评分
            score += volatility_score * 0.15
            total_weight += 0.15
            
            final_score = score / total_weight if total_weight > 0 else 50
            
            print(f"  综合评分: {final_score:.1f}/100")
            
            if final_score >= 80:
                rating = "🟢 强烈看多"
            elif final_score >= 70:
                rating = "🔵 看多"
            elif final_score >= 60:
                rating = "🟡 中性"
            elif final_score >= 50:
                rating = "🟠 看空"
            else:
                rating = "🔴 强烈看空"
            
            print(f"  技术评级: {rating}")
            
        except Exception as e:
            print(f"  ❌ 评分计算失败: {e}")
    
    def run_full_demo(self):
        """运行完整的演示"""
        print("🚀 增强型因子计算演示程序")
        print("="*80)
        
        try:
            # 初始化
            self.initialize()
            
            # 1. 显示可用因子
            self.show_available_factors()
            
            # 2. 单股票因子计算
            self.demo_single_stock_calculation("AAPL")
            
            # 3. 指定因子计算
            self.demo_selected_factors_calculation("AAPL")
            
            # 4. 批量计算
            self.demo_batch_calculation(["AAPL", "MSFT", "GOOGL"])
            
            # 5. 因子分析报告
            self.demo_factor_analysis("AAPL")
            
            print(f"\n🎉 演示完成！")
            print("💡 您可以通过API接口或直接调用Python模块来使用这些功能")
            
        except Exception as e:
            print(f"❌ 演示运行失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强型因子计算演示')
    parser.add_argument('--token', '-t', type=str, default='your_token_here',
                       help='Tushare API token')
    parser.add_argument('--symbol', '-s', type=str, default='AAPL',
                       help='要分析的股票代码')
    parser.add_argument('--demo', '-d', type=str, 
                       choices=['list', 'single', 'selected', 'batch', 'analysis', 'full'],
                       default='full',
                       help='要运行的演示类型')
    
    args = parser.parse_args()
    
    # 创建演示实例
    demo = EnhancedFactorsDemo(args.token)
    
    try:
        if args.demo == 'full':
            demo.run_full_demo()
        else:
            demo.initialize()
            
            if args.demo == 'list':
                demo.show_available_factors()
            elif args.demo == 'single':
                demo.demo_single_stock_calculation(args.symbol)
            elif args.demo == 'selected':
                demo.demo_selected_factors_calculation(args.symbol)
            elif args.demo == 'batch':
                demo.demo_batch_calculation()
            elif args.demo == 'analysis':
                demo.demo_factor_analysis(args.symbol)
                
    except KeyboardInterrupt:
        print("\n🛑 用户中断演示")
    except Exception as e:
        print(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    main() 