#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
AkShare 股票新闻工具使用示例

这个脚本演示了如何使用 akshare 新闻工具获取股票相关新闻。
"""

import json
import pandas as pd
from datetime import datetime
from src.tools.akshare.stock_news import (
    stock_news_em_tool,
    stock_news_main_cx_tool,
    news_report_time_baidu_tool,
    comprehensive_stock_news_tool,
)


def test_large_news_fetch():
    """测试大量新闻获取功能"""
    print("\n" + "=" * 80)
    print("LARGE NEWS FETCH TEST - Getting maximum news data")
    print("=" * 80)
    
    # Test with popular tickers that should have lots of news
    test_tickers = ["AAPL", "TSLA", "NVDA", "300059", "000001"]
    
    for ticker in test_tickers:
        print(f"\n📰 Testing large news fetch for {ticker}")
        print("-" * 50)
        try:
            result = stock_news_em_tool.invoke({
                "symbol": ticker,
                "max_rows": 100  # Get maximum possible news (up to 100 as per akshare docs)
            })
            data = json.loads(result)
            if "data" in data and data["data"]:
                print(f"✅ Successfully fetched {data['displayed_rows']} news items (Total: {data['total_rows']})")
                print(f"📊 Sample of latest 3 news:")
                for i, news in enumerate(data["data"][:3], 1):
                    print(f"   {i}. {news['新闻标题'][:60]}...")
                    print(f"      Time: {news['发布时间']} | Source: {news['文章来源']}")
            else:
                print(f"❌ No news found for {ticker}")
        except Exception as e:
            print(f"❌ Error fetching news for {ticker}: {e}")
        print()


def main():
    """主函数，演示各种新闻工具的使用"""
    
    print("=" * 60)
    print("AkShare 股票新闻工具使用示例")
    print("=" * 60)
    
    # Get today's date in YYYYMMDD format
    today_date_yyyymmdd = datetime.now().strftime('%Y%m%d')
    print(f"\nToday's date: {today_date_yyyymmdd}")
    
    # Define example tickers
    us_stock_ticker = "AAPL" # Apple Inc.
    cn_stock_ticker = "000001" # Ping An Bank Co., Ltd. (Example A-share ticker)
    
    # 1. Get news for a specific stock (e.g., AAPL) - Get more news
    print(f"\n1. Get news for US stock ticker ({us_stock_ticker}, from Eastmoney)")
    print("-" * 60)
    try:
        result = stock_news_em_tool.invoke({
            "symbol": us_stock_ticker,
            "max_rows": 50  # Increased from 3 to 50
        })
        data = json.loads(result)
        if "data" in data and data["data"]:
            print(f"Fetched {data['displayed_rows']} news items (Total: {data['total_rows']})")
            print(f"Showing first 10 news items:")
            print()
            for i, news in enumerate(data["data"][:10], 1):  # Show first 10 instead of 2
                print(f"{i:2d}. {news['新闻标题']}")
                print(f"     Publish Time: {news['发布时间']}")
                print(f"     Source: {news['文章来源']}")
                print(f"     Link: {news['新闻链接']}")
                print()
        else:
            print("No relevant news found")
    except Exception as e:
        print(f"Failed to get news for {us_stock_ticker}: {e}")
    
    # 2. Get news for an A-share stock (e.g., 000001) - Get more news
    print(f"\n2. Get news for A-share stock ticker ({cn_stock_ticker}, from Eastmoney)")
    print("-" * 60)
    try:
        result = stock_news_em_tool.invoke({
            "symbol": cn_stock_ticker,
            "max_rows": 50  # Increased from 3 to 50
        })
        data = json.loads(result)
        if "data" in data and data["data"]:
            print(f"Fetched {data['displayed_rows']} news items (Total: {data['total_rows']})")
            print(f"Showing first 10 news items:")
            print()
            for i, news in enumerate(data["data"][:10], 1):  # Show first 10 instead of 2
                print(f"{i:2d}. {news['新闻标题']}")
                print(f"     Publish Time: {news['发布时间']}")
                print(f"     Source: {news['文章来源']}")
                print(f"     Link: {news['新闻链接']}")
                print()
        else:
            print("No relevant news found")
    except Exception as e:
        print(f"Failed to get news for {cn_stock_ticker}: {e}")
    
    # 3. Get financial content highlights - Get more news
    print("\n3. Get financial content highlights (Caixin)")
    print("-" * 60)
    try:
        result = stock_news_main_cx_tool.invoke({"max_rows": 30})  # Increased from 3 to 30
        data = json.loads(result)
        if "data" in data and data["data"]:
            print(f"Fetched {data['displayed_rows']} financial news items (Total: {data['total_rows']})")
            print(f"Showing first 8 financial news items:")
            print()
            for i, news in enumerate(data["data"][:8], 1):  # Show first 8 instead of 2
                print(f"{i:2d}. {news['tag']}")
                print(f"     Summary: {news['summary'][:80]}...")
                print(f"     Publish Time: {news['pub_time']}")
                print(f"     Link: {news['url']}")
                print()
        else:
            print("No financial news found")
    except Exception as e:
        print(f"Failed to get financial news: {e}")
    
    # 4. Get earnings release schedule for today
    print(f"\n4. Get earnings release schedule for {today_date_yyyymmdd} (Baidu Stock)")
    print("-" * 60)
    try:
        result = news_report_time_baidu_tool.invoke({
            "date": today_date_yyyymmdd,
            "max_rows": 5
        })
        data = json.loads(result)
        if "data" in data and data["data"]:
            print(f"Fetched {data['displayed_rows']} earnings report entries")
            for i, report in enumerate(data["data"][:3], 1):
                print(f"{i}. {report['股票简称']} ({report['股票代码']})")
                print(f"   Exchange: {report['交易所']}")
                print(f"   Report Period: {report['财报期']}")
                print()
        else:
            print(f"No earnings releases scheduled for {today_date_yyyymmdd}")
    except Exception as e:
        print(f"Failed to get earnings schedule: {e}")
    
    # 5. Comprehensive news tool example (e.g., AAPL) - Get more news
    print(f"\n5. Comprehensive news tool ({us_stock_ticker})")
    print("-" * 60)
    try:
        result = comprehensive_stock_news_tool.invoke({
            "symbol": us_stock_ticker,
            "include_general_news": True,
            "date_for_reports": today_date_yyyymmdd, # Use today's date
            "max_rows_per_source": 20  # Increased from 2 to 20
        })
        data = json.loads(result)
        
        print(f"Query Symbol: {data['symbol']}")
        print(f"Query Time: {data['timestamp']}")
        print()
        
        # Display results from each source
        for source_name, source_data in data["data_sources"].items():
            print(f"Data Source: {source_name}")
            if "data" in source_data:
                print(f"  Source: {source_data['source']}")
                print(f"  Number of items: {source_data['displayed_rows']}")
                if source_data["data"]:
                    first_item = source_data["data"][0]
                    if "新闻标题" in first_item:
                        print(f"  Example: {first_item['新闻标题'][:30]}...")
                    elif "tag" in first_item:
                        print(f"  Example: {first_item['tag'][:30]}...")
                    elif "股票简称" in first_item:
                        print(f"  Example: {first_item['股票简称']} - {first_item['财报期']}")
            elif "error" in source_data:
                print(f"  Error: {source_data['error']}")
            elif "message" in source_data:
                print(f"  Message: {source_data['message']}")
            print()
            
    except Exception as e:
        print(f"Comprehensive news tool failed: {e}")
    
    # 6. Test large news fetch functionality
    test_large_news_fetch()
    
    print("=" * 60)
    print("Example finished!")
    print("=" * 60)


if __name__ == "__main__":
    main() 