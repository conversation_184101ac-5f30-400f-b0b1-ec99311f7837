# AkShare US Stock Data Tools

This module provides tools for fetching US stock data based on the AkShare library, supporting real-time quotes, historical data, company search, and more.

## Features

- 🔍 **Stock Search**: Search for US stock codes by company name
- 📊 **Real-time Quotes**: Get real-time US stock prices and trading data
- 📈 **Historical Data**: Get daily, weekly, and monthly historical data, supporting adjusted prices
- 🏆 **Famous US Stocks**: Get data for famous US stocks by category
- 💰 **Pink Sheets**: Get data for Pink Sheets (OTC) stocks
- ⏰ **Intraday Data**: Get minute-level real-time trading data

## Installation Dependencies

```bash
pip install akshare pandas
```

## Tool List

### 1. us_stock_search_tool - Stock Search Tool

Searches for US stock codes and basic information by company name.

**Parameters:**
- `company_name` (str): Company name, e.g., "Apple Inc.", "Microsoft"
- `max_rows` (int): Maximum number of rows to return, default 10

**Example:**
```python
from src.tools.akshare import us_stock_search_tool

result = us_stock_search_tool.invoke({
    "company_name": "Apple Inc.",
    "max_rows": 5
})
```

### 2. us_stock_spot_tool - Real-time Quotes Tool

Gets real-time US stock market data.

**Parameters:**
- `max_rows` (int): Maximum number of rows to return, default 100

**Example:**
```python
from src.tools.akshare import us_stock_spot_tool

result = us_stock_spot_tool.invoke({
    "max_rows": 50
})
```

### 3. us_stock_hist_tool - Historical Data Tool

Gets historical K-line data for US stocks.

**Parameters:**
- `symbol` (str): US stock code, e.g., "105.AAPL"
- `period` (str): Time period, optional "daily"/"weekly"/"monthly", default "daily"
- `start_date` (str, optional): Start date, format YYYYMMDD
- `end_date` (str, optional): End date, format YYYYMMDD
- `adjust` (str): Adjustment type, ""(no adjustment)/"qfq"(forward adjustment)/"hfq"(backward adjustment), default "qfq"
- `days_back` (int): Number of days back to get data if dates are not specified, default 365
- `max_rows` (int): Maximum number of rows to return, default 500

**Example:**
```python
from src.tools.akshare import us_stock_hist_tool

# Get daily data for Apple for the past year
result = us_stock_hist_tool.invoke({
    "symbol": "105.AAPL",
    "period": "daily",
    "adjust": "qfq",
    "days_back": 365,
    "max_rows": 100
})

# Get data for a specified date range
result = us_stock_hist_tool.invoke({
    "symbol": "105.AAPL",
    "start_date": "20240101",
    "end_date": "20241231"
})
```

### 4. us_stock_famous_tool - Famous US Stocks Tool

Gets classified data for famous US stocks.

**Parameters:**
- `category` (str): Stock category, optional:
  - "科技类"
  - "金融类" 
  - "医药食品类"
  - "媒体类"
  - "汽车能源类"
  - "制造零售类"
- `max_rows` (int): Maximum number of rows to return, default 50

**Example:**
```python
from src.tools.akshare import us_stock_famous_tool

result = us_stock_famous_tool.invoke({
    "category": "科技类",
    "max_rows": 20
})
```

### 5. us_stock_pink_tool - Pink Sheets Tool

Gets real-time market data for US Pink Sheets (OTC) stocks.

**Parameters:**
- `max_rows` (int): Maximum number of rows to return, default 50

**Example:**
```python
from src.tools.akshare import us_stock_pink_tool

result = us_stock_pink_tool.invoke({
    "max_rows": 30
})
```

### 6. us_stock_minute_tool - Intraday Data Tool

Gets minute-level real-time trading data for US stocks.

**Parameters:**
- `symbol` (str): US stock code, e.g., "105.AAPL"
- `start_date` (str, optional): Start date and time, format "YYYY-MM-DD HH:MM:SS"
- `end_date` (str, optional): End date and time, format "YYYY-MM-DD HH:MM:SS"
- `max_rows` (int): Maximum number of rows to return, default 500

**Example:**
```python
from src.tools.akshare import us_stock_minute_tool

result = us_stock_minute_tool.invoke({
    "symbol": "105.AAPL",
    "max_rows": 100
})
```

### 7. us_stock_daily_sina_tool - Sina Finance Historical Data Tool

Uses Sina Finance API to get US stock historical data, supporting forward adjustment.

**Parameters:**
- `symbol` (str): US stock code, e.g., "AAPL" (Note: no prefix needed here)
- `adjust` (str): Adjustment type, ""(no adjustment)/"qfq"(forward adjustment), default "qfq"
- `max_rows` (int): Maximum number of rows to return, default 1000

**Example:**
```python
from src.tools.akshare import us_stock_daily_sina_tool

# Get forward adjusted historical data for Apple
result = us_stock_daily_sina_tool.invoke({
    "symbol": "AAPL",
    "adjust": "qfq",
    "max_rows": 500
})

# Get unadjusted data
result = us_stock_daily_sina_tool.invoke({
    "symbol": "AAPL",
    "adjust": "",
    "max_rows": 500
})
```

**Note:**
- Sina Finance API only supports ""(no adjustment) and "qfq"(forward adjustment)
- Data may have a 15-minute delay
- Returns complete historical data, large data volume

## Data Format

All tools return data in JSON format, containing the following fields:

```json
{
  "data": [...],           // Actual data array
  "total_rows": 1000,      // Total rows
  "displayed_rows": 100,   // Displayed rows
  "message": "Displaying first 100 rows, total 1000 rows"
}
```

## Usage Example

See `example.py` file for complete usage examples.

```bash
cd src/tools/akshare
python example.py
```

## Important Notes

1. **Data Delay**: US stock data may have a 15-minute delay
2. **API Limits**: AkShare may have rate limits
3. **Network Dependency**: Requires a stable network connection
4. **Data Accuracy**: Data is for reference only, invest with caution

## Error Handling

All tools include comprehensive error handling mechanisms, and will return JSON with error information when an error occurs:

```json
{
  "error": "Error description",
  "function": "Function name",
  "error_type": "Error type"
}
```

## Dependencies

- `akshare`: Financial data interface library
- `pandas`: Data processing library
- `langchain_core`: LangChain core library

## License

MIT License 