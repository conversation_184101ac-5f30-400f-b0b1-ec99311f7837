# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
import numpy as np
from typing import Annotated, Dict, Any, List, Optional
from langchain_core.tools import tool
from src.tools.decorators import log_io

logger = logging.getLogger(__name__)


def calculate_sma(data: pd.Series, window: int) -> pd.Series:
    """计算简单移动平均线"""
    return data.rolling(window=window, min_periods=1).mean()


def calculate_ema(data: pd.Series, span: int) -> pd.Series:
    """计算指数移动平均线"""
    return data.ewm(span=span, adjust=False).mean()


def calculate_rsi(data: pd.Series, window: int = 14) -> pd.Series:
    """计算RSI相对强弱指数"""
    delta = data.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.ewm(span=window, adjust=False).mean()
    avg_loss = loss.ewm(span=window, adjust=False).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
    """计算MACD指标"""
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    
    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return {
        'MACD': macd_line,
        'Signal': signal_line,
        'Histogram': histogram
    }


def calculate_kdj(high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Dict[str, pd.Series]:
    """计算KDJ随机指标"""
    lowest_low = low.rolling(window=k_period, min_periods=1).min()
    highest_high = high.rolling(window=k_period, min_periods=1).max()
    
    rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
    rsv = rsv.fillna(50)  # 处理除零情况
    
    k = rsv.ewm(alpha=1/d_period, adjust=False).mean()
    d = k.ewm(alpha=1/j_period, adjust=False).mean()
    j = 3 * k - 2 * d
    
    return {
        'K': k,
        'D': d,
        'J': j
    }


def calculate_bollinger_bands(data: pd.Series, window: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
    """计算布林带"""
    sma = calculate_sma(data, window)
    std = data.rolling(window=window, min_periods=1).std()
    
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    
    return {
        'Upper': upper,
        'Middle': sma,
        'Lower': lower
    }


def calculate_volume_indicators(volume: pd.Series) -> Dict[str, pd.Series]:
    """计算成交量指标"""
    return {
        'Volume_SMA_5': calculate_sma(volume, 5),
        'Volume_SMA_10': calculate_sma(volume, 10),
        'Volume_SMA_20': calculate_sma(volume, 20)
    }


@tool
@log_io
def calculate_technical_indicators(
    stock_data_json: Annotated[str, "股票数据的JSON字符串，包含date, open, high, low, close, volume字段"],
    indicators: Annotated[Optional[List[str]], "要计算的指标列表，默认计算所有常用指标"] = None,
    ma_periods: Annotated[Optional[List[int]], "移动平均线周期列表，默认[5,10,20,50,200]"] = None,
) -> str:
    """
    计算股票技术指标的专业工具
    
    支持的指标包括：
    - SMA: 简单移动平均线（多个周期）
    - EMA: 指数移动平均线（12日、26日）
    - MACD: 移动平均收敛发散指标
    - RSI: 相对强弱指数
    - KDJ: 随机指标
    - BOLL: 布林带
    - Volume: 成交量指标
    """
    try:
        # 解析输入数据
        stock_data = json.loads(stock_data_json)
        
        if isinstance(stock_data, dict) and 'data' in stock_data:
            data_list = stock_data['data']
        elif isinstance(stock_data, list):
            data_list = stock_data
        else:
            return json.dumps({
                "error": "数据格式不正确，需要包含股票价格数据的JSON"
            }, ensure_ascii=False)
        
        if not data_list:
            return json.dumps({
                "error": "股票数据为空"
            }, ensure_ascii=False)
        
        # 转换为DataFrame
        df = pd.DataFrame(data_list)
        
        # 确保必要的列存在
        required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            return json.dumps({
                "error": f"缺少必要的数据列: {missing_cols}"
            }, ensure_ascii=False)
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 确保数值列为浮点类型
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 设置默认参数
        if ma_periods is None:
            ma_periods = [5, 10, 20, 50, 200]
        
        if indicators is None:
            indicators = ['SMA', 'EMA', 'MACD', 'RSI', 'KDJ', 'BOLL', 'Volume']
        
        result = {
            "message": "技术指标计算完成",
            "data_points": len(df),
            "date_range": {
                "start": df['date'].min().strftime('%Y-%m-%d'),
                "end": df['date'].max().strftime('%Y-%m-%d')
            },
            "indicators": {}
        }
        
        # 计算简单移动平均线
        if 'SMA' in indicators:
            sma_data = {}
            for period in ma_periods:
                if len(df) >= period:
                    sma_values = calculate_sma(df['close'], period)
                    # 只保留最近30个有效值
                    recent_sma = sma_values.tail(30)
                    sma_data[f'SMA_{period}'] = {
                        'latest_value': float(recent_sma.iloc[-1]) if not pd.isna(recent_sma.iloc[-1]) else None,
                        'period': period,
                        'valid_data_points': int(recent_sma.notna().sum()),
                        'recent_values': [float(x) if not pd.isna(x) else None for x in recent_sma.tail(10)]  # 最近10个值
                    }
                else:
                    sma_data[f'SMA_{period}'] = {
                        'latest_value': None,
                        'period': period,
                        'valid_data_points': 0,
                        'note': f'数据量不足，需要至少{period}个交易日'
                    }
            result['indicators']['SMA'] = sma_data
        
        # 计算指数移动平均线
        if 'EMA' in indicators:
            ema_12 = calculate_ema(df['close'], 12)
            ema_26 = calculate_ema(df['close'], 26)
            result['indicators']['EMA'] = {
                'EMA_12': {
                    'latest_value': float(ema_12.iloc[-1]) if not pd.isna(ema_12.iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in ema_12.tail(10)]
                },
                'EMA_26': {
                    'latest_value': float(ema_26.iloc[-1]) if not pd.isna(ema_26.iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in ema_26.tail(10)]
                }
            }
        
        # 计算MACD
        if 'MACD' in indicators:
            macd_data = calculate_macd(df['close'])
            result['indicators']['MACD'] = {
                'MACD_Line': {
                    'latest_value': float(macd_data['MACD'].iloc[-1]) if not pd.isna(macd_data['MACD'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in macd_data['MACD'].tail(10)]
                },
                'Signal_Line': {
                    'latest_value': float(macd_data['Signal'].iloc[-1]) if not pd.isna(macd_data['Signal'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in macd_data['Signal'].tail(10)]
                },
                'Histogram': {
                    'latest_value': float(macd_data['Histogram'].iloc[-1]) if not pd.isna(macd_data['Histogram'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in macd_data['Histogram'].tail(10)]
                },
                'signal': 'BULLISH' if macd_data['MACD'].iloc[-1] > macd_data['Signal'].iloc[-1] else 'BEARISH'
            }
        
        # 计算RSI
        if 'RSI' in indicators:
            rsi_14 = calculate_rsi(df['close'], 14)
            latest_rsi = float(rsi_14.iloc[-1]) if not pd.isna(rsi_14.iloc[-1]) else None
            
            rsi_signal = 'NEUTRAL'
            if latest_rsi is not None:
                if latest_rsi > 70:
                    rsi_signal = 'OVERBOUGHT'
                elif latest_rsi < 30:
                    rsi_signal = 'OVERSOLD'
            
            result['indicators']['RSI'] = {
                'RSI_14': {
                    'latest_value': latest_rsi,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in rsi_14.tail(10)],
                    'signal': rsi_signal,
                    'interpretation': {
                        'overbought_threshold': 70,
                        'oversold_threshold': 30
                    }
                }
            }
        
        # 计算KDJ
        if 'KDJ' in indicators:
            kdj_data = calculate_kdj(df['high'], df['low'], df['close'])
            result['indicators']['KDJ'] = {
                'K': {
                    'latest_value': float(kdj_data['K'].iloc[-1]) if not pd.isna(kdj_data['K'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in kdj_data['K'].tail(10)]
                },
                'D': {
                    'latest_value': float(kdj_data['D'].iloc[-1]) if not pd.isna(kdj_data['D'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in kdj_data['D'].tail(10)]
                },
                'J': {
                    'latest_value': float(kdj_data['J'].iloc[-1]) if not pd.isna(kdj_data['J'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in kdj_data['J'].tail(10)]
                }
            }
        
        # 计算布林带
        if 'BOLL' in indicators:
            boll_data = calculate_bollinger_bands(df['close'])
            latest_close = df['close'].iloc[-1]
            upper = boll_data['Upper'].iloc[-1]
            lower = boll_data['Lower'].iloc[-1]
            
            position = 'MIDDLE'
            if not pd.isna(upper) and not pd.isna(lower):
                if latest_close > upper:
                    position = 'ABOVE_UPPER'
                elif latest_close < lower:
                    position = 'BELOW_LOWER'
            
            result['indicators']['BOLL'] = {
                'Upper_Band': {
                    'latest_value': float(upper) if not pd.isna(upper) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in boll_data['Upper'].tail(10)]
                },
                'Middle_Band': {
                    'latest_value': float(boll_data['Middle'].iloc[-1]) if not pd.isna(boll_data['Middle'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in boll_data['Middle'].tail(10)]
                },
                'Lower_Band': {
                    'latest_value': float(lower) if not pd.isna(lower) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in boll_data['Lower'].tail(10)]
                },
                'position': position
            }
        
        # 计算成交量指标
        if 'Volume' in indicators:
            volume_data = calculate_volume_indicators(df['volume'])
            result['indicators']['Volume'] = {
                'Volume_SMA_5': {
                    'latest_value': float(volume_data['Volume_SMA_5'].iloc[-1]) if not pd.isna(volume_data['Volume_SMA_5'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in volume_data['Volume_SMA_5'].tail(10)]
                },
                'Volume_SMA_20': {
                    'latest_value': float(volume_data['Volume_SMA_20'].iloc[-1]) if not pd.isna(volume_data['Volume_SMA_20'].iloc[-1]) else None,
                    'recent_values': [float(x) if not pd.isna(x) else None for x in volume_data['Volume_SMA_20'].tail(10)]
                },
                'latest_volume': float(df['volume'].iloc[-1]),
                'avg_volume_20d': float(volume_data['Volume_SMA_20'].iloc[-1]) if not pd.isna(volume_data['Volume_SMA_20'].iloc[-1]) else None
            }
        
        # 添加数据质量和建议
        result['data_quality'] = {
            'total_data_points': len(df),
            'sufficient_for_long_term_analysis': len(df) >= 200,
            'sufficient_for_short_term_analysis': len(df) >= 50,
            'recommendations': []
        }
        
        if len(df) < 50:
            result['data_quality']['recommendations'].append("数据量较少，建议获取更多历史数据以提高分析准确性")
        if len(df) < 200:
            result['data_quality']['recommendations'].append("长期移动平均线（如200日线）可能不够准确")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"技术指标计算错误: {e}")
        return json.dumps({
            "error": f"技术指标计算失败: {str(e)}"
        }, ensure_ascii=False)


@tool
@log_io
def fibonacci_retracement_levels(
    high_price: Annotated[float, "时间段内的最高价"],
    low_price: Annotated[float, "时间段内的最低价"],
    current_price: Annotated[Optional[float], "当前价格（可选）"] = None,
) -> str:
    """
    计算斐波那契回撤位
    
    斐波那契回撤位是技术分析中常用的支撑和阻力位计算方法
    """
    try:
        price_range = high_price - low_price
        
        # 标准斐波那契比例
        fibonacci_levels = {
            '0%': high_price,
            '23.6%': high_price - (price_range * 0.236),
            '38.2%': high_price - (price_range * 0.382),
            '50%': high_price - (price_range * 0.5),
            '61.8%': high_price - (price_range * 0.618),
            '78.6%': high_price - (price_range * 0.786),
            '100%': low_price
        }
        
        result = {
            "fibonacci_retracement_levels": {
                "high_price": high_price,
                "low_price": low_price,
                "price_range": price_range,
                "levels": fibonacci_levels
            }
        }
        
        if current_price is not None:
            # 分析当前价格位置
            result["current_analysis"] = {
                "current_price": current_price,
                "position_analysis": []
            }
            
            for level_name, level_price in fibonacci_levels.items():
                distance = abs(current_price - level_price)
                percentage_from_range = (distance / price_range) * 100
                
                if distance / price_range < 0.02:  # 2%以内认为接近
                    result["current_analysis"]["position_analysis"].append({
                        "level": level_name,
                        "price": level_price,
                        "distance": distance,
                        "status": "NEAR_LEVEL"
                    })
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"斐波那契回撤位计算错误: {e}")
        return json.dumps({
            "error": f"斐波那契回撤位计算失败: {str(e)}"
        }, ensure_ascii=False) 