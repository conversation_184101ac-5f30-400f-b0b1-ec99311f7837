# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import Any, Dict, Optional, Type, Annotated
from urllib.parse import urlencode

import requests
from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.tools.decorators import create_logged_tool, log_io

logger = logging.getLogger(__name__)


class AlphaVantageInput(BaseModel):
    """Alpha Vantage API 工具的输入参数"""
    
    function: str = Field(
        description="API函数名称，如 TIME_SERIES_DAILY, GLOBAL_QUOTE, SYMBOL_SEARCH, NEWS_SENTIMENT 等"
    )
    symbol: Optional[str] = Field(
        default=None,
        description="股票代码，如 AAPL, MSFT, TSLA 等（某些函数需要）"
    )
    interval: Optional[str] = Field(
        default=None,
        description="时间间隔，用于日内数据：1min, 5min, 15min, 30min, 60min"
    )
    outputsize: Optional[str] = Field(
        default="compact",
        description="输出大小：compact（最近100个数据点）或 full（完整历史数据）"
    )
    datatype: Optional[str] = Field(
        default="json",
        description="数据格式：json 或 csv"
    )
    keywords: Optional[str] = Field(
        default=None,
        description="搜索关键词，用于 SYMBOL_SEARCH 函数"
    )
    tickers: Optional[str] = Field(
        default=None,
        description="股票代码列表（逗号分隔），用于新闻情感分析"
    )
    time_period: Optional[int] = Field(
        default=None,
        description="技术指标的时间周期"
    )
    series_type: Optional[str] = Field(
        default="close",
        description="技术指标使用的价格类型：open, high, low, close"
    )
    additional_params: Optional[Dict[str, Any]] = Field(
        default=None,
        description="其他API参数的字典"
    )


@tool
@log_io
def alpha_vantage_tool(
    function: Annotated[str, AlphaVantageInput.model_fields["function"].description],
    symbol: Annotated[Optional[str], AlphaVantageInput.model_fields["symbol"].description] = None,
    interval: Annotated[Optional[str], AlphaVantageInput.model_fields["interval"].description] = None,
    outputsize: Annotated[Optional[str], AlphaVantageInput.model_fields["outputsize"].description] = "compact",
    datatype: Annotated[Optional[str], AlphaVantageInput.model_fields["datatype"].description] = "json",
    keywords: Annotated[Optional[str], AlphaVantageInput.model_fields["keywords"].description] = None,
    tickers: Annotated[Optional[str], AlphaVantageInput.model_fields["tickers"].description] = None,
    time_period: Annotated[Optional[int], AlphaVantageInput.model_fields["time_period"].description] = None,
    series_type: Annotated[Optional[str], AlphaVantageInput.model_fields["series_type"].description] = "close",
    additional_params: Annotated[Optional[Dict[str, Any]], AlphaVantageInput.model_fields["additional_params"].description] = None,
) -> str:
    """Alpha Vantage 金融数据API工具，用于获取股票、外汇、数字货币和经济数据。

    支持的主要功能：
    - 股票数据：TIME_SERIES_DAILY（日线数据）, GLOBAL_QUOTE（实时报价）, SYMBOL_SEARCH（股票搜索）
    - 新闻分析：NEWS_SENTIMENT（新闻情感分析）
    - 技术指标：SMA（移动平均）, RSI（相对强弱指数）, MACD（指数平滑移动平均）等
    - 基本面：OVERVIEW（公司概览）, EARNINGS（财报）, INCOME_STATEMENT（损益表）等
    - 外汇：CURRENCY_EXCHANGE_RATE（汇率）, FX_DAILY（外汇日线）等
    - 数字货币：DIGITAL_CURRENCY_DAILY（数字货币日线）等
    - 经济指标：REAL_GDP（GDP）, UNEMPLOYMENT（失业率）, INFLATION（通胀率）等

    示例用法：
    - 获取苹果股票日线数据：function="TIME_SERIES_DAILY", symbol="AAPL"
    - 搜索股票代码：function="SYMBOL_SEARCH", keywords="Apple"
    - 获取实时报价：function="GLOBAL_QUOTE", symbol="MSFT"
    - 新闻情感分析：function="NEWS_SENTIMENT", tickers="AAPL,MSFT"
    """
    # 获取API密钥
    api_key = os.getenv("ALPHA_VANTAGE_API_KEY")
    if not api_key:
        logger.warning("ALPHA_VANTAGE_API_KEY 环境变量未设置，工具无法正常工作")
        return json.dumps({
            "error": "ALPHA_VANTAGE_API_KEY 环境变量未设置。请在 .env 文件中设置您的 Alpha Vantage API 密钥。"
        }, ensure_ascii=False)

    base_url = "https://www.alphavantage.co/query"

    # 构建API参数
    params = {
        "function": function,
        "apikey": api_key,
    }

    # 添加可选参数
    if symbol:
        params["symbol"] = symbol
    if interval:
        params["interval"] = interval
    if outputsize:
        params["outputsize"] = outputsize
    if datatype:
        params["datatype"] = datatype
    if keywords:
        params["keywords"] = keywords
    if tickers:
        params["tickers"] = tickers
    if time_period:
        params["time_period"] = time_period
    if series_type:
        params["series_type"] = series_type

    # 添加额外参数
    if additional_params:
        params.update(additional_params)

    try:
        # 发送API请求
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()

        if datatype == "csv":
            return response.text
        else:
            data = response.json()

            # 检查API错误
            if "Error Message" in data:
                return json.dumps({
                    "error": f"Alpha Vantage API 错误: {data['Error Message']}"
                }, ensure_ascii=False)

            if "Note" in data:
                return json.dumps({
                    "error": f"Alpha Vantage API 限制: {data['Note']}"
                }, ensure_ascii=False)

            # 格式化并返回数据
            return json.dumps(data, ensure_ascii=False, indent=2)

    except requests.exceptions.Timeout:
        return json.dumps({
            "error": "请求超时，请稍后重试"
        }, ensure_ascii=False)
    except requests.exceptions.RequestException as e:
        return json.dumps({
            "error": f"网络请求失败: {str(e)}"
        }, ensure_ascii=False)
    except json.JSONDecodeError as e:
        return json.dumps({
            "error": f"JSON解析失败: {str(e)}"
        }, ensure_ascii=False)
    except Exception as e:
        return json.dumps({
            "error": f"未知错误: {str(e)}"
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 测试示例
    # 测试获取苹果股票日线数据
    print("=== 测试苹果股票日线数据 ===")
    result = alpha_vantage_tool.invoke({
        "function": "TIME_SERIES_DAILY",
        "symbol": "AAPL",
        "outputsize": "compact"
    })
    print(result)

    print("\n=== 测试股票搜索 ===")
    result = alpha_vantage_tool.invoke({
        "function": "SYMBOL_SEARCH",
        "keywords": "Apple"
    })
    print(result) 