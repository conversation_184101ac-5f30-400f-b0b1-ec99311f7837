#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
直接测试 Yahoo Finance 新闻工具的脚本

这个脚本用于快速验证 yahoo_finance_news_tool 的功能，不依赖于完整的测试套件。
测试获取50条新闻的功能。
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# 从 src.tools 中导入工具
from src.tools.yahoo_finance_news import yahoo_finance_news_tool

def main():
    """
    主函数：调用并打印 Yahoo Finance 新闻工具的结果
    """
    print("🚀 开始测试 Yahoo Finance 新闻工具（获取50条新闻）")
    print("获取 AAPL (苹果) 的新闻...")
    
    try:
        # 直接调用工具，传入股票代码和新闻数量
        result = yahoo_finance_news_tool.invoke({"symbol": "AAPL", "max_news": 50})
        
        print("\n=== 工具返回结果 ===")
        # 打印格式化的 JSON 结果
        try:
            data = json.loads(result)
            
            # 显示统计信息
            print(f"📊 统计信息:")
            print(f"   - 股票代码: {data.get('symbol', 'N/A')}")
            print(f"   - 请求数量: {data.get('requested_count', 'N/A')}")
            print(f"   - 实际获取: {data.get('news_count', 'N/A')}")
            print(f"   - 数据源: {data.get('source', 'N/A')}")
            print(f"   - 状态: {data.get('status', 'N/A')}")
            print(f"   - 使用方法: {data.get('methods_tried', [])}")
            
            # 显示前5条新闻的标题
            if data.get('news_data'):
                print(f"\n📰 前5条新闻预览:")
                for i, news in enumerate(data['news_data'][:5], 1):
                    print(f"   {i}. {news.get('title', 'No title')}")
                    if news.get('source'):
                        print(f"      来源: {news['source']}")
                    if news.get('published'):
                        print(f"      时间: {news['published']}")
                    print()
            
            # 可选：显示完整JSON（取消注释下面的行）
            # print(f"\n=== 完整JSON数据 ===")
            # print(json.dumps(data, ensure_ascii=False, indent=2))
            
        except json.JSONDecodeError:
            print("❌ 返回结果不是有效的 JSON:")
            print(result)
            
    except Exception as e:
        print(f"❌ 调用工具时发生错误: {e}")
        
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main() 