#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Yahoo Finance 新闻工具测试脚本

这个脚本用于测试 yahoo_finance_news_tool 的功能。
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.tools.yahoo_finance_news import yahoo_finance_news_tool


def test_yahoo_finance_news():
    """测试 Yahoo Finance 新闻工具"""
    
    print("=" * 80)
    print("Yahoo Finance 新闻工具测试")
    print("=" * 80)
    
    # 测试股票列表
    test_symbols = ["AAPL", "TSLA", "MSFT", "GOOGL", "AMZN"]
    
    for symbol in test_symbols:
        print(f"\n📰 测试获取 {symbol} 的 Yahoo Finance 新闻...")
        print("-" * 50)
        
        try:
            # 调用工具
            result = yahoo_finance_news_tool.invoke({"symbol": symbol})
            
            # 解析结果
            data = json.loads(result)
            
            if "error" in data:
                print(f"❌ 获取 {symbol} 新闻失败: {data['error']}")
                continue
            
            print(f"✅ 成功获取 {symbol} 新闻")
            print(f"📊 新闻数量: {data['news_count']}")
            print(f"🔗 数据源: {data['source']}")
            
            # 显示新闻列表
            if data['news_data']:
                print(f"\n📋 新闻列表:")
                for i, news in enumerate(data['news_data'][:3], 1):  # 显示前3条
                    print(f"  {i}. {news['title']}")
                    if news.get('url'):
                        print(f"     链接: {news['url']}")
                    if news.get('summary'):
                        summary = news['summary'][:100] + "..." if len(news['summary']) > 100 else news['summary']
                        print(f"     摘要: {summary}")
                    if news.get('published'):
                        print(f"     发布时间: {news['published']}")
                    print()
            else:
                print("⚠️  未解析到结构化新闻数据")
                
            # 显示方法信息
            if data.get('methods_tried'):
                print(f"📊 尝试的方法: {', '.join(data['methods_tried'])}")
                print(f"📈 状态: {data.get('status', 'unknown')}")
                
        except Exception as e:
            print(f"❌ 测试 {symbol} 时发生错误: {e}")
        
        print()


def test_specific_symbol():
    """测试特定股票的详细信息"""
    
    print("\n" + "=" * 80)
    print("详细测试 - AAPL")
    print("=" * 80)
    
    try:
        result = yahoo_finance_news_tool.invoke({"symbol": "AAPL"})
        data = json.loads(result)
        
        print("完整结果:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"详细测试失败: {e}")


def main():
    """主函数"""
    print("🚀 开始 Yahoo Finance 新闻工具测试")
    
    # 基本测试
    test_yahoo_finance_news()
    
    # 详细测试
    test_specific_symbol()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)
    
    print("\n📖 使用说明:")
    print("1. 该工具可以抓取 Yahoo Finance 指定股票的新闻页面")
    print("2. 支持美股代码，如 AAPL, TSLA, MSFT 等")
    print("3. 返回结构化的新闻数据，包括标题、链接、摘要等")
    print("4. 如果页面结构发生变化，可能需要调整解析逻辑")


if __name__ == "__main__":
    main() 