#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
背离分析工具使用示例

这个脚本演示了如何使用 detect_price_macd_divergence 工具来检测股票价格与MACD信号线之间的背离现象。
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.tools.divergence_analysis import detect_price_macd_divergence
from src.tools.akshare import us_stock_daily_sina_tool


def test_divergence_analysis():
    """测试背离分析功能"""
    
    print("=" * 80)
    print("背离分析工具使用示例")
    print("=" * 80)
    
    # 1. 首先获取股票历史数据（以苹果公司为例）
    print("\n1. 获取苹果公司(AAPL)历史数据...")
    print("-" * 50)
    
    try:
        # 获取苹果公司过去100天的数据
        stock_data_result = us_stock_daily_sina_tool.invoke({
            "symbol": "AAPL",
            "adjust": "qfq",  # 前复权
            "max_rows": 100
        })
        
        print("✅ 成功获取股票数据")
        
        # 2. 进行背离分析
        print("\n2. 进行背离分析...")
        print("-" * 50)
        
        divergence_result = detect_price_macd_divergence.invoke({
            "stock_data_json": stock_data_result,
            "lookback_period": 20,  # 20个交易日回看期
            "min_distance": 5,      # 极值点最小间距5天
            "macd_fast": 12,        # MACD快线12日
            "macd_slow": 26,        # MACD慢线26日
            "macd_signal": 9        # MACD信号线9日
        })
        
        # 解析结果
        result_data = json.loads(divergence_result)
        
        if "error" in result_data:
            print(f"❌ 分析失败: {result_data['error']}")
            return
        
        # 3. 显示分析结果
        print("✅ 背离分析完成")
        print("\n3. 分析结果摘要:")
        print("-" * 50)
        
        summary = result_data["analysis_summary"]
        print(f"分析周期: {summary['analysis_period']['start_date']} 至 {summary['analysis_period']['end_date']}")
        print(f"数据点数: {summary['analysis_period']['total_days']} 个交易日")
        print(f"发现的背离总数: {summary['divergences_found']['total_count']}")
        print(f"  - 底背离(看涨): {summary['divergences_found']['bullish_count']} 个")
        print(f"  - 顶背离(看跌): {summary['divergences_found']['bearish_count']} 个")
        
        # 4. 显示底背离详情
        if result_data["bullish_divergences"]:
            print("\n4. 底背离信号详情:")
            print("-" * 50)
            for i, divergence in enumerate(result_data["bullish_divergences"][:3], 1):  # 显示前3个
                print(f"底背离 #{i}:")
                print(f"  发生日期: {divergence['current_date']}")
                print(f"  对比日期: {divergence['previous_date']}")
                print(f"  价格变化: ${divergence['previous_price']:.2f} → ${divergence['current_price']:.2f} ({divergence['price_change_pct']:.2f}%)")
                print(f"  MACD信号线: {divergence['previous_macd_signal']:.4f} → {divergence['current_macd_signal']:.4f} ({divergence['macd_change_pct']:.2f}%)")
                print(f"  背离强度: {divergence['strength']:.4f}")
                print(f"  间隔天数: {divergence['days_apart']} 天")
                print()
        else:
            print("\n4. 未发现底背离信号")
        
        # 5. 显示顶背离详情
        if result_data["bearish_divergences"]:
            print("\n5. 顶背离信号详情:")
            print("-" * 50)
            for i, divergence in enumerate(result_data["bearish_divergences"][:3], 1):  # 显示前3个
                print(f"顶背离 #{i}:")
                print(f"  发生日期: {divergence['current_date']}")
                print(f"  对比日期: {divergence['previous_date']}")
                print(f"  价格变化: ${divergence['previous_price']:.2f} → ${divergence['current_price']:.2f} ({divergence['price_change_pct']:.2f}%)")
                print(f"  MACD信号线: {divergence['previous_macd_signal']:.4f} → {divergence['current_macd_signal']:.4f} ({divergence['macd_change_pct']:.2f}%)")
                print(f"  背离强度: {divergence['strength']:.4f}")
                print(f"  间隔天数: {divergence['days_apart']} 天")
                print()
        else:
            print("\n5. 未发现顶背离信号")
        
        # 6. 显示当前市场状态
        print("\n6. 当前市场状态:")
        print("-" * 50)
        current_status = result_data["current_market_status"]
        print(f"最新价格: ${current_status['latest_price']:.2f}")
        print(f"最新MACD线: {current_status['latest_macd_line']:.4f}")
        print(f"最新MACD信号线: {current_status['latest_macd_signal']:.4f}")
        print(f"最新MACD柱状图: {current_status['latest_macd_histogram']:.4f}")
        
        # 7. 显示交易建议
        if "trading_suggestions" in result_data:
            print("\n7. 交易建议:")
            print("-" * 50)
            for suggestion in result_data["trading_suggestions"]:
                print(f"💡 {suggestion}")
        
        # 8. 风险提示
        print("\n8. 风险提示:")
        print("-" * 50)
        for disclaimer in result_data["risk_disclaimer"]:
            print(f"⚠️  {disclaimer}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_with_custom_data():
    """使用自定义数据测试背离分析"""
    
    print("\n" + "=" * 80)
    print("使用自定义数据测试背离分析")
    print("=" * 80)
    
    # 创建模拟的股票数据，包含明显的背离现象
    import pandas as pd
    from datetime import datetime, timedelta
    
    # 生成60天的模拟数据
    dates = [datetime.now() - timedelta(days=i) for i in range(59, -1, -1)]
    
    # 模拟价格数据：先上涨，然后下跌创新低，但MACD信号线不创新低（底背离）
    prices = []
    base_price = 100
    
    for i in range(60):
        if i < 20:
            # 前20天上涨
            price = base_price + i * 2
        elif i < 40:
            # 中间20天下跌
            price = base_price + 40 - (i - 20) * 1.5
        else:
            # 后20天再次下跌，但幅度较小（形成底背离）
            price = base_price + 10 - (i - 40) * 0.5
        
        prices.append(price)
    
    # 构建测试数据
    test_data = {
        "data": [
            {
                "date": dates[i].strftime("%Y-%m-%d"),
                "close": prices[i],
                "open": prices[i] * 0.99,
                "high": prices[i] * 1.01,
                "low": prices[i] * 0.98,
                "volume": 1000000 + i * 10000
            }
            for i in range(60)
        ]
    }
    
    print("📊 使用模拟数据进行背离分析...")
    
    try:
        result = detect_price_macd_divergence.invoke({
            "stock_data_json": json.dumps(test_data),
            "lookback_period": 15,
            "min_distance": 3
        })
        
        result_data = json.loads(result)
        
        if "error" in result_data:
            print(f"❌ 分析失败: {result_data['error']}")
            return
        
        print("✅ 模拟数据分析完成")
        print(f"发现背离总数: {result_data['analysis_summary']['divergences_found']['total_count']}")
        
        if result_data["bullish_divergences"]:
            print(f"✅ 检测到 {len(result_data['bullish_divergences'])} 个底背离信号")
        
        if result_data["bearish_divergences"]:
            print(f"✅ 检测到 {len(result_data['bearish_divergences'])} 个顶背离信号")
        
    except Exception as e:
        print(f"❌ 模拟数据测试失败: {e}")


def main():
    """主函数"""
    print("🚀 开始背离分析工具测试")
    
    # 测试1：使用真实股票数据
    test_divergence_analysis()
    
    # 测试2：使用自定义模拟数据
    test_with_custom_data()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)
    
    print("\n📖 使用说明:")
    print("1. 底背离：价格创新低，但MACD信号线不创新低，通常预示反弹")
    print("2. 顶背离：价格创新高，但MACD信号线不创新高，通常预示回调")
    print("3. 背离强度越高，信号越可靠")
    print("4. 建议结合其他技术指标确认信号")


if __name__ == "__main__":
    main() 