# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
import numpy as np
from typing import Annotated, Dict, Any, List, Optional, Tuple
from langchain_core.tools import tool
from src.tools.decorators import log_io

logger = logging.getLogger(__name__)


def calculate_ema(data: pd.Series, span: int) -> pd.Series:
    """计算指数移动平均线"""
    return data.ewm(span=span, adjust=False).mean()


def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
    """计算MACD指标"""
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    
    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return {
        'MACD': macd_line,
        'Signal': signal_line,
        'Histogram': histogram
    }


def find_local_extremes(data: pd.Series, window: int = 5) -> <PERSON>ple[List[int], List[int]]:
    """
    寻找局部极值点
    
    Args:
        data: 数据序列
        window: 窗口大小，用于确定局部极值
        
    Returns:
        (peaks_indices, troughs_indices): 峰值和谷值的索引列表
    """
    peaks = []
    troughs = []
    
    for i in range(window, len(data) - window):
        # 检查是否为局部最高点
        is_peak = True
        is_trough = True
        
        for j in range(i - window, i + window + 1):
            if j != i:
                if data.iloc[i] <= data.iloc[j]:
                    is_peak = False
                if data.iloc[i] >= data.iloc[j]:
                    is_trough = False
        
        if is_peak:
            peaks.append(i)
        if is_trough:
            troughs.append(i)
    
    return peaks, troughs


def detect_bullish_divergence(price_data: pd.Series, macd_signal: pd.Series, 
                             lookback_period: int = 20, min_distance: int = 5) -> List[Dict]:
    """
    检测底背离（看涨背离）
    
    底背离：价格创新低，但MACD信号线创新高或不创新低
    
    Args:
        price_data: 价格数据（通常使用收盘价）
        macd_signal: MACD信号线数据
        lookback_period: 回看周期
        min_distance: 两个极值点之间的最小距离
        
    Returns:
        检测到的底背离列表
    """
    divergences = []
    
    # 寻找价格的局部低点
    _, price_troughs = find_local_extremes(price_data, window=3)
    
    # 寻找MACD信号线的局部低点
    _, macd_troughs = find_local_extremes(macd_signal, window=3)
    
    # 检查每个价格低点
    for i in range(1, len(price_troughs)):
        current_price_idx = price_troughs[i]
        current_price = price_data.iloc[current_price_idx]
        
        # 在回看期内寻找之前的价格低点
        for j in range(i):
            prev_price_idx = price_troughs[j]
            prev_price = price_data.iloc[prev_price_idx]
            
            # 检查距离是否足够
            if current_price_idx - prev_price_idx < min_distance:
                continue
                
            # 检查是否在回看期内
            if current_price_idx - prev_price_idx > lookback_period:
                break
            
            # 价格创新低
            if current_price < prev_price:
                # 寻找对应时间段内的MACD信号线值
                macd_current = macd_signal.iloc[current_price_idx]
                macd_prev = macd_signal.iloc[prev_price_idx]
                
                # MACD信号线没有创新低（底背离）
                if macd_current > macd_prev:
                    divergence_strength = abs((current_price - prev_price) / prev_price) + abs((macd_current - macd_prev) / abs(macd_prev) if macd_prev != 0 else 0)
                    
                    divergences.append({
                        'type': 'bullish_divergence',
                        'current_date': price_data.index[current_price_idx],
                        'previous_date': price_data.index[prev_price_idx],
                        'current_price': float(current_price),
                        'previous_price': float(prev_price),
                        'price_change_pct': float((current_price - prev_price) / prev_price * 100),
                        'current_macd_signal': float(macd_current),
                        'previous_macd_signal': float(macd_prev),
                        'macd_change_pct': float((macd_current - macd_prev) / abs(macd_prev) * 100 if macd_prev != 0 else 0),
                        'strength': float(divergence_strength),
                        'days_apart': int(current_price_idx - prev_price_idx)
                    })
    
    return divergences


def detect_bearish_divergence(price_data: pd.Series, macd_signal: pd.Series, 
                             lookback_period: int = 20, min_distance: int = 5) -> List[Dict]:
    """
    检测顶背离（看跌背离）
    
    顶背离：价格创新高，但MACD信号线创新低或不创新高
    
    Args:
        price_data: 价格数据（通常使用收盘价）
        macd_signal: MACD信号线数据
        lookback_period: 回看周期
        min_distance: 两个极值点之间的最小距离
        
    Returns:
        检测到的顶背离列表
    """
    divergences = []
    
    # 寻找价格的局部高点
    price_peaks, _ = find_local_extremes(price_data, window=3)
    
    # 寻找MACD信号线的局部高点
    macd_peaks, _ = find_local_extremes(macd_signal, window=3)
    
    # 检查每个价格高点
    for i in range(1, len(price_peaks)):
        current_price_idx = price_peaks[i]
        current_price = price_data.iloc[current_price_idx]
        
        # 在回看期内寻找之前的价格高点
        for j in range(i):
            prev_price_idx = price_peaks[j]
            prev_price = price_data.iloc[prev_price_idx]
            
            # 检查距离是否足够
            if current_price_idx - prev_price_idx < min_distance:
                continue
                
            # 检查是否在回看期内
            if current_price_idx - prev_price_idx > lookback_period:
                break
            
            # 价格创新高
            if current_price > prev_price:
                # 寻找对应时间段内的MACD信号线值
                macd_current = macd_signal.iloc[current_price_idx]
                macd_prev = macd_signal.iloc[prev_price_idx]
                
                # MACD信号线没有创新高（顶背离）
                if macd_current < macd_prev:
                    divergence_strength = abs((current_price - prev_price) / prev_price) + abs((macd_current - macd_prev) / abs(macd_prev) if macd_prev != 0 else 0)
                    
                    divergences.append({
                        'type': 'bearish_divergence',
                        'current_date': price_data.index[current_price_idx],
                        'previous_date': price_data.index[prev_price_idx],
                        'current_price': float(current_price),
                        'previous_price': float(prev_price),
                        'price_change_pct': float((current_price - prev_price) / prev_price * 100),
                        'current_macd_signal': float(macd_current),
                        'previous_macd_signal': float(macd_prev),
                        'macd_change_pct': float((macd_current - macd_prev) / abs(macd_prev) * 100 if macd_prev != 0 else 0),
                        'strength': float(divergence_strength),
                        'days_apart': int(current_price_idx - prev_price_idx)
                    })
    
    return divergences


@tool
@log_io
def detect_price_macd_divergence(
    stock_data_json: Annotated[str, "股票数据的JSON字符串，包含date, open, high, low, close, volume字段"],
    lookback_period: Annotated[int, "回看周期，用于寻找背离，默认20个交易日"] = 20,
    min_distance: Annotated[int, "两个极值点之间的最小距离，默认5个交易日"] = 5,
    macd_fast: Annotated[int, "MACD快线周期，默认12"] = 12,
    macd_slow: Annotated[int, "MACD慢线周期，默认26"] = 26,
    macd_signal: Annotated[int, "MACD信号线周期，默认9"] = 9,
) -> str:
    """
    检测股票价格与MACD信号线之间的背离现象
    
    功能说明：
    - 底背离（看涨背离）：价格创新低，但MACD信号线不创新低或创新高，通常预示价格可能反弹
    - 顶背离（看跌背离）：价格创新高，但MACD信号线不创新高或创新低，通常预示价格可能回调
    
    技术原理：
    背离是技术分析中的重要信号，表明价格趋势可能发生转变。当价格与技术指标出现背离时，
    往往预示着当前趋势的动能正在减弱，可能即将发生反转。
    
    Args:
        stock_data_json: 股票历史数据
        lookback_period: 回看周期，用于确定背离检测的时间范围
        min_distance: 两个极值点之间的最小距离，避免噪音
        macd_fast: MACD快线EMA周期
        macd_slow: MACD慢线EMA周期  
        macd_signal: MACD信号线EMA周期
        
    Returns:
        包含检测到的背离信息的JSON字符串
    """
    try:
        # 解析输入数据
        stock_data = json.loads(stock_data_json)
        
        if isinstance(stock_data, dict) and 'data' in stock_data:
            data_list = stock_data['data']
        elif isinstance(stock_data, list):
            data_list = stock_data
        else:
            return json.dumps({
                "error": "数据格式不正确，需要包含股票价格数据的JSON"
            }, ensure_ascii=False)
        
        if not data_list:
            return json.dumps({
                "error": "股票数据为空"
            }, ensure_ascii=False)
        
        # 转换为DataFrame
        df = pd.DataFrame(data_list)
        
        # 确保必要的列存在
        required_cols = ['date', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            return json.dumps({
                "error": f"缺少必要的数据列: {missing_cols}"
            }, ensure_ascii=False)
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        df.set_index('date', inplace=True)
        
        # 确保收盘价为浮点类型
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        
        # 检查数据量是否足够
        min_required_data = max(macd_slow + macd_signal + lookback_period, 50)
        if len(df) < min_required_data:
            return json.dumps({
                "error": f"数据量不足，需要至少{min_required_data}个交易日的数据进行背离分析",
                "current_data_points": len(df),
                "suggestion": "请获取更多历史数据"
            }, ensure_ascii=False)
        
        # 计算MACD指标
        macd_data = calculate_macd(df['close'], macd_fast, macd_slow, macd_signal)
        
        # 检测底背离
        bullish_divergences = detect_bullish_divergence(
            df['close'], 
            macd_data['Signal'], 
            lookback_period, 
            min_distance
        )
        
        # 检测顶背离
        bearish_divergences = detect_bearish_divergence(
            df['close'], 
            macd_data['Signal'], 
            lookback_period, 
            min_distance
        )
        
        # 按强度排序
        bullish_divergences.sort(key=lambda x: x['strength'], reverse=True)
        bearish_divergences.sort(key=lambda x: x['strength'], reverse=True)
        
        # 构建结果
        result = {
            "analysis_summary": {
                "symbol": stock_data.get('symbol', 'Unknown'),
                "analysis_period": {
                    "start_date": df.index.min().strftime('%Y-%m-%d'),
                    "end_date": df.index.max().strftime('%Y-%m-%d'),
                    "total_days": len(df)
                },
                "parameters": {
                    "lookback_period": lookback_period,
                    "min_distance": min_distance,
                    "macd_settings": {
                        "fast": macd_fast,
                        "slow": macd_slow,
                        "signal": macd_signal
                    }
                },
                "divergences_found": {
                    "bullish_count": len(bullish_divergences),
                    "bearish_count": len(bearish_divergences),
                    "total_count": len(bullish_divergences) + len(bearish_divergences)
                }
            },
            "bullish_divergences": bullish_divergences,
            "bearish_divergences": bearish_divergences,
            "current_market_status": {
                "latest_price": float(df['close'].iloc[-1]),
                "latest_macd_signal": float(macd_data['Signal'].iloc[-1]),
                "latest_macd_line": float(macd_data['MACD'].iloc[-1]),
                "latest_macd_histogram": float(macd_data['Histogram'].iloc[-1])
            }
        }
        
        # 添加交易建议
        if bullish_divergences or bearish_divergences:
            suggestions = []
            
            if bullish_divergences:
                latest_bullish = bullish_divergences[0]
                days_since = (df.index[-1] - pd.to_datetime(latest_bullish['current_date'])).days
                if days_since <= 10:
                    suggestions.append(f"最近发现底背离信号（{days_since}天前），可能预示价格反弹机会")
            
            if bearish_divergences:
                latest_bearish = bearish_divergences[0]
                days_since = (df.index[-1] - pd.to_datetime(latest_bearish['current_date'])).days
                if days_since <= 10:
                    suggestions.append(f"最近发现顶背离信号（{days_since}天前），可能预示价格回调风险")
            
            result["trading_suggestions"] = suggestions
        
        # 添加风险提示
        result["risk_disclaimer"] = [
            "背离信号仅供参考，不构成投资建议",
            "背离信号可能出现假信号，建议结合其他技术指标确认",
            "市场有风险，投资需谨慎"
        ]
        
        return json.dumps(result, ensure_ascii=False, indent=2, default=str)
        
    except Exception as e:
        logger.error(f"背离分析错误: {e}")
        return json.dumps({
            "error": f"背离分析失败: {str(e)}"
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 测试示例
    test_data = {
        "data": [
            {"date": "2024-01-01", "close": 100},
            {"date": "2024-01-02", "close": 98},
            {"date": "2024-01-03", "close": 95},
            # ... 更多测试数据
        ]
    }
    
    result = detect_price_macd_divergence.invoke({
        "stock_data_json": json.dumps(test_data),
        "lookback_period": 20,
        "min_distance": 5
    })
    print(result) 