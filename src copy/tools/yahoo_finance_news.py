# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import re
import requests
import time
from typing import Annotated, Optional
from langchain_core.tools import tool
from src.tools.decorators import log_io

logger = logging.getLogger(__name__)


@tool
@log_io
def yahoo_finance_news_tool(
    symbol: Annotated[str, "股票代码，如 'AAPL', 'TSLA', 'MSFT' 等"] = "AAPL",
    max_news: Annotated[int, "最大新闻条数，默认50条"] = 50,
) -> str:
    """
    【Yahoo Finance工具】获取指定股票的 Yahoo Finance 新闻。
    
    这是一个专门的 Yahoo Finance 新闻抓取工具，可以获取指定股票的最新新闻资讯。
    当用户询问 Yahoo Finance 新闻、特定股票的 Yahoo 新闻时，请使用此工具。
    
    功能：通过多种方式获取 Yahoo Finance 指定股票的新闻，包括 RSS 源和分页API。
    数据来源：Yahoo Finance，实时新闻数据。
    
    Args:
        symbol: 股票代码，如 'AAPL', 'TSLA', 'MSFT' 等
        max_news: 最大新闻条数，默认50条
        
    Returns:
        JSON格式的新闻数据，包含新闻标题、链接、摘要等信息
    """
    symbol = symbol.upper()
    max_news = min(max_news, 100)  # 限制最大条数为100，避免过度请求
    
    try:
        logger.info(f"正在获取 {symbol} 的 Yahoo Finance 新闻（最多{max_news}条）...")
        
        # 尝试多种方法获取新闻
        news_data = []
        methods_tried = []
        
        # 方法1: 尝试 Yahoo Finance RSS 源（通常有限制）
        try:
            rss_news = get_yahoo_rss_news(symbol)
            if rss_news:
                news_data.extend(rss_news)
                methods_tried.append("RSS")
                logger.info(f"从 RSS 源获取到 {len(rss_news)} 条新闻")
        except Exception as e:
            logger.warning(f"RSS 源获取失败: {e}")
        
        # 方法2: 如果需要更多新闻，尝试搜索API获取
        if len(news_data) < max_news:
            try:
                remaining_count = max_news - len(news_data)
                search_news = get_yahoo_search_news(symbol, remaining_count)
                if search_news:
                    unique_search_news = remove_duplicates(search_news, news_data)
                    news_data.extend(unique_search_news)
                    methods_tried.append("Search API")
                    logger.info(f"从搜索API获取到 {len(unique_search_news)} 条新闻")
            except Exception as e:
                logger.warning(f"搜索API获取失败: {e}")
        
        # 方法3: 如果还需要更多，尝试增强搜索
        if len(news_data) < max_news:
            try:
                remaining_count = max_news - len(news_data)
                enhanced_news = get_enhanced_search_news(symbol, remaining_count)
                if enhanced_news:
                    unique_enhanced_news = remove_duplicates(enhanced_news, news_data)
                    news_data.extend(unique_enhanced_news)
                    methods_tried.append("Enhanced Search")
                    logger.info(f"从增强搜索获取到 {len(unique_enhanced_news)} 条新闻")
            except Exception as e:
                logger.warning(f"增强搜索获取失败: {e}")
        
        result = {
            "symbol": symbol,
            "source": "Yahoo Finance",
            "news_count": len(news_data),
            "requested_count": max_news,
            "news_data": news_data[:max_news],  # 确保不超过请求的数量
            "status": "success" if news_data else "no_data",
            "methods_tried": methods_tried
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        error_msg = f"获取 Yahoo Finance {symbol} 新闻失败: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            "error": error_msg,
            "symbol": symbol,
            "error_type": "general_error"
        }, ensure_ascii=False)


def get_yahoo_rss_news(symbol: str) -> list:
    """
    通过 Yahoo Finance RSS 源获取新闻
    
    Args:
        symbol: 股票代码
        
    Returns:
        新闻列表
    """
    news_list = []
    
    try:
        # Yahoo Finance RSS URL
        rss_url = f"https://feeds.finance.yahoo.com/rss/2.0/headline?s={symbol}&region=US&lang=en-US"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (compatible; NewsBot/1.0)',
            'Accept': 'application/rss+xml, application/xml, text/xml',
        }
        
        response = requests.get(rss_url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # 解析 RSS XML
        news_list = parse_rss_content(response.text, symbol)
        
    except Exception as e:
        logger.error(f"RSS 获取失败: {e}")
        raise
    
    return news_list


def get_yahoo_search_news(symbol: str, max_count: int = 20) -> list:
    """
    通过 Yahoo Finance 搜索API获取相关新闻
    
    Args:
        symbol: 股票代码
        max_count: 最大获取数量
        
    Returns:
        新闻列表
    """
    news_list = []
    
    try:
        # 尝试多个搜索关键词
        search_terms = [
            symbol,
            f"{symbol} stock",
            f"{symbol} news",
            f"{symbol} earnings"
        ]
        
        for term in search_terms:
            if len(news_list) >= max_count:
                break
                
            try:
                # Yahoo Finance 搜索API
                search_url = f"https://query1.finance.yahoo.com/v1/finance/search"
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (compatible; FinanceBot/1.0)',
                    'Accept': 'application/json',
                }
                
                params = {
                    'q': term,
                    'quotesCount': 5,
                    'newsCount': 15  # 增加每次请求的新闻数量
                }
                
                response = requests.get(search_url, headers=headers, params=params, timeout=15)
                response.raise_for_status()
                
                data = response.json()
                
                # 从搜索结果中提取新闻信息
                if 'news' in data:
                    for item in data['news']:
                        if len(news_list) >= max_count:
                            break
                        news_list.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'summary': item.get('summary', ''),
                            'source': 'Yahoo Finance Search API',
                            'published': item.get('providerPublishTime', '')
                        })
                
                # 添加延迟
                time.sleep(0.3)
                
            except Exception as e:
                logger.warning(f"搜索词 '{term}' 失败: {e}")
                continue
        
    except Exception as e:
        logger.error(f"搜索API获取失败: {e}")
        raise
    
    return news_list


def get_enhanced_search_news(symbol: str, max_count: int = 20) -> list:
    """
    通过增强搜索获取更多新闻（使用不同的关键词组合）
    
    Args:
        symbol: 股票代码
        max_count: 最大获取数量
        
    Returns:
        新闻列表
    """
    news_list = []
    
    try:
        # 公司名称映射，用于更好的搜索
        company_names = {
            'AAPL': 'Apple Inc',
            'TSLA': 'Tesla',
            'MSFT': 'Microsoft',
            'GOOGL': 'Google Alphabet',
            'AMZN': 'Amazon',
            'META': 'Meta Facebook',
            'NVDA': 'NVIDIA',
            'NFLX': 'Netflix'
        }
        
        company_name = company_names.get(symbol, symbol)
        
        # 更多搜索词组合
        enhanced_terms = [
            f"{symbol} analysis",
            f"{symbol} forecast",
            f"{company_name} quarterly",
            f"{company_name} market",
            f"{symbol} price target",
            f"{company_name} CEO",
            f"{symbol} dividend",
            f"{company_name} revenue"
        ]
        
        for term in enhanced_terms:
            if len(news_list) >= max_count:
                break
                
            try:
                search_url = f"https://query1.finance.yahoo.com/v1/finance/search"
                
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': 'application/json',
                }
                
                params = {
                    'q': term,
                    'quotesCount': 3,
                    'newsCount': 10
                }
                
                response = requests.get(search_url, headers=headers, params=params, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if 'news' in data:
                            for item in data['news']:
                                if len(news_list) >= max_count:
                                    break
                                
                                title = item.get('title', '')
                                if title and len(title) > 15:  # 过滤太短的标题
                                    news_list.append({
                                        'title': title,
                                        'url': item.get('link', ''),
                                        'summary': item.get('summary', ''),
                                        'source': 'Yahoo Finance Enhanced Search',
                                        'published': item.get('providerPublishTime', '')
                                    })
                    except json.JSONDecodeError:
                        continue
                
                # 添加延迟
                time.sleep(0.4)
                
            except Exception as e:
                logger.warning(f"增强搜索词 '{term}' 失败: {e}")
                continue
        
    except Exception as e:
        logger.error(f"增强搜索失败: {e}")
        raise
    
    return news_list


def remove_duplicates(new_news: list, existing_news: list) -> list:
    """
    去除重复的新闻条目
    
    Args:
        new_news: 新的新闻列表
        existing_news: 已有的新闻列表
        
    Returns:
        去重后的新闻列表
    """
    existing_titles = {news.get('title', '').lower() for news in existing_news}
    unique_news = []
    
    for news in new_news:
        title = news.get('title', '').lower()
        if title and title not in existing_titles and len(title) > 10:
            unique_news.append(news)
            existing_titles.add(title)
    
    return unique_news


def parse_rss_content(rss_content: str, symbol: str) -> list:
    """
    解析 RSS XML 内容
    
    Args:
        rss_content: RSS XML 内容
        symbol: 股票代码
        
    Returns:
        新闻列表
    """
    news_list = []
    
    try:
        # 使用正则表达式解析 RSS XML
        # 查找 <item> 标签
        item_pattern = r'<item>(.*?)</item>'
        items = re.findall(item_pattern, rss_content, re.DOTALL)
        
        for item in items:  # 不限制数量，让调用者决定
            # 提取标题
            title_match = re.search(r'<title><!\[CDATA\[(.*?)\]\]></title>', item)
            if not title_match:
                title_match = re.search(r'<title>(.*?)</title>', item)
            title = title_match.group(1) if title_match else ''
            
            # 提取链接
            link_match = re.search(r'<link>(.*?)</link>', item)
            link = link_match.group(1) if link_match else ''
            
            # 提取描述
            desc_match = re.search(r'<description><!\[CDATA\[(.*?)\]\]></description>', item)
            if not desc_match:
                desc_match = re.search(r'<description>(.*?)</description>', item)
            description = desc_match.group(1) if desc_match else ''
            
            # 提取发布时间
            pubdate_match = re.search(r'<pubDate>(.*?)</pubDate>', item)
            pubdate = pubdate_match.group(1) if pubdate_match else ''
            
            if title and len(title) > 10:
                news_list.append({
                    'title': title.strip(),
                    'url': link.strip(),
                    'summary': description.strip()[:300] + '...' if len(description) > 300 else description.strip(),
                    'source': 'Yahoo Finance RSS',
                    'published': pubdate.strip()
                })
        
    except Exception as e:
        logger.error(f"RSS 解析失败: {e}")
    
    return news_list


if __name__ == "__main__":
    # 测试示例
    print("=== 测试 Yahoo Finance 新闻工具 ===")
    result = yahoo_finance_news_tool.invoke({"symbol": "AAPL", "max_news": 50})
    print(result) 