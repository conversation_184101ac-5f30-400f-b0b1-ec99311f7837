# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import Annotated, Literal

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command, interrupt
from langchain_mcp_adapters.client import MultiServerMCPClient

from src.agents import create_agent
from src.tools.search import LoggedTavilySearch
from src.tools import (
    crawl_tool,
    get_web_search_tool,
    python_repl_tool,
    alpha_vantage_tool,
)

from src.config.agents import AGENT_LLM_MAP
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from backend.ai.prompts.planner_model import Plan, StepType
from src.prompts.template import apply_prompt_template
from src.utils.json_utils import repair_json_output

from .types import State
from ..config import SELECTED_SEARCH_ENGINE, SearchEngine

logger = logging.getLogger(__name__)


@tool
def handoff_to_planner(
    task_title: Annotated[str, "The title of the task to be handed off."],
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."],
):
    """Handoff to planner agent to do plan."""
    # This tool is not returning anything: we're just using it
    # as a way for LLM to signal that it needs to hand off to planner agent
    return


def background_investigation_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner"]]:
    logger.info("background investigation node is running.")
    configurable = Configuration.from_runnable_config(config)
    query = state["messages"][-1].content
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY:
        searched_content = LoggedTavilySearch(
            max_results=configurable.max_search_results
        ).invoke({"query": query})
        background_investigation_results = None
        if isinstance(searched_content, list):
            background_investigation_results = [
                {"title": elem["title"], "content": elem["content"]}
                for elem in searched_content
            ]
        else:
            logger.error(
                f"Tavily search returned malformed response: {searched_content}"
            )
    else:
        background_investigation_results = get_web_search_tool(
            configurable.max_search_results
        ).invoke(query)
    return Command(
        update={
            "background_investigation_results": json.dumps(
                background_investigation_results, ensure_ascii=False
            )
        },
        goto="planner",
    )


def planner_node(
    state: State, config: RunnableConfig
) -> Command[Literal["reporter"]]:
    """Planner node that generates a plan for the research task."""
    logger.info("Planner generating plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state.get("plan_iterations", 0)
    messages = apply_prompt_template("planner", state)

    # 添加明确的 JSON 格式要求，强调必需字段
    json_instruction = HumanMessage(
        content="""Please strictly adhere to the following JSON format for your response, without including any other text:

{
  "locale": "zh-CN",
  "has_enough_context": false,
  "thought": "Analysis thought",
  "title": "Research Plan Title",
  "steps": [
    {
      "need_web_search": true,
      "title": "Step Title",
      "description": "Step Description",
      "step_type": "research"
    }
  ]
}

CRITICAL: Each step MUST include ALL four fields:
- need_web_search: boolean (true/false)
- title: string
- description: string  
- step_type: string (must be one of: "research", "processing", "technical_analysis", "news_analysis", "fundamental_analysis")

Ensure the response is in a valid JSON format and do not add any explanatory text.""",
        name="system"
    )
    messages.append(json_instruction)

    # 使用基础 LLM 而不是结构化输出
    llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the reporter node
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="reporter")

    logger.info("Planner generating full plan")
    response = llm.invoke(messages)
    full_response = response.content

    # Validate response content
    if not full_response or (isinstance(full_response, str) and full_response.strip() == ""):
        logger.error("Planner returned empty response")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")

    logger.debug(f"Current state messages: {state['messages']}")
    logger.info(f"Planner response: {full_response}")

    try:
        # 尝试修复和解析 JSON
        repaired_json = repair_json_output(full_response)
        curr_plan = json.loads(repaired_json)
    except json.JSONDecodeError as e:
        logger.warning(f"Planner response is not a valid JSON: {e}")
        logger.warning(f"Raw response: {full_response}")
        
        # 如果 JSON 解析失败，尝试提取 JSON 部分
        import re
        json_match = re.search(r'\{.*\}', full_response, re.DOTALL)
        if json_match:
            try:
                repaired_json = repair_json_output(json_match.group())
                curr_plan = json.loads(repaired_json)
                logger.info("Successfully extracted JSON from response")
            except json.JSONDecodeError:
                logger.error("Failed to extract valid JSON from response")
                if plan_iterations > 0:
                    return Command(goto="reporter")
                else:
                    return Command(goto="__end__")
        else:
            logger.error("No JSON found in response")
            if plan_iterations > 0:
                return Command(goto="reporter")
            else:
                return Command(goto="__end__")
    
    # 验证和修复步骤中缺失的 step_type 字段
    if "steps" in curr_plan and isinstance(curr_plan["steps"], list):
        for i, step in enumerate(curr_plan["steps"]):
            if isinstance(step, dict) and "step_type" not in step:
                # 根据步骤内容推断 step_type
                title = step.get("title", "").lower()
                description = step.get("description", "").lower()
                need_web_search = step.get("need_web_search", True)
                
                # 智能推断 step_type
                if any(keyword in title + description for keyword in ["技术分析", "technical", "macd", "rsi", "指标", "chart", "price"]):
                    step_type = "technical_analysis"
                elif any(keyword in title + description for keyword in ["新闻", "news", "sentiment", "媒体", "舆情"]):
                    step_type = "news_analysis"
                elif any(keyword in title + description for keyword in ["财务", "fundamental", "财报", "估值", "financial", "balance"]):
                    step_type = "fundamental_analysis"
                elif any(keyword in title + description for keyword in ["计算", "processing", "数据处理", "分析", "代码"]):
                    step_type = "processing"
                else:
                    step_type = "research"  # 默认为研究类型
                
                step["step_type"] = step_type
                logger.info(f"Added missing step_type '{step_type}' to step {i}: {step.get('title', 'Unknown')}")
    
    if curr_plan.get("has_enough_context"):
        logger.info("Planner response has enough context.")
        try:
            new_plan = Plan.model_validate(curr_plan)
        except Exception as validation_error:
            logger.error(f"Plan validation failed: {validation_error}")
            logger.error(f"Plan content: {curr_plan}")
            if plan_iterations > 0:
                return Command(goto="reporter")
            else:
                return Command(goto="__end__")
        
        return Command(
            update={
                "messages": [AIMessage(content=repaired_json, name="planner")],
                "current_plan": new_plan,
            },
            goto="reporter",
        )
    return Command(
        update={
            "messages": [AIMessage(content=repaired_json, name="planner")],
            "current_plan": repaired_json,
            "auto_accepted_plan": True,  # Auto-accept all plans
        },
        goto="reporter",
    )


def coordinator_node(
    state: State,
) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    messages = apply_prompt_template("coordinator", state)
    response = (
        get_llm_by_type(AGENT_LLM_MAP["coordinator"])
        .bind_tools([handoff_to_planner])
        .invoke(messages)
    )
    logger.debug(f"Current state messages: {state['messages']}")

    goto = "__end__"
    locale = state.get("locale", "en-US")  # Default locale if not specified

    if len(response.tool_calls) > 0:
        goto = "planner"
        if state.get("enable_background_investigation"):
            # if the search_before_planning is True, add the web search tool to the planner agent
            goto = "background_investigator"
        try:
            for tool_call in response.tool_calls:
                if tool_call.get("name", "") != "handoff_to_planner":
                    continue
                if tool_locale := tool_call.get("args", {}).get("locale"):
                    locale = tool_locale
                    break
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}")
    else:
        logger.warning(
            "Coordinator response contains no tool calls. Terminating workflow execution."
        )
        logger.debug(f"Coordinator response: {response}")

    return Command(
        update={"locale": locale},
        goto=goto,
    )


def reporter_node(state: State):
    """Reporter node that write a final report."""
    logger.info("Reporter write final report")
    current_plan = state.get("current_plan")
    
    # Validate current_plan
    if not current_plan:
        logger.error("No current plan found for reporter")
        return {"final_report": "无法生成报告：未找到研究计划。"}
    
    # Validate plan content
    plan_title = getattr(current_plan, 'title', None) or "未知任务"
    plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    input_ = {
        "messages": [
            HumanMessage(
                f"# Research Requirements\n\n## Task\n\n{plan_title}\n\n## Description\n\n{plan_thought}"
            )
        ],
        "locale": state.get("locale", "en-US"),
    }
    invoke_messages = apply_prompt_template("reporter", input_)
    observations = state.get("observations", [])

    # Add a reminder about the new report format, citation style, and table usage
    invoke_messages.append(
        HumanMessage(
            content="IMPORTANT: Structure your report according to the format in the prompt. Remember to include:\n\n1. Key Points - A bulleted list of the most important findings\n2. Overview - A brief introduction to the topic\n3. Detailed Analysis - Organized into logical sections\n4. Survey Note (optional) - For more comprehensive reports\n5. Key Citations - List all references at the end\n\nFor citations, DO NOT include inline citations in the text. Instead, place all citations in the 'Key Citations' section at the end using the format: `- [Source Title](URL)`. Include an empty line between each citation for better readability.\n\nPRIORITIZE USING MARKDOWN TABLES for data presentation and comparison. Use tables whenever presenting comparative data, statistics, features, or options. Structure tables with clear headers and aligned columns. Example table format:\n\n| Feature | Description | Pros | Cons |\n|---------|-------------|------|------|\n| Feature 1 | Description 1 | Pros 1 | Cons 1 |\n| Feature 2 | Description 2 | Pros 2 | Cons 2 |",
            name="system",
        )
    )

    for observation in observations:
        if observation and observation.strip():  # Only add non-empty observations
            invoke_messages.append(
                HumanMessage(
                    content=f"Below are some observations for the research task:\n\n{observation}",
                    name="observation",
                )
            )
    logger.debug(f"Current invoke messages: {invoke_messages}")
    
    try:
        response = get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            response_content = "报告生成失败：未获得有效响应内容。"
            logger.warning("Reporter returned empty response content")
        
        logger.info(f"reporter response: {response_content}")
        return {"final_report": response_content}
    
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return {"final_report": f"报告生成失败：{str(e)}"}


def research_team_node(
    state: State,
) -> Command[Literal["planner", "researcher", "coder", "technical_analyst"]]:
    """Research team node that collaborates on tasks."""
    logger.info("Research team is collaborating on tasks.")
    current_plan = state.get("current_plan")
    if not current_plan or not current_plan.steps:
        return Command(goto="planner")
    if all(step.execution_res for step in current_plan.steps):
        return Command(goto="planner")
    for step in current_plan.steps:
        if not step.execution_res:
            break
    if step.step_type and step.step_type == StepType.RESEARCH:
        return Command(goto="researcher")
    if step.step_type and step.step_type == StepType.PROCESSING:
        return Command(goto="coder")
    if step.step_type and step.step_type == StepType.TECHNICAL_ANALYSIS:
        return Command(goto="technical_analyst")
    return Command(goto="planner")


async def _execute_agent_step(
    state: State, agent, agent_name: str
) -> Command[Literal["research_team"]]:
    """Helper function to execute a step using the specified agent."""
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])

    # Find the first unexecuted step
    current_step = None
    completed_steps = []
    for step in current_plan.steps:
        if not step.execution_res:
            current_step = step
            break
        else:
            completed_steps.append(step)

    if not current_step:
        logger.warning("No unexecuted step found")
        return Command(goto="research_team")

    logger.info(f"Executing step: {current_step.title}")

    # Validate step content to avoid null values
    step_title = current_step.title or "未命名任务"
    step_description = current_step.description or "无具体描述"
    
    # Format completed steps information
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            completed_steps_info += f"## Existing Finding {i+1}: {step.title or '未命名步骤'}\n\n"
            completed_steps_info += f"<finding>\n{step.execution_res or '无执行结果'}\n</finding>\n\n"

    # Prepare the input for the agent with completed steps info
    content = f"{completed_steps_info}# Current Task\n\n## Title\n\n{step_title}\n\n## Description\n\n{step_description}\n\n## Locale\n\n{state.get('locale', 'en-US')}"
    
    # Ensure content is not empty
    if not content or content.strip() == "":
        content = "默认任务：请分析当前状态并提供相关信息。"
    
    agent_input = {
        "messages": [
            HumanMessage(content=content)
        ]
    }

    # Add specific system messages for different agent types
    if agent_name == "researcher":
        system_message = (
            "IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. "
            "Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL)\n\n- [Another Source](URL)\n\n"
            "**TOOLS AVAILABLE:** 您拥有专门的AkShare股票数据工具，包括：\n"
            "- get_famous_stock_data_tool: 【推荐】直接获取知名公司股票数据（支持中文公司名称，如\"苹果公司\"、\"特斯拉\"）\n"
            "- stock_news_em_tool: 获取股票新闻\n"
            "- us_stock_spot_tool: 获取美股实时行情\n"
            "- us_stock_daily_sina_tool: 获取历史股价数据（自动按日期降序排列，最新数据在前）\n"
            "- us_stock_search_tool: 搜索美股公司（支持中英文名称映射）\n"
            "- comprehensive_stock_news_tool: 综合股票资讯\n\n"
            "**使用建议：** 当用户询问知名公司（苹果、特斯拉、微软等）股价时，请优先使用 get_famous_stock_data_tool，它支持中文公司名称并直接返回股票数据。数据已经过优化，会自动按日期降序排列，确保最新数据在前。"
        )
        if system_message and system_message.strip():
            agent_input["messages"].append(
                HumanMessage(content=system_message, name="system")
            )
    elif agent_name == "technical_analyst":
        system_message = (
            "**专业技术分析师指南：**\n\n"
            "您是一位专业的股票技术分析师，请按照以下结构进行全面的技术面分析：\n\n"
            "**核心分析框架：**\n"
            "1. 📈 趋势判断（移动平均线、趋势线分析）\n"
            "2. 🔁 支撑位与阻力位分析（关键价位、突破确认）\n"
            "3. 🔍 成交量分析（量价关系、背离信号）\n"
            "4. ⚙️ 技术指标信号（MACD、RSI、KDJ、布林带等）\n"
            "5. 🔺 图形形态与K线信号（经典形态识别）\n"
            "6. ✅ 综合结论与操作建议\n\n"
            "**数据获取和分析策略（重要！）：**\n"
            "- **获取120根K线数据**：为MACD的EMA26等技术指标提供足够的计算基础\n"
            "- **重点分析90根K线**：所有技术分析结论基于最近90个交易日\n"
            "- **技术指标计算准确性**：确保长周期指标（如EMA26、MA60）有足够的历史数据支撑\n"
            "- 优先使用 `get_famous_stock_data_tool(max_rows=120)` 获取知名公司数据\n"
            "- 使用 `us_stock_daily_sina_tool(max_rows=120)` 获取完整历史K线数据\n\n"
            "**重要：必须使用 `python_repl_tool` 进行实际的技术指标计算和数据分析**\n\n"
            "**Python代码执行要求：**\n"
            "1. **强制使用 python_repl_tool**：所有技术指标计算必须通过实际Python代码执行\n"
            "2. **数据范围划分**：\n"
            "   - df_calculation = df.tail(120)  # 用于技术指标计算\n"
            "   - df_analysis = df.tail(90)     # 用于重点分析\n"
            "3. **显示真实结果**：不要提供理论性的假设结果，必须展示实际计算出的数值\n"
            "4. **包含以下计算**：\n"
            "   - 导入必要的库：pandas, numpy, json等\n"
            "   - 解析股票数据JSON并划分数据范围\n"
            "   - 基于120根K线计算技术指标（RSI、MACD、移动平均线等）\n"
            "   - 基于90根K线识别关键价位和形态\n"
            "   - 检测背离信号和成交量分析\n"
            "   - 打印具体的数值结果和分析结论\n\n"
            "**输出要求：**\n"
            "- 使用结构化格式，包含所有6个分析部分\n"
            "- 基于实际数据进行客观分析\n"
            "- 提供具体的操作建议和风险提示\n"
            "- 必须包含投资风险警示\n"
            "- **每个分析部分都要有实际的计算结果支撑**\n"
            "- **明确说明分析基于最近90根K线，技术指标基于120根K线计算**"
        )
        if system_message and system_message.strip():
            agent_input["messages"].append(
                HumanMessage(content=system_message, name="system")
            )

    # Invoke the agent
    default_recursion_limit = 25
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default_recursion_limit))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            recursion_limit = parsed_limit
            logger.info(f"Recursion limit set to: {recursion_limit}")
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default_recursion_limit}."
            )
            recursion_limit = default_recursion_limit
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default_recursion_limit}."
        )
        recursion_limit = default_recursion_limit

    result = await agent.ainvoke(
        input=agent_input, config={"recursion_limit": recursion_limit}
    )

    # Process the result
    try:
        if not result or "messages" not in result or not result["messages"]:
            response_content = f"{agent_name} 执行完成，但未返回消息列表。"
            logger.warning(f"{agent_name} returned no messages")
        else:
            last_message = result["messages"][-1]
            response_content = getattr(last_message, 'content', None)
            if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
                response_content = f"{agent_name} 执行完成，但未返回具体内容。"
                logger.warning(f"{agent_name} returned empty response content")
    except (IndexError, AttributeError, KeyError) as e:
        response_content = f"{agent_name} 执行出错：{str(e)}"
        logger.error(f"Error processing {agent_name} result: {e}")
    
    logger.debug(f"{agent_name.capitalize()} full response: {response_content}")

    # Update the step with the execution result
    current_step.execution_res = response_content
    logger.info(f"Step '{current_step.title}' execution completed by {agent_name}")

    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=response_content,
                    name=agent_name,
                )
            ],
            "observations": observations + [response_content],
        },
        goto="research_team",
    )


async def _setup_and_execute_agent_step(
    state: State,
    config: RunnableConfig,
    agent_type: str,
    default_tools: list,
) -> Command[Literal["research_team"]]:
    """Helper function to set up an agent with appropriate tools and execute a step.

    This function handles the common logic for both researcher_node and coder_node:
    1. Configures MCP servers and tools based on agent type
    2. Creates an agent with the appropriate tools or uses the default agent
    3. Executes the agent on the current step

    Args:
        state: The current state
        config: The runnable config
        agent_type: The type of agent ("researcher" or "coder")
        default_tools: The default tools to add to the agent

    Returns:
        Command to update state and go to research_team
    """
    configurable = Configuration.from_runnable_config(config)
    mcp_servers = {}
    enabled_tools = {}

    # Extract MCP server configuration for this agent type
    if configurable.mcp_settings:
        for server_name, server_config in configurable.mcp_settings["servers"].items():
            if (
                server_config["enabled_tools"]
                and agent_type in server_config["add_to_agents"]
            ):
                mcp_servers[server_name] = {
                    k: v
                    for k, v in server_config.items()
                    if k in ("transport", "command", "args", "url", "env")
                }
                for tool_name in server_config["enabled_tools"]:
                    enabled_tools[tool_name] = server_name

    # Create and execute agent with MCP tools if available
    if mcp_servers:
        async with MultiServerMCPClient(mcp_servers) as client:
            loaded_tools = default_tools[:]
            for tool in client.get_tools():
                if tool.name in enabled_tools:
                    tool.description = (
                        f"Powered by '{enabled_tools[tool.name]}'.\n{tool.description}"
                    )
                    loaded_tools.append(tool)
            agent = create_agent(agent_type, agent_type, loaded_tools, agent_type)
            return await _execute_agent_step(state, agent, agent_type)
    else:
        # Use default tools if no MCP servers are configured
        agent = create_agent(agent_type, agent_type, default_tools, agent_type)
        return await _execute_agent_step(state, agent, agent_type)


async def researcher_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认查询消息，以避免API错误")]
    configurable = Configuration.from_runnable_config(config)
    
    # Import akshare tools
    from src.tools.akshare import (
        stock_news_em_tool,
        stock_news_main_cx_tool,
        news_report_time_baidu_tool,
        comprehensive_stock_news_tool,
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    )
    
    # Import Yahoo Finance news tool
    from src.tools.yahoo_finance_news import yahoo_finance_news_tool
    
    return await _setup_and_execute_agent_step(
        state,
        config,
        "researcher",
        [
            get_web_search_tool(configurable.max_search_results), 
            # 移除crawl_tool，因为它无法正常工作
            # Yahoo Finance 新闻工具
            yahoo_finance_news_tool,
            # AkShare 股票和新闻工具
            stock_news_em_tool,
            stock_news_main_cx_tool,
            news_report_time_baidu_tool,
            comprehensive_stock_news_tool,
            us_stock_spot_tool,
            us_stock_hist_tool,
            us_stock_search_tool,
            us_stock_famous_tool,
            us_stock_pink_tool,
            us_stock_minute_tool,
            us_stock_daily_sina_tool,
            get_famous_stock_data_tool,
        ],
    )


async def coder_node(
    state: State,
    config: RunnableConfig,
) -> Command[Literal["research_team"]]:
    """Coder node that do code analysis."""
    logger.info("Coder node is coding.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认代码分析消息，以避免API错误")]
    
    # Import akshare tools for data analysis
    from src.tools.akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_daily_sina_tool,
    )
    
    return await _setup_and_execute_agent_step(
        state,
        config,
        "coder",
        [
            python_repl_tool, 
            alpha_vantage_tool,
            # AkShare 数据分析工具
            us_stock_spot_tool,
            us_stock_hist_tool,
            us_stock_search_tool,
            us_stock_daily_sina_tool,
        ],
    )


async def technical_analyst_node(
    state: State,
    config: RunnableConfig,
) -> Command[Literal["research_team"]]:
    """Technical analyst node that performs financial technical analysis."""
    logger.info("Technical analyst node is performing technical analysis.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认技术分析消息，以避免API错误")]
    
    # Import akshare tools for technical analysis
    from src.tools.akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    )
    
    # Import dedicated technical indicators tools
    from src.tools.technical_indicators import (
        calculate_technical_indicators,
        fibonacci_retracement_levels,
    )
    
    # Import divergence analysis tool
    from src.tools.divergence_analysis import detect_price_macd_divergence
    
    return await _setup_and_execute_agent_step(
        state,
        config,
        "technical_analyst",
        [
            # 专业技术指标计算工具（核心功能）
            calculate_technical_indicators,    # 计算SMA, EMA, MACD, RSI, KDJ, BOLL等指标
            fibonacci_retracement_levels,      # 斐波那契回调分析
            detect_price_macd_divergence,      # 背离分析工具（底背离和顶背离检测）
            # 股票数据获取工具 - 技术分析的数据源
            get_famous_stock_data_tool,        # 知名公司股票数据（推荐）
            us_stock_daily_sina_tool,          # 新浪美股历史数据
            us_stock_hist_tool,                # 美股历史数据（多周期）
            us_stock_spot_tool,                # 美股实时行情
            us_stock_search_tool,              # 美股搜索
            us_stock_famous_tool,              # 知名美股分类数据
            us_stock_pink_tool,                # 粉单市场数据
            us_stock_minute_tool,              # 美股分时数据
            python_repl_tool,                  # Python代码执行（补充工具）
        ],
    )
