# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
MCP 配置模块
从 conf.yaml 文件中读取 MCP 服务器配置
"""

import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from src.config.loader import load_yaml_config

logger = logging.getLogger(__name__)


def get_mcp_servers_config() -> Dict[str, Dict[str, Any]]:
    """
    从配置文件中获取 MCP 服务器配置
    
    Returns:
        Dict: MCP 服务器配置字典，格式为:
        {
            "server_name": {
                "transport": "stdio",
                "command": "/path/to/command",
                "args": [],
                "env": {},
                "enabled_tools": [],
                "add_to_agents": []
            }
        }
    """
    try:
        # 加载配置文件
        config_path = str((Path(__file__).parent.parent.parent / "conf.yaml").resolve())
        config = load_yaml_config(config_path)
        
        # 获取 MCP_SERVERS 配置
        mcp_config = config.get("MCP_SERVERS", {})
        
        if not mcp_config:
            logger.info("未找到 MCP_SERVERS 配置")
            return {}
        
        # 转换配置格式为标准的 MCP 格式
        mcp_servers = {}
        for server_name, server_config in mcp_config.items():
            mcp_servers[server_name] = {
                "transport": server_config.get("transport", "stdio"),
                "command": server_config.get("command"),
                "args": server_config.get("args", []),
                "env": server_config.get("env", {}),
                "enabled_tools": server_config.get("enabled_tools", []),
                "add_to_agents": server_config.get("add_to_agents", ["researcher"])
            }
        
        logger.info(f"成功加载 {len(mcp_servers)} 个 MCP 服务器配置")
        return mcp_servers
        
    except Exception as e:
        logger.error(f"加载 MCP 配置失败: {e}")
        return {}


def format_mcp_config_for_langgraph(enabled_agents: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
    """
    将 MCP 配置格式化为 LangGraph 可用的格式
    
    Args:
        enabled_agents: 启用的代理列表，如果为 None 则使用配置中的默认值
        
    Returns:
        格式化后的 MCP 配置，适用于 LangGraph 的 MultiServerMCPClient
    """
    mcp_servers = get_mcp_servers_config()
    
    if not mcp_servers:
        return None
    
    # 过滤和格式化服务器配置
    formatted_servers = {}
    
    for server_name, server_config in mcp_servers.items():
        # 检查是否应该为指定的代理启用此服务器
        server_agents = server_config.get("add_to_agents", [])
        if enabled_agents:
            # 如果指定了代理列表，检查是否有交集
            if not any(agent in server_agents for agent in enabled_agents):
                continue
        
        # 构建标准的 MCP 服务器配置
        if server_config["transport"] == "stdio":
            formatted_servers[server_name] = {
                "command": server_config["command"],
                "args": server_config["args"],
                "env": server_config["env"]
            }
        elif server_config["transport"] == "sse":
            formatted_servers[server_name] = {
                "url": server_config.get("url"),
                "env": server_config["env"]
            }
    
    if not formatted_servers:
        return None
    
    return formatted_servers


def get_enabled_tools_for_server(server_name: str) -> List[str]:
    """
    获取指定 MCP 服务器的启用工具列表
    
    Args:
        server_name: MCP 服务器名称
        
    Returns:
        启用的工具名称列表
    """
    mcp_servers = get_mcp_servers_config()
    server_config = mcp_servers.get(server_name, {})
    return server_config.get("enabled_tools", [])


def is_mcp_configured() -> bool:
    """
    检查是否配置了任何 MCP 服务器
    
    Returns:
        如果配置了 MCP 服务器则返回 True，否则返回 False
    """
    mcp_servers = get_mcp_servers_config()
    return len(mcp_servers) > 0


def get_mcp_servers_for_agent(agent_type: str) -> Dict[str, Any]:
    """
    获取指定代理类型的 MCP 服务器配置
    
    Args:
        agent_type: 代理类型（如 "researcher", "coder"）
        
    Returns:
        适用于该代理的 MCP 服务器配置
    """
    return format_mcp_config_for_langgraph([agent_type]) or {} 