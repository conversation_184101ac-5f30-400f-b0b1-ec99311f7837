#!/usr/bin/env python3
"""
🚀 快速MACD背离扫描
一键扫描指数成分股的MACD背离情况
"""

import os
import sys
import asyncio
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.market_scanner import get_market_scanner
from backend.index_components import get_all_index_names

# 配置日志（只显示重要信息）
logging.basicConfig(level=logging.WARNING)

async def quick_scan():
    """快速扫描所有指数的MACD背离"""
    
    print("🚀 快速MACD背离扫描器")
    print("=" * 50)
    
    # Tushare Token
    TUSHARE_TOKEN = "d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c"
    
    try:
        scanner = get_market_scanner(TUSHARE_TOKEN)
        
        # 扫描的指数列表
        indexes_to_scan = ['NASDAQ_100', 'HANG_SENG_TECH', 'CSI_300']
        index_names = {
            'NASDAQ_100': '纳斯达克100',
            'HANG_SENG_TECH': '恒生科技', 
            'CSI_300': '中证300'
        }
        
        total_divergences = 0
        
        for index_code in indexes_to_scan:
            print(f"\n🔍 扫描 {index_names[index_code]}...")
            
            try:
                result = await scanner.scan_index(index_code, ['bullish', 'bearish'])
                
                divergence_count = len(result['divergences'])
                total_divergences += divergence_count
                
                print(f"✅ {index_names[index_code]}: {result['scanned_stocks']}/{result['total_stocks']} 只股票")
                print(f"   🚨 发现背离: {divergence_count} 个")
                
                # 显示背离详情
                if result['divergences']:
                    bullish = [d for d in result['divergences'] if d['type'] == 'bullish']
                    bearish = [d for d in result['divergences'] if d['type'] == 'bearish']
                    
                    if bullish:
                        print(f"   🟢 底背离: {', '.join([d['symbol'] for d in bullish])}")
                    if bearish:
                        print(f"   🔴 顶背离: {', '.join([d['symbol'] for d in bearish])}")
                
            except Exception as e:
                print(f"❌ {index_names[index_code]} 扫描失败: {e}")
        
        print(f"\n📊 扫描完成！总计发现 {total_divergences} 个背离信号")
        
        if total_divergences == 0:
            print("✅ 当前市场未发现明显的MACD背离信号")
        else:
            print("⚠️  请注意风险，背离信号仅供参考")
            
    except Exception as e:
        print(f"❌ 扫描过程出错: {e}")

if __name__ == "__main__":
    print("启动快速MACD背离扫描...")
    asyncio.run(quick_scan()) 