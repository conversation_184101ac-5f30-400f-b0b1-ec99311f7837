<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻搜索测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            margin: 20px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .news-item {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .news-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .news-content {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.5;
        }
        .news-meta {
            font-size: 12px;
            color: #999;
            display: flex;
            justify-content: space-between;
        }
        .news-link {
            color: #007bff;
            text-decoration: none;
        }
        .news-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>新闻搜索测试</h1>
        <p>测试新闻搜索API功能，输入关键词搜索相关新闻</p>
        
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="输入关键词，如：苹果、新能源、人工智能" />
            <button onclick="searchNews()" id="searchBtn">搜索</button>
        </div>
        
        <div id="loading" class="loading" style="display: none;">正在搜索新闻...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="results"></div>
    </div>

    <script>
        async function searchNews() {
            const keyword = document.getElementById('searchInput').value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }

            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const resultsEl = document.getElementById('results');
            const searchBtn = document.getElementById('searchBtn');

            // 显示加载状态
            loadingEl.style.display = 'block';
            errorEl.style.display = 'none';
            resultsEl.innerHTML = '';
            searchBtn.disabled = true;

            try {
                console.log('发送搜索请求:', keyword);
                const response = await fetch('http://127.0.0.1:8000/news/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        keyword: keyword,
                        max_rows: 10
                    })
                });

                console.log('响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.success && data.news_data && data.news_data.data) {
                    displayResults(data.news_data.data, keyword);
                } else {
                    throw new Error('未找到相关新闻数据');
                }

            } catch (error) {
                console.error('搜索失败:', error);
                errorEl.textContent = `搜索失败: ${error.message}`;
                errorEl.style.display = 'block';
            } finally {
                loadingEl.style.display = 'none';
                searchBtn.disabled = false;
            }
        }

        function displayResults(newsData, keyword) {
            const resultsEl = document.getElementById('results');
            
            if (!newsData || newsData.length === 0) {
                resultsEl.innerHTML = '<p>未找到相关新闻</p>';
                return;
            }

            let html = `<h3>搜索结果 (关键词: ${keyword})</h3>`;
            
            newsData.forEach(item => {
                html += `
                    <div class="news-item">
                        <div class="news-title">${item.新闻标题 || '无标题'}</div>
                        <div class="news-content">${item.新闻内容 || '无内容'}</div>
                        <div class="news-meta">
                            <span>来源: ${item.文章来源 || '未知'}</span>
                            <span>时间: ${item.发布时间 || '未知'}</span>
                            ${item.新闻链接 ? `<a href="${item.新闻链接}" target="_blank" class="news-link">查看原文</a>` : ''}
                        </div>
                    </div>
                `;
            });

            resultsEl.innerHTML = html;
        }

        // 支持回车键搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchNews();
            }
        });
    </script>
</body>
</html>
