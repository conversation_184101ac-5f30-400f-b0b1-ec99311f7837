#!/usr/bin/env python3
"""
新闻搜索功能最终测试脚本
测试后端API的各种功能和边界情况
"""

import requests
import json
import time
from typing import Dict, Any

# 配置
BASE_URL = "http://127.0.0.1:8000"
TEST_KEYWORDS = ["苹果", "新能源", "人工智能", "芯片", "医药"]

def test_health_check() -> bool:
    """测试健康检查接口"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data.get('service', 'Unknown')}")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_news_search(keyword: str, max_rows: int = 3) -> Dict[str, Any]:
    """测试新闻搜索功能"""
    try:
        payload = {
            "keyword": keyword,
            "max_rows": max_rows
        }
        
        print(f"\n🔍 搜索关键词: {keyword}")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/news/search",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000, 2)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                news_data = data.get('news_data', {})
                news_list = news_data.get('data', [])
                total_rows = news_data.get('total_rows', 0)
                displayed_rows = news_data.get('displayed_rows', 0)
                
                print(f"✅ 搜索成功 ({response_time}ms)")
                print(f"   📊 数据统计: 显示 {displayed_rows}/{total_rows} 条新闻")
                
                # 显示新闻摘要
                for i, news in enumerate(news_list[:2], 1):
                    title = news.get('新闻标题', '无标题')[:50]
                    source = news.get('文章来源', '未知来源')
                    pub_time = news.get('发布时间', '未知时间')
                    print(f"   {i}. {title}... ({source}, {pub_time})")
                
                return {
                    "success": True,
                    "keyword": keyword,
                    "response_time": response_time,
                    "total_rows": total_rows,
                    "displayed_rows": displayed_rows,
                    "news_count": len(news_list)
                }
            else:
                print(f"❌ 搜索失败: API返回success=False")
                return {"success": False, "error": "API返回success=False"}
        else:
            print(f"❌ 搜索失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except requests.exceptions.Timeout:
        print(f"❌ 搜索超时: {keyword}")
        return {"success": False, "error": "请求超时"}
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return {"success": False, "error": str(e)}

def test_cors_preflight() -> bool:
    """测试CORS预检请求"""
    try:
        response = requests.options(
            f"{BASE_URL}/news/search",
            headers={
                "Origin": "http://localhost:3002",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ CORS预检请求通过")
            return True
        else:
            print(f"❌ CORS预检请求失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS预检请求异常: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况:")
    
    # 测试空关键词
    try:
        response = requests.post(
            f"{BASE_URL}/news/search",
            json={"keyword": "", "max_rows": 5},
            timeout=10
        )
        if response.status_code != 200:
            print("✅ 空关键词正确被拒绝")
        else:
            print("⚠️  空关键词未被拒绝")
    except:
        print("✅ 空关键词处理正常")
    
    # 测试超大max_rows
    try:
        response = requests.post(
            f"{BASE_URL}/news/search",
            json={"keyword": "测试", "max_rows": 1000},
            timeout=15
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                displayed = data.get('news_data', {}).get('displayed_rows', 0)
                print(f"✅ 大数据量请求处理正常: 返回 {displayed} 条")
            else:
                print("⚠️  大数据量请求失败")
        else:
            print("⚠️  大数据量请求被拒绝")
    except:
        print("⚠️  大数据量请求异常")

def main():
    """主测试函数"""
    print("🚀 开始新闻搜索功能测试")
    print("=" * 50)
    
    # 1. 健康检查
    if not test_health_check():
        print("❌ 后端服务器不可用，测试终止")
        return
    
    # 2. CORS测试
    test_cors_preflight()
    
    # 3. 新闻搜索测试
    print("\n📰 新闻搜索功能测试:")
    results = []
    
    for keyword in TEST_KEYWORDS:
        result = test_news_search(keyword)
        results.append(result)
        time.sleep(1)  # 避免请求过于频繁
    
    # 4. 边界情况测试
    test_edge_cases()
    
    # 5. 统计结果
    print("\n📊 测试结果统计:")
    print("=" * 50)
    
    successful_tests = [r for r in results if r.get('success')]
    failed_tests = [r for r in results if not r.get('success')]
    
    print(f"✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print(f"❌ 失败测试: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_response_time = sum(r.get('response_time', 0) for r in successful_tests) / len(successful_tests)
        total_news = sum(r.get('news_count', 0) for r in successful_tests)
        print(f"⏱️  平均响应时间: {avg_response_time:.2f}ms")
        print(f"📄 总获取新闻数: {total_news} 条")
    
    if failed_tests:
        print("\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"   - {test.get('keyword', 'Unknown')}: {test.get('error', 'Unknown error')}")
    
    print("\n🎉 测试完成!")
    
    if len(successful_tests) == len(results):
        print("✅ 所有测试通过，新闻搜索功能正常工作！")
    else:
        print("⚠️  部分测试失败，请检查相关问题")

if __name__ == "__main__":
    main()
