import React, { useState, useRef, useEffect, useCallback } from 'react';
import './App.css';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import ReactECharts from 'echarts-for-react';
import MarkdownRenderer from './components/MarkdownRenderer';
import MarkdownTest from './components/MarkdownTest';

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'chart';
  content: string;
  agent?: string;
  timestamp: number;
  isStreaming?: boolean;
  chartConfig?: any;
  chartData?: any;
}

interface StreamData {
  type: 'message' | 'final_report' | 'error' | 'end' | 'agent_message' | 'workflow_complete' | 'done' | 'chart_data';
  content?: string;
  message?: string;
  agent?: string;
  timestamp: number | string;
  chart_data?: any;
  has_chart?: boolean;
}



interface Factor {
  id: string;
  name: string;
  category: 'technical' | 'fundamental' | 'capital' | 'chip';
  description: string;
  formula?: string;
  isCustom: boolean;
  isActive: boolean;
}

type AppMode = 'home' | 'ai-agent' | 'factor-management' | 'ml-models' | 'stock-scoring' | 'divergence-scanner' | 'data-query' | 'markdown-test';
type FactorMode = 'overview' | 'calculation' | 'custom' | 'analysis';
type MLMode = 'models' | 'training' | 'prediction' | 'comparison';
type ScoringMode = 'factor-score' | 'ml-score' | 'comprehensive' | 'ranking';
type DivergenceMode = 'scanner' | 'results' | 'history';

// 添加数据查询相关的接口定义
interface StockBasicInfo {
  symbol: string;
  name: string;
  market: string;
  industry: string;
}

interface KlineData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  amount: number;
  pct_change: number;
}

interface StockStatistics {
  latest_price: number;
  price_change: number;
  highest_price: number;
  lowest_price: number;
  price_range: number;
  avg_volume: number;
  total_records: number;
  date_range: {
    start: string;
    end: string;
  };
}

interface StockQueryResult {
  success: boolean;
  symbol: string;
  basic_info: StockBasicInfo;
  statistics: StockStatistics;
  kline_data: KlineData[];
  data_source: string;
  timestamp: string;
}

interface TechnicalIndicatorResult {
  success: boolean;
  symbol: string;
  period: string;
  dates: string[];
  indicators: any;
  data_info: any;
  timestamp: string;
}

const App: React.FC = () => {
  const [mode, setMode] = useState<AppMode>('home');
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Chart tracking
  const [chartMessageIds, setChartMessageIds] = useState<Set<string>>(new Set());

  // 因子管理相关状态
  const [factors, setFactors] = useState<Factor[]>([]);
  const [selectedStock, setSelectedStock] = useState('AAPL');
  const [factorData, setFactorData] = useState<any>(null);
  const [factorMode, setFactorMode] = useState<FactorMode>('overview');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  
  // 自定义因子状态
  const [customFactorName, setCustomFactorName] = useState('');
  const [customFactorDesc, setCustomFactorDesc] = useState('');
  const [customFactorFormula, setCustomFactorFormula] = useState('');
  const [aiFactorPrompt, setAiFactorPrompt] = useState('');
  const [aiFactorResult, setAiFactorResult] = useState<any>(null);
  const [isGeneratingFactor, setIsGeneratingFactor] = useState(false);

  // ML模型相关状态
  const [mlMode, setMLMode] = useState<MLMode>('models');
  const [models, setModels] = useState<any[]>([]);
  const [trainingResult, setTrainingResult] = useState<any>(null);
  const [predictionResult, setPredictionResult] = useState<any>(null);
  const [trainingSymbols, setTrainingSymbols] = useState<string>('AAPL,MSFT,GOOGL');
  const [predictionSymbols, setPredictionSymbols] = useState<string>('TSLA,NVDA');
  const [selectedModelId, setSelectedModelId] = useState<string>('random_forest');

  // 股票评分相关状态
  const [scoringMode, setScoringMode] = useState<ScoringMode>('factor-score');
  const [scoreResults, setScoreResults] = useState<any>(null);
  const [rankingResults, setRankingResults] = useState<any>(null);
  const [rankingSymbols, setRankingSymbols] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX', 'CRM', 'ORCL']);

  // 背离扫描相关状态
  const [divergenceMode, setDivergenceMode] = useState<DivergenceMode>('scanner');
  const [selectedMarket, setSelectedMarket] = useState<string>('US');
  const [scanResults, setScanResults] = useState<any>(null);
  const [recentDivergences, setRecentDivergences] = useState<any[]>([]);
  const [scanHistory, setScanHistory] = useState<any[]>([]);
  const [isScanningDivergence, setIsScanningDivergence] = useState(false);

  // 数据查询相关状态
  const [stockData, setStockData] = useState<StockQueryResult | null>(null);
  const [technicalData, setTechnicalData] = useState<TechnicalIndicatorResult | null>(null);
  const [querySymbol, setQuerySymbol] = useState<string>('AAPL');
  const [queryPeriod, setQueryPeriod] = useState<string>('1y');
  const [selectedIndicators, setSelectedIndicators] = useState<string[]>(['sma', 'ema', 'rsi', 'macd']);
  const [isLoadingStock, setIsLoadingStock] = useState(false);
  const [stockSuggestions, setStockSuggestions] = useState<any[]>([]);
  const [showStockSuggestions, setShowStockSuggestions] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<string>('');
  
  // 图表相关状态
  const [chartData, setChartData] = useState<any>(null);
  const [showChart, setShowChart] = useState(false);

  // 因子分析相关状态
  const [factorAnalysisResults, setFactorAnalysisResults] = useState<any>(null);
  const [selectedStockForChart, setSelectedStockForChart] = useState<string>('');
  const [factorStockData, setFactorStockData] = useState<any>(null);
  const [showFactorChart, setShowFactorChart] = useState(false);
  const [selectedFactorsForAnalysis, setSelectedFactorsForAnalysis] = useState<string[]>([]);
  const [factorChartData, setFactorChartData] = useState<any>(null);
  const [isPerformingAnalysis, setIsPerformingAnalysis] = useState(false);
  const [analysisStockSuggestions, setAnalysisStockSuggestions] = useState<any[]>([]);
  const [showAnalysisStockSuggestions, setShowAnalysisStockSuggestions] = useState(false);
  
  // 分析输入相关状态
  const [analysisSearchInput, setAnalysisSearchInput] = useState<string>('');
  const [analysisStocks, setAnalysisStocks] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL']);
  const [analysisSearchSuggestions, setAnalysisSearchSuggestions] = useState<any[]>([]);
  const [showAnalysisSearchSuggestions, setShowAnalysisSearchSuggestions] = useState<boolean>(false);
  const [analysisTimeframe, setAnalysisTimeframe] = useState<string>('3m');
  const [analysisMode, setAnalysisMode] = useState<'correlation' | 'returns' | 'validity' | 'risk'>('correlation');
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [correlationMatrix, setCorrelationMatrix] = useState<any>(null);
  const [returnsData, setReturnsData] = useState<any>(null);
  const [validityMetrics, setValidityMetrics] = useState<any>(null);
  const [riskMetrics, setRiskMetrics] = useState<any>(null);
  
  // Additional missing states  
  const [isTraining, setIsTraining] = useState<boolean>(false);
  const [isPredicting, setIsPredicting] = useState<boolean>(false);
  const [isRanking, setIsRanking] = useState<boolean>(false);
  const [trainingStocks, setTrainingStocks] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL']);
  const [predictionStocks, setPredictionStocks] = useState<string[]>(['TSLA', 'NVDA']);
  const [factorWeight, setFactorWeight] = useState<number>(0.6);
  const [mlWeight, setMlWeight] = useState<number>(0.4);
  
  // More missing states
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [tushareToken, setTushareToken] = useState<string>('d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c');
  const [divergenceTypes, setDivergenceTypes] = useState<string[]>(['bullish', 'bearish']);
  const [searchSuggestions, setSearchSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const [isQuerying, setIsQuerying] = useState<boolean>(false);
  const [setMLWeight] = useState(() => setMlWeight); // Alias for setMlWeight

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 加载内置因子
  useEffect(() => {
    loadBuiltInFactors();
  }, []);

  // 自动刷新逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (autoRefresh && stockData && querySymbol) {
      interval = setInterval(async () => {
        await queryStockData(true); // 静默刷新，不显示加载状态
        await getTechnicalIndicators(); // 同时刷新技术指标
      }, 30000); // 每30秒刷新一次
    }
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoRefresh, stockData, querySymbol]);

  const loadBuiltInFactors = () => {
    const builtInFactors: Factor[] = [
      // 技术面因子
      { id: 'rsi', name: 'RSI相对强弱指标', category: 'technical', description: '衡量股价超买超卖情况', isCustom: false, isActive: true },
      { id: 'macd', name: 'MACD指标', category: 'technical', description: '趋势跟踪动量指标', isCustom: false, isActive: true },
              { id: 'bollinger_position', name: '布林带位置', category: 'technical', description: '价格在布林带中的位置', isCustom: false, isActive: true },
          { id: 'sma_20', name: '20日移动平均线', category: 'technical', description: '短期趋势指标', isCustom: false, isActive: true },
    { id: 'sma_50', name: '50日移动平均线', category: 'technical', description: '中期趋势指标', isCustom: false, isActive: true },
    { id: 'sma_200', name: '200日移动平均线', category: 'technical', description: '长期趋势指标', isCustom: false, isActive: true },
              { id: 'volume_sma_20', name: '成交量移动平均', category: 'technical', description: '成交量趋势', isCustom: false, isActive: true },
      { id: 'atr', name: '平均真实波动率', category: 'technical', description: '波动性指标', isCustom: false, isActive: true },
              { id: 'stoch_k', name: '随机指标K值', category: 'technical', description: '超买超卖指标K值', isCustom: false, isActive: true },
      
      // 基本面因子
      { id: 'pe_ratio', name: '市盈率', category: 'fundamental', description: '估值指标', isCustom: false, isActive: true },
      { id: 'pb_ratio', name: '市净率', category: 'fundamental', description: '估值指标', isCustom: false, isActive: true },
      { id: 'ps_ratio', name: '市销率', category: 'fundamental', description: '估值指标', isCustom: false, isActive: true },
      { id: 'roe', name: '净资产收益率', category: 'fundamental', description: '盈利能力指标', isCustom: false, isActive: true },
      { id: 'roa', name: '总资产收益率', category: 'fundamental', description: '盈利能力指标', isCustom: false, isActive: true },
      { id: 'debt_ratio', name: '资产负债率', category: 'fundamental', description: '偿债能力指标', isCustom: false, isActive: true },
      { id: 'current_ratio', name: '流动比率', category: 'fundamental', description: '流动性指标', isCustom: false, isActive: true },
      { id: 'revenue_growth', name: '营收增长率', category: 'fundamental', description: '成长性指标', isCustom: false, isActive: true },
      { id: 'eps_growth', name: 'EPS增长率', category: 'fundamental', description: '成长性指标', isCustom: false, isActive: true },
      
      // 筹码面因子
      { id: 'turnover_rate', name: '换手率', category: 'chip', description: '筹码活跃度', isCustom: false, isActive: true },
      { id: 'concentration', name: '筹码集中度', category: 'chip', description: '筹码分布集中程度', isCustom: false, isActive: true },
      { id: 'avg_cost', name: '平均成本', category: 'chip', description: '市场平均持股成本', isCustom: false, isActive: true },
      { id: 'profit_ratio', name: '获利盘比例', category: 'chip', description: '盈利筹码占比', isCustom: false, isActive: true },
      { id: 'cost_distribution', name: '成本分布', category: 'chip', description: '筹码成本分布情况', isCustom: false, isActive: true },
      { id: 'chip_peak', name: '筹码峰值', category: 'chip', description: '主要成本区间', isCustom: false, isActive: true },
      { id: 'support_pressure', name: '支撑压力', category: 'chip', description: '基于筹码的支撑压力位', isCustom: false, isActive: true },
    ];
    setFactors(builtInFactors);
  };

  // 多智能体聊天功能
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('http://127.0.0.1:8000/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          max_plan_iterations: 1,
          max_step_num: 3,
          enable_background_investigation: true,
          debug: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      const streamingMessageId = Date.now().toString();

      const assistantMessage: Message = {
        id: streamingMessageId,
        type: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true,
      };

      setMessages(prev => [...prev, assistantMessage]);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: StreamData = JSON.parse(line.slice(6));
              
              if (data.type === 'chart_data') {
                // Handle chart data separately and prevent duplicates
                const symbol = data.chart_data?.symbol || 'unknown';
                const chartId = symbol; // Use only symbol as ID, not timestamp
                
                // Check if we already have a chart for this symbol in the current session
                const chartExists = messages.some(msg => 
                  msg.type === 'chart' && 
                  msg.chartData?.symbol === symbol
                );
                
                if (!chartExists && !chartMessageIds.has(chartId)) {
                  const chartMessage: Message = {
                    id: Date.now().toString() + '_chart',
                    type: 'chart',
                    content: `📊 ${symbol} 股票图表分析`,
                    chartConfig: data.content,
                    chartData: data.chart_data,
                    agent: data.agent,
                    timestamp: Date.now(),
                  };
                  setMessages(prev => [...prev, chartMessage]);
                  setChartMessageIds(prev => new Set(prev).add(chartId));
                } else {
                  console.log(`Chart for ${symbol} already exists, skipping duplicate`);
                }
              } else if (data.type === 'message' || data.type === 'final_report' || data.type === 'agent_message') {
                setMessages(prev => prev.map(msg => 
                  msg.id === streamingMessageId 
                    ? { 
                        ...msg, 
                        content: msg.content + (data.content || ''),
                        agent: data.agent || msg.agent 
                      }
                    : msg
                ));
              } else if (data.type === 'error') {
                setMessages(prev => prev.map(msg => 
                  msg.id === streamingMessageId 
                    ? { 
                        ...msg, 
                        content: msg.content + `\n\n❌ 错误: ${data.content || data.message}`,
                        isStreaming: false 
                      }
                    : msg
                ));
                break;
              } else if (data.type === 'end' || data.type === 'done' || data.type === 'workflow_complete') {
                setMessages(prev => prev.map(msg => 
                  msg.id === streamingMessageId 
                    ? { ...msg, isStreaming: false }
                    : msg
                ));
                break;
              }
            } catch (error) {
              console.error('解析流数据错误:', error);
            }
          }
        }
      }
    } catch (error) {
      console.error('请求错误:', error);
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: `❌ 连接错误: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: Date.now(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 因子管理功能
  const calculateFactors = async () => {
    try {
      const requestBody: any = {
        symbol: selectedStock,
        tushare_token: 'd255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c',
        factors: factors.filter(f => f.isActive).map(f => f.id)
      };

      // 如果设置了日期范围，添加到请求中
      if (startDate) {
        requestBody.start_date = startDate.replace(/-/g, '');
      }
      if (endDate) {
        requestBody.end_date = endDate.replace(/-/g, '');
      }

      const response = await fetch('http://127.0.0.1:8000/factors/calculate/smart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setFactorData(data);
    } catch (error) {
      console.error('因子计算错误:', error);
      setFactorData({ error: '计算失败，请检查网络连接或股票代码' });
    }
  };

  // 获取因子图表数据
  const getFactorChart = async () => {
    try {
      const activeFactor = factors.filter(f => f.isActive).map(f => f.id);
      if (activeFactor.length === 0) {
        alert('请至少选择一个因子');
        return;
      }

      const requestBody: any = {
        symbol: selectedStock,
        tushare_token: 'd255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c',
        factors: activeFactor
      };

      // 如果设置了日期范围，添加到请求中
      if (startDate) {
        requestBody.start_date = startDate.replace(/-/g, '');
      }
      if (endDate) {
        requestBody.end_date = endDate.replace(/-/g, '');
      }

      const response = await fetch('http://127.0.0.1:8000/factors/timeseries', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // 转换数据格式为图表所需格式
      const chartPoints = data.dates.map((date: string, index: number) => {
        const point: any = { date: date };
        
        // 添加归一化后的因子值
        Object.keys(data.normalized_factors).forEach(factorId => {
          point[factorId] = data.normalized_factors[factorId][index];
        });
        
        return point;
      });
      
      setChartData({
        ...data,
        chartPoints: chartPoints
      });
      setShowChart(true);
      
    } catch (error) {
      console.error('获取图表数据错误:', error);
      alert('获取图表数据失败，请检查网络连接或股票代码');
    }
  };

  const createCustomFactor = async () => {
    if (!customFactorName.trim() || !customFactorFormula.trim()) return;
    
    const newFactor: Factor = {
      id: `custom_${Date.now()}`,
      name: customFactorName,
      category: 'technical',
      description: '自定义因子',
      formula: customFactorFormula,
      isCustom: true,
      isActive: true,
    };
    
    setFactors(prev => [...prev, newFactor]);
    setCustomFactorName('');
    setCustomFactorFormula('');
  };

  // AI生成因子功能
  const generateAIFactor = async () => {
    if (!aiFactorPrompt.trim()) {
      alert('请输入因子描述');
      return;
    }

    setIsGeneratingFactor(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/factors/ai-generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: aiFactorPrompt
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setAiFactorResult(data);
    } catch (error) {
      console.error('AI生成因子错误:', error);
      alert('AI生成因子失败，请检查网络连接');
    } finally {
      setIsGeneratingFactor(false);
    }
  };

  // 确认AI生成的因子
  const confirmAIFactor = () => {
    if (!aiFactorResult) return;

    const newFactor: Factor = {
      id: `ai_${Date.now()}`,
      name: aiFactorResult.factor_name,
      category: 'technical',
      description: aiFactorResult.explanation,
      formula: aiFactorResult.formula,
      isCustom: true,
      isActive: true,
    };
    
    setFactors(prev => [...prev, newFactor]);
    setAiFactorPrompt('');
    setAiFactorResult(null);
  };

  // 测试AI生成的因子
  const testAIFactor = async () => {
    if (!aiFactorResult || !selectedStock) {
      alert('请先生成因子并选择股票');
      return;
    }

    try {
      const response = await fetch('http://127.0.0.1:8000/factors/test-custom', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: selectedStock,
          formula: aiFactorResult.formula,
          tushare_token: 'd255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c'
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      alert(`测试成功！\n股票: ${result.symbol}\n因子值: ${result.factor_value}\n数据点: ${result.data_points}`);
    } catch (error) {
      console.error('测试因子错误:', error);
      alert('测试因子失败，请检查公式是否正确');
    }
  };

  const handleClear = () => {
    setMessages([]);
    setChartMessageIds(new Set()); // Clear chart tracking
  };

  // 辅助函数：获取类别中文名称
  const getCategoryName = (category: string) => {
    const categoryNames: { [key: string]: string } = {
      'technical': '技术面',
      'fundamental': '基本面', 
      'capital': '资金面',
      'chip': '筹码面'
    };
    return categoryNames[category] || category;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      technical: '📈',
      fundamental: '📊',
      capital: '💰',
      chip: '🔢'
    };
    return icons[category as keyof typeof icons] || '📋';
  };

  // 主页界面
  const renderHomePage = () => (
    <div className="home-container">
      <div className="home-header">
        <h1>💰 金融投资助手</h1>
        <p>专业的智能投资分析平台</p>
      </div>
      
      <div className="module-grid">
        <div className="module-card" onClick={() => setMode('ai-agent')}>
          <div className="module-icon">🤖</div>
          <h3>AI智能分析</h3>
          <p>多智能体协作进行深度投资分析和研究</p>
          <div className="module-features">
            <span>股票技术分析</span>
            <span>市场研究</span>
            <span>投资建议</span>
          </div>
        </div>

        <div className="module-card" onClick={() => setMode('data-query')}>
          <div className="module-icon">📊</div>
          <h3>数据查询</h3>
          <p>查询股票数据，查看K线图和技术指标</p>
          <div className="module-features">
            <span>K线图表</span>
            <span>技术指标</span>
            <span>数据下载</span>
          </div>
        </div>
        
        <div className="module-card" onClick={() => setMode('factor-management')}>
          <div className="module-icon">⚙️</div>
          <h3>因子管理系统</h3>
          <p>专业的量化因子计算和分析工具</p>
          <div className="module-features">
            <span>36个内置因子</span>
            <span>自定义因子</span>
            <span>因子分析</span>
          </div>
        </div>
        
        <div className="module-card" onClick={() => setMode('ml-models')}>
          <div className="module-icon">🧠</div>
          <h3>机器学习模型</h3>
          <p>多种算法的训练、预测和评估</p>
          <div className="module-features">
            <span>5种算法</span>
            <span>模型训练</span>
            <span>性能评估</span>
          </div>
        </div>
        
        <div className="module-card" onClick={() => setMode('stock-scoring')}>
          <div className="module-icon">🏆</div>
          <h3>股票评分系统</h3>
          <p>基于多因子和ML的综合评分</p>
          <div className="module-features">
            <span>因子评分</span>
            <span>ML评分</span>
            <span>选股排名</span>
          </div>
        </div>
        
        <div className="module-card" onClick={() => setMode('divergence-scanner')}>
          <div className="module-icon">📊</div>
          <h3>MACD背离扫描</h3>
          <p>一键扫描市场MACD顶背离和底背离</p>
          <div className="module-features">
            <span>美股/港股/A股</span>
            <span>顶背离/底背离</span>
            <span>K线图展示</span>
          </div>
        </div>
        
        <div className="module-card" onClick={() => setMode('markdown-test')}>
          <div className="module-icon">📝</div>
          <h3>Markdown 测试</h3>
          <p>测试 Markdown 渲染功能</p>
          <div className="module-features">
            <span>标题样式</span>
            <span>代码高亮</span>
            <span>表格渲染</span>
          </div>
        </div>
      </div>
    </div>
  );

  // AI智能分析界面
  const renderAIAgentMode = () => (
    <div className="chat-container">
      <header className="chat-header">
        <button className="back-button" onClick={() => setMode('home')}>
          ← 返回首页
        </button>
        <div className="header-content">
          <h1>🤖 AI智能分析</h1>
          <p>多智能体协作分析</p>
        </div>
        {messages.length > 0 && (
          <button 
            onClick={handleClear}
            className="clear-button"
            title="清空对话"
          >
            🗑️
          </button>
        )}
      </header>

      <div className="messages-container">
        {messages.length === 0 ? (
          <div className="welcome-screen">
            <div className="welcome-icon">🤖</div>
            <h2>AI智能分析助手</h2>
            <p>我是您的专业投资分析助手，可以帮助您进行股票技术分析、市场研究、投资策略制定等投资相关任务。</p>
            <div className="example-questions">
              <h3>您可以问我：</h3>
              <div className="question-grid">
                <button 
                  className="example-question"
                  onClick={() => setInput('分析苹果公司AAPL的股票技术面表现')}
                >
                  📈 分析苹果公司AAPL的股票技术面表现
                </button>
                <button 
                  className="example-question"
                  onClick={() => setInput('特斯拉股票的技术面分析和投资建议')}
                >
                  🚗 特斯拉股票的技术面分析和投资建议
                </button>
                <button 
                  className="example-question"
                  onClick={() => setInput('美联储政策对股市的影响分析')}
                >
                  🏛️ 美联储政策对股市的影响分析
                </button>
                <button 
                  className="example-question"
                  onClick={() => setInput('科技股板块的投资机会分析')}
                >
                  💻 科技股板块的投资机会分析
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="messages">
            {messages.map((message) => (
              <div key={message.id} className={`message ${message.type}`}>
                <div className="message-content">
                  {message.type === 'user' && (
                    <div className="message-header">
                      <span className="user-icon">👤</span>
                      <span className="message-label">您</span>
                    </div>
                  )}
                  {message.type === 'assistant' && (
                    <div className="message-header">
                      <span className="assistant-icon">🤖</span>
                      <span className="message-label">
                        AI分析师
                        {message.agent && ` (${message.agent})`}
                      </span>
                      {message.isStreaming && (
                        <span className="streaming-indicator">💭</span>
                      )}
                    </div>
                  )}
                  {message.type === 'system' && (
                    <div className="message-header">
                      <span className="system-icon">⚙️</span>
                      <span className="message-label">系统</span>
                    </div>
                  )}
                  {message.type === 'chart' && (
                    <div className="message-header">
                      <span className="chart-icon">📊</span>
                      <span className="message-label">
                        图表分析
                        {message.agent && ` (${message.agent})`}
                      </span>
                    </div>
                  )}
                  <div className="message-text">
                    {message.type === 'chart' && message.chartConfig ? (
                      <div className="chart-container">
                        <h4>{message.content}</h4>
                        <ReactECharts
                          option={message.chartConfig}
                          style={{ height: '600px', width: '100%' }}
                          opts={{ renderer: 'canvas' }}
                        />
                      </div>
                    ) : message.type === 'assistant' ? (
                      <MarkdownRenderer content={message.content} />
                    ) : (
                      message.content
                    )}
                    {message.isStreaming && (
                      <span className="cursor">▋</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <form onSubmit={handleSubmit} className="input-form">
        <div className="input-container">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="输入您的问题..."
            className="message-input"
            rows={1}
            disabled={isLoading}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
          <button 
            type="submit" 
            className="send-button"
            disabled={!input.trim() || isLoading}
          >
            {isLoading ? '🔄' : '➤'}
          </button>
        </div>
      </form>
    </div>
  );

  // 因子管理界面
  const renderFactorManagement = () => (
    <div className="factor-container">
      <header className="factor-header">
        <button className="back-button" onClick={() => setMode('home')}>
          ← 返回首页
        </button>
        <div className="header-content">
          <h1>⚙️ 因子管理系统</h1>
          <p>专业量化因子分析工具</p>
        </div>
      </header>

      <div className="factor-nav">
        <button 
          className={`nav-button ${factorMode === 'overview' ? 'active' : ''}`}
          onClick={() => setFactorMode('overview')}
        >
          📊 因子总览
        </button>
        <button 
          className={`nav-button ${factorMode === 'calculation' ? 'active' : ''}`}
          onClick={() => setFactorMode('calculation')}
        >
          🧮 因子计算
        </button>
        <button 
          className={`nav-button ${factorMode === 'custom' ? 'active' : ''}`}
          onClick={() => setFactorMode('custom')}
        >
          ✏️ 自定义因子
        </button>
        <button 
          className={`nav-button ${factorMode === 'analysis' ? 'active' : ''}`}
          onClick={() => setFactorMode('analysis')}
        >
          📈 因子分析
        </button>
      </div>

      <div className="factor-content">
        {factorMode === 'overview' && (
          <div className="factor-overview">
            <div className="factor-stats">
              <div className="stat-card">
                <h3>总因子数</h3>
                <span className="stat-number">{factors.length}</span>
              </div>
              <div className="stat-card">
                <h3>激活因子</h3>
                <span className="stat-number">{factors.filter(f => f.isActive).length}</span>
              </div>
              <div className="stat-card">
                <h3>自定义因子</h3>
                <span className="stat-number">{factors.filter(f => f.isCustom).length}</span>
              </div>
            </div>
            
            <div className="factor-categories">
              {['technical', 'fundamental', 'capital', 'chip'].map(category => (
                <div key={category} className="category-section">
                  <h3>
                    {getCategoryIcon(category)} {getCategoryName(category)}
                    <span className="category-count">
                      ({factors.filter(f => f.category === category).length})
                    </span>
                  </h3>
                  <div className="factor-list">
                    {factors.filter(f => f.category === category).map(factor => (
                      <div key={factor.id} className="factor-item">
                        <div className="factor-info">
                          <span className="factor-name">{factor.name}</span>
                          <span className="factor-desc">{factor.description}</span>
                        </div>
                        <label className="factor-toggle">
                          <input
                            type="checkbox"
                            checked={factor.isActive}
                            onChange={(e) => {
                              setFactors(prev => prev.map(f => 
                                f.id === factor.id ? { ...f, isActive: e.target.checked } : f
                              ));
                            }}
                          />
                          <span className="toggle-slider"></span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {factorMode === 'calculation' && (
          <div className="factor-calculation">
            <div className="calculation-controls">
              <div className="stock-selector">
                <label>股票代码:</label>
                <div className="stock-input-container">
                  <input
                    type="text"
                    className="stock-input"
                    value={selectedStock}
                    onChange={(e) => setSelectedStock(e.target.value.toUpperCase())}
                    placeholder="输入股票代码，如: AAPL, MSFT, 000001.SZ"
                  />
                  <small style={{color: '#6c757d', fontSize: '12px'}}>
                    支持美股代码(如AAPL)和A股代码(如000001.SZ, 600000.SH)
                  </small>
                </div>
              </div>
              
              <div className="date-range-selector">
                <label>数据范围 (可选):</label>
                
                <div className="date-preset-buttons">
                  <button 
                    className="preset-button" 
                    onClick={() => {
                      const today = new Date();
                      const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                      setStartDate(oneMonthAgo.toISOString().split('T')[0]);
                      setEndDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    近1个月
                  </button>
                  <button 
                    className="preset-button" 
                    onClick={() => {
                      const today = new Date();
                      const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
                      setStartDate(threeMonthsAgo.toISOString().split('T')[0]);
                      setEndDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    近3个月
                  </button>
                  <button 
                    className="preset-button" 
                    onClick={() => {
                      const today = new Date();
                      const sixMonthsAgo = new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000);
                      setStartDate(sixMonthsAgo.toISOString().split('T')[0]);
                      setEndDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    近6个月
                  </button>
                  <button 
                    className="preset-button" 
                    onClick={() => {
                      const today = new Date();
                      const oneYearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000);
                      setStartDate(oneYearAgo.toISOString().split('T')[0]);
                      setEndDate(today.toISOString().split('T')[0]);
                    }}
                  >
                    近1年
                  </button>
                  <button 
                    className="preset-button clear-button" 
                    onClick={() => {
                      setStartDate('');
                      setEndDate('');
                    }}
                  >
                    清空
                  </button>
                </div>
                
                <div className="date-inputs">
                  <div className="date-input-group">
                    <label className="date-label">开始日期:</label>
                    <input
                      type="date"
                      className="date-input"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </div>
                  <div className="date-input-group">
                    <label className="date-label">结束日期:</label>
                    <input
                      type="date"
                      className="date-input"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </div>
                </div>
                <small style={{color: '#6c757d', fontSize: '12px'}}>
                  使用快捷按钮或手动选择日期范围，留空将使用默认时间范围
                </small>
              </div>
              
              <div className="calculation-buttons">
                <button className="calculate-button" onClick={calculateFactors}>
                  🧮 计算因子
                </button>
                <button className="chart-button" onClick={getFactorChart}>
                  📈 查看图表
                </button>
              </div>
            </div>
            
            {factorData && (
              <div className="calculation-results">
                <h3>计算结果</h3>
                {factorData.error ? (
                  <div className="error-message">
                    ❌ {factorData.error}
                  </div>
                ) : (
                  <>
                    {factorData.data_info && (
                      <div className="data-info">
                        <p>📊 股票: {factorData.symbol}</p>
                        <p>📈 数据记录: {factorData.data_info.total_records} 条</p>
                        <p>📅 数据范围: {factorData.data_info.date_range?.start} - {factorData.data_info.date_range?.end}</p>
                        {factorData.data_info.was_downloaded && (
                          <p>⬇️ 已自动下载最新数据</p>
                        )}
                      </div>
                    )}
                    <div className="results-grid">
                      {factorData.factors && Object.entries(factorData.factors).map(([factorId, value]) => {
                        const factor = factors.find(f => f.id === factorId);
                        return (
                          <div key={factorId} className="result-item">
                            <span className="result-name">{factor?.name || factorId}</span>
                            <span className="result-value">
                              {typeof value === 'number' ? value.toFixed(4) : String(value)}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </>
                )}
              </div>
            )}

            {showChart && chartData && (
              <div className="chart-container">
                <div className="chart-header">
                  <h3>📈 因子时间序列图表 (归一化)</h3>
                  <button 
                    className="close-chart-button" 
                    onClick={() => setShowChart(false)}
                  >
                    ✕ 关闭图表
                  </button>
                </div>
                
                {chartData.data_info && (
                  <div className="chart-info">
                    <p>📊 股票: {chartData.symbol}</p>
                    <p>📈 数据点: {chartData.data_info.timeseries_points} 个</p>
                    <p>📅 时间范围: {chartData.data_info.date_range?.start} - {chartData.data_info.date_range?.end}</p>
                  </div>
                )}

                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={chartData.chartPoints}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tick={{ fontSize: 12 }}
                        interval="preserveStartEnd"
                      />
                      <YAxis 
                        domain={[0, 1]}
                        tick={{ fontSize: 12 }}
                        label={{ value: '归一化值', angle: -90, position: 'insideLeft' }}
                      />
                      <Tooltip 
                        labelFormatter={(value) => `日期: ${value}`}
                        formatter={(value: any, name: string) => [
                          typeof value === 'number' ? value.toFixed(4) : value,
                          factors.find(f => f.id === name)?.name || name
                        ]}
                      />
                      <Legend 
                        formatter={(value) => factors.find(f => f.id === value)?.name || value}
                      />
                      {Object.keys(chartData.normalized_factors).map((factorId, index) => {
                        const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff', '#ff0000'];
                        return (
                          <Line
                            key={factorId}
                            type="monotone"
                            dataKey={factorId}
                            stroke={colors[index % colors.length]}
                            strokeWidth={2}
                            dot={false}
                            connectNulls={false}
                          />
                        );
                      })}
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                <div className="chart-legend">
                  <h4>因子说明:</h4>
                  <div className="legend-grid">
                    {Object.keys(chartData.normalized_factors).map((factorId) => {
                      const factor = factors.find(f => f.id === factorId);
                      return (
                        <div key={factorId} className="legend-item">
                          <span className="legend-name">{factor?.name || factorId}</span>
                          <span className="legend-desc">{factor?.description || '无描述'}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {factorMode === 'custom' && (
          <div className="custom-factor">
            <div className="ai-factor-section">
              <h3>🤖 AI智能生成因子</h3>
              <div className="ai-form">
                <div className="form-group">
                  <label>描述您想要的因子:</label>
                  <textarea
                    value={aiFactorPrompt}
                    onChange={(e) => setAiFactorPrompt(e.target.value)}
                    placeholder="例如: 我希望生成一个3倍布林带因子、RSI超买因子、MACD金叉信号等"
                    rows={3}
                    className="ai-description-input"
                  />
                </div>
                <button 
                  className="ai-generate-button" 
                  onClick={generateAIFactor}
                  disabled={isGeneratingFactor}
                >
                  {isGeneratingFactor ? '🔄 AI生成中...' : '🤖 AI生成因子'}
                </button>
                
                {aiFactorResult && (
                  <div className="ai-result">
                    <h4>AI生成结果</h4>
                    <div className="ai-factor-preview">
                      <div className="ai-factor-header">
                        <span className="ai-factor-name">{aiFactorResult.factor_name}</span>
                        <span className="ai-confidence">置信度: {(aiFactorResult.confidence * 100).toFixed(0)}%</span>
                      </div>
                      <div className="ai-factor-formula">
                        <strong>公式:</strong> {aiFactorResult.formula}
                      </div>
                      <div className="ai-factor-explanation">
                        <strong>说明:</strong> {aiFactorResult.explanation}
                      </div>
                      <div className="ai-factor-actions">
                        <button className="test-button" onClick={testAIFactor}>
                          🧪 测试因子
                        </button>
                        <button className="confirm-button" onClick={confirmAIFactor}>
                          ✅ 确认创建
                        </button>
                        <button className="reject-button" onClick={() => setAiFactorResult(null)}>
                          ❌ 重新生成
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="manual-factor-section">
              <h3>✏️ 手动创建因子</h3>
              <div className="custom-form">
                <div className="form-group">
                  <label>因子名称:</label>
                  <input
                    type="text"
                    value={customFactorName}
                    onChange={(e) => setCustomFactorName(e.target.value)}
                    placeholder="输入因子名称"
                  />
                </div>
                <div className="form-group">
                  <label>计算公式:</label>
                  <textarea
                    value={customFactorFormula}
                    onChange={(e) => setCustomFactorFormula(e.target.value)}
                    placeholder="输入计算公式，例如: (close - sma(close, 20)) / sma(close, 20)"
                    rows={3}
                  />
                </div>
                <button className="create-button" onClick={createCustomFactor}>
                  ✏️ 创建因子
                </button>
              </div>
            </div>
            
            <div className="custom-factors-list">
              <h3>已创建的自定义因子</h3>
              {factors.filter(f => f.isCustom).length === 0 ? (
                <div className="no-custom-factors">
                  <p>还没有自定义因子，试试用AI生成一个吧！</p>
                </div>
              ) : (
                factors.filter(f => f.isCustom).map(factor => (
                  <div key={factor.id} className="custom-factor-item">
                    <div className="custom-factor-info">
                      <span className="custom-factor-name">{factor.name}</span>
                      <span className="custom-factor-description">{factor.description}</span>
                      <span className="custom-factor-formula">{factor.formula}</span>
                    </div>
                    <button 
                      className="delete-button"
                      onClick={() => setFactors(prev => prev.filter(f => f.id !== factor.id))}
                    >
                      🗑️
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {factorMode === 'analysis' && (
          <div className="factor-analysis">
            <h3>📊 因子分析</h3>
            
            {/* 配置面板 */}
            <div className="analysis-config">
              <div className="config-section">
                <h4>📈 分析股票</h4>
                <div className="stock-input">
                  <div className="stock-search-container">
                    <input
                      type="text"
                      value={analysisSearchInput}
                      onChange={(e) => {
                        setAnalysisSearchInput(e.target.value);
                        searchStocksForAnalysis(e.target.value);
                      }}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const symbol = analysisSearchInput.trim().toUpperCase();
                          if (symbol && !analysisStocks.includes(symbol)) {
                            setAnalysisStocks([...analysisStocks, symbol]);
                            setAnalysisSearchInput('');
                            setShowAnalysisSearchSuggestions(false);
                            setAnalysisSearchSuggestions([]);
                          }
                        }
                      }}
                      placeholder="输入股票代码或名称 (如: AAPL, Apple Inc.)"
                      className="stock-search-input"
                    />
                    
                    {showAnalysisSearchSuggestions && analysisSearchSuggestions.length > 0 && (
                      <div className="search-suggestions">
                        {analysisSearchSuggestions.map((stock, index) => (
                          <div
                            key={index}
                            className="suggestion-item"
                            onClick={() => selectAnalysisStockSuggestion(stock)}
                          >
                            <span className="suggestion-symbol">{stock.symbol}</span>
                            <span className="suggestion-name">{stock.name}</span>
                            <span className="suggestion-market">{stock.market}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <div className="selected-stocks">
                    {analysisStocks.map(stock => (
                      <span key={stock} className="stock-tag">
                        {stock}
                        <button 
                          onClick={() => setAnalysisStocks(analysisStocks.filter(s => s !== stock))}
                          className="remove-btn"
                        >×</button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="config-section">
                <h4>🔍 分析因子</h4>
                <div className="factor-selection">
                  {['rsi', 'macd', 'bollinger_position', 'sma_20', 'sma_50', 'atr', 'stoch_k', 'volume_ratio'].map(factor => (
                    <label key={factor} className="factor-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedFactorsForAnalysis.includes(factor)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFactorsForAnalysis([...selectedFactorsForAnalysis, factor]);
                          } else {
                            setSelectedFactorsForAnalysis(selectedFactorsForAnalysis.filter(f => f !== factor));
                          }
                        }}
                      />
                      {factor.toUpperCase()}
                    </label>
                  ))}
                </div>
              </div>

              <div className="config-section">
                <h4>📅 分析时间范围</h4>
                <select 
                  value={analysisTimeframe} 
                  onChange={(e) => setAnalysisTimeframe(e.target.value as any)}
                  className="timeframe-select"
                >
                  <option value="1m">近1个月</option>
                  <option value="3m">近3个月</option>
                  <option value="6m">近6个月</option>
                  <option value="1y">近1年</option>
                </select>
              </div>
            </div>

            {/* 分析类型选择 */}
            <div className="analysis-type-selector">
              <button 
                className={`analysis-type-btn ${analysisMode === 'correlation' ? 'active' : ''}`}
                onClick={() => setAnalysisMode('correlation')}
              >
                📊 相关性分析
              </button>
              <button 
                className={`analysis-type-btn ${analysisMode === 'returns' ? 'active' : ''}`}
                onClick={() => setAnalysisMode('returns')}
              >
                📈 收益分析
              </button>
              <button 
                className={`analysis-type-btn ${analysisMode === 'validity' ? 'active' : ''}`}
                onClick={() => setAnalysisMode('validity')}
              >
                🎯 有效性测试
              </button>
              <button 
                className={`analysis-type-btn ${analysisMode === 'risk' ? 'active' : ''}`}
                onClick={() => setAnalysisMode('risk')}
              >
                📉 风险分析
              </button>
            </div>

            {/* 股票图表查看 */}
            <div className="config-section">
              <h4>📊 股票图表查看</h4>
              <div className="chart-controls">
                <select 
                  value={selectedStockForChart} 
                  onChange={(e) => setSelectedStockForChart(e.target.value)}
                  className="stock-chart-select"
                >
                  <option value="">选择股票查看图表</option>
                  {analysisStocks.map(stock => (
                    <option key={stock} value={stock}>{stock}</option>
                  ))}
                </select>
                <button 
                  onClick={() => getFactorStockData(selectedStockForChart)}
                  disabled={!selectedStockForChart || selectedFactorsForAnalysis.length === 0}
                  className="view-chart-btn"
                >
                  📈 查看K线+因子图表
                </button>
              </div>
            </div>

            {/* 开始分析按钮 */}
            <div className="analysis-actions">
              <button 
                onClick={performFactorAnalysis}
                disabled={isAnalyzing || analysisStocks.length === 0 || selectedFactorsForAnalysis.length === 0}
                className="start-analysis-btn"
              >
                {isAnalyzing ? '🔄 分析中...' : '🚀 开始分析'}
              </button>
            </div>

            {/* 股票图表展示 */}
            {showFactorChart && factorStockData && (
              <div className="factor-chart-section">
                <div className="chart-header">
                  <h3>📊 {selectedStockForChart} - K线图与因子分析</h3>
                  <button 
                    className="close-chart-button" 
                    onClick={() => setShowFactorChart(false)}
                  >
                    ✕ 关闭图表
                  </button>
                </div>
                
                <div className="chart-info">
                  <p>📈 股票: {factorStockData.basic_info.name} ({factorStockData.basic_info.symbol})</p>
                  <p>📊 数据点: {factorStockData.kline_data.length} 个</p>
                  <p>📅 时间范围: {factorStockData.statistics.date_range.start} 至 {factorStockData.statistics.date_range.end}</p>
                  <p>💹 最新价格: ${factorStockData.statistics.latest_price.toFixed(2)}</p>
                </div>

                {/* K线图 */}
                <div className="kline-chart-container">
                  <h4>📈 K线图表</h4>
                  <ReactECharts
                    option={getFactorKlineChartOption()}
                    style={{ height: '500px', width: '100%' }}
                    notMerge={true}
                    lazyUpdate={true}
                  />
                </div>

                {/* 因子曲线图 */}
                {factorChartData && (
                  <div className="factor-curve-container">
                    <h4>📊 因子趋势图</h4>
                    <ReactECharts
                      option={getFactorCurveChartOption()}
                      style={{ height: '400px', width: '100%' }}
                      notMerge={true}
                      lazyUpdate={true}
                    />
                    <div className="factor-legend">
                      <h5>因子说明:</h5>
                      <div className="legend-grid">
                        {selectedFactorsForAnalysis.map((factorId) => {
                          const factorNames: any = {
                            'rsi': 'RSI相对强弱指标',
                            'macd': 'MACD指标',
                            'bollinger_position': '布林带位置',
                            'sma_20': '20日移动平均线',
                            'sma_50': '50日移动平均线',
                            'atr': '平均真实波动率',
                            'stoch_k': '随机指标K值',
                            'volume_ratio': '成交量比率'
                          };
                          return (
                            <div key={factorId} className="legend-item">
                              <span className="legend-name">{factorNames[factorId] || factorId.toUpperCase()}</span>
                              <span className="legend-desc">技术分析指标</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 分析结果展示 */}
            {analysisResults && (
              <div className="analysis-results">
                <h4>📋 分析结果</h4>

                {/* 相关性分析结果 */}
                {analysisMode === 'correlation' && correlationMatrix && (
                  <div className="correlation-results">
                    <h5>📊 因子相关性矩阵</h5>
                    <div className="correlation-matrix">
                      <table className="correlation-table">
                        <thead>
                          <tr>
                            <th>因子</th>
                            {selectedFactorsForAnalysis.map(factor => (
                              <th key={factor}>{factor.toUpperCase()}</th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {selectedFactorsForAnalysis.map(factor1 => (
                            <tr key={factor1}>
                              <td className="factor-name">{factor1.toUpperCase()}</td>
                              {selectedFactorsForAnalysis.map(factor2 => (
                                <td key={factor2} className={`correlation-cell ${
                                  Math.abs(correlationMatrix[factor1]?.[factor2] || 0) > 0.7 ? 'high-correlation' :
                                  Math.abs(correlationMatrix[factor1]?.[factor2] || 0) > 0.3 ? 'medium-correlation' :
                                  'low-correlation'
                                }`}>
                                  {(correlationMatrix[factor1]?.[factor2] || 0).toFixed(3)}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    <div className="correlation-legend">
                                             <span className="legend-item high-correlation">强相关 (|r| {'>'} 0.7)</span>
                       <span className="legend-item medium-correlation">中等相关 (0.3 {'<'} |r| ≤ 0.7)</span>
                       <span className="legend-item low-correlation">弱相关 (|r| ≤ 0.3)</span>
                    </div>
                  </div>
                )}

                {/* 收益分析结果 */}
                {analysisMode === 'returns' && returnsData && (
                  <div className="returns-results">
                    <h5>📈 因子收益分析</h5>
                    <div className="returns-table">
                      <table>
                        <thead>
                          <tr>
                            <th>因子</th>
                            <th>平均收益</th>
                            <th>波动率</th>
                            <th>夏普比率</th>
                            <th>最大值</th>
                            <th>最小值</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(returnsData).map(([factor, data]: [string, any]) => (
                            <tr key={factor}>
                              <td className="factor-name">{factor.toUpperCase()}</td>
                              <td className={data.averageReturn > 0 ? 'positive' : 'negative'}>
                                {data.averageReturn.toFixed(4)}
                              </td>
                              <td>{data.volatility.toFixed(4)}</td>
                              <td className={data.sharpeRatio > 0 ? 'positive' : 'negative'}>
                                {data.sharpeRatio.toFixed(4)}
                              </td>
                              <td>{data.maxValue.toFixed(4)}</td>
                              <td>{data.minValue.toFixed(4)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* 有效性测试结果 */}
                {analysisMode === 'validity' && validityMetrics && (
                  <div className="validity-results">
                    <h5>🎯 因子有效性测试</h5>
                    <div className="validity-cards">
                      {Object.entries(validityMetrics).map(([factor, data]: [string, any]) => (
                        <div key={factor} className="validity-card">
                          <h6>{factor.toUpperCase()}</h6>
                          <div className="validity-score">
                            <div className={`score-circle ${
                              data.validityScore >= 80 ? 'excellent' :
                              data.validityScore >= 60 ? 'good' :
                              data.validityScore >= 40 ? 'fair' : 'poor'
                            }`}>
                              {data.validityScore.toFixed(0)}
                            </div>
                            <span>有效性得分</span>
                          </div>
                          <div className="validity-metrics">
                            <div className="metric">
                              <span>均值:</span>
                              <span>{data.mean.toFixed(4)}</span>
                            </div>
                            <div className="metric">
                              <span>标准差:</span>
                              <span>{data.standardDeviation.toFixed(4)}</span>
                            </div>
                            <div className="metric">
                              <span>变异系数:</span>
                              <span>{data.coefficientOfVariation.toFixed(4)}</span>
                            </div>
                            <div className="metric">
                              <span>信息比率:</span>
                              <span>{data.informationRatio.toFixed(4)}</span>
                            </div>
                            <div className="metric">
                              <span>样本数:</span>
                              <span>{data.sampleSize}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 风险分析结果 */}
                {analysisMode === 'risk' && riskMetrics && (
                  <div className="risk-results">
                    <h5>📉 因子风险分析</h5>
                    <div className="risk-table">
                      <table>
                        <thead>
                          <tr>
                            <th>因子</th>
                            <th>标准差</th>
                            <th>VaR (95%)</th>
                            <th>最大回撤 (%)</th>
                            <th>风险等级</th>
                            <th>风险评分</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(riskMetrics).map(([factor, data]: [string, any]) => (
                            <tr key={factor}>
                              <td className="factor-name">{factor.toUpperCase()}</td>
                              <td>{data.standardDeviation.toFixed(4)}</td>
                              <td>{data.var95.toFixed(4)}</td>
                              <td className={data.maxDrawdown > 20 ? 'high-risk' : 'normal'}>
                                {data.maxDrawdown.toFixed(2)}%
                              </td>
                              <td>
                                <span className={`risk-level ${data.riskLevel.toLowerCase()}`}>
                                  {data.riskLevel === 'High' ? '高风险' : 
                                   data.riskLevel === 'Medium' ? '中风险' : '低风险'}
                                </span>
                              </td>
                              <td>
                                <div className="risk-score-bar">
                                  <div 
                                    className="risk-score-fill"
                                    style={{width: `${Math.min(100, data.riskScore)}%`}}
                                  ></div>
                                  <span>{data.riskScore.toFixed(0)}</span>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* 原始因子数据 */}
                <div className="raw-data-section">
                  <h5>📊 原始因子数据</h5>
                  <div className="raw-data-table">
                    <table>
                      <thead>
                        <tr>
                          <th>股票</th>
                          {selectedFactorsForAnalysis.map(factor => (
                            <th key={factor}>{factor.toUpperCase()}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(analysisResults).map(([symbol, factors]: [string, any]) => (
                          <tr key={symbol}>
                            <td className="stock-symbol">{symbol}</td>
                            {selectedFactorsForAnalysis.map(factor => (
                              <td key={factor}>
                                {factors[factor] !== undefined ? factors[factor].toFixed(4) : 'N/A'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // 机器学习模块功能
  const loadAvailableModels = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8000/ml/models');
      const data = await response.json();
      setModels(data.models || []);
    } catch (error) {
      console.error('加载模型列表错误:', error);
    }
  };

  const trainModel = async () => {
    setIsTraining(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/ml/train', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          symbols: trainingStocks,
          model_id: selectedModelId,
          test_size: 0.2
        })
      });
      const result = await response.json();
      setTrainingResult(result);
    } catch (error) {
      console.error('模型训练错误:', error);
    } finally {
      setIsTraining(false);
    }
  };

  const predictStocks = async () => {
    setIsPredicting(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/ml/predict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          symbols: predictionStocks,
          model_id: selectedModelId
        })
      });
      const result = await response.json();
      setPredictionResult(result);
    } catch (error) {
      console.error('股票预测错误:', error);
    } finally {
      setIsPredicting(false);
    }
  };

  // 评分系统功能
  const calculateScore = async (scoreType: string, symbol: string = selectedStock) => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/scoring/${scoreType}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol })
      });
      const result = await response.json();
      setScoreResults(result);
    } catch (error) {
      console.error('评分计算错误:', error);
    }
  };

  const rankStocks = async () => {
    setIsRanking(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/scoring/ranking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          symbols: rankingSymbols,
          max_stocks: 20,
          factor_weight: factorWeight,
          ml_weight: mlWeight
        })
      });
      const result = await response.json();
      setRankingResults(result);
    } catch (error) {
      console.error('股票排名错误:', error);
    } finally {
      setIsRanking(false);
    }
  };

  // MACD背离检测功能
  const scanMarketDivergence = async () => {
    setIsScanning(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/divergence/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          market: selectedMarket,
          tushare_token: tushareToken,
          divergence_types: divergenceTypes
        })
      });
      const result = await response.json();
      setScanResults(result.data);
    } catch (error) {
      console.error('扫描背离错误:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const loadRecentDivergences = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/divergence/recent/${selectedMarket}?hours=24`);
      const result = await response.json();
      setRecentDivergences(result.divergences || []);
    } catch (error) {
      console.error('加载最近背离错误:', error);
    }
  };

  const loadScanHistory = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/divergence/history/${selectedMarket}?limit=10`);
      const result = await response.json();
      setScanHistory(result.history || []);
    } catch (error) {
      console.error('加载扫描历史错误:', error);
    }
  };

  const getMarketName = (market: string) => {
    const names = { 'US': '美股', 'HK': '港股', 'CN': 'A股' };
    return names[market as keyof typeof names] || market;
  };

  const getDivergenceTypeName = (type: string) => {
    return type === 'bullish' ? '底背离' : '顶背离';
  };

  const getDivergenceColor = (type: string) => {
    return type === 'bullish' ? '#4CAF50' : '#F44336';
  };

  // 数据查询相关函数
  const searchStocks = async (query: string) => {
    if (!query.trim()) {
      setSearchSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await fetch(`http://localhost:8000/stocks/search/${encodeURIComponent(query)}`);
      const data = await response.json();
      
      if (data.success) {
        setSearchSuggestions(data.suggestions);
        setShowSuggestions(true);
      }
    } catch (error) {
      console.error('搜索股票失败:', error);
    }
  };

  // 因子分析页面专用的股票搜索函数
  const searchStocksForAnalysis = async (query: string) => {
    if (!query.trim()) {
      setAnalysisSearchSuggestions([]);
      setShowAnalysisSearchSuggestions(false);
      return;
    }

    try {
      const response = await fetch(`http://localhost:8000/stocks/search/${encodeURIComponent(query)}`);
      const data = await response.json();
      
      if (data.success) {
        setAnalysisSearchSuggestions(data.suggestions);
        setShowAnalysisSearchSuggestions(true);
      }
    } catch (error) {
      console.error('搜索股票失败:', error);
    }
  };

  // 获取因子分析页面的股票数据
  const getFactorStockData = async (symbol: string) => {
    if (!symbol.trim()) {
      alert('请选择股票');
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/stocks/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: symbol,
          tushare_token: 'd255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c',
          period: analysisTimeframe,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setFactorStockData(data);
        setSelectedStockForChart(symbol);
        
        // 同时获取因子数据
        await getFactorDataForChart(symbol);
      } else {
        alert('获取股票数据失败: ' + (data.detail || '未知错误'));
      }
    } catch (error: any) {
      console.error('获取股票数据失败:', error);
      alert('获取股票数据失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 获取因子数据用于图表显示
  const getFactorDataForChart = async (symbol: string) => {
    if (!symbol || selectedFactorsForAnalysis.length === 0) {
      alert('请选择股票和因子');
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/factors/calculate/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbols: [symbol],
          factors: selectedFactorsForAnalysis,
          timeframe: analysisTimeframe,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setFactorChartData(data);
        setShowFactorChart(true);
      } else {
        alert('获取因子数据失败: ' + (data.detail || '未知错误'));
      }
    } catch (error: any) {
      console.error('获取因子数据失败:', error);
      alert('获取因子数据失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  // 因子分析页面选择股票建议
  const selectAnalysisStockSuggestion = (stock: any) => {
    const symbol = stock.symbol.trim().toUpperCase();
    if (symbol && !analysisStocks.includes(symbol)) {
      setAnalysisStocks([...analysisStocks, symbol]);
    }
    setAnalysisSearchInput('');
    setShowAnalysisSearchSuggestions(false);
    setAnalysisSearchSuggestions([]);
  };

  const queryStockData = useCallback(async (silent: boolean = false) => {
    if (!querySymbol.trim() || !tushareToken.trim()) {
      if (!silent) alert('请输入股票代码和Tushare Token');
      return;
    }

    if (!silent) setIsQuerying(true);
    try {
      const response = await fetch('http://localhost:8000/stocks/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: querySymbol,
          tushare_token: tushareToken,
          period: queryPeriod,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setStockData(data);
        setShowChart(true);
        setLastRefreshTime(new Date().toLocaleTimeString());
      } else {
        if (!silent) alert('查询失败: ' + (data.detail || '未知错误'));
      }
    } catch (error: any) {
      console.error('查询股票数据失败:', error);
      if (!silent) alert('查询失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      if (!silent) setIsQuerying(false);
    }
  }, [querySymbol, tushareToken, queryPeriod]);

  const getTechnicalIndicators = useCallback(async () => {
    if (!querySymbol.trim() || !tushareToken.trim()) {
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/stocks/technical-indicators', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: querySymbol,
          tushare_token: tushareToken,
          indicators: selectedIndicators,
          period: queryPeriod,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setTechnicalData(data);
      }
    } catch (error) {
      console.error('获取技术指标失败:', error);
    }
  }, [querySymbol, tushareToken, selectedIndicators, queryPeriod]);

  const selectStockSuggestion = (stock: any) => {
    setQuerySymbol(stock.symbol);
    setShowSuggestions(false);
    setSearchSuggestions([]);
  };

  const toggleIndicator = (indicator: string) => {
    setSelectedIndicators(prev => 
      prev.includes(indicator) 
        ? prev.filter(ind => ind !== indicator)
        : [...prev, indicator]
    );
  };

  // 导出数据功能
  const exportData = (format: 'csv' | 'json') => {
    if (!stockData) return;

    const data = stockData.kline_data;
    const filename = `${stockData.basic_info.symbol}_${queryPeriod}_data`;

    if (format === 'csv') {
      // 生成CSV格式
      const headers = ['日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额', '涨跌幅'];
      const csvContent = [
        headers.join(','),
        ...data.map(row => [
          row.date,
          row.open,
          row.high,
          row.low,
          row.close,
          row.volume,
          row.amount,
          row.pct_change
        ].join(','))
      ].join('\n');

      downloadFile(csvContent, `${filename}.csv`, 'text/csv');
    } else if (format === 'json') {
      // 生成JSON格式
      const jsonContent = JSON.stringify({
        symbol: stockData.basic_info.symbol,
        name: stockData.basic_info.name,
        market: stockData.basic_info.market,
        period: queryPeriod,
        export_time: new Date().toISOString(),
        statistics: stockData.statistics,
        data: data
      }, null, 2);

      downloadFile(jsonContent, `${filename}.json`, 'application/json');
    }
  };

  // 下载文件辅助函数
  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // 因子分析相关函数
  const performFactorAnalysis = async () => {
    if (analysisStocks.length === 0 || selectedFactorsForAnalysis.length === 0) {
      alert('请选择股票和因子');
      return;
    }

    setIsAnalyzing(true);
    try {
      // 计算时间范围
      const endDate = new Date();
      const startDate = new Date();
      
      switch (analysisTimeframe) {
        case '1m':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3m':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6m':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // 转换为后端期望的日期格式 (YYYYMMDD)
      const startDateStr = startDate.toISOString().split('T')[0].replace(/-/g, '');
      const endDateStr = endDate.toISOString().split('T')[0].replace(/-/g, '');

      // 批量获取因子数据
      const response = await fetch('http://localhost:8000/factors/calculate/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbols: analysisStocks,
          factor_names: selectedFactorsForAnalysis,
          start_date: startDateStr,
          end_date: endDateStr
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.success) {
          setFactorAnalysisResults(data.results);
          
          // 根据分析模式计算不同的指标
          try {
            switch (analysisMode) {
              case 'correlation':
                await calculateCorrelationMatrix(data.results);
                break;
              case 'returns':
                await calculateReturnsAnalysis(data.results);
                break;
              case 'validity':
                await calculateValidityMetrics(data.results);
                break;
              case 'risk':
                await calculateRiskMetrics(data.results);
                break;
            }
          } catch (calcError) {
            console.error('计算分析指标时出错:', calcError);
            alert('分析计算失败: ' + calcError);
            return;
          }
        } else {
          alert('分析失败: ' + (data.message || '未知错误'));
        }
      } else {
        throw new Error('网络请求失败');
      }
    } catch (error) {
      console.error('因子分析错误:', error);
      alert('分析过程中出现错误: ' + error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const calculateCorrelationMatrix = async (factorData: any) => {
    try {
      // 计算因子相关性矩阵
      console.log('开始计算相关性矩阵, factorData:', factorData);
      
      const stockSymbols = Object.keys(factorData);
      const factorNames = selectedFactorsForAnalysis;
      
      console.log('股票符号:', stockSymbols);
      console.log('因子名称:', factorNames);
      
      if (stockSymbols.length === 0) {
        throw new Error('没有股票数据');
      }
      
      if (factorNames.length === 0) {
        throw new Error('没有选中的因子');
      }
      
      const matrix: any = {};
      factorNames.forEach(factor1 => {
        matrix[factor1] = {};
        factorNames.forEach(factor2 => {
          const values1: number[] = [];
          const values2: number[] = [];
          
          stockSymbols.forEach(symbol => {
            if (factorData[symbol] && factorData[symbol][factor1] !== undefined && factorData[symbol][factor2] !== undefined) {
              values1.push(factorData[symbol][factor1]);
              values2.push(factorData[symbol][factor2]);
            }
          });
          
          // 计算皮尔逊相关系数
          const correlation = calculatePearsonCorrelation(values1, values2);
          matrix[factor1][factor2] = correlation;
        });
      });
      
      console.log('相关性矩阵计算完成:', matrix);
      setCorrelationMatrix(matrix);
    } catch (error) {
      console.error('相关性矩阵计算错误:', error);
      throw new Error('相关性矩阵计算失败: ' + error);
    }
  };

  const calculatePearsonCorrelation = (x: number[], y: number[]): number => {
    if (x.length !== y.length || x.length === 0) return 0;
    
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((acc, xi, i) => acc + xi * y[i], 0);
    const sumX2 = x.reduce((acc, xi) => acc + xi * xi, 0);
    const sumY2 = y.reduce((acc, yi) => acc + yi * yi, 0);
    
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  };

  const calculateReturnsAnalysis = async (factorData: any) => {
    try {
      // 模拟计算因子收益分析
      console.log('开始计算收益分析, factorData:', factorData);
      
      const stockSymbols = Object.keys(factorData);
      const returns: any = {};
      
      console.log('股票符号:', stockSymbols);
      console.log('因子名称:', selectedFactorsForAnalysis);
      
      if (stockSymbols.length === 0) {
        throw new Error('没有股票数据');
      }
      
      selectedFactorsForAnalysis.forEach(factor => {
        console.log(`处理因子: ${factor}`);
        
        const values = stockSymbols.map(symbol => {
          const value = factorData[symbol]?.[factor];
          if (value === undefined || value === null || isNaN(value)) {
            console.warn(`股票 ${symbol} 的因子 ${factor} 值无效:`, value);
            return 0;
          }
          return value;
        });
        
        console.log(`因子 ${factor} 的值:`, values);
        
        if (values.length === 0) {
          throw new Error(`因子 ${factor} 没有有效数据`);
        }
        
        const avgReturn = values.reduce((a, b) => a + b, 0) / values.length;
        const variance = values.reduce((acc, val) => acc + Math.pow(val - avgReturn, 2), 0) / values.length;
        const volatility = Math.sqrt(variance);
        const sharpeRatio = volatility !== 0 ? avgReturn / volatility : 0;
        
        const maxValue = Math.max(...values);
        const minValue = Math.min(...values);
        
        returns[factor] = {
          averageReturn: avgReturn,
          volatility: volatility,
          sharpeRatio: sharpeRatio,
          maxValue: maxValue,
          minValue: minValue
        };
        
        console.log(`因子 ${factor} 计算结果:`, returns[factor]);
      });
      
      console.log('收益分析计算完成:', returns);
      setReturnsData(returns);
    } catch (error) {
      console.error('收益分析计算错误:', error);
      throw new Error('收益分析计算失败: ' + error);
    }
  };

  const calculateValidityMetrics = async (factorData: any) => {
    // 计算因子有效性指标
    const stockSymbols = Object.keys(factorData);
    const validity: any = {};
    
    selectedFactorsForAnalysis.forEach(factor => {
      const values = stockSymbols.map(symbol => factorData[symbol]?.[factor] || 0).filter(v => !isNaN(v));
      
      if (values.length > 0) {
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
        const standardDeviation = Math.sqrt(variance);
        const coefficientOfVariation = mean !== 0 ? standardDeviation / Math.abs(mean) : 0;
        
        // 计算信息比率（简化版）
        const informationRatio = standardDeviation !== 0 ? mean / standardDeviation : 0;
        
        // 计算有效性得分（综合指标）
        const validityScore = Math.min(100, Math.max(0, (Math.abs(informationRatio) * 20 + (1 - coefficientOfVariation) * 30 + 50)));
        
        validity[factor] = {
          mean: mean,
          standardDeviation: standardDeviation,
          coefficientOfVariation: coefficientOfVariation,
          informationRatio: informationRatio,
          validityScore: validityScore,
          sampleSize: values.length
        };
      }
    });
    
    setValidityMetrics(validity);
  };

  const calculateRiskMetrics = async (factorData: any) => {
    // 计算因子风险指标
    const stockSymbols = Object.keys(factorData);
    const risk: any = {};
    
    selectedFactorsForAnalysis.forEach(factor => {
      const values = stockSymbols.map(symbol => factorData[symbol]?.[factor] || 0);
      const sortedValues = [...values].sort((a, b) => a - b);
      
      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
      const standardDeviation = Math.sqrt(variance);
      
      // 计算VaR (Value at Risk) - 95%置信水平
      const var95Index = Math.floor(0.05 * sortedValues.length);
      const var95 = sortedValues[var95Index];
      
      // 计算最大回撤
      let maxDrawdown = 0;
      let peak = values[0];
      values.forEach(value => {
        if (value > peak) peak = value;
        const drawdown = (peak - value) / peak;
        if (drawdown > maxDrawdown) maxDrawdown = drawdown;
      });
      
      // 风险等级评估
      const riskLevel = standardDeviation > mean * 0.5 ? 'High' : 
                       standardDeviation > mean * 0.2 ? 'Medium' : 'Low';
      
      risk[factor] = {
        standardDeviation: standardDeviation,
        var95: var95,
        maxDrawdown: maxDrawdown * 100, // 转换为百分比
        riskLevel: riskLevel,
        riskScore: Math.min(100, standardDeviation * 50) // 风险评分
      };
    });
    
    setRiskMetrics(risk);
  };

  // 生成K线图配置
  const getKlineChartOption = () => {
    if (!stockData || !stockData.kline_data) return {};

    const klineData = stockData.kline_data.map(item => [
      item.open,
      item.close,
      item.low,
      item.high,
      item.volume
    ]);

    const dates = stockData.kline_data.map(item => item.date);

    return {
      animation: false,
      legend: {
        bottom: 10,
        left: 'center',
        data: ['K线', '成交量']
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#000'
        },
        formatter: function (params: any) {
          const data = params[0];
          const kline = stockData.kline_data[data.dataIndex];
          return `
            <div style="margin: 0px 0 0px 0;line-height:1;">
              <div style="font-size:14px;color:#666;font-weight:400;line-height:1;">${data.axisValueLabel}</div>
              <div style="margin: 10px 0 0 0;line-height:1;">
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#ec0000;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">开盘</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.open.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#00da3c;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">收盘</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.close.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#fac858;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">最高</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.high.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#91cc75;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">最低</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.low.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#ee6666;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">成交量</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${(kline.volume / 1000000).toFixed(2)}M</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#73c0de;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">涨跌幅</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.pct_change.toFixed(2)}%</span>
                  <div style="clear:both"></div>
                </div>
              </div>
            </div>
          `;
        }
      },
      axisPointer: {
        link: {
          xAxisIndex: 'all'
        },
        label: {
          backgroundColor: '#777'
        }
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: false
          },
          brush: {
            type: ['lineX', 'clear']
          }
        }
      },
      brush: {
        xAxisIndex: 'all',
        brushLink: 'all',
        outOfBrush: {
          colorAlpha: 0.1
        }
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          height: '60%'
        },
        {
          left: '10%',
          right: '8%',
          top: '75%',
          height: '16%'
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax',
          axisPointer: {
            z: 100
          }
        },
        {
          type: 'category',
          gridIndex: 1,
          data: dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 90,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '85%',
          start: 90,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: klineData,
          itemStyle: {
            color: '#ec0000',
            color0: '#00da3c',
            borderColor: '#8A0000',
            borderColor0: '#008F28'
          },
          markPoint: {
            label: {
              formatter: function (param: any) {
                return param != null ? Math.round(param.value) + '' : '';
              }
            },
            data: [
              {
                name: 'Mark',
                coord: ['2013/5/31', 2300],
                value: 2300,
                itemStyle: {
                  color: 'rgb(41,60,85)'
                }
              },
              {
                name: 'highest value',
                type: 'max',
                valueDim: 'highest'
              },
              {
                name: 'lowest value',
                type: 'min',
                valueDim: 'lowest'
              },
              {
                name: 'average value on close',
                type: 'average',
                valueDim: 'close'
              }
            ],
            tooltip: {
              formatter: function (param: any) {
                return param.name + '<br>' + (param.data.coord || '');
              }
            }
          },
          markLine: {
            symbol: ['none', 'none'],
            data: [
              [
                {
                  name: 'from lowest to highest',
                  type: 'min',
                  valueDim: 'lowest',
                  symbol: 'circle',
                  symbolSize: 10,
                  label: {
                    show: false
                  },
                  emphasis: {
                    label: {
                      show: false
                    }
                  }
                },
                {
                  type: 'max',
                  valueDim: 'highest',
                  symbol: 'circle',
                  symbolSize: 10,
                  label: {
                    show: false
                  },
                  emphasis: {
                    label: {
                      show: false
                    }
                  }
                }
              ],
              {
                name: 'min line on close',
                type: 'min',
                valueDim: 'close'
              },
              {
                name: 'max line on close',
                type: 'max',
                valueDim: 'close'
              }
            ]
          }
        },
        {
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: stockData.kline_data.map(item => item.volume)
        }
      ]
    };
  };

  // 生成技术指标图表配置
  const getTechnicalIndicatorOption = (indicator: string, data: any) => {
    if (!technicalData || !technicalData.dates) return {};

    const dates = technicalData.dates;
    const values = technicalData.indicators[indicator];

    let series: any[] = [];
    let title = indicator.toUpperCase();

    if (indicator === 'bollinger' && values && values.length > 0) {
      // 布林带特殊处理
      series = [
        {
          name: '上轨',
          type: 'line',
          data: values.map((v: any) => v.upper),
          lineStyle: { color: '#FF6B6B' }
        },
        {
          name: '中轨',
          type: 'line',
          data: values.map((v: any) => v.middle),
          lineStyle: { color: '#4ECDC4' }
        },
        {
          name: '下轨',
          type: 'line',
          data: values.map((v: any) => v.lower),
          lineStyle: { color: '#45B7D1' }
        }
      ];
    } else if (Array.isArray(values)) {
      series = [{
        name: title,
        type: 'line',
        data: values,
        lineStyle: { color: '#667eea' }
      }];
    }

    return {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        bottom: 5,
        left: 'center'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false
      },
      yAxis: {
        type: 'value'
      },
      series: series
    };
  };

  // 生成因子分析的K线图配置
  const getFactorKlineChartOption = () => {
    if (!factorStockData || !factorStockData.kline_data) return {};

    const klineData = factorStockData.kline_data.map((item: any) => [
      item.open,
      item.close,
      item.low,
      item.high,
      item.volume
    ]);

    const dates = factorStockData.kline_data.map((item: any) => item.date);

    return {
      animation: false,
      legend: {
        bottom: 10,
        left: 'center',
        data: ['K线', '成交量']
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#000'
        },
        formatter: function (params: any) {
          const data = params[0];
          const kline = factorStockData.kline_data[data.dataIndex];
          return `
            <div style="margin: 0px 0 0px 0;line-height:1;">
              <div style="font-size:14px;color:#666;font-weight:400;line-height:1;">${data.axisValueLabel}</div>
              <div style="margin: 10px 0 0 0;line-height:1;">
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#ec0000;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">开盘</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.open.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#00da3c;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">收盘</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.close.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#fac858;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">最高</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.high.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#91cc75;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">最低</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.low.toFixed(2)}</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#ee6666;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">成交量</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${(kline.volume / 1000000).toFixed(2)}M</span>
                  <div style="clear:both"></div>
                </div>
                <div style="margin: 0px 0 2px 0;line-height:1;">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#73c0de;"></span>
                  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">涨跌幅</span>
                  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${kline.pct_change.toFixed(2)}%</span>
                  <div style="clear:both"></div>
                </div>
              </div>
            </div>
          `;
        }
      },
      axisPointer: {
        link: {
          xAxisIndex: 'all'
        },
        label: {
          backgroundColor: '#777'
        }
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: false
          },
          brush: {
            type: ['lineX', 'clear']
          }
        }
      },
      brush: {
        xAxisIndex: 'all',
        brushLink: 'all',
        outOfBrush: {
          colorAlpha: 0.1
        }
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          height: '60%'
        },
        {
          left: '10%',
          right: '8%',
          top: '75%',
          height: '16%'
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax',
          axisPointer: {
            z: 100
          }
        },
        {
          type: 'category',
          gridIndex: 1,
          data: dates,
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 90,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '85%',
          start: 90,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: klineData,
          itemStyle: {
            color: '#ec0000',
            color0: '#00da3c',
            borderColor: '#8A0000',
            borderColor0: '#008F28'
          }
        },
        {
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: factorStockData.kline_data.map((item: any) => item.volume)
        }
      ]
    };
  };

  // 生成因子曲线图表配置
  const getFactorCurveChartOption = () => {
    if (!factorChartData || !factorChartData.factors) return {};

    const stockSymbol = selectedStockForChart;
    const stockData = factorChartData.factors[stockSymbol];
    
    if (!stockData) return {};

    // 获取时间序列数据
    const dates = stockData.dates || [];
    const factors = stockData.factors || {};

    const series = selectedFactorsForAnalysis.map((factorId, index) => {
      const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff', '#ff0000'];
      const factorData = factors[factorId] || [];
      
      return {
        name: factorId.toUpperCase(),
        type: 'line',
        data: factorData,
        stroke: colors[index % colors.length],
        strokeWidth: 2,
        lineStyle: {
          color: colors[index % colors.length],
          width: 2
        },
        symbol: 'none',
        smooth: true
      };
    });

    return {
      title: {
        text: `${stockSymbol} 因子曲线`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function (params: any) {
          let result = `<div style="font-weight:bold;">${params[0].axisValueLabel}</div>`;
          params.forEach((param: any) => {
            result += `<div style="margin:5px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value ? param.value.toFixed(4) : 'N/A'}
            </div>`;
          });
          return result;
        }
      },
      legend: {
        bottom: 10,
        left: 'center',
        data: selectedFactorsForAnalysis.map(f => f.toUpperCase())
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#666'
          }
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisLine: {
          lineStyle: {
            color: '#666'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        }
      },
      series: series
    };
  };

  // 机器学习模块界面
  const renderMLModels = () => (
    <div className="factor-container">
      <header className="factor-header">
        <button className="back-button" onClick={() => setMode('home')}>
          ← 返回首页
        </button>
        <div className="header-content">
          <h1>🧠 机器学习模型</h1>
          <p>多种算法的训练、预测和评估</p>
        </div>
      </header>

      <div className="factor-nav">
        <button 
          className={`nav-button ${mlMode === 'models' ? 'active' : ''}`}
          onClick={() => setMLMode('models')}
        >
          🔧 模型管理
        </button>
        <button 
          className={`nav-button ${mlMode === 'training' ? 'active' : ''}`}
          onClick={() => setMLMode('training')}
        >
          🏋️ 模型训练
        </button>
        <button 
          className={`nav-button ${mlMode === 'prediction' ? 'active' : ''}`}
          onClick={() => setMLMode('prediction')}
        >
          🔮 股票预测
        </button>
        <button 
          className={`nav-button ${mlMode === 'comparison' ? 'active' : ''}`}
          onClick={() => setMLMode('comparison')}
        >
          📊 模型比较
        </button>
      </div>

      <div className="factor-content">
        {mlMode === 'models' && (
          <div className="ml-models">
            <div className="models-header">
              <h3>可用模型</h3>
              <button className="load-button" onClick={loadAvailableModels}>
                🔄 刷新列表
              </button>
            </div>
            
            <div className="models-list">
              {models.map(model => (
                <div key={model.id} className="model-item">
                  <div className="model-info">
                    <span className="model-name">{model.name}</span>
                    <span className="model-id">({model.id})</span>
                  </div>
                  <span className={`model-status ${model.available ? 'available' : 'unavailable'}`}>
                    {model.available ? '✅ 可用' : '❌ 不可用'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {mlMode === 'training' && (
          <div className="ml-training">
            <div className="training-controls">
              <div className="control-group">
                <label>选择模型:</label>
                <select value={selectedModelId} onChange={(e) => setSelectedModelId(e.target.value)}>
                  {models.map(model => (
                    <option key={model.id} value={model.id}>{model.name}</option>
                  ))}
                </select>
              </div>
              
              <div className="control-group">
                <label>训练股票 (用逗号分隔):</label>
                <input
                  type="text"
                  value={trainingStocks.join(', ')}
                  onChange={(e) => setTrainingStocks(e.target.value.split(',').map(s => s.trim()))}
                  placeholder="AAPL, MSFT, GOOGL"
                />
              </div>
              
              <button 
                className="train-button" 
                onClick={trainModel}
                disabled={isTraining}
              >
                {isTraining ? '🔄 训练中...' : '🏋️ 开始训练'}
              </button>
            </div>

            {trainingResult && trainingResult.model_name && (
              <div className="training-results">
                <h3>训练结果</h3>
                <div className="results-summary">
                  <div className="result-item">
                    <span>模型:</span>
                    <span>{trainingResult.model_name}</span>
                  </div>
                  <div className="result-item">
                    <span>训练样本:</span>
                    <span>{trainingResult.training_samples}</span>
                  </div>
                  <div className="result-item">
                    <span>测试样本:</span>
                    <span>{trainingResult.test_samples}</span>
                  </div>
                  <div className="result-item">
                    <span>测试R²:</span>
                    <span>{trainingResult.test_metrics?.r2?.toFixed(4)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {mlMode === 'prediction' && (
          <div className="ml-prediction">
            <div className="prediction-controls">
              <div className="control-group">
                <label>选择模型:</label>
                <select value={selectedModelId} onChange={(e) => setSelectedModelId(e.target.value)}>
                  {models.map(model => (
                    <option key={model.id} value={model.id}>{model.name}</option>
                  ))}
                </select>
              </div>
              
              <div className="control-group">
                <label>预测股票 (用逗号分隔):</label>
                <input
                  type="text"
                  value={predictionStocks.join(', ')}
                  onChange={(e) => setPredictionStocks(e.target.value.split(',').map(s => s.trim()))}
                  placeholder="AAPL, MSFT"
                />
              </div>
              
              <button 
                className="predict-button" 
                onClick={predictStocks}
                disabled={isPredicting}
              >
                {isPredicting ? '🔄 预测中...' : '🔮 开始预测'}
              </button>
            </div>

            {predictionResult && predictionResult.predictions && (
              <div className="prediction-results">
                <h3>预测结果</h3>
                <div className="predictions-list">
                  {Object.entries(predictionResult.predictions).map(([symbol, result]: [string, any]) => (
                    <div key={symbol} className="prediction-item">
                      <span className="symbol">{symbol}</span>
                      <span className="prediction">
                        {result.error ? `错误: ${result.error}` : `预测收益: ${result.stock_predictions?.[0]?.predicted_return?.toFixed(4) || 'N/A'}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {mlMode === 'comparison' && (
          <div className="ml-comparison">
            <p>模型比较功能开发中...</p>
            <div className="comparison-placeholder">
              <div className="placeholder-item">📊 模型性能对比</div>
              <div className="placeholder-item">📈 准确率比较</div>
              <div className="placeholder-item">⏱️ 训练时间对比</div>
              <div className="placeholder-item">🎯 预测能力评估</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // 股票评分系统界面
  const renderStockScoring = () => (
    <div className="factor-container">
      <header className="factor-header">
        <button className="back-button" onClick={() => setMode('home')}>
          ← 返回首页
        </button>
        <div className="header-content">
          <h1>🏆 股票评分系统</h1>
          <p>基于多因子和ML的综合评分</p>
        </div>
      </header>

      <div className="factor-nav">
        <button 
          className={`nav-button ${scoringMode === 'factor-score' ? 'active' : ''}`}
          onClick={() => setScoringMode('factor-score')}
        >
          ⚙️ 因子评分
        </button>
        <button 
          className={`nav-button ${scoringMode === 'ml-score' ? 'active' : ''}`}
          onClick={() => setScoringMode('ml-score')}
        >
          🧠 ML评分
        </button>
        <button 
          className={`nav-button ${scoringMode === 'comprehensive' ? 'active' : ''}`}
          onClick={() => setScoringMode('comprehensive')}
        >
          🎯 综合评分
        </button>
        <button 
          className={`nav-button ${scoringMode === 'ranking' ? 'active' : ''}`}
          onClick={() => setScoringMode('ranking')}
        >
          🏆 选股排名
        </button>
      </div>

      <div className="factor-content">
        {scoringMode === 'factor-score' && (
          <div className="scoring-section">
            <div className="scoring-controls">
              <div className="stock-selector">
                <label>选择股票:</label>
                <select value={selectedStock} onChange={(e) => setSelectedStock(e.target.value)}>
                  <option value="AAPL">苹果 (AAPL)</option>
                  <option value="MSFT">微软 (MSFT)</option>
                  <option value="GOOGL">谷歌 (GOOGL)</option>
                  <option value="TSLA">特斯拉 (TSLA)</option>
                  <option value="AMZN">亚马逊 (AMZN)</option>
                </select>
              </div>
              <button className="score-button" onClick={() => calculateScore('factor')}>
                ⚙️ 计算因子评分
              </button>
            </div>

            {scoreResults && scoreResults.factor_scoring && (
              <div className="score-results">
                <h3>因子评分结果</h3>
                <div className="score-summary">
                  <div className="score-item">
                    <span>综合评分:</span>
                    <span className="score-value">{scoreResults.factor_scoring.overall_score}</span>
                  </div>
                  <div className="score-item">
                    <span>评分等级:</span>
                    <span className={`score-grade ${scoreResults.factor_scoring.score_grade}`}>
                      {scoreResults.factor_scoring.score_grade}
                    </span>
                  </div>
                </div>
                
                <div className="category-scores">
                  {Object.entries(scoreResults.factor_scoring.category_scores || {}).map(([category, data]: [string, any]) => (
                    <div key={category} className="category-score">
                      <span className="category-name">{getCategoryName(category)}</span>
                      <span className="category-value">{data.score?.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {scoringMode === 'ml-score' && (
          <div className="scoring-section">
            <div className="scoring-controls">
              <div className="stock-selector">
                <label>选择股票:</label>
                <select value={selectedStock} onChange={(e) => setSelectedStock(e.target.value)}>
                  <option value="AAPL">苹果 (AAPL)</option>
                  <option value="MSFT">微软 (MSFT)</option>
                  <option value="GOOGL">谷歌 (GOOGL)</option>
                  <option value="TSLA">特斯拉 (TSLA)</option>
                  <option value="AMZN">亚马逊 (AMZN)</option>
                </select>
              </div>
              <button className="score-button" onClick={() => calculateScore('ml')}>
                🧠 计算ML评分
              </button>
            </div>

            {scoreResults && scoreResults.ml_scoring && (
              <div className="score-results">
                <h3>ML评分结果</h3>
                <div className="score-summary">
                  <div className="score-item">
                    <span>集成评分:</span>
                    <span className="score-value">{scoreResults.ml_scoring.ensemble_score}</span>
                  </div>
                  <div className="score-item">
                    <span>评分等级:</span>
                    <span className={`score-grade ${scoreResults.ml_scoring.score_grade}`}>
                      {scoreResults.ml_scoring.score_grade}
                    </span>
                  </div>
                  <div className="score-item">
                    <span>使用模型数:</span>
                    <span>{scoreResults.ml_scoring.models_used}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {scoringMode === 'comprehensive' && (
          <div className="scoring-section">
            <div className="scoring-controls">
              <div className="stock-selector">
                <label>选择股票:</label>
                <select value={selectedStock} onChange={(e) => setSelectedStock(e.target.value)}>
                  <option value="AAPL">苹果 (AAPL)</option>
                  <option value="MSFT">微软 (MSFT)</option>
                  <option value="GOOGL">谷歌 (GOOGL)</option>
                  <option value="TSLA">特斯拉 (TSLA)</option>
                  <option value="AMZN">亚马逊 (AMZN)</option>
                </select>
              </div>
              <button className="score-button" onClick={() => calculateScore('comprehensive')}>
                🎯 计算综合评分
              </button>
            </div>

            {scoreResults && scoreResults.composite_score && (
              <div className="score-results">
                <h3>综合评分结果</h3>
                <div className="score-summary">
                  <div className="score-item">
                    <span>综合评分:</span>
                    <span className="score-value large">{scoreResults.composite_score}</span>
                  </div>
                </div>
                
                <div className="composite-breakdown">
                  <div className="breakdown-item">
                    <span>因子评分:</span>
                    <span>{scoreResults.factor_scoring?.overall_score}</span>
                  </div>
                  <div className="breakdown-item">
                    <span>ML评分:</span>
                    <span>{scoreResults.ml_scoring?.ensemble_score}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {scoringMode === 'ranking' && (
          <div className="ranking-section">
            <div className="ranking-controls">
              <div className="control-group">
                <label>股票列表 (用逗号分隔):</label>
                <input
                  type="text"
                  value={rankingSymbols.join(', ')}
                  onChange={(e) => setRankingSymbols(e.target.value.split(',').map(s => s.trim()))}
                  placeholder="AAPL, MSFT, GOOGL, TSLA, NVDA, META, AMZN, NFLX, CRM, ORCL"
                />
              </div>
              
              <div className="weight-controls">
                <div className="weight-item">
                  <label>因子权重: {factorWeight.toFixed(1)}</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={factorWeight}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      setFactorWeight(value);
                      setMLWeight(1 - value);
                    }}
                  />
                </div>
                <div className="weight-item">
                  <label>ML权重: {mlWeight.toFixed(1)}</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={mlWeight}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      setMLWeight(value);
                      setFactorWeight(1 - value);
                    }}
                  />
                </div>
              </div>
              
              <button 
                className="rank-button" 
                onClick={rankStocks}
                disabled={isRanking}
              >
                {isRanking ? '🔄 排名中...' : '🏆 开始排名'}
              </button>
            </div>

            {rankingResults && rankingResults.rankings && (
              <div className="ranking-results">
                <h3>排名结果</h3>
                <div className="ranking-stats">
                  <span>分析股票: {rankingResults.total_analyzed}</span>
                  <span>排名股票: {rankingResults.total_ranked}</span>
                </div>
                
                <div className="rankings-list">
                  {rankingResults.rankings.slice(0, 10).map((stock: any, index: number) => (
                    <div key={stock.symbol} className="ranking-item">
                      <span className="rank">#{index + 1}</span>
                      <span className="symbol">{stock.symbol}</span>
                      <span className="composite-score">{stock.composite_score}</span>
                      <span className={`grade ${stock.composite_grade}`}>{stock.composite_grade}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // MACD背离扫描界面
  const renderDivergenceScanner = () => (
    <div className="factor-container">
      <header className="factor-header">
        <button className="back-button" onClick={() => setMode('home')}>
          ← 返回首页
        </button>
        <div className="header-content">
          <h1>📊 MACD背离扫描</h1>
          <p>一键扫描市场MACD顶背离和底背离</p>
        </div>
      </header>

      <div className="factor-nav">
        <button 
          className={`nav-button ${divergenceMode === 'scanner' ? 'active' : ''}`}
          onClick={() => setDivergenceMode('scanner')}
        >
          🔍 市场扫描
        </button>
        <button 
          className={`nav-button ${divergenceMode === 'results' ? 'active' : ''}`}
          onClick={() => {
            setDivergenceMode('results');
            loadRecentDivergences();
          }}
        >
          📈 最近结果
        </button>
        <button 
          className={`nav-button ${divergenceMode === 'history' ? 'active' : ''}`}
          onClick={() => {
            setDivergenceMode('history');
            loadScanHistory();
          }}
        >
          📋 扫描历史
        </button>
      </div>

      <div className="factor-content">
        {divergenceMode === 'scanner' && (
          <div className="divergence-scanner">
            <div className="scanner-controls">
              <div className="control-group">
                <label>Tushare Token:</label>
                <input
                  type="text"
                  value={tushareToken}
                  onChange={(e) => setTushareToken(e.target.value)}
                  placeholder="请输入您的Tushare Token"
                />
              </div>
              
              <div className="market-selector">
                <label>选择市场:</label>
                <div className="market-buttons">
                  {(['US', 'HK', 'CN'] as const).map(market => (
                    <button
                      key={market}
                      className={`market-button ${selectedMarket === market ? 'active' : ''}`}
                      onClick={() => setSelectedMarket(market)}
                    >
                      {getMarketName(market)}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="divergence-type-selector">
                <label>背离类型:</label>
                <div className="type-checkboxes">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={divergenceTypes.includes('bullish')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setDivergenceTypes([...divergenceTypes, 'bullish']);
                        } else {
                          setDivergenceTypes(divergenceTypes.filter(t => t !== 'bullish'));
                        }
                      }}
                    />
                    <span style={{color: getDivergenceColor('bullish')}}>
                      📈 底背离（看涨）
                    </span>
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={divergenceTypes.includes('bearish')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setDivergenceTypes([...divergenceTypes, 'bearish']);
                        } else {
                          setDivergenceTypes(divergenceTypes.filter(t => t !== 'bearish'));
                        }
                      }}
                    />
                    <span style={{color: getDivergenceColor('bearish')}}>
                      📉 顶背离（看跌）
                    </span>
                  </label>
                </div>
              </div>
              
              <button 
                className="scan-button" 
                onClick={scanMarketDivergence}
                disabled={isScanning || divergenceTypes.length === 0}
              >
                {isScanning ? '🔄 扫描中...' : '🔍 开始扫描'}
              </button>
            </div>

            {scanResults && (
              <div className="scan-results">
                <div className="results-header">
                  <h3>扫描结果</h3>
                  <div className="results-stats">
                    <span>市场: {getMarketName(scanResults.market)}</span>
                    <span>扫描股票: {scanResults.total_stocks}</span>
                    <span>发现背离: {scanResults.divergences?.length || 0}</span>
                    <span>耗时: {scanResults.scan_duration?.toFixed(2)}秒</span>
                  </div>
                </div>
                
                {scanResults.divergences && scanResults.divergences.length > 0 ? (
                  <div className="divergences-list">
                    {scanResults.divergences.map((divergence: any, index: number) => (
                      <div key={index} className="divergence-item">
                        <div className="divergence-header">
                          <span className="symbol">{divergence.symbol}</span>
                          <span 
                            className="divergence-type"
                            style={{color: getDivergenceColor(divergence.type)}}
                          >
                            {getDivergenceTypeName(divergence.type)}
                          </span>
                          <span className="confidence">
                            置信度: {(divergence.confidence * 100).toFixed(1)}%
                          </span>
                        </div>
                        
                        <div className="divergence-details">
                          <div className="detail-item">
                            <span>时间范围:</span>
                            <span>{divergence.start_date} ~ {divergence.end_date}</span>
                          </div>
                          <div className="detail-item">
                            <span>价格区间:</span>
                            <span>{divergence.price_range}</span>
                          </div>
                          <div className="detail-item">
                            <span>MACD区间:</span>
                            <span>{divergence.macd_range}</span>
                          </div>
                          <div className="detail-item">
                            <span>强度:</span>
                            <span>{(divergence.strength * 100).toFixed(1)}%</span>
                          </div>
                        </div>
                        
                        {scanResults.charts && scanResults.charts[divergence.symbol] && (
                          <div className="chart-container">
                            <h4>K线图 - {divergence.symbol}</h4>
                            <img 
                              src={`data:image/png;base64,${scanResults.charts[divergence.symbol]}`}
                              alt={`${divergence.symbol} K线图`}
                              className="divergence-chart"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="no-results">
                    <p>未发现背离信号</p>
                  </div>
                )}
                
                {scanResults.errors && scanResults.errors.length > 0 && (
                  <div className="scan-errors">
                    <h4>扫描错误:</h4>
                    <ul>
                      {scanResults.errors.map((error: string, index: number) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {divergenceMode === 'results' && (
          <div className="recent-divergences">
            <div className="results-header">
              <h3>最近24小时背离信号</h3>
              <button className="refresh-button" onClick={loadRecentDivergences}>
                🔄 刷新
              </button>
            </div>
            
            {recentDivergences.length > 0 ? (
              <div className="divergences-list">
                {recentDivergences.map((divergence: any, index: number) => (
                  <div key={index} className="divergence-item">
                    <div className="divergence-header">
                      <span className="symbol">{divergence.symbol}</span>
                      <span 
                        className="divergence-type"
                        style={{color: getDivergenceColor(divergence.divergence_type)}}
                      >
                        {getDivergenceTypeName(divergence.divergence_type)}
                      </span>
                      <span className="market">{getMarketName(divergence.market)}</span>
                    </div>
                    
                    <div className="divergence-details">
                      <div className="detail-item">
                        <span>检测时间:</span>
                        <span>{new Date(divergence.detected_at).toLocaleString()}</span>
                      </div>
                      <div className="detail-item">
                        <span>置信度:</span>
                        <span>{(divergence.confidence * 100).toFixed(1)}%</span>
                      </div>
                      <div className="detail-item">
                        <span>强度:</span>
                        <span>{(divergence.strength * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-results">
                <p>最近24小时内未发现背离信号</p>
              </div>
            )}
          </div>
        )}

        {divergenceMode === 'history' && (
          <div className="scan-history">
            <div className="results-header">
              <h3>扫描历史</h3>
              <button className="refresh-button" onClick={loadScanHistory}>
                🔄 刷新
              </button>
            </div>
            
            {scanHistory.length > 0 ? (
              <div className="history-list">
                {scanHistory.map((record: any, index: number) => (
                  <div key={index} className="history-item">
                    <div className="history-header">
                      <span className="market">{getMarketName(record.market)}</span>
                      <span className="scan-date">
                        {new Date(record.scan_date).toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="history-stats">
                      <div className="stat-item">
                        <span>扫描股票:</span>
                        <span>{record.total_stocks}</span>
                      </div>
                      <div className="stat-item">
                        <span>发现背离:</span>
                        <span>{record.divergence_count}</span>
                      </div>
                      <div className="stat-item">
                        <span>扫描耗时:</span>
                        <span>{record.scan_duration?.toFixed(2)}秒</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-results">
                <p>暂无扫描历史</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // 数据查询页面渲染函数
  const renderDataQuery = () => (
    <div className="data-query-container">
      <div className="data-query-header">
        <button onClick={() => setMode('home')} className="back-button">
          ← 返回
        </button>
        <div className="header-content">
          <h1>数据查询</h1>
          <p>查询股票数据，查看K线图和技术指标</p>
        </div>
      </div>

      <div className="data-query-content">
        <div className="query-controls">
          <div className="search-section">
            <h3>搜索股票</h3>
            <div className="stock-search-container">
              <input
                type="text"
                value={querySymbol}
                onChange={(e) => {
                  setQuerySymbol(e.target.value);
                  searchStocks(e.target.value);
                }}
                placeholder="输入股票代码或名称 (如: AAPL, 600519.SH, 00700.HK)"
                className="stock-search-input"
              />
              
              {showSuggestions && searchSuggestions.length > 0 && (
                <div className="search-suggestions">
                  {searchSuggestions.map((stock, index) => (
                    <div
                      key={index}
                      className="suggestion-item"
                      onClick={() => selectStockSuggestion(stock)}
                    >
                      <span className="suggestion-symbol">{stock.symbol}</span>
                      <span className="suggestion-name">{stock.name}</span>
                      <span className="suggestion-market">{stock.market}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="query-options">
            <div className="period-selector">
              <label>时间范围:</label>
              <select value={queryPeriod} onChange={(e) => setQueryPeriod(e.target.value)}>
                <option value="1m">1个月</option>
                <option value="3m">3个月</option>
                <option value="6m">6个月</option>
                <option value="1y">1年</option>
                <option value="2y">2年</option>
                <option value="5y">5年</option>
              </select>
            </div>

            <div className="indicators-selector">
              <label>技术指标:</label>
              <div className="indicators-checkboxes">
                {[
                  { id: 'sma', name: '简单移动平均' },
                  { id: 'ema', name: '指数移动平均' },
                  { id: 'rsi', name: 'RSI' },
                  { id: 'macd', name: 'MACD' },
                  { id: 'bollinger', name: '布林带' },
                ].map(indicator => (
                  <label key={indicator.id} className="indicator-checkbox">
                    <input
                      type="checkbox"
                      checked={selectedIndicators.includes(indicator.id)}
                      onChange={() => toggleIndicator(indicator.id)}
                    />
                    {indicator.name}
                  </label>
                ))}
              </div>
            </div>

            <div className="query-actions">
              <button 
                onClick={() => queryStockData()} 
                disabled={isQuerying || !querySymbol.trim() || !tushareToken.trim()}
                className="query-button"
              >
                {isQuerying ? '查询中...' : '查询股票数据'}
              </button>
              
              {stockData && (
                <div className="refresh-controls">
                  <label className="auto-refresh-toggle">
                    <input
                      type="checkbox"
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                    />
                    <span>自动刷新 (30秒)</span>
                  </label>
                  <button 
                    onClick={() => queryStockData()}
                    className="manual-refresh-button"
                    disabled={isQuerying}
                  >
                    🔄 手动刷新
                  </button>
                  {lastRefreshTime && (
                    <span className="last-refresh-time">
                      最后更新: {lastRefreshTime}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {stockData && (
          <div className="stock-data-results">
            <div className="stock-info-panel">
              <h3>📈 {stockData.basic_info.name} ({stockData.basic_info.symbol})</h3>
              <div className="stock-basic-info">
                <div className="info-item">
                  <span>市场:</span>
                  <span>{stockData.basic_info.market}</span>
                </div>
                <div className="info-item">
                  <span>行业:</span>
                  <span>{stockData.basic_info.industry}</span>
                </div>
                <div className="info-item">
                  <span>数据来源:</span>
                  <span>{stockData.data_source === 'database' ? '数据库' : '新下载'}</span>
                </div>
              </div>

              <div className="stock-statistics">
                <div className="stat-grid">
                  <div className="stat-item">
                    <span className="stat-label">最新价格</span>
                    <span className="stat-value">${stockData.statistics.latest_price.toFixed(2)}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">价格变化</span>
                    <span className={`stat-value ${stockData.statistics.price_change >= 0 ? 'positive' : 'negative'}`}>
                      {stockData.statistics.price_change >= 0 ? '+' : ''}{stockData.statistics.price_change.toFixed(2)}%
                    </span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">最高价</span>
                    <span className="stat-value">${stockData.statistics.highest_price.toFixed(2)}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">最低价</span>
                    <span className="stat-value">${stockData.statistics.lowest_price.toFixed(2)}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">平均成交量</span>
                    <span className="stat-value">{(stockData.statistics.avg_volume / 1000000).toFixed(2)}M</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">数据点数</span>
                    <span className="stat-value">{stockData.statistics.total_records}</span>
                  </div>
                </div>
                
                <div className="export-controls">
                  <h4>📥 导出数据</h4>
                  <div className="export-buttons">
                    <button onClick={() => exportData('csv')} className="export-button csv">
                      📊 导出 CSV
                    </button>
                    <button onClick={() => exportData('json')} className="export-button json">
                      📄 导出 JSON
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {showChart && (
              <div className="chart-section">
                <h3>📊 K线图表</h3>
                <div className="kline-chart-container">
                  <ReactECharts
                    option={getKlineChartOption()}
                    style={{ height: '500px', width: '100%' }}
                    notMerge={true}
                    lazyUpdate={true}
                  />
                  <div className="chart-info">
                    <p>数据点数: {stockData.kline_data.length}</p>
                    <p>日期范围: {stockData.statistics.date_range.start} 至 {stockData.statistics.date_range.end}</p>
                    <p>提示: 可以拖拽缩放图表，使用滚轮缩放，双击重置</p>
                  </div>
                </div>
              </div>
            )}

            {technicalData && (
              <div className="indicators-section">
                <h3>📈 技术指标</h3>
                <div className="indicators-display">
                  {Object.entries(technicalData.indicators).map(([indicator, values]) => (
                    <div key={indicator} className="indicator-item">
                      <h4>{indicator.toUpperCase()}</h4>
                      <div className="indicator-chart-container">
                        <ReactECharts
                          option={getTechnicalIndicatorOption(indicator, values)}
                          style={{ height: '300px', width: '100%' }}
                          notMerge={true}
                          lazyUpdate={true}
                        />
                        <div className="indicator-info">
                          <p>数据点数: {Array.isArray(values) ? values.length : '复合数据'}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="app">
      {mode === 'home' && renderHomePage()}
      {mode === 'ai-agent' && renderAIAgentMode()}
      {mode === 'data-query' && renderDataQuery()}
      {mode === 'factor-management' && renderFactorManagement()}
      {mode === 'ml-models' && renderMLModels()}
      {mode === 'stock-scoring' && renderStockScoring()}
      {mode === 'divergence-scanner' && renderDivergenceScanner()}
      {mode === 'markdown-test' && (
        <div className="chat-container">
          <header className="chat-header">
            <button className="back-button" onClick={() => setMode('home')}>
              ← 返回首页
            </button>
            <div className="header-content">
              <h1>📝 Markdown 测试</h1>
              <p>测试 Markdown 渲染功能</p>
            </div>
          </header>
          <div className="messages-container">
            <MarkdownTest />
          </div>
        </div>
      )}
    </div>
  );
};

export default App;
