/* 重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f7f8;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#root {
  height: 100%;
  width: 100%;
}

.app {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

/* ===== 主页样式 ===== */
.home-container {
  width: 100%;
  height: 100%;
  max-width: none;
  background: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.home-header {
  padding: 40px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.home-header h1 {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 12px;
}

.home-header p {
  font-size: 18px;
  opacity: 0.9;
}

.module-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 20px;
  align-items: start;
  overflow-y: auto;
  overflow-x: hidden;
}

.module-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.module-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.module-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.module-card h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 12px;
  font-weight: 600;
}

.module-card p {
  font-size: 16px;
  color: #718096;
  margin-bottom: 20px;
  line-height: 1.6;
}

.module-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.module-features span {
  background: #f7faff;
  color: #667eea;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

/* ===== 聊天界面样式 ===== */
.chat-container {
  width: 100%;
  height: 100%;
  max-width: none;
  background: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 头部样式 */
.chat-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.header-content {
  flex: 1;
}

.header-content h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.header-content p {
  font-size: 14px;
  opacity: 0.9;
}

.clear-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.clear-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 欢迎屏幕 */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
}

.welcome-icon {
  font-size: 64px;
  margin-bottom: 24px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.welcome-screen h2 {
  font-size: 28px;
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
}

.welcome-screen p {
  font-size: 16px;
  color: #718096;
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 500px;
}

.example-questions {
  width: 100%;
  max-width: 600px;
}

.example-questions h3 {
  font-size: 18px;
  color: #2d3748;
  margin-bottom: 20px;
  font-weight: 500;
}

.question-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.example-question {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.example-question:hover {
  border-color: #667eea;
  background: #f7faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

/* 消息样式 */
.messages {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.message {
  display: flex;
  flex-direction: column;
}

.message.user {
  align-items: flex-end;
}

.message.assistant,
.message.system {
  align-items: flex-start;
}

.message-content {
  max-width: 85%;
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.assistant .message-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.message.system .message-content {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

.message-header span {
  font-size: 16px;
}

.user-icon,
.assistant-icon,
.system-icon {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.streaming-indicator {
  animation: pulse 1.5s infinite;
  margin-left: auto;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.message-text {
  line-height: 1.6;
  font-size: 15px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Markdown 内容在消息中的样式调整 */
.message-text .markdown-content {
  white-space: normal;
}

.message-text .markdown-content .markdown-h1,
.message-text .markdown-content .markdown-h2,
.message-text .markdown-content .markdown-h3,
.message-text .markdown-content .markdown-h4 {
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.message-text .markdown-content .markdown-h1:first-child,
.message-text .markdown-content .markdown-h2:first-child,
.message-text .markdown-content .markdown-h3:first-child,
.message-text .markdown-content .markdown-h4:first-child {
  margin-top: 0;
}

.message-text .markdown-content .markdown-p:last-child {
  margin-bottom: 0;
}

.message-text .markdown-content .code-block {
  margin: 0.8em 0;
  font-size: 0.85em;
}

.message-text .markdown-content .markdown-table {
  font-size: 0.9em;
}

.message-text .markdown-content .markdown-blockquote {
  margin: 0.8em 0;
  padding: 0.5em 1em;
}

.cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 输入区域 */
.input-form {
  padding: 20px 24px;
  border-top: 1px solid #e5e5e5;
  background: white;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 100%;
}

.message-input {
  flex: 1;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 15px;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  min-height: 44px;
  max-height: 120px;
  line-height: 1.4;
}

.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input:disabled {
  background-color: #f7f7f8;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== 因子管理系统样式 ===== */
.factor-container {
  width: 100%;
  height: 100%;
  max-width: none;
  background: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.factor-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
}

.factor-nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e5e5;
}

.nav-button {
  flex: 1;
  padding: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s;
}

.nav-button:hover {
  background: #e9ecef;
  color: #495057;
}

.nav-button.active {
  background: white;
  color: #667eea;
  border-bottom: 2px solid #667eea;
}

.factor-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 因子总览样式 */
.factor-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.factor-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.stat-card h3 {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #667eea;
}

.factor-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.category-section {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.category-section h3 {
  font-size: 18px;
  color: #2d3748;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-count {
  color: #6c757d;
  font-size: 14px;
  font-weight: 400;
}

.factor-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.factor-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.factor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.factor-name {
  font-weight: 500;
  color: #2d3748;
}

.factor-desc {
  font-size: 12px;
  color: #6c757d;
}

.factor-toggle {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.factor-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #667eea;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* 因子计算样式 */
.factor-calculation {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.calculation-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.stock-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stock-selector label {
  font-weight: 500;
  color: #2d3748;
}

.stock-selector select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
}

.calculate-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.calculate-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.calculation-results {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.calculation-results h3 {
  margin-bottom: 16px;
  color: #2d3748;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-name {
  font-weight: 500;
  color: #2d3748;
}

.result-value {
  font-weight: 600;
  color: #667eea;
}

/* 自定义因子样式 */
.custom-factor {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* AI因子生成样式 */
.ai-factor-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border: 2px solid #667eea;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.ai-factor-section h3 {
  color: #667eea;
  margin-bottom: 20px;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ai-description-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.ai-description-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ai-generate-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.ai-generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.ai-generate-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ai-result {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-result h4 {
  color: #2d3748;
  margin-bottom: 16px;
  font-size: 18px;
}

.ai-factor-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ai-factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.ai-factor-name {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.ai-confidence {
  background: #e6fffa;
  color: #065f46;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.ai-factor-formula {
  background: #f7fafc;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #2d3748;
}

.ai-factor-explanation {
  color: #4a5568;
  line-height: 1.6;
  font-size: 14px;
}

.ai-factor-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.test-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.confirm-button {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.confirm-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.reject-button {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.reject-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
}

/* 手动创建因子样式 */
.manual-factor-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.manual-factor-section h3 {
  color: #2d3748;
  margin-bottom: 20px;
  font-size: 18px;
}

.custom-form {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
}

.custom-form h3 {
  margin-bottom: 20px;
  color: #2d3748;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.create-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.create-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.custom-factors-list {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.custom-factors-list h3 {
  margin-bottom: 16px;
  color: #2d3748;
}

.custom-factor-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.custom-factor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.custom-factor-name {
  font-weight: 500;
  color: #2d3748;
}

.custom-factor-description {
  font-size: 13px;
  color: #6c757d;
  margin: 4px 0;
  line-height: 1.4;
}

.custom-factor-formula {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
}

.no-custom-factors {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.no-custom-factors p {
  font-size: 16px;
  margin: 0;
}

.delete-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.delete-button:hover {
  background: #c82333;
}

/* 因子分析样式 */
.factor-analysis {
  text-align: center;
  padding: 40px;
}

.factor-analysis h3 {
  margin-bottom: 16px;
  color: #2d3748;
}

.factor-analysis p {
  color: #6c757d;
  margin-bottom: 32px;
}

.analysis-placeholder {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-item {
  background: #f8f9fa;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  color: #6c757d;
  font-weight: 500;
}

/* ===== 机器学习模块样式 ===== */
.ml-models, .ml-training, .ml-prediction, .ml-comparison {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.models-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.load-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.load-button:hover {
  background: #5a6fd8;
}

.models-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-name {
  font-weight: 500;
  color: #2d3748;
}

.model-id {
  font-size: 12px;
  color: #6c757d;
}

.model-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.model-status.available {
  background: #d4edda;
  color: #155724;
}

.model-status.unavailable {
  background: #f8d7da;
  color: #721c24;
}

.training-controls, .prediction-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-weight: 500;
  color: #2d3748;
}

.control-group input, .control-group select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
}

.train-button, .predict-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.train-button:hover, .predict-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.train-button:disabled, .predict-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.training-results, .prediction-results {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prediction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.prediction-item .symbol {
  font-weight: 600;
  color: #2d3748;
}

.prediction-item .prediction {
  color: #667eea;
  font-weight: 500;
}

/* ===== 股票评分系统样式 ===== */
.scoring-section, .ranking-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.scoring-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.score-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.score-results {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.score-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.score-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.score-item span:first-child {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.score-value {
  font-size: 24px;
  font-weight: 600;
  color: #667eea;
}

.score-value.large {
  font-size: 32px;
}

.score-grade {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.score-grade.excellent {
  background: #d4edda;
  color: #155724;
}

.score-grade.good {
  background: #cce5ff;
  color: #004085;
}

.score-grade.average {
  background: #fff3cd;
  color: #856404;
}

.score-grade.poor {
  background: #f8d7da;
  color: #721c24;
}

.score-grade.very_poor {
  background: #e2e3e5;
  color: #383d41;
}

.category-scores {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.category-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.category-name {
  font-weight: 500;
  color: #2d3748;
}

.category-value {
  font-weight: 600;
  color: #667eea;
}

.composite-breakdown {
  display: flex;
  gap: 20px;
  margin-top: 16px;
}

.breakdown-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.breakdown-item span:first-child {
  font-size: 12px;
  color: #6c757d;
}

.breakdown-item span:last-child {
  font-size: 18px;
  font-weight: 600;
  color: #667eea;
}

.ranking-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weight-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.weight-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weight-item input[type="range"] {
  width: 100%;
}

.rank-button {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.rank-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.rank-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ranking-results {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
}

.ranking-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #6c757d;
}

.rankings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ranking-item {
  display: grid;
  grid-template-columns: 50px 80px 1fr 100px;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 12px;
}

.ranking-item .rank {
  font-weight: 600;
  color: #667eea;
}

.ranking-item .symbol {
  font-weight: 600;
  color: #2d3748;
}

.ranking-item .composite-score {
  font-weight: 600;
  color: #28a745;
}

.ranking-item .grade {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  text-transform: uppercase;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app {
    padding: 0;
  }
  
  .home-container,
  .chat-container,
  .factor-container,
  .data-query-container {
    height: 100%;
    border-radius: 0;
    box-shadow: none;
  }
  
  .module-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }
  
  .module-card {
    min-height: auto;
    padding: 20px;
  }
  
  .question-grid {
    grid-template-columns: 1fr;
  }
  
  .example-question {
    font-size: 13px;
    padding: 14px;
  }
  
  .message-content {
    max-width: 95%;
  }
  
  .messages {
    padding: 16px;
  }
  
  .input-form {
    padding: 16px;
  }
  
  .factor-stats {
    grid-template-columns: 1fr;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-placeholder {
    grid-template-columns: 1fr;
  }
  
  .factor-nav {
    flex-direction: column;
  }
  
  .nav-button {
    padding: 12px;
  }
  
  .results-summary {
    grid-template-columns: 1fr;
  }
  
  .weight-controls {
    grid-template-columns: 1fr;
  }
  
  .ranking-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .category-scores {
    grid-template-columns: 1fr;
  }
  
  .composite-breakdown {
    flex-direction: column;
    gap: 12px;
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 计算结果样式增强 */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  margin-bottom: 16px;
  font-weight: 500;
}

.data-info {
  background: #d1ecf1;
  color: #0c5460;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #bee5eb;
  margin-bottom: 16px;
}

.data-info p {
  margin: 4px 0;
  font-size: 14px;
}

.data-info p:first-child {
  font-weight: 600;
  font-size: 15px;
}

/* 股票选择器增强 */
.stock-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stock-input {
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s;
}

.stock-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.stock-input::placeholder {
  color: #a0aec0;
}

/* 日期范围选择器样式 */
.date-range-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-range-selector label {
  font-weight: 500;
  color: #2d3748;
}

/* 日期预设按钮样式 */
.date-preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}

.preset-button {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  color: #4a5568;
  transition: all 0.2s;
}

.preset-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e0;
}

.preset-button.clear-button {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.preset-button.clear-button:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.date-inputs {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.date-input {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s;
  min-width: 140px;
}

.date-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 计算按钮组样式 */
.calculation-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.chart-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* 图表容器样式 */
.chart-container {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
  margin-top: 24px;
}

/* 聊天消息中的图表样式 */
.message.chart .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.message.chart .message-header .chart-icon {
  color: #ffd700;
  font-size: 18px;
}

.message.chart .chart-container {
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
  margin: 12px 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.message.chart .chart-container h4 {
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding: 0;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e5e5;
}

.chart-header h3 {
  color: #2d3748;
  margin: 0;
}

.close-chart-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.close-chart-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.chart-info {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border-left: 4px solid #667eea;
}

.chart-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #495057;
}

.chart-info p:first-child {
  font-weight: 600;
  color: #2d3748;
}

.chart-wrapper {
  margin: 20px 0;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.chart-legend {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e5e5;
}

.chart-legend h4 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 16px;
}

.legend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.legend-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.legend-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.legend-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* MACD背离扫描样式 */
.divergence-scanner {
  padding: 24px;
}

.scanner-controls {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.market-selector {
  margin: 20px 0;
}

.market-selector label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2d3748;
}

.market-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.market-button {
  padding: 12px 24px;
  border: 2px solid #e5e5e5;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  color: #4a5568;
}

.market-button:hover {
  border-color: #667eea;
  color: #667eea;
}

.market-button.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.divergence-type-selector {
  margin: 20px 0;
}

.divergence-type-selector label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2d3748;
}

.type-checkboxes {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.scan-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 20px;
}

.scan-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.scan-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.scan-results {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.results-header h3 {
  color: #2d3748;
  margin: 0;
}

.results-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #6c757d;
}

.results-stats span {
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.divergences-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.divergence-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s;
}

.divergence-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.divergence-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.divergence-header .symbol {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

.divergence-type {
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid currentColor;
}

.confidence {
  font-size: 14px;
  color: #6c757d;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.divergence-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.detail-item span:first-child {
  font-weight: 500;
  color: #4a5568;
}

.detail-item span:last-child {
  font-weight: 600;
  color: #2d3748;
}

.chart-container {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.chart-container h4 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 16px;
}

.divergence-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.no-results p {
  font-size: 16px;
  margin: 0;
}

.scan-errors {
  margin-top: 20px;
  padding: 16px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
}

.scan-errors h4 {
  color: #c53030;
  margin: 0 0 12px 0;
}

.scan-errors ul {
  margin: 0;
  padding-left: 20px;
  color: #e53e3e;
}

.recent-divergences,
.scan-history {
  padding: 24px;
}

.refresh-button {
  background: #48bb78;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.refresh-button:hover {
  background: #38a169;
  transform: translateY(-1px);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header .market {
  font-weight: 600;
  color: #2d3748;
}

.history-header .scan-date {
  font-size: 14px;
  color: #6c757d;
}

.history-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  min-width: 120px;
  padding: 6px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-size: 14px;
}

.stat-item span:first-child {
  color: #6c757d;
}

.stat-item span:last-child {
  font-weight: 600;
  color: #2d3748;
}

/* 响应式图表样式 */
@media (max-width: 768px) {
  .calculation-buttons {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }
  
  .calculate-button,
  .chart-button {
    width: 100%;
  }
  
  .date-inputs {
    flex-direction: column;
    gap: 12px;
  }
  
  .date-input {
    min-width: 100%;
  }
  
  .date-preset-buttons {
    justify-content: center;
  }
  
  .preset-button {
    flex: 1;
    min-width: 80px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .close-chart-button {
    align-self: flex-end;
  }
  
  .chart-wrapper {
    padding: 8px;
  }
  
  .legend-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    padding: 16px;
  }
  
  /* AI因子生成响应式样式 */
  .ai-factor-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .ai-factor-section h3 {
    font-size: 18px;
  }
  
  .ai-generate-button {
    width: 100%;
    padding: 14px 20px;
    font-size: 14px;
  }
  
  .ai-factor-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .ai-factor-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .confirm-button,
  .reject-button {
    width: 100%;
    padding: 12px 20px;
  }
  
  .manual-factor-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .manual-factor-section h3 {
    font-size: 16px;
  }
  
  /* MACD背离扫描响应式样式 */
  .divergence-scanner {
    padding: 16px;
  }
  
  .scanner-controls {
    padding: 16px;
  }
  
  .market-buttons {
    flex-direction: column;
  }
  
  .market-button {
    width: 100%;
    text-align: center;
  }
  
  .type-checkboxes {
    flex-direction: column;
    gap: 12px;
  }
  
  .divergence-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .divergence-details {
    grid-template-columns: 1fr;
  }
  
  .results-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .history-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    min-width: 100%;
  }
}

/* ===== 数据查询页面样式 ===== */
.data-query-container {
  width: 100%;
  height: 100%;
  max-width: none;
  background: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.data-query-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
}

.data-query-header .back-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.data-query-header .back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.data-query-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.data-query-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

.search-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.search-section h3 {
  margin-bottom: 16px;
  color: #2d3748;
  font-weight: 600;
  font-size: 18px;
}

.stock-search-container {
  position: relative;
  margin-bottom: 20px;
}

.stock-search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s;
}

.stock-search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f7faff;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-symbol {
  font-weight: 600;
  color: #2d3748;
  min-width: 100px;
}

.suggestion-name {
  flex: 1;
  margin: 0 10px;
  color: #4a5568;
}

.suggestion-market {
  font-size: 12px;
  color: #667eea;
  background: #f7faff;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.query-controls {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.query-options {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  align-items: end;
}

.period-selector label,
.indicators-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.period-selector select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s;
}

.period-selector select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.indicators-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.indicator-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
}

.indicator-checkbox input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.query-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.query-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.query-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.query-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  font-size: 14px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #4a5568;
}

.auto-refresh-toggle input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.manual-refresh-button {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.manual-refresh-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.manual-refresh-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.last-refresh-time {
  color: #6c757d;
  font-size: 12px;
  font-style: italic;
}

/* 导出控件样式 */
.export-controls {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.export-controls h4 {
  margin-bottom: 12px;
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
}

.export-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.export-button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.export-button.csv {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.export-button.csv:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.export-button.json {
  background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%);
  color: white;
}

.export-button.json:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.stock-data-results {
  margin-top: 24px;
}

.stock-info-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stock-info-panel h3 {
  margin-bottom: 20px;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
}

.stock-basic-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item span:first-child {
  font-weight: 500;
  color: #4a5568;
}

.info-item span:last-child {
  color: #2d3748;
  font-weight: 600;
}

.stock-statistics .stat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stock-statistics .stat-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

.stat-value.positive {
  color: #48bb78;
}

.stat-value.negative {
  color: #f56565;
}

.chart-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-section h3 {
  margin-bottom: 20px;
  color: #2d3748;
  font-weight: 600;
}

.chart-placeholder {
  background: #f8f9fa;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  color: #6c757d;
}

.chart-placeholder p {
  margin: 8px 0;
  font-size: 14px;
}

.indicators-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicators-section h3 {
  margin-bottom: 20px;
  color: #2d3748;
  font-weight: 600;
}

.indicators-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.indicator-item {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicator-item h4 {
  margin-bottom: 16px;
  color: #2d3748;
  text-align: center;
  font-weight: 600;
  font-size: 16px;
}

.indicator-chart-placeholder {
  background: white;
  border: 2px dashed #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

.indicator-chart-placeholder p {
  margin: 4px 0;
}

/* K线图表样式 */
.kline-chart-container {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kline-chart-container .chart-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

.kline-chart-container .chart-info p {
  margin: 6px 0;
}

/* 技术指标图表样式 */
.indicator-chart-container {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.indicator-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
  color: #6c757d;
  font-size: 13px;
}

.indicator-info p {
  margin: 4px 0;
}

/* 响应式样式 */
/* 因子分析样式 */
.factor-analysis {
  padding: 24px;
  background: white;
  border-radius: 12px;
  margin-top: 20px;
}

.factor-analysis h3 {
  color: #2d3748;
  margin-bottom: 24px;
  font-weight: 600;
  font-size: 24px;
}

/* 配置面板 */
.analysis-config {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  color: #4a5568;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
}

/* 股票输入 */
.stock-input input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 12px;
}

.stock-input input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.selected-stocks {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stock-tag {
  background: #4299e1;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.remove-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 因子选择 */
.factor-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.factor-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  font-weight: 500;
}

.factor-checkbox:hover {
  border-color: #4299e1;
  background: #f7fafc;
}

.factor-checkbox input {
  margin: 0;
}

/* 时间范围选择 */
.timeframe-select {
  width: 200px;
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.timeframe-select:focus {
  outline: none;
  border-color: #4299e1;
}

/* 分析类型选择器 */
.analysis-type-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.analysis-type-btn {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.analysis-type-btn:hover {
  border-color: #4299e1;
  background: #f7fafc;
}

.analysis-type-btn.active {
  background: #4299e1;
  color: white;
  border-color: #4299e1;
}

/* 分析操作 */
.analysis-actions {
  text-align: center;
  margin-bottom: 32px;
}

.start-analysis-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.start-analysis-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.start-analysis-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 分析结果 */
.analysis-results {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
}

.analysis-results h4, .analysis-results h5 {
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
}

/* 相关性分析结果 */
.correlation-results {
  margin-bottom: 32px;
}

.correlation-matrix {
  overflow-x: auto;
  margin-bottom: 16px;
}

.correlation-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.correlation-table th,
.correlation-table td {
  padding: 8px 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.correlation-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

.factor-name {
  font-weight: 600;
  background: #f7fafc !important;
}

.correlation-cell.high-correlation {
  background: #fed7d7;
  color: #c53030;
  font-weight: 600;
}

.correlation-cell.medium-correlation {
  background: #fefcbf;
  color: #d69e2e;
  font-weight: 500;
}

.correlation-cell.low-correlation {
  background: #c6f6d5;
  color: #38a169;
}

.correlation-legend {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.legend-item {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.legend-item.high-correlation {
  background: #fed7d7;
  color: #c53030;
}

.legend-item.medium-correlation {
  background: #fefcbf;
  color: #d69e2e;
}

.legend-item.low-correlation {
  background: #c6f6d5;
  color: #38a169;
}

/* 收益分析结果 */
.returns-results {
  margin-bottom: 32px;
}

.returns-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.returns-table th,
.returns-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.returns-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

.returns-table .positive {
  color: #38a169;
  font-weight: 600;
}

.returns-table .negative {
  color: #e53e3e;
  font-weight: 600;
}

/* 有效性测试结果 */
.validity-results {
  margin-bottom: 32px;
}

.validity-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.validity-card {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.validity-card h6 {
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

.validity-score {
  margin-bottom: 20px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  font-size: 24px;
  font-weight: 700;
  color: white;
}

.score-circle.excellent {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.score-circle.good {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

.score-circle.fair {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.score-circle.poor {
  background: linear-gradient(135deg, #e53e3e, #c53030);
}

.validity-metrics {
  text-align: left;
}

.metric {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
}

.metric:last-child {
  border-bottom: none;
}

.metric span:first-child {
  color: #4a5568;
  font-weight: 500;
}

.metric span:last-child {
  color: #2d3748;
  font-weight: 600;
}

/* 风险分析结果 */
.risk-results {
  margin-bottom: 32px;
}

.risk-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.risk-table th,
.risk-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.risk-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

.risk-level.high {
  background: #fed7d7;
  color: #c53030;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.risk-level.medium {
  background: #fefcbf;
  color: #d69e2e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.risk-level.low {
  background: #c6f6d5;
  color: #38a169;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.high-risk {
  color: #e53e3e;
  font-weight: 600;
}

.risk-score-bar {
  position: relative;
  background: #e2e8f0;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.risk-score-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #48bb78, #ed8936, #e53e3e);
  transition: width 0.3s ease;
}

.risk-score-bar span {
  position: relative;
  z-index: 1;
  color: #2d3748;
  font-weight: 600;
  font-size: 12px;
}

/* 原始数据表格 */
.raw-data-section {
  margin-top: 32px;
}

.raw-data-table {
  overflow-x: auto;
}

.raw-data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.raw-data-table th,
.raw-data-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.raw-data-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
  position: sticky;
  top: 0;
}

.stock-symbol {
  font-weight: 600;
  background: #f7fafc !important;
  color: #2d3748;
}

@media (max-width: 768px) {
  .query-options {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .stock-basic-info {
    grid-template-columns: 1fr;
  }
  
  .stock-statistics .stat-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .indicators-checkboxes {
    grid-template-columns: 1fr;
  }
  
  .indicators-display {
    grid-template-columns: 1fr;
  }

  .kline-chart-container {
    padding: 15px;
    margin: 15px 0;
  }

  .indicators-display {
    grid-template-columns: 1fr;
  }

  /* 移动端因子分析样式调整 */
  .analysis-type-selector {
    grid-template-columns: repeat(2, 1fr);
  }

  .factor-selection {
    grid-template-columns: repeat(2, 1fr);
  }

  .validity-cards {
    grid-template-columns: 1fr;
  }

  .correlation-matrix,
  .raw-data-table {
    font-size: 12px;
  }

  .analysis-config {
    padding: 16px;
  }

  .factor-analysis {
    padding: 16px;
  }
}

/* 图表控件 */
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.stock-chart-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 200px;
}

.stock-chart-select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.view-chart-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.view-chart-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.view-chart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 因子图表区域 */
.factor-chart-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.factor-curve-container {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.factor-curve-container h4 {
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
  text-align: center;
}

.factor-curve-container h5 {
  color: #4a5568;
  margin: 16px 0 12px 0;
  font-weight: 600;
  font-size: 16px;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stock-chart-select {
    min-width: auto;
    width: 100%;
  }
  
  .view-chart-btn {
    width: 100%;
    text-align: center;
  }
  
  .factor-chart-section {
    padding: 16px;
    margin: 16px 0;
  }
  
  .factor-curve-container {
    padding: 16px;
  }
}
