/* Markdown 内容容器 */
.markdown-content {
  line-height: 1.6;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* 标题样式 */
.markdown-h1 {
  font-size: 2em;
  font-weight: 700;
  margin: 1.5em 0 0.8em 0;
  padding-bottom: 0.3em;
  border-bottom: 2px solid #e1e4e8;
  color: #1a1a1a;
}

.markdown-h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1.2em 0 0.6em 0;
  padding-bottom: 0.2em;
  border-bottom: 1px solid #e1e4e8;
  color: #2c3e50;
}

.markdown-h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  color: #34495e;
}

.markdown-h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 0.8em 0 0.4em 0;
  color: #34495e;
}

/* 段落样式 */
.markdown-p {
  margin: 0.8em 0;
  line-height: 1.7;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: #2c3e50;
}

.markdown-em {
  font-style: italic;
  color: #555;
}

/* 列表样式 */
.markdown-ul, .markdown-ol {
  margin: 0.8em 0;
  padding-left: 2em;
}

.markdown-li {
  margin: 0.3em 0;
  line-height: 1.6;
}

.markdown-ul .markdown-li {
  list-style-type: disc;
}

.markdown-ol .markdown-li {
  list-style-type: decimal;
}

/* 嵌套列表 */
.markdown-ul .markdown-ul .markdown-li {
  list-style-type: circle;
}

.markdown-ul .markdown-ul .markdown-ul .markdown-li {
  list-style-type: square;
}

/* 代码样式 */
.inline-code {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.9em;
  color: #d73a49;
}

.code-block {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 1em;
  margin: 1em 0;
  overflow-x: auto;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.9em;
  line-height: 1.45;
}

.code-block code {
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}

/* 表格样式 */
.table-wrapper {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-table {
  border-collapse: collapse;
  width: 100%;
  background-color: #fff;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  padding: 0.75em 1em;
  text-align: left;
  font-weight: 600;
  color: #24292e;
}

.markdown-table td {
  border: 1px solid #e1e4e8;
  padding: 0.75em 1em;
  color: #586069;
}

.markdown-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.markdown-table tr:hover {
  background-color: #f1f8ff;
}

/* 链接样式 */
.markdown-link {
  color: #0366d6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  color: #0366d6;
  border-bottom-color: #0366d6;
  text-decoration: none;
}

.markdown-link:visited {
  color: #6f42c1;
}

/* 引用样式 */
.markdown-blockquote {
  border-left: 4px solid #dfe2e5;
  padding: 0 1em;
  margin: 1em 0;
  color: #6a737d;
  background-color: #f8f9fa;
  border-radius: 0 3px 3px 0;
}

.markdown-blockquote .markdown-p {
  margin: 0.5em 0;
}

/* 分割线样式 */
.markdown-hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, #e1e4e8, #f6f8fa, #e1e4e8);
  margin: 2em 0;
  border-radius: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 0.9em;
  }
  
  .markdown-h1 {
    font-size: 1.8em;
  }
  
  .markdown-h2 {
    font-size: 1.4em;
  }
  
  .markdown-h3 {
    font-size: 1.2em;
  }
  
  .code-block {
    padding: 0.8em;
    font-size: 0.8em;
  }
  
  .markdown-table {
    font-size: 0.8em;
  }
  
  .markdown-table th,
  .markdown-table td {
    padding: 0.5em 0.7em;
  }
}

/* 特殊内容样式 */
.markdown-content .task-list-item {
  list-style-type: none;
  margin-left: -1.5em;
}

.markdown-content .task-list-item input {
  margin-right: 0.5em;
}

/* 数学公式样式（如果需要） */
.markdown-content .math {
  font-family: 'KaTeX_Main', 'Times New Roman', serif;
}

/* 高亮样式覆盖 */
.markdown-content .hljs {
  background: #f6f8fa !important;
  color: #24292e !important;
}

/* 特殊标记样式 */
.markdown-content mark {
  background-color: #fff3cd;
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

/* 键盘按键样式 */
.markdown-content kbd {
  background-color: #fafbfc;
  border: 1px solid #c6cbd1;
  border-bottom-color: #959da5;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #959da5;
  color: #444d56;
  display: inline-block;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.85em;
  line-height: 1;
  padding: 3px 5px;
  vertical-align: middle;
}

/* 删除线样式 */
.markdown-content del {
  text-decoration: line-through;
  color: #6a737d;
}

/* 下标和上标样式 */
.markdown-content sub,
.markdown-content sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.markdown-content sup {
  top: -0.5em;
}

.markdown-content sub {
  bottom: -0.25em;
} 