'use client';

import React, { useState } from 'react';
import { healthCheck, testConnectivity } from '@/utils/api';

const DebugPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  
  const addResult = (test: string, result: any, error?: any) => {
    const newResult = {
      test,
      timestamp: new Date().toISOString(),
      success: !error,
      result,
      error: error?.message || error,
    };
    setTestResults(prev => [newResult, ...prev]);
  };

  const testDirectFetch = async () => {
    try {
      const result = await testConnectivity();
      addResult('Direct Fetch Test', result);
    } catch (error) {
      addResult('Direct Fetch Test', null, error);
    }
  };

  const testAxiosHealth = async () => {
    try {
      const result = await healthCheck();
      addResult('Axios Health Check', result);
    } catch (error) {
      addResult('Axios Health Check', null, error);
    }
  };

  const testBackendDirect = async () => {
    try {
      const response = await fetch('http://localhost:8000/health');
      const data = await response.json();
      addResult('Direct Backend Test', data);
    } catch (error) {
      addResult('Direct Backend Test', null, error);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Debug Panel</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testDirectFetch}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Test Direct Fetch
        </button>
        
        <button
          onClick={testAxiosHealth}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Test Axios Health Check
        </button>
        
        <button
          onClick={testBackendDirect}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Test Backend Direct
        </button>
        
        <button
          onClick={() => setTestResults([])}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Clear Results
        </button>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results</h2>
        {testResults.map((result, index) => (
          <div
            key={index}
            className={`p-4 rounded border ${
              result.success 
                ? 'border-green-300 bg-green-50' 
                : 'border-red-300 bg-red-50'
            }`}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium">{result.test}</h3>
              <span className="text-sm text-gray-500">{result.timestamp}</span>
            </div>
            
            {result.success ? (
              <div>
                <span className="text-green-600 font-medium">✅ Success</span>
                <pre className="mt-2 text-sm bg-white p-2 rounded">
                  {JSON.stringify(result.result, null, 2)}
                </pre>
              </div>
            ) : (
              <div>
                <span className="text-red-600 font-medium">❌ Failed</span>
                <pre className="mt-2 text-sm bg-white p-2 rounded text-red-700">
                  {JSON.stringify(result.error, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DebugPage; 