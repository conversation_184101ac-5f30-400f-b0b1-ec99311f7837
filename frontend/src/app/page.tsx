'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import ReactECharts from 'echarts-for-react';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import MarkdownTest from '@/components/MarkdownTest';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorDisplay from '@/components/ErrorDisplay';
import FinancialDashboard from '@/components/FinancialDashboard';
import FinancialNews from '@/components/FinancialNews';
import ChatDebug from '@/components/ChatDebug';
import AppLayout from '@/components/AppLayout';
import AIAgentMode from '@/components/AIAgentMode';
import FactorManagementMode from '@/components/FactorManagementMode';
import DataQueryMode from '@/components/DataQueryMode';
import MLModelsMode from '@/components/MLModelsMode';
import StockScoringMode from '@/components/StockScoringMode';
import DivergenceScannerMode from '@/components/DivergenceScannerMode';
import { 
  useHealthCheck,
  useStockData,
  useFactors,
  useDivergenceScanner,
  useAutoRefresh
} from '@/hooks/useApi';
import { 
  Message, 
  StreamData, 
  Factor, 
  AppMode, 
  FactorMode, 
  MLMode, 
  ScoringMode, 
  DivergenceMode,
  StockBasicInfo,
  KlineData,
  StockStatistics,
  StockQueryResult,
  TechnicalIndicatorResult
} from '@/types';

const MainApp: React.FC = () => {
  const [mode, setMode] = useState<AppMode>('home');
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Chart tracking
  const [chartMessageIds, setChartMessageIds] = useState<Set<string>>(new Set());

  // 因子管理相关状态
  const [factors, setFactors] = useState<Factor[]>([]);
  const [selectedStock, setSelectedStock] = useState('AAPL');
  const [factorData, setFactorData] = useState<any>(null);
  const [factorMode, setFactorMode] = useState<FactorMode>('overview');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  
  // 自定义因子状态
  const [customFactorName, setCustomFactorName] = useState('');
  const [customFactorDesc, setCustomFactorDesc] = useState('');
  const [customFactorFormula, setCustomFactorFormula] = useState('');
  const [aiFactorPrompt, setAiFactorPrompt] = useState('');
  const [aiFactorResult, setAiFactorResult] = useState<any>(null);
  const [isGeneratingFactor, setIsGeneratingFactor] = useState(false);

  // ML模型相关状态
  const [mlMode, setMLMode] = useState<MLMode>('models');
  const [models, setModels] = useState<any[]>([]);
  const [trainingResult, setTrainingResult] = useState<any>(null);
  const [predictionResult, setPredictionResult] = useState<any>(null);
  const [trainingSymbols, setTrainingSymbols] = useState<string>('AAPL,MSFT,GOOGL');
  const [predictionSymbols, setPredictionSymbols] = useState<string>('TSLA,NVDA');
  const [selectedModelId, setSelectedModelId] = useState<string>('random_forest');

  // 股票评分相关状态
  const [scoringMode, setScoringMode] = useState<ScoringMode>('factor-score');
  const [scoreResults, setScoreResults] = useState<any>(null);
  const [rankingResults, setRankingResults] = useState<any>(null);
  const [rankingSymbols, setRankingSymbols] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX', 'CRM', 'ORCL']);

  // 背离扫描相关状态
  const [divergenceMode, setDivergenceMode] = useState<DivergenceMode>('scanner');
  const [selectedMarket, setSelectedMarket] = useState<string>('US');
  const [scanResults, setScanResults] = useState<any>(null);
  const [recentDivergences, setRecentDivergences] = useState<any[]>([]);
  const [scanHistory, setScanHistory] = useState<any[]>([]);
  const [isScanningDivergence, setIsScanningDivergence] = useState(false);

  // Note: Data query related states are now managed within DataQueryMode component via useEnhancedDataQuery hook

  // 因子分析相关状态
  const [factorAnalysisResults, setFactorAnalysisResults] = useState<any>(null);
  const [selectedStockForChart, setSelectedStockForChart] = useState<string>('');
  const [factorStockData, setFactorStockData] = useState<any>(null);
  const [showFactorChart, setShowFactorChart] = useState(false);
  const [selectedFactorsForAnalysis, setSelectedFactorsForAnalysis] = useState<string[]>([]);
  const [factorChartData, setFactorChartData] = useState<any>(null);
  const [isPerformingAnalysis, setIsPerformingAnalysis] = useState(false);
  const [analysisStockSuggestions, setAnalysisStockSuggestions] = useState<any[]>([]);
  const [showAnalysisStockSuggestions, setShowAnalysisStockSuggestions] = useState(false);
  
  // 分析输入相关状态
  const [analysisSearchInput, setAnalysisSearchInput] = useState<string>('');
  const [analysisStocks, setAnalysisStocks] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL']);
  const [analysisSearchSuggestions, setAnalysisSearchSuggestions] = useState<any[]>([]);
  const [showAnalysisSearchSuggestions, setShowAnalysisSearchSuggestions] = useState<boolean>(false);
  const [analysisTimeframe, setAnalysisTimeframe] = useState<string>('3m');
  const [analysisMode, setAnalysisMode] = useState<'correlation' | 'returns' | 'validity' | 'risk'>('correlation');
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [correlationMatrix, setCorrelationMatrix] = useState<any>(null);
  const [returnsData, setReturnsData] = useState<any>(null);
  const [validityMetrics, setValidityMetrics] = useState<any>(null);
  const [riskMetrics, setRiskMetrics] = useState<any>(null);
  
  // Additional missing states  
  const [isTraining, setIsTraining] = useState<boolean>(false);
  const [isPredicting, setIsPredicting] = useState<boolean>(false);
  const [isRanking, setIsRanking] = useState<boolean>(false);
  const [trainingStocks, setTrainingStocks] = useState<string[]>(['AAPL', 'MSFT', 'GOOGL']);
  const [predictionStocks, setPredictionStocks] = useState<string[]>(['TSLA', 'NVDA']);
  const [factorWeight, setFactorWeight] = useState<number>(0.6);
  const [mlWeight, setMlWeight] = useState<number>(0.4);
  
  // More missing states
  const [isScanning, setIsScanning] = useState<boolean>(false);

  // API hooks integration - automatic checks disabled to prevent log spam
  const { isHealthy, error: healthError, checkHealth, lastChecked } = useHealthCheck({
    autoCheck: false, // Disabled to prevent backend log spam
    interval: 300000, // 5 minutes - only for manual checks
    enableLogging: false // Disable console logging to reduce spam
  });
  const { 
    stockData: apiStockData, 
    technicalData: apiTechnicalData, 
    searchResults, 
    loading: stockLoading, 
    error: stockError,
    queryStock,
    queryTechnicalIndicators,
    searchStock
  } = useStockData();
  
  const {
    factors: apiFactors,
    factorData: apiFactorData,
    timeseriesData,
    loading: factorLoading,
    error: factorError,
    loadFactors,
    calculateFactorData
  } = useFactors();

  const {
    scanResults: apiScanResults,
    recentDivergences: apiRecentDivergences,
    loading: divergenceLoading,
    error: divergenceError,
    scanMarket,
    loadRecentDivergences
  } = useDivergenceScanner();

  // Note: Auto-refresh is now handled within DataQueryMode component

  // Effect to load initial data when component mounts
  useEffect(() => {
    // Load factors on startup
    loadFactors();
    
    // Load recent divergences for default market
    loadRecentDivergences('US', 24);
  }, [loadFactors, loadRecentDivergences]);

  // Note: Stock query and technical indicators functions are now handled within DataQueryMode component

  // Handle factor calculation
  const handleFactorCalculation = useCallback(async () => {
    if (!selectedStock.trim()) return;
    
    try {
      const result = await calculateFactorData({
        symbol: selectedStock.trim().toUpperCase(),
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        tushare_token: '', // Will use default token
      });
      setFactorData(result);
    } catch (error) {
      console.error('Factor calculation failed:', error);
    }
  }, [selectedStock, startDate, endDate, calculateFactorData]);

  // Handle divergence scan
  const handleDivergenceScan = useCallback(async () => {
    setIsScanningDivergence(true);
    try {
      const result = await scanMarket({
        market: selectedMarket,
        tushare_token: '', // Will use default token
        divergence_types: ['bullish', 'bearish'],
      });
      setScanResults(result);
    } catch (error) {
      console.error('Divergence scan failed:', error);
    } finally {
      setIsScanningDivergence(false);
    }
  }, [selectedMarket, scanMarket]);

  // Note: Stock and technical data state updates are now handled within DataQueryMode component

  useEffect(() => {
    if (apiFactors) {
      setFactors(apiFactors);
    }
  }, [apiFactors]);

  useEffect(() => {
    if (apiFactorData) {
      setFactorData(apiFactorData);
    }
  }, [apiFactorData]);

  useEffect(() => {
    if (apiScanResults) {
      setScanResults(apiScanResults);
    }
  }, [apiScanResults]);

  useEffect(() => {
    if (apiRecentDivergences) {
      setRecentDivergences(apiRecentDivergences);
    }
  }, [apiRecentDivergences]);

  // Note: Refresh time is now handled within DataQueryMode component

  return (
    <AppLayout
      currentMode={mode}
      onModeChange={setMode}
    >
      <div className="home-container">
        {/* Health Error Display */}
        {healthError && (
          <div className="mt-4">
            <ErrorDisplay 
              error={healthError} 
              onRetry={checkHealth}
            />
          </div>
        )}
        
        {/* Note: Quick Data Preview moved to respective mode components for better separation of concerns */}
        

        {/* Dashboard for Home Mode */}
        {mode === 'home' && (
          <div className="mt-8">
            <FinancialDashboard />
          </div>
        )}

        {/* Financial News Section for Home Mode - Positioned at Bottom */}
        {mode === 'home' && (
          <div className="mt-8">
            <FinancialNews />
          </div>
        )}

        {/* Conditional Rendering for Different Modes */}
        {mode === 'ai-agent' && <AIAgentMode 
          setMode={setMode}
        />}

        {mode === 'factor-management' && <FactorManagementMode
          factors={factors}
          setFactors={setFactors}
          selectedStock={selectedStock}
          setSelectedStock={setSelectedStock}
          factorData={factorData}
          setFactorData={setFactorData}
          factorMode={factorMode}
          setFactorMode={setFactorMode}
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
          customFactorName={customFactorName}
          setCustomFactorName={setCustomFactorName}
          customFactorDesc={customFactorDesc}
          setCustomFactorDesc={setCustomFactorDesc}
          customFactorFormula={customFactorFormula}
          setCustomFactorFormula={setCustomFactorFormula}
          aiFactorPrompt={aiFactorPrompt}
          setAiFactorPrompt={setAiFactorPrompt}
          aiFactorResult={aiFactorResult}
          setAiFactorResult={setAiFactorResult}
          isGeneratingFactor={isGeneratingFactor}
          setIsGeneratingFactor={setIsGeneratingFactor}
          factorAnalysisResults={factorAnalysisResults}
          setFactorAnalysisResults={setFactorAnalysisResults}
          selectedStockForChart={selectedStockForChart}
          setSelectedStockForChart={setSelectedStockForChart}
          factorStockData={factorStockData}
          setFactorStockData={setFactorStockData}
          showFactorChart={showFactorChart}
          setShowFactorChart={setShowFactorChart}
          selectedFactorsForAnalysis={selectedFactorsForAnalysis}
          setSelectedFactorsForAnalysis={setSelectedFactorsForAnalysis}
          factorChartData={factorChartData}
          setFactorChartData={setFactorChartData}
          isPerformingAnalysis={isPerformingAnalysis}
          setIsPerformingAnalysis={setIsPerformingAnalysis}
          analysisStockSuggestions={analysisStockSuggestions}
          setAnalysisStockSuggestions={setAnalysisStockSuggestions}
          showAnalysisStockSuggestions={showAnalysisStockSuggestions}
          setShowAnalysisStockSuggestions={setShowAnalysisStockSuggestions}
          analysisSearchInput={analysisSearchInput}
          setAnalysisSearchInput={setAnalysisSearchInput}
          analysisStocks={analysisStocks}
          setAnalysisStocks={setAnalysisStocks}
          analysisSearchSuggestions={analysisSearchSuggestions}
          setAnalysisSearchSuggestions={setAnalysisSearchSuggestions}
          showAnalysisSearchSuggestions={showAnalysisSearchSuggestions}
          setShowAnalysisSearchSuggestions={setShowAnalysisSearchSuggestions}
          analysisTimeframe={analysisTimeframe}
          setAnalysisTimeframe={setAnalysisTimeframe}
          analysisMode={analysisMode}
          setAnalysisMode={setAnalysisMode}
          isAnalyzing={isAnalyzing}
          setIsAnalyzing={setIsAnalyzing}
          analysisResults={analysisResults}
          setAnalysisResults={setAnalysisResults}
          correlationMatrix={correlationMatrix}
          setCorrelationMatrix={setCorrelationMatrix}
          returnsData={returnsData}
          setReturnsData={setReturnsData}
          validityMetrics={validityMetrics}
          setValidityMetrics={setValidityMetrics}
          riskMetrics={riskMetrics}
          setRiskMetrics={setRiskMetrics}
          setMode={setMode}
        />}

        {mode === 'data-query' && <DataQueryMode
          setMode={setMode}
        />}

        {mode === 'ml-models' && <MLModelsMode
          mlMode={mlMode}
          setMLMode={setMLMode}
          models={models}
          setModels={setModels}
          trainingResult={trainingResult}
          setTrainingResult={setTrainingResult}
          predictionResult={predictionResult}
          setPredictionResult={setPredictionResult}
          trainingSymbols={trainingSymbols}
          setTrainingSymbols={setTrainingSymbols}
          predictionSymbols={predictionSymbols}
          setPredictionSymbols={setPredictionSymbols}
          selectedModelId={selectedModelId}
          setSelectedModelId={setSelectedModelId}
          isTraining={isTraining}
          setIsTraining={setIsTraining}
          isPredicting={isPredicting}
          setIsPredicting={setIsPredicting}
          trainingStocks={trainingStocks}
          setTrainingStocks={setTrainingStocks}
          predictionStocks={predictionStocks}
          setPredictionStocks={setPredictionStocks}
          setMode={setMode}
        />}

        {mode === 'stock-scoring' && <StockScoringMode
          scoringMode={scoringMode}
          setScoringMode={setScoringMode}
          scoreResults={scoreResults}
          setScoreResults={setScoreResults}
          rankingResults={rankingResults}
          setRankingResults={setRankingResults}
          rankingSymbols={rankingSymbols}
          setRankingSymbols={setRankingSymbols}
          factorWeight={factorWeight}
          setFactorWeight={setFactorWeight}
          mlWeight={mlWeight}
          setMlWeight={setMlWeight}
          isRanking={isRanking}
          setIsRanking={setIsRanking}
          setMode={setMode}
        />}

        {mode === 'divergence-scanner' && <DivergenceScannerMode
          divergenceMode={divergenceMode}
          setDivergenceMode={setDivergenceMode}
          selectedMarket={selectedMarket}
          setSelectedMarket={setSelectedMarket}
          scanResults={scanResults}
          setScanResults={setScanResults}
          recentDivergences={recentDivergences}
          setRecentDivergences={setRecentDivergences}
          scanHistory={scanHistory}
          setScanHistory={setScanHistory}
          isScanningDivergence={isScanningDivergence}
          setIsScanningDivergence={setIsScanningDivergence}
          isScanning={isScanning}
          setIsScanning={setIsScanning}
          setMode={setMode}
        />}

        {mode === 'markdown-test' && (
          <div className="mode-container">
            <div className="mode-header">
              <button
                onClick={() => setMode('home')}
                className="back-button"
              >
                ← 返回首页
              </button>
            </div>
            <MarkdownTest />
          </div>
        )}

        {mode === 'debug' && (
          <div className="mode-container">
            <div className="mode-header">
              <button
                onClick={() => setMode('home')}
                className="back-button"
              >
                ← 返回首页
              </button>
            </div>
            <ChatDebug />
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default MainApp;
