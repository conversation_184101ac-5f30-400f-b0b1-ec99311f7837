'use client';

import React, { useState } from 'react';

export default function TestAPI() {
  const [results, setResults] = useState<any[]>([]);

  const addResult = (test: string, result: any, error?: any) => {
    setResults(prev => [...prev, {
      test,
      success: !error,
      result: result ? JSON.stringify(result, null, 2) : null,
      error: error ? JSON.stringify(error, null, 2) : null,
      timestamp: new Date().toISOString()
    }]);
  };

  const testDirectFetch = async () => {
    try {
      console.log('Testing direct fetch to http://localhost:8000/health');
      const response = await fetch('http://localhost:8000/health');
      const data = await response.json();
      addResult('Direct Fetch to Backend', data);
    } catch (error) {
      console.error('Direct fetch error:', error);
      addResult('Direct Fetch to Backend', null, error);
    }
  };

  const testAxios = async () => {
    try {
      console.log('Testing axios call');
      const { healthCheck } = await import('@/utils/api');
      const result = await healthCheck();
      addResult('Axios Health Check', result);
    } catch (error) {
      console.error('Axios error:', error);
      addResult('Axios Health Check', null, error);
    }
  };

  const testRelativeFetch = async () => {
    try {
      console.log('Testing relative fetch to /health');
      const response = await fetch('/health');
      const data = await response.json();
      addResult('Relative Fetch /health', data);
    } catch (error) {
      console.error('Relative fetch error:', error);
      addResult('Relative Fetch /health', null, error);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API Test Page</h1>
      
      <div className="space-x-4 mb-6">
        <button 
          onClick={testDirectFetch}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test Direct Fetch
        </button>
        
        <button 
          onClick={testAxios}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test Axios
        </button>
        
        <button 
          onClick={testRelativeFetch}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Test Relative Fetch
        </button>
        
        <button 
          onClick={() => setResults([])}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear
        </button>
      </div>

      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index} className={`p-4 border rounded ${result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'}`}>
            <div className="flex justify-between mb-2">
              <h3 className="font-bold">{result.test}</h3>
              <span className="text-sm text-gray-500">{result.timestamp}</span>
            </div>
            {result.success ? (
              <pre className="text-sm bg-white p-2 rounded overflow-auto">{result.result}</pre>
            ) : (
              <pre className="text-sm bg-white p-2 rounded overflow-auto text-red-600">{result.error}</pre>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 