/* ===== Visual Indicators System ===== */
/* Modern CSS-based indicators to replace skeuomorphic emojis */

/* Base indicator classes */
.indicator-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.indicator-dot {
  @apply indicator-base;
  border: 2px solid transparent;
}

.indicator-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
}

.indicator-bar {
  @apply inline-block rounded-full;
  transition: all 0.2s ease-in-out;
}

.indicator-shape {
  @apply indicator-base;
  border: 2px solid transparent;
}

/* Size variants */
.indicator-xs {
  width: 0.5rem;
  height: 0.5rem;
}

.indicator-sm {
  width: 0.75rem;
  height: 0.75rem;
}

.indicator-md {
  width: 1rem;
  height: 1rem;
}

.indicator-lg {
  width: 1.25rem;
  height: 1.25rem;
}

.indicator-xl {
  width: 1.5rem;
  height: 1.5rem;
}

/* Category-specific colors */

/* Analysis indicators - Blue */
.indicator-analysis {
  background-color: #3B82F6;
  border-color: #93C5FD;
}

.indicator-analysis.indicator-badge {
  @apply bg-blue-100 text-blue-700 border-blue-200;
}

.indicator-analysis:hover {
  background-color: #2563EB;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Chart indicators - Purple */
.indicator-chart {
  background-color: #8B5CF6;
  border-color: #C4B5FD;
}

.indicator-chart.indicator-badge {
  @apply bg-purple-100 text-purple-700 border-purple-200;
}

.indicator-chart:hover {
  background-color: #7C3AED;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

/* Recommendation indicators - Green */
.indicator-recommendation {
  background-color: #10B981;
  border-color: #86EFAC;
}

.indicator-recommendation.indicator-badge {
  @apply bg-green-100 text-green-700 border-green-200;
}

.indicator-recommendation:hover {
  background-color: #059669;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Warning indicators - Yellow */
.indicator-warning {
  background-color: #F59E0B;
  border-color: #FCD34D;
}

.indicator-warning.indicator-badge {
  @apply bg-yellow-100 text-yellow-700 border-yellow-200;
}

.indicator-warning:hover {
  background-color: #D97706;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* Error indicators - Red */
.indicator-error {
  background-color: #EF4444;
  border-color: #FCA5A5;
}

.indicator-error.indicator-badge {
  @apply bg-red-100 text-red-700 border-red-200;
}

.indicator-error:hover {
  background-color: #DC2626;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Data indicators - Indigo */
.indicator-data {
  background-color: #6366F1;
  border-color: #A5B4FC;
}

.indicator-data.indicator-badge {
  @apply bg-indigo-100 text-indigo-700 border-indigo-200;
}

.indicator-data:hover {
  background-color: #4F46E5;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

/* Navigation indicators - Gray */
.indicator-navigation {
  background-color: #6B7280;
  border-color: #D1D5DB;
}

.indicator-navigation.indicator-badge {
  @apply bg-gray-100 text-gray-700 border-gray-200;
}

.indicator-navigation:hover {
  background-color: #4B5563;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

/* Success indicators - Emerald */
.indicator-success {
  background-color: #059669;
  border-color: #6EE7B7;
}

.indicator-success.indicator-badge {
  @apply bg-emerald-100 text-emerald-700 border-emerald-200;
}

.indicator-success:hover {
  background-color: #047857;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

/* Research indicators - Teal */
.indicator-research {
  background-color: #0D9488;
  border-color: #5EEAD4;
}

.indicator-research.indicator-badge {
  @apply bg-teal-100 text-teal-700 border-teal-200;
}

.indicator-research:hover {
  background-color: #0F766E;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.4);
}

/* Priority indicators */
.indicator-priority-critical {
  @apply indicator-error;
  animation: pulse-critical 2s infinite;
}

.indicator-priority-high {
  @apply indicator-warning;
}

.indicator-priority-medium {
  @apply indicator-recommendation;
}

.indicator-priority-low {
  @apply indicator-navigation;
  opacity: 0.7;
}

/* Special shape variants */
.indicator-square {
  border-radius: 0.25rem;
}

.indicator-diamond {
  transform: rotate(45deg);
  border-radius: 0.125rem;
}

.indicator-triangle {
  width: 0;
  height: 0;
  border-radius: 0;
  border-left: 0.5rem solid transparent;
  border-right: 0.5rem solid transparent;
  border-bottom: 0.75rem solid currentColor;
}

/* Loading states */
.indicator-loading {
  @apply indicator-navigation;
  animation: pulse-loading 1.5s infinite;
}

.indicator-streaming {
  @apply indicator-analysis;
  animation: pulse-streaming 2s infinite;
}

/* Accessibility states */
.indicator-base:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

.indicator-base[aria-pressed="true"] {
  transform: scale(0.95);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes pulse-critical {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes pulse-loading {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@keyframes pulse-streaming {
  0%, 100% {
    background-color: #3B82F6;
  }
  50% {
    background-color: #60A5FA;
  }
}

/* Grouped indicators */
.indicator-group {
  @apply flex items-center space-x-1;
}

.indicator-cluster {
  @apply flex items-center -space-x-1;
}

.indicator-cluster .indicator-base {
  border: 2px solid white;
  z-index: 1;
}

.indicator-cluster .indicator-base:hover {
  z-index: 2;
}

/* Text labels for indicators */
.indicator-label {
  @apply text-xs font-medium ml-1;
  color: inherit;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .indicator-badge {
    @apply bg-opacity-20 border-opacity-30;
  }
  
  .indicator-cluster .indicator-base {
    border-color: #1F2937;
  }
  
  .indicator-base:focus {
    outline-color: #60A5FA;
  }
}

/* ===== Avatar Indicators ===== */
.vi-avatar {
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.vi-avatar:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.vi-avatar-user {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border-color: rgba(59, 130, 246, 0.3);
}

.vi-avatar-assistant {
  background: linear-gradient(135deg, #8B5CF6, #7C3AED);
  color: white;
  border-color: rgba(139, 92, 246, 0.3);
}

.vi-avatar-system {
  background: linear-gradient(135deg, #6B7280, #4B5563);
  color: white;
  border-color: rgba(107, 114, 128, 0.3);
}

.vi-avatar-text {
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Avatar focus states for accessibility */
.vi-avatar:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgb(59, 130, 246);
}

/* Dark mode avatar adjustments */
@media (prefers-color-scheme: dark) {
  .vi-avatar-user {
    background: linear-gradient(135deg, #2563EB, #1E40AF);
    border-color: rgba(37, 99, 235, 0.4);
  }
  
  .vi-avatar-assistant {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    border-color: rgba(124, 58, 237, 0.4);
  }
  
  .vi-avatar-system {
    background: linear-gradient(135deg, #4B5563, #374151);
    border-color: rgba(75, 85, 99, 0.4);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .indicator-lg {
    width: 1rem;
    height: 1rem;
  }
  
  .indicator-xl {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .indicator-badge {
    @apply px-1 py-0.5 text-xs;
  }
} 