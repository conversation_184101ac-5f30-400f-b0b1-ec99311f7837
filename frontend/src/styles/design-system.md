# Design System Documentation

## Typography and Color System Implementation - Task 2.5 ✅

This document outlines the comprehensive typography and color system implemented for the Cash Flow frontend application.

## Color Palette

### Primary Colors (Blue)
- `--color-primary-50` to `--color-primary-950`: Complete blue color scale
- Primary brand color: `--color-primary-600` (#2563eb)
- Used for: Primary buttons, links, focus states, brand elements

### Secondary Colors (Purple)
- `--color-secondary-50` to `--color-secondary-950`: Complete purple color scale
- Secondary brand color: `--color-secondary-600` (#9333ea)
- Used for: Accent elements, secondary actions, highlights

### Semantic Colors

#### Success (Green)
- `--color-success-50`, `--color-success-100`, `--color-success-200`
- Primary: `--color-success-500` (#22c55e), `--color-success-600` (#16a34a)
- Used for: Success messages, positive indicators, confirmation states

#### Warning (Yellow/Orange)
- `--color-warning-50`, `--color-warning-100`, `--color-warning-200`
- Primary: `--color-warning-500` (#f59e0b), `--color-warning-600` (#d97706)
- Used for: Warning messages, caution indicators, pending states

#### Error (Red)
- `--color-error-50`, `--color-error-100`, `--color-error-200`
- Primary: `--color-error-500` (#ef4444), `--color-error-600` (#dc2626)
- Used for: Error messages, destructive actions, validation errors

#### Info (Cyan)
- `--color-info-50`, `--color-info-100`, `--color-info-200`
- Primary: `--color-info-500` (#06b6d4), `--color-info-600` (#0891b2)
- Used for: Information messages, neutral notifications, tips

### Neutral Colors (Gray)
- `--color-gray-50` to `--color-gray-950`: Complete grayscale
- Used for: Text, borders, backgrounds, subtle elements

### Background Colors
- `--color-background-primary`: Main background (#ffffff)
- `--color-background-secondary`: Secondary background (#f9fafb)
- `--color-background-tertiary`: Tertiary background (#f3f4f6)
- `--color-background-overlay`: Modal/overlay background (rgba)

### Text Colors
- `--color-text-primary`: Primary text (#111827)
- `--color-text-secondary`: Secondary text (#4b5563)
- `--color-text-tertiary`: Tertiary text (#6b7280)
- `--color-text-inverse`: Inverse text (#ffffff)
- `--color-text-muted`: Muted text (#9ca3af)

### Border Colors
- `--color-border-primary`: Primary borders (#e5e7eb)
- `--color-border-secondary`: Secondary borders (#d1d5db)
- `--color-border-focus`: Focus state borders (#3b82f6)
- `--color-border-error`: Error state borders (#ef4444)

## Typography Scale

### Font Sizes
- `--font-size-xs`: 0.75rem (12px)
- `--font-size-sm`: 0.875rem (14px)
- `--font-size-base`: 1rem (16px)
- `--font-size-lg`: 1.125rem (18px)
- `--font-size-xl`: 1.25rem (20px)
- `--font-size-2xl`: 1.5rem (24px)
- `--font-size-3xl`: 1.875rem (30px)
- `--font-size-4xl`: 2.25rem (36px)
- `--font-size-5xl`: 3rem (48px)
- `--font-size-6xl`: 3.75rem (60px)

### Line Heights
- `--line-height-tight`: 1.25
- `--line-height-snug`: 1.375
- `--line-height-normal`: 1.5
- `--line-height-relaxed`: 1.625
- `--line-height-loose`: 2

### Font Weights
- `--font-weight-light`: 300
- `--font-weight-normal`: 400
- `--font-weight-medium`: 500
- `--font-weight-semibold`: 600
- `--font-weight-bold`: 700
- `--font-weight-extrabold`: 800

## Typography Utility Classes

### Display Text
```css
.text-display-2xl  /* 60px, extrabold, tight line-height */
.text-display-xl   /* 48px, bold, tight line-height */
.text-display-lg   /* 36px, bold, tight line-height */
```

### Headings
```css
.text-heading-xl   /* 30px, bold, snug line-height */
.text-heading-lg   /* 24px, semibold, snug line-height */
.text-heading-md   /* 20px, semibold, snug line-height */
.text-heading-sm   /* 18px, semibold, snug line-height */
```

### Body Text
```css
.text-body-lg      /* 18px, normal weight, relaxed line-height */
.text-body-md      /* 16px, normal weight, normal line-height */
.text-body-sm      /* 14px, normal weight, normal line-height */
```

### Specialized Text
```css
.text-caption      /* 12px, medium weight, uppercase, letter-spacing */
.text-label        /* 14px, medium weight, normal line-height */
```

## Color Utility Classes

### Text Colors
```css
.text-primary      /* Primary text color */
.text-secondary    /* Secondary text color */
.text-tertiary     /* Tertiary text color */
.text-inverse      /* Inverse text color */
.text-muted        /* Muted text color */

.text-brand-primary    /* Brand primary color */
.text-brand-secondary  /* Brand secondary color */

.text-success      /* Success text color */
.text-warning      /* Warning text color */
.text-error        /* Error text color */
.text-info         /* Info text color */
```

### Background Colors
```css
.bg-primary        /* Primary background */
.bg-secondary      /* Secondary background */
.bg-tertiary       /* Tertiary background */

.bg-brand-primary  /* Brand primary background */
.bg-brand-secondary /* Brand secondary background */

.bg-success        /* Success background (light) */
.bg-warning        /* Warning background (light) */
.bg-error          /* Error background (light) */
.bg-info           /* Info background (light) */
```

### Border Colors
```css
.border-primary    /* Primary border color */
.border-secondary  /* Secondary border color */
.border-focus      /* Focus state border color */
.border-error      /* Error state border color */
```

## Spacing System

### Spacing Scale
- `--spacing-xs`: 0.25rem (4px)
- `--spacing-sm`: 0.5rem (8px)
- `--spacing-md`: 0.75rem (12px)
- `--spacing-lg`: 1rem (16px)
- `--spacing-xl`: 1.25rem (20px)
- `--spacing-2xl`: 1.5rem (24px)
- `--spacing-3xl`: 2rem (32px)
- `--spacing-4xl`: 2.5rem (40px)
- `--spacing-5xl`: 3rem (48px)
- `--spacing-6xl`: 4rem (64px)

### Spacing Utilities
```css
.space-xs          /* 4px gap */
.space-sm          /* 8px gap */
.space-md          /* 12px gap */
.space-lg          /* 16px gap */
.space-xl          /* 20px gap */
.space-2xl         /* 24px gap */
.space-3xl         /* 32px gap */
```

## Layout Grid System

### Container Sizes
```css
.container-xs      /* 20rem max-width */
.container-sm      /* 24rem max-width */
.container-md      /* 28rem max-width */
.container-lg      /* 32rem max-width */
.container-xl      /* 36rem max-width */
.container-2xl     /* 42rem max-width */
.container-3xl     /* 48rem max-width */
.container-4xl     /* 56rem max-width */
.container-5xl     /* 64rem max-width */
.container-6xl     /* 72rem max-width */
.container-7xl     /* 80rem max-width */
```

## Component Base Styles

### Buttons
```css
.btn-base          /* Base button styles */
.btn-sm            /* Small button size */
.btn-md            /* Medium button size */
.btn-lg            /* Large button size */
.btn-primary       /* Primary button variant */
.btn-secondary     /* Secondary button variant */
```

### Cards
```css
.card-base         /* Base card styles with hover effects */
```

### Inputs
```css
.input-base        /* Base input styles with focus states */
```

## Design Tokens

### Border Radius
- `--radius-sm`: 0.25rem (4px)
- `--radius-md`: 0.375rem (6px)
- `--radius-lg`: 0.5rem (8px)
- `--radius-xl`: 0.75rem (12px)
- `--radius-2xl`: 1rem (16px)
- `--radius-full`: 9999px

### Shadows
- `--shadow-sm`: Subtle shadow
- `--shadow-md`: Medium shadow
- `--shadow-lg`: Large shadow
- `--shadow-xl`: Extra large shadow

### Transitions
- `--transition-fast`: 150ms ease-in-out
- `--transition-normal`: 250ms ease-in-out
- `--transition-slow`: 350ms ease-in-out

## Responsive Design

### Typography Scaling
On mobile devices (max-width: 640px):
- Display text scales down appropriately
- Maintains readability at smaller sizes
- Preserves hierarchy relationships

### Mobile-First Approach
- Base styles target mobile devices
- Progressive enhancement for larger screens
- Touch-friendly interaction targets (44px minimum)

## Dark Mode Support

### Automatic Dark Mode
- Respects `prefers-color-scheme: dark`
- Automatically adjusts background and text colors
- Maintains contrast ratios for accessibility

### Dark Mode Color Overrides
- Background colors: Dark grays and blacks
- Text colors: Light grays and whites
- Border colors: Adjusted for dark backgrounds
- Semantic colors: Maintained for consistency

## Accessibility Features

### WCAG Compliance
- Proper contrast ratios (4.5:1 for normal text, 3:1 for large text)
- Color is not the only means of conveying information
- Focus indicators for keyboard navigation

### Reduced Motion Support
- Respects `prefers-reduced-motion: reduce`
- Disables animations for users who prefer reduced motion
- Maintains functionality without animations

### High Contrast Support
- Respects `prefers-contrast: high`
- Increases border visibility
- Enhances text contrast

### Focus Management
```css
.focus-visible     /* Enhanced focus indicators */
```

## Usage Examples

### Typography
```html
<h1 class="text-display-xl text-primary">Main Heading</h1>
<h2 class="text-heading-lg text-secondary">Section Heading</h2>
<p class="text-body-md text-tertiary">Body text content</p>
<span class="text-caption text-muted">Caption text</span>
```

### Colors
```html
<div class="bg-primary border-primary">
  <p class="text-primary">Primary content</p>
</div>

<div class="bg-success border border-success">
  <p class="text-success">Success message</p>
</div>
```

### Components
```html
<button class="btn-base btn-md btn-primary">
  Primary Action
</button>

<div class="card-base p-6">
  <h3 class="text-heading-md">Card Title</h3>
  <p class="text-body-sm text-secondary">Card content</p>
</div>

<input class="input-base" placeholder="Enter text..." />
```

### Layout
```html
<div class="container-lg space-lg">
  <div class="space-md">
    <!-- Content with consistent spacing -->
  </div>
</div>
```

## Implementation Status

✅ **Complete Color Palette**: Modern, professional color system with semantic meanings
✅ **Typography Scale**: Comprehensive font size and weight system
✅ **Utility Classes**: Ready-to-use CSS classes for rapid development
✅ **Component Base Styles**: Consistent foundation for UI components
✅ **Responsive Design**: Mobile-first approach with proper scaling
✅ **Dark Mode Support**: Automatic dark mode with proper contrast
✅ **Accessibility**: WCAG compliant with reduced motion and high contrast support
✅ **Layout Grid**: Flexible container and spacing system

## Next Steps

The typography and color system is now complete and ready for use throughout the application. Developers can:

1. Use the utility classes for consistent styling
2. Reference the design tokens for custom components
3. Extend the system as needed while maintaining consistency
4. Leverage the responsive and accessibility features

This system provides a solid foundation for building professional, accessible, and maintainable user interfaces. 