export interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'chart';
  content: string;
  agent?: string;
  timestamp: number;
  isStreaming?: boolean;
  chartConfig?: any;
  chartData?: any;
}

export interface StreamData {
  type: 'message' | 'final_report' | 'error' | 'end' | 'agent_message' | 'workflow_complete' | 'done' | 'chart_data' | 'workflow_progress';
  content?: string;
  message?: string;
  agent?: string;
  timestamp?: string;
  
  // Chart data specific fields
  chart_data?: any;
  has_chart?: boolean;
  
  // Workflow progress specific fields
  step_name?: string;
  substep?: string;
  progress?: number;
  total_steps?: number;
  estimated_time?: number;
}

export interface Factor {
  id: string;
  name: string;
  category: 'technical' | 'fundamental' | 'capital' | 'chip';
  description: string;
  formula?: string;
  isCustom: boolean;
  isActive: boolean;
}

export type AppMode = 'home' | 'ai-agent' | 'factor-management' | 'ml-models' | 'stock-scoring' | 'divergence-scanner' | 'data-query' | 'markdown-test' | 'debug';
export type FactorMode = 'overview' | 'calculation' | 'custom' | 'analysis';
export type MLMode = 'models' | 'training' | 'prediction' | 'comparison';
export type ScoringMode = 'factor-score' | 'ml-score' | 'comprehensive' | 'ranking';
export type DivergenceMode = 'scanner' | 'results' | 'history';

export interface StockBasicInfo {
  symbol: string;
  name: string;
  market: string;
  industry: string;
}

export interface KlineData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  amount: number;
  pct_change: number;
}

export interface StockStatistics {
  latest_price: number;
  price_change: number;
  highest_price: number;
  lowest_price: number;
  price_range: number;
  avg_volume: number;
  total_records: number;
  date_range: {
    start: string;
    end: string;
  };
}

export interface StockQueryResult {
  success: boolean;
  symbol: string;
  basic_info: StockBasicInfo;
  statistics: StockStatistics;
  kline_data: KlineData[];
  data_source: string;
  timestamp: string;
}

export interface TechnicalIndicatorResult {
  success: boolean;
  symbol: string;
  period: string;
  indicators: Record<string, any>;
  timestamp: string;
}

// Enhanced Data Query Types
export interface StockNewsItem {
  id: string;
  title: string;
  content: string;
  source: string;
  timestamp: string;
  url?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface NewsQueryResult {
  success: boolean;
  symbol?: string;
  keyword?: string;
  news_items: StockNewsItem[];
  total_count: number;
  data_sources: Record<string, any>;
  timestamp: string;
  source_type?: 'tushare' | 'akshare' | 'fallback';
}

export interface MarketOverviewData {
  market_indices: Array<{
    name: string;
    value: number;
    change: number;
    change_percent: number;
    market: string;
  }>;
  sector_performance: Array<{
    sector: string;
    change_percent: number;
    volume: number;
    market_cap: number;
  }>;
  top_gainers: Array<{
    symbol: string;
    name: string;
    price: number;
    change_percent: number;
  }>;
  top_losers: Array<{
    symbol: string;
    name: string;
    price: number;
    change_percent: number;
  }>;
  market_status: {
    is_open: boolean;
    next_open: string;
    timezone: string;
  };
}

export interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  change_percent: number;
  added_date: string;
  alerts_enabled: boolean;
  target_price?: number;
  stop_loss?: number;
}

export interface DataQueryPreferences {
  default_period: string;
  auto_refresh: boolean;
  refresh_interval: number;
  default_indicators: string[];
  news_sources: string[];
  show_charts: boolean;
  chart_type: string;
}

export interface EnhancedDataQueryState {
  activeTab: 'stock-data' | 'market-news' | 'live-news' | 'technical-analysis' | 'market-overview';
  stockData: StockQueryResult | null;
  newsData: NewsQueryResult | null;
  technicalData: TechnicalIndicatorResult | null;
  marketOverview: MarketOverviewData | null;
  watchlist: WatchlistItem[];
  preferences: DataQueryPreferences;
  loading: {
    stock: boolean;
    news: boolean;
    technical: boolean;
    market: boolean;
  };
  errors: {
    stock: string | null;
    news: string | null;
    technical: string | null;
    market: string | null;
  };
} 