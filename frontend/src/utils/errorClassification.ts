// Error Classification and User-Friendly Message System
// This module provides comprehensive error classification and user feedback

export interface ErrorDetails {
  type: ErrorType;
  severity: ErrorSeverity;
  category: ErrorCategory;
  code: string;
  title: string;
  message: string;
  userMessage: string;
  suggestions: string[];
  canRetry: boolean;
  autoRetry: boolean;
  retryDelay?: number;
  maxRetries?: number;
  icon: string;
  color: string;
}

export type ErrorType = 
  | 'network' 
  | 'server' 
  | 'client' 
  | 'validation' 
  | 'timeout' 
  | 'auth' 
  | 'rate_limit' 
  | 'service_unavailable'
  | 'parsing'
  | 'streaming'
  | 'unknown';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export type ErrorCategory = 
  | 'connectivity' 
  | 'api' 
  | 'user_input' 
  | 'system' 
  | 'temporary' 
  | 'configuration';

// Predefined error templates for common issues
const ERROR_TEMPLATES: Record<string, Partial<ErrorDetails>> = {
  // Network Errors
  'ERR_NETWORK': {
    type: 'network',
    severity: 'high',
    category: 'connectivity',
    title: '网络连接错误',
    userMessage: '无法连接到服务器，请检查您的网络连接',
    suggestions: [
      '检查网络连接是否正常',
      '确认服务器地址是否正确',
      '尝试刷新页面',
      '联系技术支持'
    ],
    canRetry: true,
    autoRetry: true,
    retryDelay: 3000,
    maxRetries: 3,
    icon: '🌐',
    color: 'red'
  },

  'ERR_INTERNET_DISCONNECTED': {
    type: 'network',
    severity: 'critical',
    category: 'connectivity',
    title: '网络断开',
    userMessage: '检测到网络连接已断开',
    suggestions: [
      '检查WiFi或移动网络连接',
      '重新连接网络后页面将自动恢复',
      '确保设备网络功能正常'
    ],
    canRetry: false,
    autoRetry: false,
    icon: '📶',
    color: 'red'
  },

  // Server Errors
  'ERR_BAD_RESPONSE': {
    type: 'server',
    severity: 'medium',
    category: 'api',
    title: '服务器响应异常',
    userMessage: '服务器返回了无效的响应数据',
    suggestions: [
      '稍后重试',
      '检查输入数据是否正确',
      '联系技术支持报告此问题'
    ],
    canRetry: true,
    autoRetry: false,
    retryDelay: 5000,
    maxRetries: 2,
    icon: '🔧',
    color: 'orange'
  },

  'ERR_INTERNAL_SERVER': {
    type: 'server',
    severity: 'high',
    category: 'system',
    title: '服务器内部错误',
    userMessage: '服务器遇到内部错误，我们正在处理此问题',
    suggestions: [
      '请稍后重试',
      '问题已自动报告给技术团队',
      '如果问题持续，请联系技术支持'
    ],
    canRetry: true,
    autoRetry: true,
    retryDelay: 10000,
    maxRetries: 1,
    icon: '⚠️',
    color: 'red'
  },

  // Timeout Errors
  'ERR_TIMEOUT': {
    type: 'timeout',
    severity: 'medium',
    category: 'temporary',
    title: '请求超时',
    userMessage: '请求处理时间过长，已自动取消',
    suggestions: [
      '网络可能较慢，请重试',
      '简化您的查询内容',
      '检查网络连接速度'
    ],
    canRetry: true,
    autoRetry: false,
    retryDelay: 2000,
    maxRetries: 2,
    icon: '⏱️',
    color: 'yellow'
  },

  // Streaming Errors
  'ERR_STREAM_DISCONNECTED': {
    type: 'streaming',
    severity: 'medium',
    category: 'connectivity',
    title: '流式连接中断',
    userMessage: 'AI 响应流中断，正在尝试重新连接',
    suggestions: [
      '正在自动重连，请稍候',
      '如果问题持续，请刷新页面',
      '检查网络连接稳定性'
    ],
    canRetry: true,
    autoRetry: true,
    retryDelay: 1500,
    maxRetries: 5,
    icon: '🔄',
    color: 'blue'
  },

  'ERR_STREAM_PARSING': {
    type: 'parsing',
    severity: 'low',
    category: 'temporary',
    title: '数据解析错误',
    userMessage: '接收到的数据格式有误，已跳过',
    suggestions: [
      '这通常是临时问题',
      '如果影响使用，请重新发送消息',
      '系统会自动处理此类错误'
    ],
    canRetry: false,
    autoRetry: false,
    icon: '📝',
    color: 'gray'
  },

  // Rate Limiting
  'ERR_RATE_LIMIT': {
    type: 'rate_limit',
    severity: 'medium',
    category: 'temporary',
    title: '请求频率限制',
    userMessage: '请求过于频繁，请稍后再试',
    suggestions: [
      '请等待片刻后重试',
      '避免频繁发送请求',
      '系统将在限制解除后自动恢复'
    ],
    canRetry: true,
    autoRetry: true,
    retryDelay: 30000,
    maxRetries: 1,
    icon: '🚦',
    color: 'orange'
  },

  // Validation Errors
  'ERR_INVALID_INPUT': {
    type: 'validation',
    severity: 'low',
    category: 'user_input',
    title: '输入数据无效',
    userMessage: '请检查您的输入内容',
    suggestions: [
      '确保输入内容符合要求',
      '检查特殊字符和格式',
      '参考输入示例'
    ],
    canRetry: false,
    autoRetry: false,
    icon: '✏️',
    color: 'blue'
  },

  // Authentication Errors
  'ERR_UNAUTHORIZED': {
    type: 'auth',
    severity: 'high',
    category: 'configuration',
    title: '身份验证失败',
    userMessage: '访问被拒绝，请检查身份验证',
    suggestions: [
      '检查API密钥是否正确',
      '确认账户权限',
      '联系管理员获取帮助'
    ],
    canRetry: false,
    autoRetry: false,
    icon: '🔐',
    color: 'red'
  },

  // Service Unavailable
  'ERR_SERVICE_UNAVAILABLE': {
    type: 'service_unavailable',
    severity: 'high',
    category: 'system',
    title: '服务暂时不可用',
    userMessage: 'AI 服务正在维护中，请稍后重试',
    suggestions: [
      '服务正在维护或升级中',
      '请稍后重试',
      '关注系统状态更新'
    ],
    canRetry: true,
    autoRetry: true,
    retryDelay: 60000,
    maxRetries: 1,
    icon: '🔧',
    color: 'orange'
  }
};

// Error classification function
export const classifyError = (error: any): ErrorDetails => {
  // Default error details
  const defaultError: ErrorDetails = {
    type: 'unknown',
    severity: 'medium',
    category: 'system',
    code: 'ERR_UNKNOWN',
    title: '未知错误',
    message: error?.message || 'Unknown error occurred',
    userMessage: '发生了未知错误，请重试或联系技术支持',
    suggestions: [
      '请重试操作',
      '刷新页面',
      '联系技术支持'
    ],
    canRetry: true,
    autoRetry: false,
    icon: '❓',
    color: 'gray'
  };

  // Analyze error to determine type
  let errorCode = 'ERR_UNKNOWN';
  
  if (!navigator.onLine) {
    errorCode = 'ERR_INTERNET_DISCONNECTED';
  } else if (error?.name === 'TypeError' && error?.message?.includes('fetch')) {
    errorCode = 'ERR_NETWORK';
  } else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    errorCode = 'ERR_TIMEOUT';
  } else if (error?.response?.status) {
    const status = error.response.status;
    
    if (status >= 500) {
      errorCode = 'ERR_INTERNAL_SERVER';
    } else if (status === 429) {
      errorCode = 'ERR_RATE_LIMIT';
    } else if (status === 401 || status === 403) {
      errorCode = 'ERR_UNAUTHORIZED';
    } else if (status === 400) {
      errorCode = 'ERR_INVALID_INPUT';
    } else if (status === 503) {
      errorCode = 'ERR_SERVICE_UNAVAILABLE';
    } else if (status >= 400) {
      errorCode = 'ERR_BAD_RESPONSE';
    }
  } else if (error?.message?.includes('Failed to parse')) {
    errorCode = 'ERR_STREAM_PARSING';
  } else if (error?.message?.includes('stream') || error?.message?.includes('SSE')) {
    errorCode = 'ERR_STREAM_DISCONNECTED';
  } else if (error?.message?.includes('Network Error')) {
    errorCode = 'ERR_NETWORK';
  }

  // Get template and build final error details
  const template = ERROR_TEMPLATES[errorCode] || {};
  
  return {
    ...defaultError,
    ...template,
    code: errorCode,
    message: error?.message || template.message || defaultError.message,
  };
};

// Check if error should trigger auto-retry
export const shouldAutoRetry = (errorDetails: ErrorDetails, currentRetryCount: number = 0): boolean => {
  return errorDetails.autoRetry && 
         errorDetails.canRetry && 
         currentRetryCount < (errorDetails.maxRetries || 0);
};

// Get retry delay for exponential backoff
export const getRetryDelay = (errorDetails: ErrorDetails, retryCount: number = 0): number => {
  const baseDelay = errorDetails.retryDelay || 1000;
  
  // Exponential backoff for network errors
  if (errorDetails.type === 'network' || errorDetails.type === 'streaming') {
    return Math.min(baseDelay * Math.pow(2, retryCount), 30000);
  }
  
  return baseDelay;
};

// Get user-friendly error message with context
export const getContextualErrorMessage = (
  errorDetails: ErrorDetails, 
  context?: { action?: string; details?: string }
): string => {
  let message = errorDetails.userMessage;
  
  if (context?.action) {
    message = `在${context.action}时发生错误：${message}`;
  }
  
  if (context?.details) {
    message += `\n详细信息：${context.details}`;
  }
  
  return message;
};

// Error recovery suggestions based on context
export const getRecoverySuggestions = (
  errorDetails: ErrorDetails, 
  context?: { isStreaming?: boolean; hasInternet?: boolean }
): string[] => {
  let suggestions = [...errorDetails.suggestions];
  
  if (context?.isStreaming && errorDetails.type === 'network') {
    suggestions.unshift('尝试暂停后恢复流式响应');
  }
  
  if (!context?.hasInternet) {
    suggestions = ['请先连接到网络', ...suggestions.filter(s => !s.includes('刷新'))];
  }
  
  return suggestions;
};

// Export utility for easy access
export const ErrorClassification = {
  classify: classifyError,
  shouldAutoRetry,
  getRetryDelay,
  getContextualMessage: getContextualErrorMessage,
  getRecoverySuggestions,
  templates: ERROR_TEMPLATES
}; 