/**
 * Utility functions for handling images in message content
 */

// Regex patterns for detecting base64 images
const BASE64_IMAGE_PATTERNS = [
  // Standard data URL format
  /data:image\/(png|jpe?g|gif|webp|svg\+xml);base64,([A-Za-z0-9+/=]+)/gi,
  // Standalone base64 strings that might be images (with context clues)
  /(?:image|chart|graph|plot).*?([A-Za-z0-9+/=]{100,})/gi,
  // Base64 strings in markdown image syntax
  /!\[([^\]]*)\]\(data:image\/(png|jpe?g|gif|webp|svg\+xml);base64,([A-Za-z0-9+/=]+)\)/gi
];

// Common image file extensions
const IMAGE_EXTENSIONS = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'];

/**
 * Detects base64 images in text content
 */
export function detectBase64Images(content: string): Array<{
  match: string;
  format: string;
  data: string;
  alt?: string;
  startIndex: number;
  endIndex: number;
}> {
  const images: Array<{
    match: string;
    format: string;
    data: string;
    alt?: string;
    startIndex: number;
    endIndex: number;
  }> = [];

  // Check each pattern
  BASE64_IMAGE_PATTERNS.forEach(pattern => {
    let match;
    const regex = new RegExp(pattern.source, pattern.flags);
    
    while ((match = regex.exec(content)) !== null) {
      if (match[0].startsWith('data:image/')) {
        // Standard data URL format
        const format = match[1] || 'png';
        const data = match[2] || match[0];
        
        images.push({
          match: match[0],
          format,
          data: match[0], // Keep full data URL
          startIndex: match.index,
          endIndex: match.index + match[0].length
        });
      } else if (match[0].startsWith('![')) {
        // Markdown image with base64
        const alt = match[1] || '';
        const format = match[2] || 'png';
        const data = `data:image/${format};base64,${match[3]}`;
        
        images.push({
          match: match[0],
          format,
          data,
          alt,
          startIndex: match.index,
          endIndex: match.index + match[0].length
        });
      }
    }
  });

  // Sort by position to handle replacements correctly
  return images.sort((a, b) => a.startIndex - b.startIndex);
}

/**
 * Validates if a string is a valid base64 image
 */
export function isValidBase64Image(dataUrl: string): boolean {
  try {
    // Check if it's a data URL
    if (!dataUrl.startsWith('data:image/')) {
      return false;
    }

    // Extract the base64 part
    const base64Part = dataUrl.split(',')[1];
    if (!base64Part) {
      return false;
    }

    // Basic base64 validation
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Regex.test(base64Part)) {
      return false;
    }

    // Check minimum length (very small images should be at least a few hundred characters)
    return base64Part.length > 100;
  } catch (error) {
    return false;
  }
}

/**
 * Converts base64 images in content to proper markdown format
 */
export function formatBase64ImagesAsMarkdown(content: string): string {
  const images = detectBase64Images(content);
  
  if (images.length === 0) {
    return content;
  }

  let processedContent = content;
  let offset = 0;

  images.forEach((image, index) => {
    const { match, data, alt, startIndex, endIndex } = image;
    
    // Skip if already in markdown format
    if (match.startsWith('![')) {
      return;
    }

    // Create markdown image syntax
    const altText = alt || `Agent Generated Image ${index + 1}`;
    const markdownImage = `![${altText}](${data})`;

    // Replace in content
    const actualStart = startIndex + offset;
    const actualEnd = endIndex + offset;
    
    processedContent = 
      processedContent.slice(0, actualStart) + 
      markdownImage + 
      processedContent.slice(actualEnd);
    
    // Update offset for next replacements
    offset += markdownImage.length - match.length;
  });

  return processedContent;
}

/**
 * Extracts all images from content and returns them separately
 */
export function extractImagesFromContent(content: string): {
  textContent: string;
  images: Array<{
    data: string;
    alt: string;
    format: string;
  }>;
} {
  const images = detectBase64Images(content);
  
  if (images.length === 0) {
    return {
      textContent: content,
      images: []
    };
  }

  let textContent = content;
  const extractedImages: Array<{
    data: string;
    alt: string;
    format: string;
  }> = [];

  // Remove images from text content (in reverse order to maintain indices)
  images.reverse().forEach((image, index) => {
    const { match, data, format, alt, startIndex, endIndex } = image;
    
    // Add to extracted images
    extractedImages.unshift({
      data,
      alt: alt || `Image ${images.length - index}`,
      format
    });

    // Remove from text content
    textContent = textContent.slice(0, startIndex) + textContent.slice(endIndex);
  });

  return {
    textContent: textContent.trim(),
    images: extractedImages
  };
}

/**
 * Gets image dimensions from base64 data (approximate)
 */
export function getImageDimensions(dataUrl: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    img.src = dataUrl;
  });
}

/**
 * Compresses base64 image if it's too large
 */
export function compressBase64Image(
  dataUrl: string, 
  maxWidth: number = 800, 
  quality: number = 0.8
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      // Calculate new dimensions
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      try {
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedDataUrl);
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image for compression'));
    };
    
    img.src = dataUrl;
  });
} 