/**
 * URL编码处理工具函数
 * 专门处理Tushare新闻URL中的中文参数编码
 */

export interface TushareUrlParseResult {
  source: string | null;
  keyword: string | null;
  encodedKeyword: string | null;
  isValid: boolean;
}

/**
 * 编码Tushare URL中的中文参数
 */
export const encodeTushareUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams();
    
    // 重新编码所有参数，特别是中文参数
    urlObj.searchParams.forEach((value, key) => {
      searchParams.set(key, encodeURIComponent(value));
    });
    
    // 重建URL
    urlObj.search = searchParams.toString();
    return urlObj.toString();
  } catch (error) {
    console.warn('URL编码失败，返回原始URL:', error);
    return url;
  }
};

/**
 * 解码Tushare URL中的中文参数
 */
export const decodeTushareUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams();
    
    // 解码所有参数
    urlObj.searchParams.forEach((value, key) => {
      searchParams.set(key, decodeURIComponent(value));
    });
    
    // 重建URL（保持中文）
    const decodedPairs: string[] = [];
    searchParams.forEach((value, key) => {
      decodedPairs.push(`${key}=${value}`);
    });
    
    return `${urlObj.origin}${urlObj.pathname}?${decodedPairs.join('&')}`;
  } catch (error) {
    console.warn('URL解码失败，返回原始URL:', error);
    return url;
  }
};

/**
 * 检测是否为Tushare新闻URL格式
 */
export const isTushareNewsUrl = (input: string): boolean => {
  if (!input) return false;
  
  // 移除可能的@前缀
  const cleanInput = input.trim().replace(/^@/, '');
  
  // 检查URL格式
  const tusharePatterns = [
    /^https?:\/\/tushare\.pro\/news\/[^/]+\?s=.+/,
    /^tushare\.pro\/news\/[^/]+\?s=.+/
  ];
  
  return tusharePatterns.some(pattern => pattern.test(cleanInput));
};

/**
 * 解析Tushare新闻URL获取参数
 */
export const parseTushareNewsUrl = (url: string): TushareUrlParseResult => {
  const result: TushareUrlParseResult = {
    source: null,
    keyword: null,
    encodedKeyword: null,
    isValid: false
  };
  
  try {
    // 确保URL有协议
    let cleanUrl = url.replace(/^@/, '');
    if (!cleanUrl.startsWith('http')) {
      cleanUrl = 'https://' + cleanUrl;
    }
    
    const urlObj = new URL(cleanUrl);
    
    // 检查是否为Tushare域名
    if (!urlObj.hostname.includes('tushare.pro')) {
      return result;
    }
    
    // 提取新闻源
    const pathParts = urlObj.pathname.split('/').filter(part => part);
    if (pathParts.length >= 2 && pathParts[0] === 'news') {
      result.source = pathParts[1];
    }
    
    // 提取搜索关键词
    const keyword = urlObj.searchParams.get('s');
    if (keyword) {
      result.encodedKeyword = keyword;
      result.keyword = decodeURIComponent(keyword);
      result.isValid = true;
    }
    
    return result;
  } catch (error) {
    console.warn('解析Tushare URL失败:', error);
    return result;
  }
};

/**
 * 从Tushare URL格式的输入中提取关键词
 */
export const extractKeywordFromTushareUrl = (input: string): string | null => {
  if (!isTushareNewsUrl(input)) {
    return null;
  }
  
  const parsed = parseTushareNewsUrl(input);
  return parsed.keyword;
};

/**
 * 处理Tushare搜索输入，确保中文参数正确编码
 */
export const processTushareSearchInput = (input: string): string => {
  if (!input.trim()) return input;
  
  // 检查是否为Tushare URL格式
  if (isTushareNewsUrl(input)) {
    try {
      let cleanUrl = input.replace(/^@/, '');
      
      // 确保有协议
      if (!cleanUrl.startsWith('http')) {
        cleanUrl = 'https://' + cleanUrl;
      }
      
      // 编码URL
      const encodedUrl = encodeTushareUrl(cleanUrl);
      
      // 恢复@前缀（如果原来有的话）
      return input.startsWith('@') ? '@' + encodedUrl : encodedUrl;
    } catch (error) {
      console.warn('处理Tushare搜索输入失败:', error);
      return input;
    }
  }
  
  return input;
};

/**
 * 获取Tushare URL的显示格式（用于用户界面显示）
 */
export const getTushareUrlDisplayFormat = (input: string): string => {
  if (!isTushareNewsUrl(input)) {
    return input;
  }
  
  const parsed = parseTushareNewsUrl(input);
  if (parsed.isValid && parsed.keyword && parsed.source) {
    return `Tushare ${parsed.source} 搜索: ${parsed.keyword}`;
  }
  
  return input;
};

/**
 * 验证Tushare URL格式是否正确
 */
export const validateTushareUrl = (input: string): { isValid: boolean; error?: string } => {
  if (!input.trim()) {
    return { isValid: false, error: '输入不能为空' };
  }
  
  if (!isTushareNewsUrl(input)) {
    return { isValid: false, error: '不是有效的Tushare新闻URL格式' };
  }
  
  const parsed = parseTushareNewsUrl(input);
  if (!parsed.isValid) {
    return { isValid: false, error: '无法解析URL参数' };
  }
  
  if (!parsed.keyword) {
    return { isValid: false, error: '缺少搜索关键词参数(s)' };
  }
  
  if (!parsed.source) {
    return { isValid: false, error: '无法识别新闻源' };
  }
  
  return { isValid: true };
}; 