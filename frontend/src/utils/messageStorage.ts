import { EnhancedMessage, MessageStatus } from '@/hooks/useChatStreaming';

// Storage keys for different data types
const STORAGE_KEYS = {
  MESSAGES: 'chat_messages',
  CONVERSATIONS: 'chat_conversations',
  USER_PREFERENCES: 'chat_preferences',
  MESSAGE_DRAFTS: 'message_drafts',
} as const;

// Conversation metadata interface
export interface ConversationMetadata {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
  preview: string; // First user message or summary
  tags: string[];
  isBookmarked: boolean;
  isArchived: boolean;
}

// Enhanced message with additional storage metadata
export interface StoredMessage extends EnhancedMessage {
  conversationId: string;
  source: 'user' | 'api' | 'system' | 'imported';
  version: number; // For edit tracking
  originalId?: string; // For edited messages
  editHistory?: Array<{
    content: string;
    editedAt: number;
    reason?: string;
  }>;
  reactions?: Array<{
    type: 'like' | 'dislike' | 'helpful' | 'unhelpful';
    timestamp: number;
  }>;
  isDeleted: boolean;
  deletedAt?: number;
}

// User preferences for message management
export interface MessagePreferences {
  enablePersistentStorage: boolean;
  autoSaveConversations: boolean;
  maxStoredConversations: number;
  maxMessagesPerConversation: number;
  enableMessageEditing: boolean;
  enableMessageDeletion: boolean;
  showTimestamps: boolean;
  timestampFormat: '12h' | '24h' | 'relative';
  enableReadReceipts: boolean;
  enableTypingIndicators: boolean;
}

// Default user preferences
const DEFAULT_PREFERENCES: MessagePreferences = {
  enablePersistentStorage: true,
  autoSaveConversations: true,
  maxStoredConversations: 50,
  maxMessagesPerConversation: 1000,
  enableMessageEditing: true,
  enableMessageDeletion: true,
  showTimestamps: true,
  timestampFormat: 'relative',
  enableReadReceipts: true,
  enableTypingIndicators: true,
};

// Message validation and sanitization
export class MessageValidator {
  static validateMessage(message: Partial<StoredMessage>): boolean {
    return !!(
      message.id &&
      message.type &&
      message.content !== undefined &&
      message.timestamp &&
      typeof message.timestamp === 'number'
    );
  }

  static sanitizeMessage(message: Partial<StoredMessage>): StoredMessage | null {
    if (!this.validateMessage(message)) {
      return null;
    }

    return {
      id: message.id!,
      type: message.type!,
      content: String(message.content || '').trim(),
      timestamp: message.timestamp!,
      conversationId: message.conversationId || 'default',
      source: message.source || 'user',
      version: message.version || 1,
      isStreaming: Boolean(message.isStreaming),
      isDeleted: Boolean(message.isDeleted),
      status: message.status || 'sent',
      retryCount: message.retryCount || 0,
      agent: message.agent,
      metadata: message.metadata,
      displayContent: message.displayContent,
      rawContent: message.rawContent,
      error: message.error,
      chartData: message.chartData,
      chartConfig: message.chartConfig,
      editHistory: message.editHistory || [],
      reactions: message.reactions || [],
    };
  }
}

// Main message storage class
export class MessageStorage {
  private static instance: MessageStorage;
  private preferences: MessagePreferences;

  private constructor() {
    this.preferences = this.loadPreferences();
  }

  static getInstance(): MessageStorage {
    if (!MessageStorage.instance) {
      MessageStorage.instance = new MessageStorage();
    }
    return MessageStorage.instance;
  }

  // Helper to check if we're in the browser
  private isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof window.localStorage !== 'undefined';
  }

  loadPreferences(): MessagePreferences {
    if (!this.isBrowser()) {
      return DEFAULT_PREFERENCES;
    }
    
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (stored) {
        return { ...DEFAULT_PREFERENCES, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
    return DEFAULT_PREFERENCES;
  }

  savePreferences(preferences: Partial<MessagePreferences>): void {
    if (!this.isBrowser()) {
      console.warn('Cannot save preferences: localStorage not available');
      return;
    }
    
    try {
      this.preferences = { ...this.preferences, ...preferences };
      localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }

  getPreferences(): MessagePreferences {
    return { ...this.preferences };
  }

  // Message storage operations
  saveMessage(message: EnhancedMessage, conversationId: string = 'default'): boolean {
    if (!this.isBrowser() || !this.preferences.enablePersistentStorage) return false;

    try {
      const storedMessage: StoredMessage = {
        ...message,
        conversationId,
        source: message.type === 'user' ? 'user' : 'api',
        version: 1,
        isDeleted: false,
        editHistory: [],
        reactions: [],
      };

      const validatedMessage = MessageValidator.sanitizeMessage(storedMessage);
      if (!validatedMessage) {
        console.warn('Failed to validate message for storage:', message);
        return false;
      }

      const messages = this.loadMessages(conversationId);
      const existingIndex = messages.findIndex(m => m.id === message.id);

      if (existingIndex >= 0) {
        // Update existing message
        messages[existingIndex] = validatedMessage;
      } else {
        // Add new message
        messages.push(validatedMessage);
        
        // Enforce message limit per conversation
        if (messages.length > this.preferences.maxMessagesPerConversation) {
          messages.splice(0, messages.length - this.preferences.maxMessagesPerConversation);
        }
      }

      this.saveMessagesToStorage(conversationId, messages);
      this.updateConversationMetadata(conversationId, messages);
      
      return true;
    } catch (error) {
      console.error('Failed to save message:', error);
      return false;
    }
  }

  loadMessages(conversationId: string = 'default'): StoredMessage[] {
    if (!this.isBrowser() || !this.preferences.enablePersistentStorage) return [];

    try {
      const key = `${STORAGE_KEYS.MESSAGES}_${conversationId}`;
      const stored = localStorage.getItem(key);
      if (stored) {
        const messages = JSON.parse(stored) as StoredMessage[];
        return messages.filter(m => !m.isDeleted).sort((a, b) => a.timestamp - b.timestamp);
      }
    } catch (error) {
      console.warn('Failed to load messages:', error);
    }
    return [];
  }

  // Message editing operations
  editMessage(messageId: string, newContent: string, reason?: string, conversationId: string = 'default'): boolean {
    if (!this.isBrowser() || !this.preferences.enableMessageEditing) return false;

    try {
      const messages = this.loadMessages(conversationId);
      const messageIndex = messages.findIndex(m => m.id === messageId);
      
      if (messageIndex === -1) return false;

      const message = messages[messageIndex];
      
      // Add to edit history
      if (!message.editHistory) message.editHistory = [];
      message.editHistory.push({
        content: message.content,
        editedAt: Date.now(),
        reason,
      });

      // Update message
      message.content = newContent.trim();
      message.displayContent = newContent.trim();
      message.rawContent = newContent.trim();
      message.version = (message.version || 1) + 1;

      this.saveMessagesToStorage(conversationId, messages);
      return true;
    } catch (error) {
      console.error('Failed to edit message:', error);
      return false;
    }
  }

  // Message deletion operations
  deleteMessage(messageId: string, conversationId: string = 'default', permanent: boolean = false): boolean {
    if (!this.isBrowser() || !this.preferences.enableMessageDeletion) return false;

    try {
      const messages = this.loadMessages(conversationId);
      const messageIndex = messages.findIndex(m => m.id === messageId);
      
      if (messageIndex === -1) return false;

      if (permanent) {
        messages.splice(messageIndex, 1);
      } else {
        messages[messageIndex].isDeleted = true;
        messages[messageIndex].deletedAt = Date.now();
      }

      this.saveMessagesToStorage(conversationId, messages);
      this.updateConversationMetadata(conversationId, messages);
      return true;
    } catch (error) {
      console.error('Failed to delete message:', error);
      return false;
    }
  }

  // Message status updates
  updateMessageStatus(messageId: string, status: MessageStatus, conversationId: string = 'default'): boolean {
    if (!this.isBrowser()) return false;
    
    try {
      const messages = this.loadMessages(conversationId);
      const messageIndex = messages.findIndex(m => m.id === messageId);
      
      if (messageIndex === -1) return false;

      messages[messageIndex].status = status;
      this.saveMessagesToStorage(conversationId, messages);
      return true;
    } catch (error) {
      console.error('Failed to update message status:', error);
      return false;
    }
  }

  // Conversation management
  loadConversations(): ConversationMetadata[] {
    if (!this.isBrowser()) return [];
    
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
      if (stored) {
        return JSON.parse(stored) as ConversationMetadata[];
      }
    } catch (error) {
      console.warn('Failed to load conversations:', error);
    }
    return [];
  }

  createConversation(title?: string): string {
    if (!this.isBrowser()) {
      console.warn('Cannot create conversation: localStorage not available');
      return 'default';
    }
    
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const conversations = this.loadConversations();
    
    const metadata: ConversationMetadata = {
      id: conversationId,
      title: title || `对话 ${new Date().toLocaleDateString()}`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      messageCount: 0,
      preview: '',
      tags: [],
      isBookmarked: false,
      isArchived: false,
    };

    conversations.unshift(metadata);
    
    // Enforce conversation limit
    if (conversations.length > this.preferences.maxStoredConversations) {
      conversations.splice(this.preferences.maxStoredConversations);
    }

    try {
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
      return conversationId;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      return 'default';
    }
  }

  deleteConversation(conversationId: string): boolean {
    if (!this.isBrowser()) {
      console.warn('Cannot delete conversation: localStorage not available');
      return false;
    }
    
    try {
      // Delete messages
      const messageKey = `${STORAGE_KEYS.MESSAGES}_${conversationId}`;
      localStorage.removeItem(messageKey);

      // Delete conversation metadata
      const conversations = this.loadConversations();
      const filteredConversations = conversations.filter(c => c.id !== conversationId);
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(filteredConversations));

      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      return false;
    }
  }

  // Storage cleanup
  clearAllData(): void {
    if (!this.isBrowser()) {
      console.warn('Cannot clear data: localStorage not available');
      return;
    }
    
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      
      // Clear conversation-specific message storage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(STORAGE_KEYS.MESSAGES)) {
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }

  // Private helper methods
  private saveMessagesToStorage(conversationId: string, messages: StoredMessage[]): void {
    if (!this.isBrowser()) return;
    
    const key = `${STORAGE_KEYS.MESSAGES}_${conversationId}`;
    localStorage.setItem(key, JSON.stringify(messages));
  }

  private updateConversationMetadata(conversationId: string, messages: StoredMessage[]): void {
    if (!this.isBrowser()) return;
    
    const conversations = this.loadConversations();
    const conversationIndex = conversations.findIndex(c => c.id === conversationId);
    
    if (conversationIndex >= 0) {
      const activeMessages = messages.filter(m => !m.isDeleted);
      const firstUserMessage = activeMessages.find(m => m.type === 'user');
      
      conversations[conversationIndex].updatedAt = Date.now();
      conversations[conversationIndex].messageCount = activeMessages.length;
      conversations[conversationIndex].preview = firstUserMessage?.content.slice(0, 100) + '...' || '';
      
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
    }
  }

  // Session Hydration functionality
  saveConversationState(conversationId: string, messages: any[], isComplete: boolean = false): boolean {
    if (!this.isBrowser() || !this.preferences.enablePersistentStorage) return false;

    try {
      const conversationState = {
        conversationId,
        messages: messages.map(msg => ({
          ...msg,
          storedAt: Date.now()
        })),
        isComplete,
        lastUpdated: Date.now(),
        messageCount: messages.length
      };

      const key = `chat_conversation_state_${conversationId}`;
      localStorage.setItem(key, JSON.stringify(conversationState));

      // Also save hydration metadata
      const hydrationMeta = {
        conversationId,
        lastHydrationTime: Date.now(),
        conversationComplete: isComplete,
        messageCount: messages.length
      };
      localStorage.setItem(`chat_hydration_meta_${conversationId}`, JSON.stringify(hydrationMeta));

      return true;
    } catch (error) {
      console.error('Failed to save conversation state:', error);
      return false;
    }
  }

  loadConversationState(conversationId: string): { messages: any[]; isComplete: boolean; lastUpdated: number } | null {
    if (!this.isBrowser() || !this.preferences.enablePersistentStorage) return null;

    try {
      const key = `chat_conversation_state_${conversationId}`;
      const stored = localStorage.getItem(key);
      
      if (stored) {
        const state = JSON.parse(stored);
        return {
          messages: state.messages || [],
          isComplete: state.isComplete || false,
          lastUpdated: state.lastUpdated || 0
        };
      }
    } catch (error) {
      console.warn('Failed to load conversation state:', error);
    }
    return null;
  }

  isConversationComplete(conversationId: string): boolean {
    if (!this.isBrowser()) return false;

    try {
      const key = `chat_hydration_meta_${conversationId}`;
      const stored = localStorage.getItem(key);
      
      if (stored) {
        const meta = JSON.parse(stored);
        return meta.conversationComplete || false;
      }
    } catch (error) {
      console.warn('Failed to check conversation completion:', error);
    }
    return false;
  }

  clearConversationCache(conversationId: string): boolean {
    if (!this.isBrowser()) return false;

    try {
      localStorage.removeItem(`chat_conversation_state_${conversationId}`);
      localStorage.removeItem(`chat_hydration_meta_${conversationId}`);
      return true;
    } catch (error) {
      console.error('Failed to clear conversation cache:', error);
      return false;
    }
  }

  // Export/Import functionality
  exportConversation(conversationId: string): { metadata: ConversationMetadata; messages: StoredMessage[] } | null {
    if (!this.isBrowser()) {
      console.warn('Cannot export conversation: localStorage not available');
      return null;
    }
    
    try {
      const conversations = this.loadConversations();
      const metadata = conversations.find(c => c.id === conversationId);
      const messages = this.loadMessages(conversationId);
      
      if (metadata) {
        return { metadata, messages };
      }
    } catch (error) {
      console.error('Failed to export conversation:', error);
    }
    return null;
  }

  importConversation(data: { metadata: ConversationMetadata; messages: StoredMessage[] }): boolean {
    if (!this.isBrowser()) {
      console.warn('Cannot import conversation: localStorage not available');
      return false;
    }
    
    try {
      const { metadata, messages } = data;
      
      // Validate and sanitize messages
      const validMessages = messages
        .map(m => MessageValidator.sanitizeMessage(m))
        .filter(m => m !== null) as StoredMessage[];
      
      // Save messages
      this.saveMessagesToStorage(metadata.id, validMessages);
      
      // Save conversation metadata
      const conversations = this.loadConversations();
      conversations.unshift(metadata);
      localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
      
      return true;
    } catch (error) {
      console.error('Failed to import conversation:', error);
      return false;
    }
  }
}

// Export singleton instance
export const messageStorage = MessageStorage.getInstance(); 