// Visual Indicators Utility
// Modern CSS-based indicator system to replace emojis

export interface IndicatorConfig {
  type: 'dot' | 'badge' | 'bar' | 'shape';
  category: string;
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  label?: string;
  shape?: 'circle' | 'square' | 'diamond' | 'triangle';
  priority?: 'critical' | 'high' | 'medium' | 'low';
  state?: 'loading' | 'streaming' | 'default';
}

export interface IndicatorProps extends React.HTMLAttributes<HTMLElement> {
  config: IndicatorConfig;
  showLabel?: boolean;
  interactive?: boolean;
  ariaLabel?: string;
}

// Emoji to indicator mapping
export const emojiToIndicatorMap: Record<string, IndicatorConfig> = {
  // Chart and analysis
  '📊': { type: 'dot', category: 'chart', size: 'md', label: '图表' },
  '📈': { type: 'dot', category: 'analysis', size: 'md', label: '分析' },
  '📉': { type: 'dot', category: 'analysis', size: 'md', label: '分析' },
  
  // Financial
  '💰': { type: 'dot', category: 'success', size: 'md', label: '财务' },
  '💵': { type: 'dot', category: 'success', size: 'md', label: '金额' },
  '💲': { type: 'dot', category: 'success', size: 'md', label: '价格' },
  
  // Actions and tools
  '🔍': { type: 'dot', category: 'research', size: 'md', label: '搜索' },
  '📋': { type: 'dot', category: 'data', size: 'md', label: '数据' },
  '💡': { type: 'dot', category: 'recommendation', size: 'md', label: '建议' },
  '🚀': { type: 'dot', category: 'recommendation', size: 'md', label: '启动' },
  '⚡': { type: 'dot', category: 'warning', size: 'md', label: '快速' },
  '🎯': { type: 'dot', category: 'recommendation', size: 'md', label: '目标' },
  
  // Status indicators
  '⚠️': { type: 'dot', category: 'warning', size: 'md', label: '警告', priority: 'high' },
  '❌': { type: 'dot', category: 'error', size: 'md', label: '错误', priority: 'critical' },
  '✅': { type: 'dot', category: 'success', size: 'md', label: '成功' },
  '✓': { type: 'dot', category: 'success', size: 'sm', label: '完成' },
  '🔄': { type: 'dot', category: 'analysis', size: 'md', label: '处理中', state: 'streaming' },
  '⏳': { type: 'dot', category: 'navigation', size: 'md', label: '等待', state: 'loading' },
  
  // Technology
  '📱': { type: 'dot', category: 'navigation', size: 'md', label: '移动设备' },
  '💻': { type: 'dot', category: 'data', size: 'md', label: '计算机' },
  '🔧': { type: 'dot', category: 'navigation', size: 'md', label: '工具' },
  '⚙️': { type: 'dot', category: 'navigation', size: 'md', label: '设置' },
  
  // Navigation and controls
  '▶️': { type: 'shape', category: 'navigation', size: 'sm', label: '播放', shape: 'triangle' },
  '⏸️': { type: 'shape', category: 'navigation', size: 'sm', label: '暂停', shape: 'square' },
  '⏹️': { type: 'shape', category: 'navigation', size: 'sm', label: '停止', shape: 'square' },
  '▼️': { type: 'shape', category: 'navigation', size: 'sm', label: '展开', shape: 'triangle' },
  
  // Actions
  '🗑️': { type: 'dot', category: 'error', size: 'md', label: '删除' },
  '✏️': { type: 'dot', category: 'navigation', size: 'md', label: '编辑' },
  '🖼️': { type: 'dot', category: 'navigation', size: 'md', label: '图片' },
};

// Category to CSS class mapping
export const categoryClassMap: Record<string, string> = {
  analysis: 'indicator-analysis',
  chart: 'indicator-chart',
  recommendation: 'indicator-recommendation',
  warning: 'indicator-warning',
  error: 'indicator-error',
  data: 'indicator-data',
  navigation: 'indicator-navigation',
  success: 'indicator-success',
  research: 'indicator-research',
};

// Generate CSS classes for indicator
export const getIndicatorClasses = (config: IndicatorConfig): string => {
  const baseClasses = [`indicator-${config.type}`, `indicator-${config.size}`];
  
  // Add category class
  const categoryClass = categoryClassMap[config.category];
  if (categoryClass) {
    baseClasses.push(categoryClass);
  }
  
  // Add priority class
  if (config.priority) {
    baseClasses.push(`indicator-priority-${config.priority}`);
  }
  
  // Add state class
  if (config.state) {
    baseClasses.push(`indicator-${config.state}`);
  }
  
  // Add shape class
  if (config.shape && config.shape !== 'circle') {
    baseClasses.push(`indicator-${config.shape}`);
  }
  
  return baseClasses.join(' ');
};

// Convert emoji to indicator component props
export const emojiToIndicator = (emoji: string): IndicatorConfig | null => {
  return emojiToIndicatorMap[emoji] || null;
};

// Generate indicator from message type
export const getMessageTypeIndicator = (messageType: string): IndicatorConfig => {
  const typeMap: Record<string, IndicatorConfig> = {
    analysis: { type: 'badge', category: 'analysis', size: 'md', label: '分析' },
    recommendation: { type: 'badge', category: 'recommendation', size: 'md', label: '建议' },
    warning: { type: 'badge', category: 'warning', size: 'md', label: '警告' },
    error: { type: 'badge', category: 'error', size: 'md', label: '错误' },
    chart: { type: 'badge', category: 'chart', size: 'md', label: '图表' },
    summary: { type: 'badge', category: 'data', size: 'md', label: '总结' },
    data: { type: 'badge', category: 'data', size: 'md', label: '数据' },
  };
  
  return typeMap[messageType] || typeMap.analysis;
};

// Generate indicator from agent type
export const getAgentIndicator = (agentType: string): IndicatorConfig => {
  const agentMap: Record<string, IndicatorConfig> = {
    researcher: { type: 'badge', category: 'research', size: 'md', label: '研究员' },
    analyst: { type: 'badge', category: 'analysis', size: 'md', label: '分析师' },
    advisor: { type: 'badge', category: 'recommendation', size: 'md', label: '顾问' },
    programmer: { type: 'badge', category: 'data', size: 'md', label: '程序员' },
    coordinator: { type: 'badge', category: 'navigation', size: 'md', label: '协调员' },
    validator: { type: 'badge', category: 'success', size: 'md', label: '验证员' },
    risk_manager: { type: 'badge', category: 'warning', size: 'md', label: '风险管理员' },
  };
  
  const normalizedType = agentType.toLowerCase().replace('_agent', '').replace('agent', '');
  return agentMap[normalizedType] || agentMap.analyst;
};

// Generate indicator from priority level
export const getPriorityIndicator = (priority: string): IndicatorConfig => {
  const priorityMap: Record<string, IndicatorConfig> = {
    critical: { type: 'dot', category: 'error', size: 'sm', priority: 'critical' },
    high: { type: 'dot', category: 'warning', size: 'sm', priority: 'high' },
    medium: { type: 'dot', category: 'success', size: 'sm', priority: 'medium' },
    low: { type: 'dot', category: 'navigation', size: 'sm', priority: 'low' },
  };
  
  return priorityMap[priority] || priorityMap.medium;
};

// Generate indicator from status
export const getStatusIndicator = (status: string): IndicatorConfig => {
  const statusMap: Record<string, IndicatorConfig> = {
    sending: { type: 'dot', category: 'navigation', size: 'sm', state: 'loading', label: '发送中' },
    sent: { type: 'dot', category: 'success', size: 'sm', label: '已发送' },
    streaming: { type: 'dot', category: 'analysis', size: 'sm', state: 'streaming', label: '接收中' },
    complete: { type: 'dot', category: 'success', size: 'sm', label: '完成' },
    failed: { type: 'dot', category: 'error', size: 'sm', label: '失败' },
    cancelled: { type: 'dot', category: 'navigation', size: 'sm', label: '已取消' },
    timeout: { type: 'dot', category: 'warning', size: 'sm', label: '超时' },
  };
  
  return statusMap[status] || statusMap.complete;
};

// Utility function to create accessible indicator attributes
export const getIndicatorAriaAttributes = (config: IndicatorConfig): Record<string, string> => {
  const attributes: Record<string, string> = {
    role: 'img',
    'aria-label': config.label || '指示器',
  };
  
  if (config.priority === 'critical') {
    attributes['aria-live'] = 'assertive';
  } else if (config.priority === 'high') {
    attributes['aria-live'] = 'polite';
  }
  
  return attributes;
};

// Helper function to replace emoji text with indicator markup
export const replaceEmojiWithIndicator = (text: string): string => {
  let result = text;
  
  Object.keys(emojiToIndicatorMap).forEach(emoji => {
    const config = emojiToIndicatorMap[emoji];
    const classes = getIndicatorClasses(config);
    const ariaAttrs = getIndicatorAriaAttributes(config);
    
    const indicatorMarkup = `<span class="${classes}" ${Object.entries(ariaAttrs).map(([key, value]) => `${key}="${value}"`).join(' ')}></span>`;
    
    result = result.replace(new RegExp(emoji, 'g'), indicatorMarkup);
  });
  
  return result;
};

// Helper function to get navigation icon indicator
export const getNavigationIndicator = (iconType: string): IndicatorConfig => {
  const navMap: Record<string, IndicatorConfig> = {
    dashboard: { type: 'dot', category: 'chart', size: 'md', label: '仪表盘' },
    search: { type: 'dot', category: 'research', size: 'md', label: '搜索' },
    analysis: { type: 'dot', category: 'analysis', size: 'md', label: '分析' },
    settings: { type: 'dot', category: 'navigation', size: 'md', label: '设置' },
    tools: { type: 'dot', category: 'navigation', size: 'md', label: '工具' },
  };
  
  return navMap[iconType] || navMap.dashboard;
}; 