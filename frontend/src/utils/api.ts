import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { 
  StockQueryResult, 
  TechnicalIndicatorResult,
  Message,
  StreamData,
  NewsQueryResult,
  MarketOverviewData
} from '@/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const TUSHARE_TOKEN = process.env.NEXT_PUBLIC_TUSHARE_TOKEN || '';

// Create axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth headers if needed
apiClient.interceptors.request.use(
  (config) => {
    console.log('📤 Making API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      headers: config.headers
    });
    // Add any auth tokens here if needed
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    const errorInfo = {
      message: error.message || 'Unknown error',
      status: error.response?.status || 'No status',
      statusText: error.response?.statusText || 'No status text',
      data: error.response?.data || 'No response data',
      url: error.config?.url || 'No URL',
      method: error.config?.method || 'No method',
      baseURL: error.config?.baseURL || 'No base URL',
      code: error.code || 'No error code',
      cause: error.cause || 'No cause',
      isNetworkError: !error.response,
      isTimeoutError: error.code === 'ECONNABORTED'
    };
    
    console.error('🚨 API Request Failed:', {
      url: `${errorInfo.baseURL}${errorInfo.url}`,
      method: errorInfo.method?.toUpperCase(),
      status: errorInfo.status,
      message: errorInfo.message,
      isNetworkError: errorInfo.isNetworkError,
      isTimeoutError: errorInfo.isTimeoutError
    });
    
    console.error('📋 Full Error Details:', errorInfo);
    
    // Log additional context for debugging
    if (errorInfo.isNetworkError) {
      console.error('🌐 Network Error - Check if backend server is running on', errorInfo.baseURL);
      console.error('💡 Debug: Try curl -X GET', `${errorInfo.baseURL}/health`, 'to test backend connectivity');
    }
    
    // Enhanced error context for news search
    if (errorInfo.url?.includes('/news/search')) {
      console.error('📰 News Search Debug Info:', {
        'Expected Backend Port': '8000',
        'Frontend API URL': errorInfo.baseURL,
        'Request Data': error.config?.data,
        'Suggestion': 'Check if backend is running on port 8000 with: ps aux | grep uvicorn'
      });
    }
    
    return Promise.reject(error);
  }
);

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.response) {
    return error.response.data?.detail || error.response.data?.message || 'Server error';
  } else if (error.request) {
    return 'Network error - please check your connection';
  } else {
    return error.message || 'Unknown error occurred';
  }
};

// Health check with NO logging to prevent backend log spam
export const healthCheck = async (): Promise<any> => {
  try {
    const response = await apiClient.get('/health');
    return response.data;
  } catch (error: any) {
    // Silent health checks - no console logging to prevent spam
    throw error;
  }
};

// Test connectivity with simple fetch
export const testConnectivity = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if (response.ok) {
      const data = await response.json();
      console.log('Direct fetch test successful:', data);
      return true;
    } else {
      console.error('Direct fetch failed with status:', response.status);
      return false;
    }
  } catch (error) {
    console.error('Direct fetch error:', error);
    return false;
  }
};

// ===== AI Chat API =====
export interface ChatRequest {
  message: string;
  max_plan_iterations?: number;
  max_step_num?: number;
  enable_background_investigation?: boolean;
  debug?: boolean;
}

export const streamChat = async (
  request: ChatRequest,
  onMessage: (data: StreamData) => void,
  onError: (error: string) => void
): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Failed to get response reader');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            onMessage(data);
          } catch (e) {
            console.warn('Failed to parse SSE data:', line);
          }
        }
      }
    }
  } catch (error) {
    onError(handleApiError(error));
  }
};

// ===== Factor Management API =====
export interface FactorRequest {
  symbol: string;
  tushare_token: string;
  start_date?: string;
  end_date?: string;
  factors?: string[];
}

export interface AIFactorRequest {
  description: string;
}

export const getFactors = async (): Promise<any> => {
  const response = await apiClient.get('/factors');
  return response.data;
};

export const calculateFactors = async (request: FactorRequest): Promise<any> => {
  const response = await apiClient.post('/factors/calculate', request);
  return response.data;
};

export const calculateFactorsSmart = async (request: FactorRequest): Promise<any> => {
  const response = await apiClient.post('/factors/calculate/smart', request);
  return response.data;
};

export const getFactorTimeseries = async (request: FactorRequest): Promise<any> => {
  const response = await apiClient.post('/factors/timeseries', request);
  return response.data;
};

export const generateAIFactor = async (request: AIFactorRequest): Promise<any> => {
  const response = await apiClient.post('/factors/ai-generate', request);
  return response.data;
};

export const testCustomFactor = async (request: {
  symbol: string;
  formula: string;
  tushare_token: string;
}): Promise<any> => {
  const response = await apiClient.post('/factors/test-custom', request);
  return response.data;
};

// ===== Stock Data Query API =====
export interface StockDataRequest {
  symbol: string;
  tushare_token: string;
  period?: string;
  start_date?: string;
  end_date?: string;
}

export interface TechnicalIndicatorRequest {
  symbol: string;
  tushare_token: string;
  indicators: string[];
  period?: string;
}

export const queryStockData = async (request: StockDataRequest): Promise<StockQueryResult> => {
  const response = await apiClient.post('/stocks/query', request);
  return response.data;
};

export const getTechnicalIndicators = async (request: TechnicalIndicatorRequest): Promise<TechnicalIndicatorResult> => {
  const response = await apiClient.post('/stocks/technical-indicators', request);
  return response.data;
};

export const searchStocks = async (query: string): Promise<any> => {
  const response = await apiClient.get(`/stocks/search/${encodeURIComponent(query)}`);
  return response.data;
};

// Enhanced Data Query APIs
export interface NewsRequest {
  symbol: string;
  include_general_news?: boolean;
  date_for_reports?: string;
  max_rows_per_source?: number;
}

export interface NewsSearchRequest {
  keyword: string;
  max_rows?: number;
  use_tushare?: boolean;
  tushare_source?: string;
  tushare_token?: string;
}

export const getStockNews = async (request: NewsRequest): Promise<NewsQueryResult> => {
  const response = await apiClient.post('/stocks/news', request);
  return response.data;
};

export const searchNews = async (request: NewsSearchRequest): Promise<NewsQueryResult> => {
  // 检测是否为Tushare URL格式
  const isTushareFormat = request.keyword.includes('@https://tushare.pro/news/') || 
                         request.keyword.includes('tushare.pro/news/');
  
  // 如果是Tushare格式，设置相应参数
  const requestBody = {
    ...request,
    use_tushare: isTushareFormat || request.use_tushare,
    tushare_source: request.tushare_source || 'sina'
  };

  console.log('📡 News search API call:', {
    endpoint: '/news/search',
    baseURL: API_BASE_URL,
    fullURL: `${API_BASE_URL}/news/search`,
    requestBody
  });

  const response = await apiClient.post('/news/search', requestBody);
  return response.data;
};

// ===== Live News API =====
export interface LiveNewsRequest {
  sources?: string;
  max_rows_per_source?: number;
  hours_back?: number;
  max_total_rows?: number;
}

export const queryLiveNews = async (request: LiveNewsRequest): Promise<any> => {
  console.log('📺 [API] 开始实时新闻请求:', request);
  
  try {
    const liveNewsData = {
      sources: request.sources || "",
      max_rows_per_source: request.max_rows_per_source || 10,
      hours_back: request.hours_back || 6,
      max_total_rows: request.max_total_rows || 50
    };
    
    console.log('📤 [API] 发送实时新闻数据:', liveNewsData);
    
    const response = await apiClient.post('/news/live', liveNewsData);
    
    console.log('📦 [API] 收到实时新闻响应状态:', response.status);
    console.log('📦 [API] 收到实时新闻响应数据:', response.data);
    
    if (response.data.success) {
      console.log('✅ [API] 实时新闻获取成功');
      return response.data.live_data;
    } else {
      console.error('❌ [API] 实时新闻获取失败:', response.data);
      throw new Error(response.data.error || response.data.message || '实时新闻获取失败');
    }
  } catch (error: any) {
    console.error('💥 [API] 实时新闻异常:', error);
    
    // 详细错误分析
    if (error.code === 'ECONNREFUSED') {
      console.error('🚫 [API] 连接拒绝 - 后端服务可能未启动');
      console.error('🔧 [API] 请检查: uvicorn backend.server:app --host localhost --port 8000');
    } else if (error.response?.status === 404) {
      console.error('🔍 [API] 404错误 - API端点未找到');
      console.error('🔧 [API] 请检查: /news/live 端点是否正确');
    } else if (error.response?.status === 500) {
      console.error('⚡ [API] 500错误 - 服务器内部错误');
      console.error('🔧 [API] 请检查后端日志: tail -f backend.log');
    }
    
    throw error;
  }
};

export const queryNewsSourcesSummary = async (): Promise<any> => {
  console.log('📊 [API] 开始获取新闻源摘要');
  
  try {
    const response = await apiClient.get('/news/sources/summary');
    
    console.log('📦 [API] 收到新闻源摘要响应:', response.data);
    
    if (response.data.success) {
      console.log('✅ [API] 新闻源摘要获取成功');
      return response.data.summary;
    } else {
      console.error('❌ [API] 新闻源摘要获取失败:', response.data);
      throw new Error('新闻源摘要获取失败');
    }
  } catch (error: any) {
    console.error('💥 [API] 新闻源摘要异常:', error);
    throw error;
  }
};

export const getMarketOverview = async (market?: string): Promise<MarketOverviewData> => {
  const response = await apiClient.get(`/market/overview${market ? `?market=${market}` : ''}`);
  return response.data;
};

export interface WatchlistRequest {
  action: 'add' | 'remove' | 'update' | 'list';
  symbol?: string;
  target_price?: number;
  stop_loss?: number;
  alerts_enabled?: boolean;
}

export const manageWatchlist = async (request: WatchlistRequest): Promise<any> => {
  const response = await apiClient.post('/watchlist/manage', request);
  return response.data;
};

export interface DataExportRequest {
  type: 'stock-data' | 'news' | 'technical-indicators';
  symbol: string;
  format: 'csv' | 'json';
  period?: string;
  indicators?: string[];
}

export const exportData = async (request: DataExportRequest): Promise<Blob> => {
  const response = await apiClient.post('/data/export', request, {
    responseType: 'blob'
  });
  return response.data;
};

// Real-time market status
export const getMarketStatus = async (market?: string): Promise<any> => {
  const response = await apiClient.get(`/market/status${market ? `?market=${market}` : ''}`);
  return response.data;
};

// Enhanced stock search with suggestions
export const getStockSuggestions = async (query: string, limit: number = 10): Promise<any> => {
  const response = await apiClient.get(`/stocks/suggestions/${encodeURIComponent(query)}?limit=${limit}`);
  return response.data;
};

// ===== Machine Learning API =====
export interface MLTrainRequest {
  symbols: string[];
  model_id?: string;
  test_size?: number;
}

export interface MLPredictRequest {
  symbols: string[];
  model_id?: string;
}

export const trainMLModel = async (request: MLTrainRequest): Promise<any> => {
  const response = await apiClient.post('/ml/train', request);
  return response.data;
};

export const predictMLModel = async (request: MLPredictRequest): Promise<any> => {
  const response = await apiClient.post('/ml/predict', request);
  return response.data;
};

// ===== Stock Scoring API =====
export interface StockScoreRequest {
  symbol: string;
  tushare_token: string;
}

export interface StockRankingRequest {
  symbols: string[];
  max_stocks?: number;
  factor_weight?: number;
  ml_weight?: number;
  tushare_token: string;
}

export const calculateStockScore = async (request: StockScoreRequest): Promise<any> => {
  const response = await apiClient.post('/score/factor', request);
  return response.data;
};

export const calculateMLScore = async (request: StockScoreRequest): Promise<any> => {
  const response = await apiClient.post('/score/ml', request);
  return response.data;
};

export const rankStocks = async (request: StockRankingRequest): Promise<any> => {
  const response = await apiClient.post('/score/ranking', request);
  return response.data;
};

// ===== Divergence Scanner API =====
export interface DivergenceScanRequest {
  market: string;
  tushare_token: string;
  divergence_types?: string[];
}

export const scanMarketDivergence = async (request: DivergenceScanRequest): Promise<any> => {
  const response = await apiClient.post('/divergence/scan', request);
  return response.data;
};

export const getRecentDivergences = async (market: string, hours: number = 24): Promise<any> => {
  const response = await apiClient.get(`/divergence/recent/${market}?hours=${hours}`);
  return response.data;
};

export const getDivergenceHistory = async (market: string, limit: number = 10): Promise<any> => {
  const response = await apiClient.get(`/divergence/history/${market}?limit=${limit}`);
  return response.data;
};

export const getDivergenceChart = async (symbol: string, market: string): Promise<any> => {
  const response = await apiClient.get(`/divergence/chart/${symbol}?market=${market}`);
  return response.data;
};

export const getSupportedMarkets = async (): Promise<any> => {
  const response = await apiClient.get('/divergence/markets');
  return response.data;
};

// ===== Utility Functions =====
export const getDefaultTushareToken = (): string => TUSHARE_TOKEN;

// Enhanced error types
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export const createApiError = (error: any): ApiError => {
  return {
    message: handleApiError(error),
    status: error.response?.status,
    code: error.response?.data?.code,
  };
};

// Loading state management utility
export class LoadingManager {
  private loadingStates = new Map<string, boolean>();
  private callbacks = new Map<string, ((loading: boolean) => void)[]>();

  setLoading(key: string, loading: boolean): void {
    this.loadingStates.set(key, loading);
    const callbacks = this.callbacks.get(key) || [];
    callbacks.forEach(callback => callback(loading));
  }

  isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false;
  }

  subscribe(key: string, callback: (loading: boolean) => void): () => void {
    if (!this.callbacks.has(key)) {
      this.callbacks.set(key, []);
    }
    this.callbacks.get(key)!.push(callback);

    return () => {
      const callbacks = this.callbacks.get(key) || [];
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    };
  }
}

export const loadingManager = new LoadingManager();

export default apiClient; 