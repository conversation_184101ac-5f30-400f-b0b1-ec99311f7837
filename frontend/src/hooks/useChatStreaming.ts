import { useState, useCallback, useRef, useEffect } from 'react';
import { Message, StreamData } from '@/types';
import { streamChat, ChatRequest, handleApiError } from '@/utils/api';
import { messageStorage } from '@/utils/messageStorage';
import { ErrorClassification, ErrorDetails } from '@/utils/errorClassification';
import { preprocessStreamingContent } from '@/utils/messagePreprocessor';

// Enhanced message status types
export type MessageStatus = 'pending' | 'sending' | 'sent' | 'failed' | 'streaming' | 'paused' | 'complete' | 'cancelled' | 'timeout';

// Streaming control types
export type StreamingControl = 'play' | 'pause' | 'stop';

// Enhanced message interface with streaming features
export interface EnhancedMessage extends Message {
  status?: MessageStatus;
  displayContent?: string; // For typewriter effect
  rawContent?: string; // Original content for instant display
  streamingSpeed?: number; // Characters per interval
  retryCount?: number;
  error?: string;
  version?: number; // For edit tracking
  chartData?: any; // Chart data for chart messages
  chartConfig?: any; // Chart configuration for chart messages
  editHistory?: Array<{
    content: string;
    editedAt: number;
    reason?: string;
  }>;
  metadata?: {
    agent?: string;
    step?: string;
    progress?: number;
    substep?: string;
    messageType?: 'analysis' | 'recommendation' | 'warning' | 'summary' | 'data' | 'general' | 'chart' | 'code' | 'research' | 'market_update' | 'portfolio' | 'ai_insight';
    estimatedTime?: number;
    dataSource?: string;
    chartData?: any;
    chartConfig?: any;
  };
}

// Streaming configuration
export interface StreamingConfig {
  typewriterSpeed: number; // milliseconds per character
  chunkSize: number; // characters to add per interval
  enableTypewriter: boolean;
  enablePauseResume: boolean;
  autoScrollThreshold: number; // px from bottom to enable auto-scroll
  maxRetries: number;
}

// Default streaming configuration
const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  typewriterSpeed: 20, // 20ms per character (fast typing speed)
  chunkSize: 1, // Add 1 character at a time for smooth effect
  enableTypewriter: true,
  enablePauseResume: true,
  autoScrollThreshold: 100,
  maxRetries: 3,
};

// Workflow progress state
export interface WorkflowProgress {
  isActive: boolean;
  stepName: string;
  substepDescription?: string; // NEW: Current substep details
  progress: number; // 0-100
  totalSteps: number;
  estimatedTimeRemaining?: number; // NEW: ETA in milliseconds
  startTime?: number; // NEW: When the workflow started
}

// Chat streaming state
export interface ChatStreamingState {
  messages: EnhancedMessage[];
  isLoading: boolean;
  error: string | null;
  errorDetails: ErrorDetails | null;
  isConnected: boolean;
  currentStreamingMessageId: string | null;
  streamingControl: StreamingControl;
  config: StreamingConfig;
  networkStatus: 'online' | 'offline' | 'reconnecting';
  retryCount: number;
  workflowProgress: WorkflowProgress | null;
  hydrated: boolean;
  lastHydrationTime: number;
  conversationComplete: boolean;
}

// Generate unique message ID
const generateMessageId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Custom hook for enhanced chat streaming with typewriter effects
export const useChatStreaming = (initialConfig?: Partial<StreamingConfig>, conversationId?: string) => {
  // Merge user config with defaults
  const config = { ...DEFAULT_STREAMING_CONFIG, ...initialConfig };
  const currentConversationId = conversationId || 'default';

  // Chat streaming state
  const [streamingState, setStreamingState] = useState<ChatStreamingState>({
    messages: [],
    isLoading: false,
    error: null,
    errorDetails: null,
    isConnected: true,
    currentStreamingMessageId: null,
    streamingControl: 'play',
    config,
    networkStatus: 'online',
    retryCount: 0,
    workflowProgress: null,
    hydrated: false,
    lastHydrationTime: 0,
    conversationComplete: false,
  });

  // Refs for managing streaming and typewriter effects
  const abortControllerRef = useRef<AbortController | null>(null);
  const typewriterTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const streamingStateRef = useRef<boolean>(false);
  const messageQueueRef = useRef<Map<string, string>>(new Map());

  // Cleanup function for typewriter timers
  const cleanupTypewriterTimer = useCallback((messageId: string) => {
    const timer = typewriterTimersRef.current.get(messageId);
    if (timer) {
      clearInterval(timer);
      typewriterTimersRef.current.delete(messageId);
    }
  }, []);

  // Cleanup all timers
  const cleanupAllTimers = useCallback(() => {
    typewriterTimersRef.current.forEach((timer) => clearInterval(timer));
    typewriterTimersRef.current.clear();
  }, []);

  // Load conversation state from storage (hydration)
  const loadFromStorage = useCallback((): boolean => {
    console.log('[LOADING] Loading conversation state from storage...');
    
    const conversationState = messageStorage.loadConversationState(currentConversationId);
    
    if (conversationState && conversationState.messages.length > 0) {
      console.log('💾 Found stored conversation with', conversationState.messages.length, 'messages');
      
      setStreamingState(prev => ({
        ...prev,
        messages: conversationState.messages,
        hydrated: true,
        lastHydrationTime: Date.now(),
        conversationComplete: conversationState.isComplete,
      }));
      
      return true;
    }
    
    console.log('📭 No stored conversation found, starting fresh');
    setStreamingState(prev => ({
      ...prev,
      hydrated: true,
      lastHydrationTime: Date.now(),
      conversationComplete: false,
    }));
    
    return false;
  }, [currentConversationId]);

  // Check if we should skip streaming for complete conversations
  const shouldSkipStreaming = useCallback((): boolean => {
    return streamingState.hydrated && streamingState.conversationComplete;
  }, [streamingState.hydrated, streamingState.conversationComplete]);

  // Auto-save conversation state when messages are complete
  useEffect(() => {
    if (streamingState.hydrated && streamingState.messages.length > 0) {
      const allComplete = streamingState.messages.every(msg => 
        msg.status === 'complete' || msg.status === 'sent'
      );
      
      if (allComplete && !streamingState.currentStreamingMessageId) {
        console.log('💾 Auto-saving complete conversation state');
        messageStorage.saveConversationState(
          currentConversationId,
          streamingState.messages,
          true
        );
        
        setStreamingState(prev => ({
          ...prev,
          conversationComplete: true,
        }));
      }
    }
  }, [streamingState.messages, streamingState.hydrated, streamingState.currentStreamingMessageId, currentConversationId]);

  // Load from storage on component mount
  useEffect(() => {
    if (!streamingState.hydrated) {
      loadFromStorage();
    }
  }, [loadFromStorage, streamingState.hydrated]);

  // Typewriter effect function
  const startTypewriterEffect = useCallback((
    messageId: string,
    targetContent: string,
    onComplete?: () => void
  ) => {
    if (!config.enableTypewriter) {
      // If typewriter is disabled, show content immediately
      setStreamingState(prev => ({
        ...prev,
        messages: prev.messages.map(msg =>
          msg.id === messageId
            ? { ...msg, displayContent: targetContent, status: 'complete' as MessageStatus }
            : msg
        ),
      }));
      onComplete?.();
      return;
    }

    let currentIndex = 0;
    cleanupTypewriterTimer(messageId);

    const timer = setInterval(() => {
      if (streamingState.streamingControl === 'pause') {
        return; // Pause typewriter effect
      }

      currentIndex += config.chunkSize;
      const displayContent = preprocessStreamingContent(targetContent.slice(0, currentIndex));

      setStreamingState(prev => ({
        ...prev,
        messages: prev.messages.map(msg =>
          msg.id === messageId
            ? { ...msg, displayContent, status: currentIndex >= targetContent.length ? 'complete' as MessageStatus : 'streaming' as MessageStatus }
            : msg
        ),
      }));

      if (currentIndex >= targetContent.length) {
        cleanupTypewriterTimer(messageId);
        onComplete?.();
      }
    }, config.typewriterSpeed);

    typewriterTimersRef.current.set(messageId, timer);
  }, [config, streamingState.streamingControl, cleanupTypewriterTimer]);

  // Add user message
  const addUserMessage = useCallback((content: string): string => {
    const messageId = generateMessageId();
    const userMessage: EnhancedMessage = {
      id: messageId,
      type: 'user',
      content: content.trim(),
      displayContent: content.trim(),
      rawContent: content.trim(),
      timestamp: Date.now(),
      isStreaming: false,
      status: 'sent',
      retryCount: 0,
    };

    setStreamingState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      error: null,
    }));

    // Save to persistent storage
    messageStorage.saveMessage(userMessage, currentConversationId);

    return messageId;
  }, []);

  // Add assistant message (for streaming responses)
  const addAssistantMessage = useCallback((id: string, content: string = '', metadata?: any): void => {
    setStreamingState(prev => {
      const existingMessageIndex = prev.messages.findIndex(msg => msg.id === id);
      
      if (existingMessageIndex >= 0) {
        // Update existing message
        const updatedMessages = [...prev.messages];
        updatedMessages[existingMessageIndex] = {
          ...updatedMessages[existingMessageIndex],
          rawContent: content,
          displayContent: config.enableTypewriter ? '' : content,
          metadata,
          status: 'streaming' as MessageStatus,
          isStreaming: true,
        };
        return {
          ...prev,
          messages: updatedMessages,
        };
      } else {
        // Create new assistant message
        const assistantMessage: EnhancedMessage = {
          id,
          type: 'assistant',
          content,
          rawContent: content,
          displayContent: config.enableTypewriter ? '' : content,
          metadata,
          timestamp: Date.now(),
          isStreaming: true,
          status: 'streaming' as MessageStatus,
          retryCount: 0,
        };
        return {
          ...prev,
          messages: [...prev.messages, assistantMessage],
        };
      }
    });
  }, [config.enableTypewriter]);

  // Update message content with typewriter effect
  const updateMessageContent = useCallback((
    id: string, 
    content: string, 
    isComplete: boolean = false,
    metadata?: any
  ): void => {
    setStreamingState(prev => {
      const updatedMessages = prev.messages.map(msg => {
        if (msg.id === id) {
          const updatedMsg = {
            ...msg,
            content,
            rawContent: content,
            metadata: { ...msg.metadata, ...metadata },
            isStreaming: !isComplete,
            status: isComplete ? 'complete' as MessageStatus : 'streaming' as MessageStatus,
          };

          // Start typewriter effect if enabled and content has changed
          if (config.enableTypewriter && content !== msg.rawContent) {
            setTimeout(() => {
              startTypewriterEffect(id, content, () => {
                if (isComplete) {
                  setStreamingState(prev => ({
                    ...prev,
                    currentStreamingMessageId: null,
                  }));
                }
              });
            }, 0);
          } else if (!config.enableTypewriter) {
            updatedMsg.displayContent = preprocessStreamingContent(content);
          }

          return updatedMsg;
        }
        return msg;
      });

      return {
        ...prev,
        messages: updatedMessages,
        currentStreamingMessageId: isComplete ? null : id,
      };
    });
  }, [config.enableTypewriter, startTypewriterEffect]);

  // Add chart message
  const addChartMessage = useCallback((chartData: any, chartConfig: any): void => {
    const chartMessage: EnhancedMessage = {
      id: generateMessageId(),
      type: 'chart',
      content: 'Interactive Chart',
      displayContent: 'Interactive Chart',
      rawContent: 'Interactive Chart',
      timestamp: Date.now(),
      isStreaming: false,
      status: 'complete',
      chartData,
      chartConfig,
      retryCount: 0,
    };

    setStreamingState(prev => ({
      ...prev,
      messages: [...prev.messages, chartMessage],
    }));
  }, []);

  // Handle streaming data with enhanced processing
  const handleStreamData = useCallback((data: StreamData, assistantMessageId: string): void => {
    console.log('📡 Received stream data:', data);

    // Store content in queue for smooth processing
    const currentQueue = messageQueueRef.current.get(assistantMessageId) || '';
    
    switch (data.type) {
      case 'workflow_progress':
        // Update workflow progress state
        console.log('[WORKFLOW] Workflow Progress Update:', {
          step_name: (data as any).step_name,
          substep: (data as any).substep,
          progress: (data as any).progress,
          total_steps: (data as any).total_steps,
          estimated_time: (data as any).estimated_time,
          isActive: true
        });
        setStreamingState(prev => {
          const newWorkflowProgress = {
            isActive: true,
            stepName: (data as any).step_name || '处理中',
            substepDescription: (data as any).substep || undefined,
            progress: (data as any).progress || 0,
            totalSteps: (data as any).total_steps || 8,
            estimatedTimeRemaining: (data as any).estimated_time || undefined,
            startTime: prev.workflowProgress?.startTime || Date.now(),
          };
          console.log('[WORKFLOW] Setting workflowProgress:', newWorkflowProgress);
          console.log('[WORKFLOW] Previous workflowProgress:', prev.workflowProgress);
          return {
            ...prev,
            workflowProgress: newWorkflowProgress,
          };
        });
        break;

      case 'message':
      case 'agent_message':
        if (data.content) {
          const newContent = currentQueue + data.content;
          messageQueueRef.current.set(assistantMessageId, newContent);
          updateMessageContent(assistantMessageId, newContent, false, {
            agent: data.agent,
            step: data.type,
            progress: undefined,
          });
        }
        break;

      case 'final_report':
        if (data.content) {
          const finalContent = currentQueue + data.content;
          console.log('📝 Final Report Processing:', {
            assistantMessageId,
            contentLength: finalContent.length,
            queueContent: currentQueue.length,
            newContent: data.content.length,
            agent: data.agent,
            reportType: (data as any).report_type,
            timestamp: data.timestamp
          });
          
          // Log detailed final report information
          console.log('📝 Final Report Content Preview:', finalContent.substring(0, 300));
          console.log('📝 Final Report Full Data:', data);
          
          messageQueueRef.current.delete(assistantMessageId);
          updateMessageContent(assistantMessageId, finalContent, true, {
            agent: data.agent,
            step: 'final_report',
            progress: 100,
          });
          
          // Ensure message is marked as complete
          setStreamingState(prev => {
            const updatedMessages = prev.messages.map(msg =>
              msg.id === assistantMessageId
                ? { 
                    ...msg, 
                    status: 'complete' as MessageStatus, 
                    isStreaming: false,
                    displayContent: finalContent,
                    content: finalContent,
                    metadata: {
                      ...msg.metadata,
                      agent: data.agent,
                      messageType: 'ai_insight' as const,
                      reportType: (data as any).report_type || 'comprehensive'
                    }
                  }
                : msg
            );
            
            console.log('📝 Final Report - Updated message:', updatedMessages.find(msg => msg.id === assistantMessageId));
            console.log('📝 Final Report - Messages state after update:', updatedMessages.map(msg => ({
              id: msg.id, 
              status: msg.status, 
              isStreaming: msg.isStreaming, 
              displayContentLength: msg.displayContent?.length,
              contentLength: msg.content?.length
            })));
            
            return {
              ...prev,
              messages: updatedMessages,
              currentStreamingMessageId: null,
            };
          });
        } else {
          console.warn('📝 Final Report received with no content:', data);
        }
        break;

      case 'chart_data':
        if (data.chart_data || data.content) {
          // Use chart_data if available, otherwise fall back to content
          const chartData = data.chart_data || data.content;
          const chartConfig = data.content || data.chart_data;
          
          console.log('[CHART] Creating separate chart message:', { chartData, chartConfig });
          
          // Create a new chart message that appears before the assistant message
          const chartMessage: EnhancedMessage = {
            id: generateMessageId(),
            type: 'chart',
            content: `${chartData?.symbol || '股票'} 图表分析`,
            displayContent: `${chartData?.symbol || '股票'} 图表分析`,
            rawContent: `${chartData?.symbol || '股票'} 图表分析`,
            timestamp: Date.now() - 1000, // Set timestamp slightly before assistant message
            isStreaming: false,
            status: 'complete',
            chartData,
            chartConfig,
            metadata: {
              chartData,
              chartConfig,
              messageType: 'chart' as const,
              agent: data.agent || 'chart_generator'
            },
            retryCount: 0
          };
          
          // Insert chart message before the current assistant message
          setStreamingState(prev => {
            const assistantMessageIndex = prev.messages.findIndex(msg => msg.id === assistantMessageId);
            if (assistantMessageIndex !== -1) {
              const newMessages = [...prev.messages];
              newMessages.splice(assistantMessageIndex, 0, chartMessage);
              return {
                ...prev,
                messages: newMessages,
              };
            } else {
              // If assistant message not found, append chart message
              return {
                ...prev,
                messages: [...prev.messages, chartMessage],
              };
            }
          });
        }
        break;

      case 'error':
        const errorMessage = data.content || data.message || 'Unknown error occurred';
        setStreamingState(prev => ({
          ...prev,
          error: errorMessage,
          isLoading: false,
          currentStreamingMessageId: null,
          networkStatus: 'online',
          workflowProgress: null, // Clear progress on error
        }));
        
        // Update the message with error status
        setStreamingState(prev => ({
          ...prev,
          messages: prev.messages.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, status: 'failed' as MessageStatus, error: errorMessage, isStreaming: false }
              : msg
          ),
        }));
        messageQueueRef.current.delete(assistantMessageId);
        break;

      case 'end':
      case 'workflow_complete':
      case 'done':
        // Mark the current message as complete and update its content
        const completedContent = messageQueueRef.current.get(assistantMessageId) || '';
        console.log('✅ Workflow Complete/Done:', {
          type: data.type,
          assistantMessageId,
          completedContentLength: completedContent.length,
          previousWorkflowProgress: streamingState.workflowProgress
        });
        messageQueueRef.current.delete(assistantMessageId);
        setStreamingState(prev => {
          const updatedMessages = prev.messages.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, content: completedContent, displayContent: completedContent, status: 'complete' as MessageStatus, isStreaming: false }
              : msg
          );
          const newWorkflowProgress = prev.workflowProgress ? {
            ...prev.workflowProgress,
            isActive: false,
            progress: 100,
          } : null;
          console.log('✅ Final workflowProgress state:', newWorkflowProgress);
          console.log('✅ Done/Complete - Messages state after update:', updatedMessages.map(msg => ({id: msg.id, status: msg.status, isStreaming: msg.isStreaming, displayContentLength: msg.displayContent?.length})));
          return {
            ...prev,
            messages: updatedMessages,
            isLoading: false,
            currentStreamingMessageId: null,
            workflowProgress: newWorkflowProgress,
          };
        });
        break;

      default:
        console.log('[STREAM] Unhandled stream type:', data.type, data);
    }
  }, [updateMessageContent, addChartMessage]);

  // Handle streaming errors with enhanced classification and retry logic
  const handleStreamError = useCallback((error: string | any, messageId?: string): void => {
    console.error('❌ Stream error:', error);
    
    // Classify the error
    const errorDetails = ErrorClassification.classify(error);
    
    setStreamingState(prev => ({
      ...prev,
      error: typeof error === 'string' ? error : error.message,
      errorDetails,
      isLoading: false,
      isConnected: false,
      currentStreamingMessageId: null,
      networkStatus: errorDetails.type === 'network' ? 'offline' : 'online',
      retryCount: prev.retryCount + 1,
    }));

    // Update specific message with error if messageId provided
    if (messageId) {
      setStreamingState(prev => ({
        ...prev,
        messages: prev.messages.map(msg =>
          msg.id === messageId
            ? { 
                ...msg, 
                status: 'failed' as MessageStatus, 
                error: errorDetails.userMessage,
                isStreaming: false,
                retryCount: (msg.retryCount || 0) + 1,
              }
            : msg
        ),
      }));
      messageQueueRef.current.delete(messageId);
    }

    // Auto-retry logic based on error classification
    if (ErrorClassification.shouldAutoRetry(errorDetails, streamingState.retryCount)) {
      const delay = ErrorClassification.getRetryDelay(errorDetails, streamingState.retryCount);
      console.log(`[RETRY] Auto-retry scheduled in ${delay}ms`);
      
      setTimeout(() => {
        // Find the last user message and retry
        const lastUserMessage = [...streamingState.messages]
          .reverse()
          .find(msg => msg.type === 'user');
        
        if (lastUserMessage) {
          sendMessage(lastUserMessage.content);
        }
      }, delay);
    }
  }, [streamingState.retryCount, streamingState.messages]);

  // Clear hydration cache and refresh from server
  const clearHydrationCache = useCallback((): void => {
          console.log('[CACHE] Clearing hydration cache');
    messageStorage.clearConversationCache(currentConversationId);
    setStreamingState(prev => ({
      ...prev,
      messages: [],
      hydrated: false,
      lastHydrationTime: 0,
      conversationComplete: false,
    }));
  }, [currentConversationId]);

  // Refresh conversation from server
  const refreshFromServer = useCallback((): void => {
    console.log('[REFRESH] Refreshing conversation from server');
    clearHydrationCache();
    // The useEffect will automatically trigger loadFromStorage when hydrated becomes false
  }, [clearHydrationCache]);

  // Send message with enhanced error handling and retry logic
  const sendMessage = useCallback(async (
    content: string,
    options: Partial<ChatRequest> = {}
  ): Promise<void> => {
    if (!content.trim()) {
              console.warn('[WARNING] Attempted to send empty message');
      return;
    }

    if (streamingStateRef.current) {
              console.warn('[WARNING] Another message is currently being processed');
      return;
    }

    // Skip streaming if conversation is complete and we're just hydrating
    if (shouldSkipStreaming()) {
              console.log('[SKIP] Skipping streaming for complete conversation');
      return;
    }

    try {
      // Cancel any previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();
      streamingStateRef.current = true;

      // Add user message
      const userMessageId = addUserMessage(content);
      
      // Set loading state
      setStreamingState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        isConnected: true,
        networkStatus: 'online',
      }));

      // Prepare assistant message for streaming response
      const assistantMessageId = generateMessageId();
      addAssistantMessage(assistantMessageId, '');

      setStreamingState(prev => ({
        ...prev,
        currentStreamingMessageId: assistantMessageId,
      }));

      // Prepare chat request
      const chatRequest: ChatRequest = {
        message: content,
        max_plan_iterations: 1,
        max_step_num: 3,
        enable_background_investigation: true,
        debug: false,
        ...options,
      };

      console.log('📤 Sending chat request:', chatRequest);

      // Start streaming
      await streamChat(
        chatRequest,
        (data: StreamData) => handleStreamData(data, assistantMessageId),
        (error: string) => handleStreamError(error, assistantMessageId)
      );

    } catch (error: any) {
      console.error('💥 Send message error:', error);
      const errorMessage = handleApiError(error);
      handleStreamError(errorMessage);
    } finally {
      streamingStateRef.current = false;
      abortControllerRef.current = null;
    }
  }, [addUserMessage, addAssistantMessage, handleStreamData, handleStreamError]);

  // Control streaming (pause/resume/stop)
  const controlStream = useCallback((control: StreamingControl): void => {
    setStreamingState(prev => ({
      ...prev,
      streamingControl: control,
    }));

    if (control === 'stop') {
      // Stop all streaming and typewriter effects
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      cleanupAllTimers();
      streamingStateRef.current = false;
      
      setStreamingState(prev => ({
        ...prev,
        isLoading: false,
        currentStreamingMessageId: null,
      }));
    }
  }, [cleanupAllTimers]);

  // Retry failed message
  const retryMessage = useCallback((messageId: string): void => {
    const message = streamingState.messages.find(msg => msg.id === messageId);
    if (!message || (message.retryCount || 0) >= config.maxRetries) return;

    // Find the user message that triggered this response
    const messageIndex = streamingState.messages.findIndex(msg => msg.id === messageId);
    const userMessage = streamingState.messages
      .slice(0, messageIndex)
      .reverse()
      .find(msg => msg.type === 'user');

    if (userMessage) {
      // Update retry count
      setStreamingState(prev => ({
        ...prev,
        messages: prev.messages.map(msg =>
          msg.id === messageId
            ? { ...msg, retryCount: (msg.retryCount || 0) + 1, status: 'sending' as MessageStatus, error: undefined }
            : msg
        ),
      }));

      // Retry sending
      sendMessage(userMessage.content);
    }
  }, [streamingState.messages, config.maxRetries, sendMessage]);

  // Clear all messages
  const clearMessages = useCallback((): void => {
    controlStream('stop');
    messageQueueRef.current.clear();
    setStreamingState(prev => ({
      ...prev,
      messages: [],
      error: null,
    }));
  }, [controlStream]);

  // Update streaming configuration
  const updateConfig = useCallback((newConfig: Partial<StreamingConfig>): void => {
    setStreamingState(prev => ({
      ...prev,
      config: { ...prev.config, ...newConfig },
    }));
  }, []);

  // Cleanup effect
  useEffect(() => {
    return () => {
      cleanupAllTimers();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [cleanupAllTimers]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => {
      setStreamingState(prev => ({
        ...prev,
        networkStatus: 'online',
        isConnected: true,
      }));
    };

    const handleOffline = () => {
      setStreamingState(prev => ({
        ...prev,
        networkStatus: 'offline',
        isConnected: false,
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    // State
    messages: streamingState.messages,
    isLoading: streamingState.isLoading,
    error: streamingState.error,
    errorDetails: streamingState.errorDetails,
    isConnected: streamingState.isConnected,
    currentStreamingMessageId: streamingState.currentStreamingMessageId,
    streamingControl: streamingState.streamingControl,
    networkStatus: streamingState.networkStatus,
    config: streamingState.config,
    retryCount: streamingState.retryCount,
    workflowProgress: streamingState.workflowProgress,
    hydrated: streamingState.hydrated,
    lastHydrationTime: streamingState.lastHydrationTime,
    conversationComplete: streamingState.conversationComplete,

    // Computed state
    isStreaming: streamingStateRef.current && streamingState.currentStreamingMessageId !== null,
    hasMessages: streamingState.messages.length > 0,
    canRetry: streamingState.messages.some(msg => msg.status === 'failed' && (msg.retryCount || 0) < config.maxRetries),
    shouldAutoRetry: streamingState.errorDetails ? ErrorClassification.shouldAutoRetry(streamingState.errorDetails, streamingState.retryCount) : false,

    // Actions
    sendMessage,
    controlStream,
    retryMessage,
    clearMessages,
    updateConfig,

    // Advanced actions
    addUserMessage,
    addAssistantMessage,
    updateMessageContent,
    addChartMessage,
    
    // Hydration actions
    loadFromStorage,
    shouldSkipStreaming,
    clearHydrationCache,
    refreshFromServer,
    
    // Error management
    clearError: () => setStreamingState(prev => ({ ...prev, error: null, errorDetails: null, retryCount: 0 })),
  };
}; 