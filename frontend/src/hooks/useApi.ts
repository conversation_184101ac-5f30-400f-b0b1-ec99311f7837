import { useState, useEffect, useCallback, useRef } from 'react';
import {
  healthCheck,
  streamChat,
  queryStockData,
  getTechnicalIndicators,
  searchStocks,
  getFactors,
  calculateFactors,
  calculateFactorsSmart,
  getFactorTimeseries,
  generateAIFactor,
  testCustomFactor,
  scanMarketDivergence,
  getRecentDivergences,
  getDivergenceHistory,
  getSupportedMarkets,
  handleApiError,
  createApiError,
  getDefaultTushareToken,
  type ChatRequest,
  type StockDataRequest,
  type TechnicalIndicatorRequest,
  type FactorRequest,
  type AIFactorRequest,
  type DivergenceScanRequest,
  type ApiError,
} from '@/utils/api';
import { StreamData, StockQueryResult, TechnicalIndicatorResult } from '@/types';

// Generic API hook for basic CRUD operations
export const useApi = <T = any>(initialData: T | null = null) => {
  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (apiCall: () => Promise<T>) => {
    setLoading(true);
    setError(null);
    try {
      const result = await apiCall();
      setData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(initialData);
    setError(null);
    setLoading(false);
  }, [initialData]);

  return { data, loading, error, execute, reset, setData };
};

// Health check hook with configurable intervals
export const useHealthCheck = (options?: {
  autoCheck?: boolean;
  interval?: number;
  enableLogging?: boolean;
}) => {
  const {
    autoCheck = true,
    interval = 300000, // 5 minutes instead of 30 seconds
    enableLogging = false // Reduce console spam
  } = options || {};

  // Completely disable health checks if environment variable is set
  const healthCheckDisabled = process.env.NEXT_PUBLIC_DISABLE_HEALTH_CHECKS === 'true';

  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastChecked, setLastChecked] = useState<string>('');

  const checkHealth = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      if (enableLogging) {
        console.log('Health check starting...');
      }
      const result = await healthCheck();
      if (enableLogging) {
        console.log('Health check result:', result);
      }
      setIsHealthy(true);
      setError(null);
      setLastChecked(new Date().toLocaleTimeString());
    } catch (err: any) {
      if (enableLogging) {
        console.error('Health check hook error:', err);
      }
      setIsHealthy(false);
      setError(handleApiError(err));
      setLastChecked(new Date().toLocaleTimeString());
    } finally {
      setLoading(false);
    }
  }, [enableLogging]);

  useEffect(() => {
    // Skip all health checks if disabled via environment variable
    if (healthCheckDisabled) {
      setIsHealthy(true); // Assume healthy if checks are disabled
      return;
    }

    // Initial health check
    checkHealth();
    
    // Set up periodic health checks only if autoCheck is enabled
    if (autoCheck && interval > 0) {
      const intervalId = setInterval(checkHealth, interval);
      return () => clearInterval(intervalId);
    }
  }, [checkHealth, autoCheck, interval, healthCheckDisabled]);

  return { isHealthy, loading, error, checkHealth, lastChecked };
};

// Chat streaming hook
export const useChatStream = () => {
  const [messages, setMessages] = useState<StreamData[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const sendMessage = useCallback(async (request: ChatRequest) => {
    setIsStreaming(true);
    setError(null);
    
    // Cancel previous request if it exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    const onMessage = (data: StreamData) => {
      setMessages(prev => [...prev, data]);
    };

    const onError = (errorMessage: string) => {
      setError(errorMessage);
      setIsStreaming(false);
    };

    try {
      await streamChat(request, onMessage, onError);
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setError(handleApiError(err));
      }
    } finally {
      setIsStreaming(false);
    }
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsStreaming(false);
    }
  }, []);

  return {
    messages,
    isStreaming,
    error,
    sendMessage,
    clearMessages,
    stopStreaming,
  };
};

// Stock data hook
export const useStockData = () => {
  const [stockData, setStockData] = useState<StockQueryResult | null>(null);
  const [technicalData, setTechnicalData] = useState<TechnicalIndicatorResult | null>(null);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const queryStock = useCallback(async (request: StockDataRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await queryStockData({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setStockData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const queryTechnicalIndicators = useCallback(async (request: TechnicalIndicatorRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await getTechnicalIndicators({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setTechnicalData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const searchStock = useCallback(async (query: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await searchStocks(query);
      setSearchResults(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    stockData,
    technicalData,
    searchResults,
    loading,
    error,
    queryStock,
    queryTechnicalIndicators,
    searchStock,
    setStockData,
    setTechnicalData,
  };
};

// Factor management hook
export const useFactors = () => {
  const [factors, setFactors] = useState<any[]>([]);
  const [factorData, setFactorData] = useState<any>(null);
  const [timeseriesData, setTimeseriesData] = useState<any>(null);
  const [aiFactorResult, setAiFactorResult] = useState<any>(null);
  const [customFactorResult, setCustomFactorResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadFactors = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getFactors();
      setFactors(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const calculateFactorData = useCallback(async (request: FactorRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await calculateFactors({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setFactorData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const calculateSmartFactors = useCallback(async (request: FactorRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await calculateFactorsSmart({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setFactorData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadFactorTimeseries = useCallback(async (request: FactorRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await getFactorTimeseries({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setTimeseriesData(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const generateAiFactor = useCallback(async (request: AIFactorRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await generateAIFactor(request);
      setAiFactorResult(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const testCustomFactorFormula = useCallback(async (request: {
    symbol: string;
    formula: string;
    tushare_token?: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      const result = await testCustomFactor({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setCustomFactorResult(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    factors,
    factorData,
    timeseriesData,
    aiFactorResult,
    customFactorResult,
    loading,
    error,
    loadFactors,
    calculateFactorData,
    calculateSmartFactors,
    loadFactorTimeseries,
    generateAiFactor,
    testCustomFactorFormula,
    setFactorData,
    setTimeseriesData,
  };
};

// Divergence scanner hook
export const useDivergenceScanner = () => {
  const [scanResults, setScanResults] = useState<any>(null);
  const [recentDivergences, setRecentDivergences] = useState<any[]>([]);
  const [scanHistory, setScanHistory] = useState<any[]>([]);
  const [supportedMarkets, setSupportedMarkets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const scanMarket = useCallback(async (request: DivergenceScanRequest) => {
    setLoading(true);
    setError(null);
    try {
      const result = await scanMarketDivergence({
        ...request,
        tushare_token: request.tushare_token || getDefaultTushareToken(),
      });
      setScanResults(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadRecentDivergences = useCallback(async (market: string, hours: number = 24) => {
    setLoading(true);
    setError(null);
    try {
      const result = await getRecentDivergences(market, hours);
      setRecentDivergences(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadScanHistory = useCallback(async (market: string, limit: number = 10) => {
    setLoading(true);
    setError(null);
    try {
      const result = await getDivergenceHistory(market, limit);
      setScanHistory(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadSupportedMarkets = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getSupportedMarkets();
      setSupportedMarkets(result);
      return result;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw createApiError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    scanResults,
    recentDivergences,
    scanHistory,
    supportedMarkets,
    loading,
    error,
    scanMarket,
    loadRecentDivergences,
    loadScanHistory,
    loadSupportedMarkets,
    setScanResults,
  };
};

// Auto-refresh hook for real-time data
export const useAutoRefresh = (
  refreshFunction: () => Promise<void>,
  interval: number = 300000, // 5 minutes default instead of 30 seconds to reduce log spam
  enabled: boolean = false
) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<string>('');

  const startAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(async () => {
      await refreshFunction();
      setLastRefreshTime(new Date().toLocaleTimeString());
    }, interval);
  }, [refreshFunction, interval]);

  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (enabled) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }

    return () => stopAutoRefresh();
  }, [enabled, startAutoRefresh, stopAutoRefresh]);

  return { lastRefreshTime, startAutoRefresh, stopAutoRefresh };
}; 