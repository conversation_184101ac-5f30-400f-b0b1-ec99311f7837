import { useState, useCallback, useRef, useEffect } from 'react';

// Auto-scroll state interface
export interface AutoScrollState {
  autoScrollPaused: boolean;
  lastUserScrollTime: number;
  isAtBottom: boolean;
  showJumpButton: boolean;
}

// Hook configuration
interface UseAutoScrollConfig {
  pauseTimeout?: number; // Time in ms to wait before resuming auto-scroll
  bottomThreshold?: number; // Distance from bottom to consider "at bottom"
  enabled?: boolean; // Whether auto-scroll is enabled
}

const DEFAULT_CONFIG: Required<UseAutoScrollConfig> = {
  pauseTimeout: 1000,
  bottomThreshold: 100,
  enabled: true,
};

export const useAutoScroll = (
  containerRef: React.RefObject<HTMLElement>,
  isStreaming: boolean = false,
  config: UseAutoScrollConfig = {}
) => {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Auto-scroll state
  const [autoScrollState, setAutoScrollState] = useState<AutoScrollState>({
    autoScrollPaused: false,
    lastUserScrollTime: 0,
    isAtBottom: true,
    showJumpButton: false,
  });

  // Refs for managing timeouts and scroll detection
  const pauseTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUserScrollingRef = useRef<boolean>(false);

  // Check if user is at bottom of container
  const checkIfAtBottom = useCallback((): boolean => {
    if (!containerRef.current) return true;
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    
    return distanceFromBottom <= fullConfig.bottomThreshold;
  }, [containerRef, fullConfig.bottomThreshold]);

  // Update "at bottom" state
  const updateBottomState = useCallback(() => {
    const isAtBottom = checkIfAtBottom();
    setAutoScrollState(prev => ({
      ...prev,
      isAtBottom,
      showJumpButton: prev.autoScrollPaused && isStreaming && !isAtBottom,
    }));
  }, [checkIfAtBottom, isStreaming]);

  // Handle user-initiated scroll
  const handleUserScroll = useCallback(() => {
    if (!fullConfig.enabled || !containerRef.current) return;

    isUserScrollingRef.current = true;
    const now = Date.now();

    setAutoScrollState(prev => ({
      ...prev,
      autoScrollPaused: true,
      lastUserScrollTime: now,
    }));

    // Clear existing timeout
    if (pauseTimeoutRef.current) {
      clearTimeout(pauseTimeoutRef.current);
    }

    // Set new timeout to resume auto-scroll
    pauseTimeoutRef.current = setTimeout(() => {
      const isAtBottom = checkIfAtBottom();
      
      setAutoScrollState(prev => ({
        ...prev,
        autoScrollPaused: !isAtBottom, // Only resume if we're back at bottom
      }));
      
      isUserScrollingRef.current = false;
    }, fullConfig.pauseTimeout);

    // Update bottom state
    updateBottomState();
  }, [fullConfig.enabled, fullConfig.pauseTimeout, containerRef, checkIfAtBottom, updateBottomState]);

  // Scroll to bottom function
  const scrollToBottom = useCallback((force: boolean = false) => {
    if (!containerRef.current) return;

    if (force || (!autoScrollState.autoScrollPaused && fullConfig.enabled)) {
      containerRef.current.scrollTo({
        top: containerRef.current.scrollHeight,
        behavior: force ? 'smooth' : 'auto'
      });

      setAutoScrollState(prev => ({
        ...prev,
        isAtBottom: true,
        autoScrollPaused: false,
        showJumpButton: false,
      }));
    }
  }, [autoScrollState.autoScrollPaused, fullConfig.enabled, containerRef]);

  // Jump to latest (manual scroll to bottom)
  const jumpToLatest = useCallback(() => {
    scrollToBottom(true);
  }, [scrollToBottom]);

  // Auto-scroll when new content arrives
  useEffect(() => {
    if (isStreaming && !autoScrollState.autoScrollPaused && fullConfig.enabled) {
      scrollToBottom();
    }
  }, [isStreaming, autoScrollState.autoScrollPaused, fullConfig.enabled, scrollToBottom]);

  // Set up scroll event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !fullConfig.enabled) return;

    const handleScroll = (event: Event) => {
      // Only handle user-initiated scrolls
      if (!isUserScrollingRef.current) {
        isUserScrollingRef.current = true;
        setTimeout(() => {
          isUserScrollingRef.current = false;
        }, 50);
        handleUserScroll();
      }
      updateBottomState();
    };

    const handleWheel = (event: WheelEvent) => {
      handleUserScroll();
    };

    const handleTouchStart = () => {
      handleUserScroll();
    };

    // Add event listeners
    container.addEventListener('scroll', handleScroll, { passive: true });
    container.addEventListener('wheel', handleWheel, { passive: true });
    container.addEventListener('touchstart', handleTouchStart, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
      container.removeEventListener('wheel', handleWheel);
      container.removeEventListener('touchstart', handleTouchStart);
    };
  }, [fullConfig.enabled, handleUserScroll, updateBottomState, containerRef]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (pauseTimeoutRef.current) {
        clearTimeout(pauseTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    autoScrollPaused: autoScrollState.autoScrollPaused,
    isAtBottom: autoScrollState.isAtBottom,
    showJumpButton: autoScrollState.showJumpButton,
    lastUserScrollTime: autoScrollState.lastUserScrollTime,

    // Actions
    scrollToBottom,
    jumpToLatest,
    handleUserScroll,

    // Computed
    canAutoScroll: !autoScrollState.autoScrollPaused && fullConfig.enabled,
  };
};

export default useAutoScroll; 