'use client';

import { useState, useCallback, useRef } from 'react';
import { useApi } from './useApi';

// Chart data interfaces
export interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface ChartData {
  symbol: string;
  company_name: string;
  current_price: number;
  price_change: number;
  price_change_percent: number;
  dates: string[];
  kline_data: [number, number, number, number][]; // [open, close, low, high]
  volume_data: number[];
  last_update: string;
  data_source?: string;
  market?: string;
}

export interface ChartRequest {
  symbol: string;
  period?: string;
  market?: 'US' | 'CN';
  tushare_token?: string;
}

export interface ChartError {
  code: string;
  message: string;
  details?: any;
}

const useChartData = () => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ChartError | null>(null);
  const [cache, setCache] = useState<Map<string, { data: ChartData; timestamp: number }>>(new Map());
  
  // Cache timeout (5 minutes)
  const CACHE_TIMEOUT = 5 * 60 * 1000;
  
  const { execute } = useApi();
  
  // Generate cache key
  const getCacheKey = useCallback((request: ChartRequest): string => {
    return `${request.symbol}_${request.period || '1y'}_${request.market || 'US'}`;
  }, []);
  
  // Check cache
  const getCachedData = useCallback((cacheKey: string): ChartData | null => {
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TIMEOUT) {
      return cached.data;
    }
    if (cached) {
      // Remove expired cache
      const newCache = new Map(cache);
      newCache.delete(cacheKey);
      setCache(newCache);
    }
    return null;
  }, [cache, CACHE_TIMEOUT]);
  
  // Set cache
  const setCachedData = useCallback((cacheKey: string, data: ChartData) => {
    const newCache = new Map(cache);
    newCache.set(cacheKey, { data, timestamp: Date.now() });
    setCache(newCache);
  }, [cache]);
  
  // Parse API response to ChartData format
  const parseApiResponse = useCallback((response: any, symbol: string): ChartData => {
    // Handle different API response formats
    if (response.chart_data) {
      return response.chart_data;
    }
    
    if (response.kline_data && Array.isArray(response.kline_data)) {
      // Convert from stock query format
      const klineData = response.kline_data;
      const dates = klineData.map((item: any) => item.date || item.trade_date);
      const candlestickData = klineData.map((item: any) => [
        item.open,
        item.close, 
        item.low,
        item.high
      ]);
      const volumeData = klineData.map((item: any) => item.volume || item.vol);
      
      const latest = klineData[klineData.length - 1] || {};
      const previous = klineData[klineData.length - 2] || {};
      
      const currentPrice = latest.close || 0;
      const previousPrice = previous.close || currentPrice;
      const priceChange = currentPrice - previousPrice;
      const priceChangePercent = previousPrice > 0 ? (priceChange / previousPrice) * 100 : 0;
      
      return {
        symbol: symbol.toUpperCase(),
        company_name: response.basic_info?.name || response.symbol || symbol,
        current_price: currentPrice,
        price_change: priceChange,
        price_change_percent: priceChangePercent,
        dates,
        kline_data: candlestickData,
        volume_data: volumeData,
        last_update: new Date().toISOString(),
        data_source: response.data_source || 'api',
        market: response.basic_info?.market || 'US'
      };
    }
    
    throw new Error('Invalid API response format');
  }, []);
  
  // Fetch chart data from API
  const fetchChartData = useCallback(async (request: ChartRequest): Promise<ChartData> => {
    setLoading(true);
    setError(null);
    
    try {
      const cacheKey = getCacheKey(request);
      
      // Check cache first
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        setChartData(cachedData);
        setLoading(false);
        return cachedData;
      }
      
      // Prepare API request based on market
      let apiEndpoint: string;
      let apiRequest: any;
      
      if (request.market === 'CN') {
        // Chinese market
        apiEndpoint = '/api/stock-data/query';
        apiRequest = {
          symbol: request.symbol,
          period: request.period || '1y',
          tushare_token: request.tushare_token || '',
        };
      } else {
        // US market (default)
        apiEndpoint = '/api/stock-data/query';
        apiRequest = {
          symbol: request.symbol,
          period: request.period || '1y',
          tushare_token: '',
        };
      }
      
      const data = await execute(async () => {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${apiEndpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiRequest),
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
      });
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch chart data');
      }
      
      const chartData = parseApiResponse(data, request.symbol);
      
      // Cache the result
      setCachedData(cacheKey, chartData);
      setChartData(chartData);
      
      return chartData;
      
    } catch (err: any) {
      const chartError: ChartError = {
        code: err.code || 'FETCH_ERROR',
        message: err.message || 'Failed to fetch chart data',
        details: err
      };
      setError(chartError);
      throw chartError;
    } finally {
      setLoading(false);
    }
  }, [execute, getCacheKey, getCachedData, setCachedData, parseApiResponse]);
  
  // Generate chart data for testing/demo purposes
  const generateDemoChartData = useCallback((symbol: string): ChartData => {
    const days = 30;
    const startPrice = 100 + Math.random() * 200;
    const dates: string[] = [];
    const klineData: [number, number, number, number][] = [];
    const volumeData: number[] = [];
    
    let currentPrice = startPrice;
    const today = new Date();
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
      
      const open = currentPrice;
      const change = (Math.random() - 0.5) * 10; // ±5 price change
      const high = Math.max(open, open + change) + Math.random() * 5;
      const low = Math.min(open, open + change) - Math.random() * 5;
      const close = open + change;
      
      klineData.push([open, close, low, high]);
      volumeData.push(Math.floor(1000000 + Math.random() * 5000000));
      
      currentPrice = close;
    }
    
    const priceChange = currentPrice - startPrice;
    const priceChangePercent = (priceChange / startPrice) * 100;
    
    return {
      symbol: symbol.toUpperCase(),
      company_name: `${symbol.toUpperCase()} Corp`,
      current_price: currentPrice,
      price_change: priceChange,
      price_change_percent: priceChangePercent,
      dates,
      kline_data: klineData,
      volume_data: volumeData,
      last_update: new Date().toISOString(),
      data_source: 'demo',
      market: 'DEMO'
    };
  }, []);
  
  // Clear cache
  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);
  
  // Clear current data
  const clearData = useCallback(() => {
    setChartData(null);
    setError(null);
  }, []);
  
  return {
    chartData,
    loading,
    error,
    fetchChartData,
    generateDemoChartData,
    clearCache,
    clearData,
    cacheSize: cache.size
  };
};

export default useChartData; 