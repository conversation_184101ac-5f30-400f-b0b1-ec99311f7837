'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AppMode } from '@/types';
import { useChatStreaming } from '@/hooks/useChatStreaming';
import ChatContainer from './ChatContainer';
import MessageBubble from './MessageBubble';
import ChatInput from './ChatInput';
import ChatControls from './ChatControls';
import JumpToLatestButton from './JumpToLatestButton';
import RefreshButton from './RefreshButton';
import { AvatarIndicator, StatusIndicator } from './VisualIndicator';

interface AIAgentModeProps {
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const AIAgentMode: React.FC<AIAgentModeProps> = ({ setMode }) => {
  // Local state for input and UI
  const [input, setInput] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Use the enhanced chat streaming hook
  const {
    messages,
    isLoading,
    error,
    isStreaming,
    hasMessages,
    workflowProgress,
    hydrated,
    lastHydrationTime,
    conversationComplete,
    sendMessage,
    clearMessages,
    clearError,
    refreshFromServer,
    retryMessage,
  } = useChatStreaming({
    typewriterSpeed: 15,
    enableTypewriter: true,
    enablePauseResume: true,
  }, 'default');

  // Window-based scrolling for single page scroll
  const [showJumpButton, setShowJumpButton] = useState(false);
  
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, []);

  const jumpToLatest = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, []);

  // Debug logging for workflowProgress changes - REMOVED per user request
  // useEffect(() => {
  //   console.log('🎯 AIAgentMode - workflowProgress changed:', {
  //     workflowProgress,
  //     isActive: workflowProgress?.isActive,
  //     progress: workflowProgress?.progress,
  //     stepName: workflowProgress?.stepName,
  //     shouldShowProgress: workflowProgress && workflowProgress.isActive
  //   });
  // }, [workflowProgress]);

  // Add scroll detection for jump button
  useEffect(() => {
    const handleScroll = () => {
      if (messagesEndRef.current) {
        const rect = messagesEndRef.current.getBoundingClientRect();
        const isNearBottom = rect.bottom <= window.innerHeight + 100;
        setShowJumpButton(!isNearBottom && messages.length > 0);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [messages.length]);

  // Auto-scroll functionality for new messages
  useEffect(() => {
    if (isStreaming && messages.length > 0) {
      // Small delay to ensure content is rendered
      const timeoutId = setTimeout(() => {
        scrollToBottom();
      }, 100);
      
      return () => clearTimeout(timeoutId);
    }
  }, [messages, isStreaming, scrollToBottom]);

  // Auto-resize textarea
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto';
      const newHeight = Math.min(inputRef.current.scrollHeight, 128); // 8rem = 128px
      inputRef.current.style.height = `${newHeight}px`;
    }
  }, [input]);

  // Handle send message
  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;
    
    const messageContent = input.trim();
    setInput(''); // Clear input immediately
    
    try {
      await sendMessage(messageContent, {
        max_plan_iterations: 1,
        max_step_num: 3,
        enable_background_investigation: true,
        debug: false,
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Quick action examples
  const examplePrompts = [
    '分析今日大盘走势和主要指数表现',
    '推荐几只当前值得关注的热门股票',
    '对指定股票进行技术分析，包括K线图和技术指标',
    '当前市场有哪些主要风险需要注意？'
  ];

  const handleExampleClick = (prompt: string) => {
    setInput(prompt);
    sendMessage(prompt);
  };

  return (
    <div className="flex flex-col h-screen w-full pb-4">
      {/* Chat Controls */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <ChatControls
          isStreaming={isStreaming}
          messageCount={messages.length}
          onClearConversation={clearMessages}
          onRetryLastMessage={() => {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage && lastMessage.status === 'failed') {
              retryMessage(lastMessage.id);
            }
          }}
          onRefreshFromServer={refreshFromServer}
          onExportConversation={(format) => {
            console.log('Export conversation:', format);
            // TODO: Implement export functionality
          }}
          variant="full"
        />
      </div>

      {/* Chat Container */}
      <div className="flex-1">
        <ChatContainer
          messages={messages}
          isStreaming={isStreaming}
          isLoading={isLoading}
          error={error}
          title="AI 投资助手"
          subtitle="智能金融分析与投资建议"
          className="mb-4"
        >
          {messages.map((message) => (
            <MessageBubble
              key={message.id}
              message={message}
              isStreaming={isStreaming && message.id === messages[messages.length - 1]?.id}
              conversationId="ai-agent"
              onRetry={retryMessage}
              onReaction={(messageId, reaction) => {
                console.log('Message reaction:', messageId, reaction);
              }}
            />
          ))}
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </ChatContainer>
      </div>

      {/* Chat Input - Sticky at bottom */}
      <div className="sticky bottom-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t pt-4">
        <ChatInput
          onSendMessage={(message) => {
            sendMessage(message, {
              max_plan_iterations: 1,
              max_step_num: 3,
              enable_background_investigation: true,
              debug: false,
            });
          }}
          isStreaming={isStreaming}
          isLoading={isLoading}
          placeholder="请输入您的问题..."
          maxLength={4000}
          showCharacterCount={true}
        />
      </div>

      {/* Jump to latest button */}
      <JumpToLatestButton
        show={showJumpButton}
        onClick={jumpToLatest}
        className="fixed bottom-24 right-6 z-50"
      />
    </div>
  );
};

export default AIAgentMode; 