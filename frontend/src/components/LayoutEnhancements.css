/* ===== Layout and Visual Hierarchy Optimization ===== */
/* Task 2.8: Layout and Visual Hierarchy Optimization Implementation */

/* ===== Background Patterns and Visual Depth ===== */

/* Subtle background patterns */
.bg-pattern-dots {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
  background-size: 24px 24px;
}

.bg-pattern-diagonal {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(0, 0, 0, 0.02) 10px,
    rgba(0, 0, 0, 0.02) 20px
  );
}

/* Gradient backgrounds for visual depth */
.bg-gradient-subtle {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, #fef7f0 0%, #fdf2f8 100%);
}

.bg-gradient-cool {
  background: linear-gradient(135deg, #f0f9ff 0%, #f0f4ff 100%);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
}

/* Layered backgrounds for depth */
.bg-layered {
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.bg-layered::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* ===== Visual Hierarchy System ===== */

/* Typography hierarchy */
.text-hierarchy-1 {
  @apply text-4xl font-bold text-gray-900 leading-tight;
  letter-spacing: -0.025em;
}

.text-hierarchy-2 {
  @apply text-3xl font-semibold text-gray-800 leading-tight;
  letter-spacing: -0.02em;
}

.text-hierarchy-3 {
  @apply text-2xl font-semibold text-gray-800 leading-snug;
  letter-spacing: -0.015em;
}

.text-hierarchy-4 {
  @apply text-xl font-medium text-gray-700 leading-relaxed;
}

.text-hierarchy-5 {
  @apply text-lg font-medium text-gray-600 leading-relaxed;
}

.text-hierarchy-6 {
  @apply text-base font-normal text-gray-600 leading-relaxed;
}

.text-hierarchy-caption {
  @apply text-sm font-medium text-gray-500 leading-normal;
  letter-spacing: 0.025em;
}

.text-hierarchy-label {
  @apply text-xs font-semibold text-gray-400 uppercase tracking-wider;
}

/* Content sections with proper hierarchy */
.content-section {
  @apply mb-12;
}

.content-section-header {
  @apply mb-6 pb-3 border-b border-gray-200;
}

.content-section-title {
  @apply text-hierarchy-3 mb-2;
}

.content-section-subtitle {
  @apply text-hierarchy-caption;
}

.content-section-body {
  @apply space-y-4;
}

/* Visual weight system */
.visual-weight-heavy {
  @apply font-bold text-gray-900;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.visual-weight-medium {
  @apply font-semibold text-gray-800;
}

.visual-weight-light {
  @apply font-normal text-gray-600;
}

.visual-weight-subtle {
  @apply font-normal text-gray-500;
}

/* ===== Responsive Breakpoint System ===== */

/* Container system */
.container-fluid {
  @apply w-full px-4;
}

.container-sm {
  @apply max-w-sm mx-auto px-4;
}

.container-md {
  @apply max-w-md mx-auto px-4;
}

.container-lg {
  @apply max-w-4xl mx-auto px-6;
}

.container-xl {
  @apply max-w-6xl mx-auto px-8;
}

.container-full {
  @apply max-w-7xl mx-auto px-4;
}

/* Responsive grid system */
.grid-responsive {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-responsive-sm {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-responsive-lg {
  @apply grid gap-8;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Responsive spacing */
.spacing-responsive {
  @apply p-4 md:p-6 lg:p-8;
}

.spacing-responsive-sm {
  @apply p-2 md:p-4 lg:p-6;
}

.spacing-responsive-lg {
  @apply p-6 md:p-8 lg:p-12;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .mobile-stack > * {
    @apply w-full mb-4;
  }
  
  .mobile-center {
    @apply text-center;
  }
  
  .mobile-hide {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

@media (min-width: 768px) {
  .tablet-show {
    @apply block;
  }
  
  .tablet-flex {
    @apply flex;
  }
  
  .tablet-grid {
    @apply grid;
  }
}

@media (min-width: 1024px) {
  .desktop-show {
    @apply block;
  }
  
  .desktop-sidebar {
    @apply w-64 flex-shrink-0;
  }
  
  .desktop-main {
    @apply flex-1 min-w-0;
  }
}

/* ===== Empty States and Error Messages ===== */

/* Empty state container */
.empty-state {
  @apply flex flex-col items-center justify-center py-16 px-8 text-center;
  min-height: 300px;
}

.empty-state-icon {
  @apply text-6xl mb-6 opacity-30;
}

.empty-state-title {
  @apply text-hierarchy-4 mb-3 text-gray-700;
}

.empty-state-description {
  @apply text-hierarchy-6 mb-8 max-w-md text-gray-500;
}

.empty-state-action {
  @apply inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg;
  @apply hover:bg-primary-600 transition-colors duration-200;
}

/* Error message styles */
.error-message {
  @apply bg-gradient-to-r from-error-50 to-error-100 border border-error-200;
  @apply rounded-xl p-6 mb-6;
}

.error-message-icon {
  @apply text-error-500 text-2xl mb-3;
}

.error-message-title {
  @apply text-hierarchy-5 text-error-800 mb-2;
}

.error-message-description {
  @apply text-hierarchy-6 text-error-700 mb-4;
}

.error-message-actions {
  @apply flex flex-wrap gap-3;
}

/* Success message styles */
.success-message {
  @apply bg-gradient-to-r from-success-50 to-success-100 border border-success-200;
  @apply rounded-xl p-6 mb-6;
}

.success-message-icon {
  @apply text-success-500 text-2xl mb-3;
}

.success-message-title {
  @apply text-hierarchy-5 text-success-800 mb-2;
}

.success-message-description {
  @apply text-hierarchy-6 text-success-700;
}

/* Warning message styles */
.warning-message {
  @apply bg-gradient-to-r from-warning-50 to-warning-100 border border-warning-200;
  @apply rounded-xl p-6 mb-6;
}

.warning-message-icon {
  @apply text-warning-500 text-2xl mb-3;
}

.warning-message-title {
  @apply text-hierarchy-5 text-warning-800 mb-2;
}

.warning-message-description {
  @apply text-hierarchy-6 text-warning-700;
}

/* ===== Professional Icons and Visual Elements ===== */

/* Icon containers */
.icon-container {
  @apply inline-flex items-center justify-center rounded-full;
}

.icon-container-sm {
  @apply icon-container w-8 h-8 text-sm;
}

.icon-container-md {
  @apply icon-container w-12 h-12 text-lg;
}

.icon-container-lg {
  @apply icon-container w-16 h-16 text-xl;
}

.icon-container-xl {
  @apply icon-container w-20 h-20 text-2xl;
}

/* Icon variants */
.icon-primary {
  @apply bg-primary-100 text-primary-600;
}

.icon-secondary {
  @apply bg-secondary-100 text-secondary-600;
}

.icon-success {
  @apply bg-success-100 text-success-600;
}

.icon-warning {
  @apply bg-warning-100 text-warning-600;
}

.icon-error {
  @apply bg-error-100 text-error-600;
}

.icon-gray {
  @apply bg-gray-100 text-gray-600;
}

/* Status indicators */
.status-indicator {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.status-online {
  @apply status-indicator bg-success-100 text-success-800;
}

.status-offline {
  @apply status-indicator bg-gray-100 text-gray-800;
}

.status-busy {
  @apply status-indicator bg-warning-100 text-warning-800;
}

.status-error {
  @apply status-indicator bg-error-100 text-error-800;
}

/* Progress indicators */
.progress-indicator {
  @apply flex items-center space-x-3;
}

.progress-step {
  @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium;
  @apply border-2 transition-all duration-200;
}

.progress-step-completed {
  @apply progress-step bg-success-500 border-success-500 text-white;
}

.progress-step-current {
  @apply progress-step bg-primary-500 border-primary-500 text-white;
}

.progress-step-pending {
  @apply progress-step bg-gray-100 border-gray-300 text-gray-500;
}

.progress-connector {
  @apply flex-1 h-0.5 bg-gray-200;
}

.progress-connector-completed {
  @apply progress-connector bg-success-500;
}

/* ===== Content Spacing and Alignment ===== */

/* Spacing system */
.space-section {
  @apply mb-16;
}

.space-subsection {
  @apply mb-12;
}

.space-component {
  @apply mb-8;
}

.space-element {
  @apply mb-6;
}

.space-item {
  @apply mb-4;
}

.space-tight {
  @apply mb-2;
}

/* Alignment utilities */
.align-content-start {
  @apply items-start text-left;
}

.align-content-center {
  @apply items-center text-center;
}

.align-content-end {
  @apply items-end text-right;
}

/* Content width constraints */
.content-width-narrow {
  @apply max-w-2xl;
}

.content-width-medium {
  @apply max-w-4xl;
}

.content-width-wide {
  @apply max-w-6xl;
}

.content-width-full {
  @apply max-w-none;
}

/* Reading width optimization */
.reading-width {
  @apply max-w-prose;
  line-height: 1.7;
}

/* ===== Component Style Guide ===== */

/* Card system */
.card-base {
  @apply bg-white rounded-xl border border-gray-200 shadow-sm;
  @apply hover:shadow-md transition-shadow duration-200;
}

.card-elevated {
  @apply card-base shadow-lg hover:shadow-xl;
}

.card-interactive {
  @apply card-base cursor-pointer;
  @apply hover:border-gray-300 hover:-translate-y-1;
  @apply transition-all duration-200;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl;
}

/* List system */
.list-base {
  @apply space-y-1;
}

.list-item {
  @apply flex items-center px-4 py-3 rounded-lg;
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.list-item-active {
  @apply list-item bg-primary-50 text-primary-700;
  @apply border-l-4 border-primary-500;
}

.list-divider {
  @apply border-t border-gray-200 my-2;
}

/* Form system */
.form-group {
  @apply mb-6;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg;
  @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply transition-colors duration-200;
}

.form-help {
  @apply mt-2 text-sm text-gray-500;
}

.form-error {
  @apply mt-2 text-sm text-error-600;
}

/* ===== Dark Mode Adaptations ===== */
@media (prefers-color-scheme: dark) {
  .bg-pattern-dots {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }
  
  .bg-pattern-grid {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }
  
  .bg-gradient-subtle {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
  
  .text-hierarchy-1,
  .text-hierarchy-2,
  .text-hierarchy-3 {
    @apply text-white;
  }
  
  .text-hierarchy-4,
  .text-hierarchy-5,
  .text-hierarchy-6 {
    @apply text-gray-300;
  }
  
  .card-base {
    @apply bg-gray-800 border-gray-700;
  }
  
  .card-footer {
    @apply bg-gray-900;
  }
  
  .list-item {
    @apply hover:bg-gray-700;
  }
  
  .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}

/* ===== Print Styles ===== */
@media print {
  .bg-pattern-dots,
  .bg-pattern-grid,
  .bg-pattern-diagonal {
    background: none !important;
  }
  
  .card-base,
  .card-elevated,
  .card-interactive {
    @apply shadow-none border border-gray-400;
  }
  
  .empty-state,
  .error-message,
  .success-message,
  .warning-message {
    @apply shadow-none;
  }
}

/* ===== Accessibility Enhancements ===== */
@media (prefers-reduced-motion: reduce) {
  .card-interactive {
    @apply hover:transform-none;
  }
  
  .list-item,
  .form-input {
    transition: none !important;
  }
}

/* Focus visible for keyboard navigation */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card-base {
    @apply border-2;
  }
  
  .text-hierarchy-1,
  .text-hierarchy-2,
  .text-hierarchy-3 {
    @apply text-black;
  }
  
  .status-indicator {
    @apply border-2 border-current;
  }
} 