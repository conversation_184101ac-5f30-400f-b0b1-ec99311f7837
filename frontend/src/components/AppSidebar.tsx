'use client';

import React from 'react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from '@/components/ui/sidebar';
import {
  Home,
  MessageSquare,
  Database,
  BarChart3,
  Brain,
  Target,
  TrendingUp,
  Bug,
  Settings,
  ChevronRight,
  FileText,
} from 'lucide-react';
import { AppMode } from '@/types';

interface AppSidebarProps {
  currentMode: AppMode;
  onModeChange: (mode: AppMode) => void;
}

const navigationItems = [
  {
    group: '主要功能',
    items: [
      {
        mode: 'home' as AppMode,
        label: '首页',
        icon: Home,
        description: '系统概览和状态',
      },
      {
        mode: 'ai-agent' as AppMode,
        label: 'AI 助手',
        icon: MessageSquare,
        description: '智能投资分析对话',
      },
      {
        mode: 'data-query' as AppMode,
        label: '数据查询',
        icon: Database,
        description: '股票数据和技术指标',
      },
    ],
  },
  {
    group: '分析工具',
    items: [
      {
        mode: 'factor-management' as AppMode,
        label: '因子管理',
        icon: BarChart3,
        description: '因子分析和管理',
      },
      {
        mode: 'ml-models' as AppMode,
        label: 'ML 模型',
        icon: Brain,
        description: '机器学习模型训练',
      },
      {
        mode: 'stock-scoring' as AppMode,
        label: '股票评分',
        icon: Target,
        description: '股票综合评分系统',
      },
      {
        mode: 'divergence-scanner' as AppMode,
        label: '背离扫描',
        icon: TrendingUp,
        description: 'MACD背离检测',
      },
    ],
  },
  {
    group: '开发工具',
    items: [
      {
        mode: 'markdown-test' as AppMode,
        label: 'Markdown 测试',
        icon: FileText,
        description: 'Markdown 渲染测试',
      },
      {
        mode: 'debug' as AppMode,
        label: '调试面板',
        icon: Bug,
        description: '系统调试和测试',
      },
    ],
  },
];

export default function AppSidebar({ currentMode, onModeChange }: AppSidebarProps) {
  return (
    <Sidebar variant="inset">
      <SidebarHeader className="border-b border-border/40">
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <BarChart3 className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">金融投资助手</span>
            <span className="truncate text-xs text-muted-foreground">
              智能分析平台
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {navigationItems.map((group) => (
          <SidebarGroup key={group.group}>
            <SidebarGroupLabel>{group.group}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentMode === item.mode;
                  
                  return (
                    <SidebarMenuItem key={item.mode}>
                      <SidebarMenuButton
                        onClick={() => onModeChange(item.mode)}
                        isActive={isActive}
                        tooltip={item.description}
                        className="group relative"
                      >
                        <Icon className="h-4 w-4" />
                        <span className="flex-1 truncate">{item.label}</span>
                        {isActive && (
                          <ChevronRight className="h-3 w-3 opacity-60" />
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="border-t border-border/40">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <button className="w-full">
                <Settings className="h-4 w-4" />
                <span>设置</span>
              </button>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
} 