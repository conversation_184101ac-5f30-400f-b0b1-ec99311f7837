'use client';

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Bot, User, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatLoadingStatesProps {
  variant?: 'message' | 'typing' | 'conversation' | 'chart';
  count?: number;
  className?: string;
}

// Loading skeleton for a single message
const MessageSkeleton: React.FC<{ isUser?: boolean; className?: string }> = ({ 
  isUser = false, 
  className = '' 
}) => (
  <div className={cn(
    "flex mb-6 group",
    isUser ? "justify-end" : "justify-start",
    className
  )}>
    <div className={cn(
      "flex gap-3 max-w-[85%] md:max-w-[75%]",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar skeleton */}
      <Avatar className="w-8 h-8 flex-shrink-0 mt-1">
        <AvatarFallback className={cn(
          "text-xs font-medium",
          isUser 
            ? "bg-blue-500 text-white" 
            : "bg-purple-500 text-white"
        )}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </AvatarFallback>
      </Avatar>

      {/* Message content skeleton */}
      <div className={cn(
        "flex flex-col gap-1",
        isUser ? "items-end" : "items-start"
      )}>
        {/* Agent badge skeleton */}
        {!isUser && (
          <Skeleton className="h-5 w-16 mb-2" />
        )}

        {/* Message bubble skeleton */}
        <Card className={cn(
          "relative transition-all duration-200",
          isUser 
            ? "bg-blue-500 text-white border-blue-500" 
            : "bg-card border-border"
        )}>
          <CardContent className="p-3 md:p-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-4/5" />
              <Skeleton className="h-4 w-3/5" />
            </div>
          </CardContent>
        </Card>

        {/* Metadata skeleton */}
        <div className={cn(
          "flex items-center gap-2",
          isUser ? "flex-row-reverse" : "flex-row"
        )}>
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-3 rounded-full" />
        </div>
      </div>
    </div>
  </div>
);

// Typing indicator with animation
const TypingIndicator: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={cn("flex justify-start mb-6", className)}>
    <div className="flex gap-3 max-w-[85%] md:max-w-[75%]">
      {/* Avatar */}
      <Avatar className="w-8 h-8 flex-shrink-0 mt-1">
        <AvatarFallback className="bg-purple-500 text-white">
          <Bot className="w-4 h-4" />
        </AvatarFallback>
      </Avatar>

      {/* Typing content */}
      <div className="flex flex-col gap-1">
        <Badge variant="secondary" className="text-xs mb-2 animate-pulse">
          <Loader2 className="w-3 h-3 mr-1 animate-spin" />
          AI is thinking...
        </Badge>

        <Card className="bg-card border-border">
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-1">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span className="text-sm text-muted-foreground ml-2">Generating response...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
);

// Chart loading skeleton
const ChartSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={cn("flex justify-start mb-6", className)}>
    <div className="max-w-[95%]">
      <Card className="bg-card border-border">
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Chart header */}
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-5 w-20" />
            </div>
            
            {/* Chart area */}
            <div className="relative">
              <Skeleton className="h-64 w-full rounded-lg" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Loading chart data...</p>
                </div>
              </div>
            </div>
            
            {/* Chart controls */}
            <div className="flex gap-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

// Conversation loading skeleton
const ConversationSkeleton: React.FC<{ count?: number; className?: string }> = ({ 
  count = 3, 
  className = '' 
}) => (
  <div className={cn("space-y-4", className)}>
    {Array.from({ length: count }, (_, index) => (
      <div key={index}>
        {/* User message */}
        <MessageSkeleton isUser={true} />
        {/* Assistant message */}
        <MessageSkeleton isUser={false} />
      </div>
    ))}
  </div>
);

// Main component
const ChatLoadingStates: React.FC<ChatLoadingStatesProps> = ({
  variant = 'message',
  count = 1,
  className = ''
}) => {
  switch (variant) {
    case 'typing':
      return <TypingIndicator className={className} />;
    
    case 'chart':
      return <ChartSkeleton className={className} />;
    
    case 'conversation':
      return <ConversationSkeleton count={count} className={className} />;
    
    case 'message':
    default:
      return (
        <div className={cn("space-y-4", className)}>
          {Array.from({ length: count }, (_, index) => (
            <MessageSkeleton key={index} isUser={index % 2 === 0} />
          ))}
        </div>
      );
  }
};

export default ChatLoadingStates; 