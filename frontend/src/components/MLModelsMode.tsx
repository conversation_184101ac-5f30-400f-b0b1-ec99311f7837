'use client';

import React from 'react';
import { AppMode } from '@/types';

interface MLModelsModeProps {
  mlMode: string;
  setMLMode: React.Dispatch<React.SetStateAction<any>>;
  models: any[];
  setModels: React.Dispatch<React.SetStateAction<any[]>>;
  trainingResult: any;
  setTrainingResult: React.Dispatch<React.SetStateAction<any>>;
  predictionResult: any;
  setPredictionResult: React.Dispatch<React.SetStateAction<any>>;
  trainingSymbols: string;
  setTrainingSymbols: React.Dispatch<React.SetStateAction<string>>;
  predictionSymbols: string;
  setPredictionSymbols: React.Dispatch<React.SetStateAction<string>>;
  selectedModelId: string;
  setSelectedModelId: React.Dispatch<React.SetStateAction<string>>;
  isTraining: boolean;
  setIsTraining: React.Dispatch<React.SetStateAction<boolean>>;
  isPredicting: boolean;
  setIsPredicting: React.Dispatch<React.SetStateAction<boolean>>;
  trainingStocks: string[];
  setTrainingStocks: React.Dispatch<React.SetStateAction<string[]>>;
  predictionStocks: string[];
  setPredictionStocks: React.Dispatch<React.SetStateAction<string[]>>;
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const MLModelsMode: React.FC<MLModelsModeProps> = ({
  setMode,
}) => {
  return (
    <div className="mode-container">
      <div className="mode-header">
        <button
          onClick={() => setMode('home')}
          className="back-button"
        >
          ← 返回首页
        </button>
        <h1>🧠 机器学习</h1>
        <p>使用机器学习模型进行股票预测和分析</p>
      </div>
      
      <div className="ml-content">
        <div className="text-center p-8">
          <div className="text-4xl mb-4">🧠</div>
          <h3 className="text-lg font-semibold mb-2">机器学习模块</h3>
          <p className="text-gray-600">模型训练和预测功能即将推出</p>
        </div>
      </div>
    </div>
  );
};

export default MLModelsMode; 