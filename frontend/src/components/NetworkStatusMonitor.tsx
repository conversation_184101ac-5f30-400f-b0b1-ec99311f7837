'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { testConnectivity } from '@/utils/api';
import LoadingSpinner from './LoadingSpinner';

interface NetworkStatusMonitorProps {
  onStatusChange?: (status: NetworkStatus) => void;
  className?: string;
  showDetails?: boolean;
  autoReconnect?: boolean;
  checkInterval?: number; // in milliseconds
}

export interface NetworkStatus {
  isOnline: boolean;
  isConnected: boolean;
  lastCheck: number;
  error?: string;
  pingTime?: number;
  status: 'online' | 'offline' | 'checking' | 'error' | 'reconnecting';
}

const NetworkStatusMonitor: React.FC<NetworkStatusMonitorProps> = ({
  onStatusChange,
  className = '',
  showDetails = false,
  autoReconnect = true,
  checkInterval = 300000, // 5 minutes instead of 30 seconds to reduce log spam
}) => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isConnected: false,
    lastCheck: 0,
    status: 'checking',
  });

  const [isChecking, setIsChecking] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);

  // Check backend connectivity
  const checkConnectivity = useCallback(async (): Promise<void> => {
    if (isChecking) return;

    setIsChecking(true);
    const startTime = Date.now();

    try {
      const isConnected = await testConnectivity();
      const pingTime = Date.now() - startTime;

      const newStatus: NetworkStatus = {
        isOnline: navigator.onLine,
        isConnected,
        lastCheck: Date.now(),
        pingTime,
        status: isConnected ? 'online' : 'error',
      };

      setNetworkStatus(newStatus);
      onStatusChange?.(newStatus);

      if (isConnected) {
        setReconnectAttempts(0);
      }
    } catch (error: any) {
      const newStatus: NetworkStatus = {
        isOnline: navigator.onLine,
        isConnected: false,
        lastCheck: Date.now(),
        error: error.message,
        status: navigator.onLine ? 'error' : 'offline',
      };

      setNetworkStatus(newStatus);
      onStatusChange?.(newStatus);
    } finally {
      setIsChecking(false);
    }
  }, [isChecking, onStatusChange]);

  // Auto-reconnect logic
  const attemptReconnect = useCallback(async (): Promise<void> => {
    if (!autoReconnect || isChecking || networkStatus.isConnected) return;

    setReconnectAttempts(prev => prev + 1);
    
    setNetworkStatus(prev => ({
      ...prev,
      status: 'reconnecting',
    }));

    // Exponential backoff for reconnection attempts
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
    
    setTimeout(async () => {
      await checkConnectivity();
    }, delay);
  }, [autoReconnect, isChecking, networkStatus.isConnected, reconnectAttempts, checkConnectivity]);

  // Browser network event handlers
  useEffect(() => {
    const handleOnline = () => {
      setNetworkStatus(prev => ({
        ...prev,
        isOnline: true,
        status: 'checking',
      }));
      // Check connectivity when coming back online
      setTimeout(checkConnectivity, 1000);
    };

    const handleOffline = () => {
      setNetworkStatus(prev => ({
        ...prev,
        isOnline: false,
        isConnected: false,
        status: 'offline',
      }));
      setReconnectAttempts(0);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [checkConnectivity]);

  // Periodic connectivity checks
  useEffect(() => {
    // Initial check
    checkConnectivity();

    // Set up periodic checks
    const interval = setInterval(() => {
      if (navigator.onLine) {
        checkConnectivity();
      }
    }, checkInterval);

    return () => clearInterval(interval);
  }, [checkConnectivity, checkInterval]);

  // Auto-reconnect when disconnected
  useEffect(() => {
    if (!networkStatus.isConnected && networkStatus.isOnline && autoReconnect) {
      const maxAttempts = 5;
      if (reconnectAttempts < maxAttempts) {
        const timeoutId = setTimeout(attemptReconnect, 2000);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [networkStatus.isConnected, networkStatus.isOnline, autoReconnect, reconnectAttempts, attemptReconnect]);

  const getStatusIcon = (): string => {
    switch (networkStatus.status) {
      case 'online':
        return '🟢';
      case 'offline':
        return '🔴';
      case 'checking':
      case 'reconnecting':
        return '🟡';
      case 'error':
        return '🟠';
      default:
        return '❓';
    }
  };

  const getStatusText = (): string => {
    switch (networkStatus.status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'checking':
        return '检查中';
      case 'reconnecting':
        return '重连中';
      case 'error':
        return '连接错误';
      default:
        return '未知';
    }
  };

  const getStatusColor = (): string => {
    switch (networkStatus.status) {
      case 'online':
        return 'text-green-600';
      case 'offline':
        return 'text-red-600';
      case 'checking':
      case 'reconnecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatLastCheck = (): string => {
    if (!networkStatus.lastCheck) return '未检查';
    
    const diff = Date.now() - networkStatus.lastCheck;
    if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`;
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    return `${Math.floor(diff / 3600000)}小时前`;
  };

  const handleManualCheck = () => {
    if (!isChecking) {
      checkConnectivity();
    }
  };

  if (!showDetails) {
    // Compact view
    return (
      <div 
        className={`flex items-center space-x-2 ${className}`}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <div className="relative">
          <span className="text-sm">{getStatusIcon()}</span>
          <span className={`text-xs ${getStatusColor()}`}>{getStatusText()}</span>
          
          {(networkStatus.status === 'checking' || networkStatus.status === 'reconnecting') && (
            <div className="absolute -right-1 -top-1">
              <LoadingSpinner size="sm" color="gray" className="w-3 h-3" />
            </div>
          )}

          {/* Tooltip */}
          {showTooltip && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg z-50 whitespace-nowrap">
              <div>状态: {getStatusText()}</div>
              <div>最后检查: {formatLastCheck()}</div>
              {networkStatus.pingTime && (
                <div>响应时间: {networkStatus.pingTime}ms</div>
              )}
              {reconnectAttempts > 0 && (
                <div>重连次数: {reconnectAttempts}</div>
              )}
              {networkStatus.error && (
                <div className="text-red-300">错误: {networkStatus.error}</div>
              )}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Detailed view
  return (
    <div className={`border rounded-lg p-3 bg-white ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon()}</span>
          <div>
            <div className={`font-medium text-sm ${getStatusColor()}`}>
              网络状态: {getStatusText()}
            </div>
            <div className="text-xs text-gray-500">
              最后检查: {formatLastCheck()}
            </div>
          </div>
        </div>

        <button
          onClick={handleManualCheck}
          disabled={isChecking}
          className="px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 disabled:opacity-50 transition-colors"
        >
          {isChecking ? (
            <div className="flex items-center space-x-1">
              <LoadingSpinner size="sm" />
              <span>检查中</span>
            </div>
          ) : (
            '检查连接'
          )}
        </button>
      </div>

      {/* Status details */}
      <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
        <div>
          <span className="font-medium">浏览器网络:</span> 
          <span className={networkStatus.isOnline ? 'text-green-600' : 'text-red-600'}>
            {networkStatus.isOnline ? '已连接' : '已断开'}
          </span>
        </div>
        <div>
          <span className="font-medium">后端连接:</span> 
          <span className={networkStatus.isConnected ? 'text-green-600' : 'text-red-600'}>
            {networkStatus.isConnected ? '正常' : '异常'}
          </span>
        </div>
        {networkStatus.pingTime && (
          <div>
            <span className="font-medium">响应时间:</span> 
            <span className={networkStatus.pingTime < 1000 ? 'text-green-600' : networkStatus.pingTime < 3000 ? 'text-yellow-600' : 'text-red-600'}>
              {networkStatus.pingTime}ms
            </span>
          </div>
        )}
        {reconnectAttempts > 0 && (
          <div>
            <span className="font-medium">重连次数:</span> 
            <span className="text-orange-600">{reconnectAttempts}</span>
          </div>
        )}
      </div>

      {/* Error message */}
      {networkStatus.error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
          <strong>错误详情:</strong> {networkStatus.error}
        </div>
      )}

      {/* Reconnection status */}
      {networkStatus.status === 'reconnecting' && (
        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700 flex items-center space-x-2">
          <LoadingSpinner size="sm" color="gray" />
          <span>正在尝试重新连接... (第 {reconnectAttempts} 次尝试)</span>
        </div>
      )}
    </div>
  );
};

export default NetworkStatusMonitor; 