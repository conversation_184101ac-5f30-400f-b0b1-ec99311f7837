'use client';

import React, { useState } from 'react';
import { streamChat } from '@/utils/api';
import SimpleAIChat from './SimpleAIChat';

const ChatDebug: React.FC = () => {
  const [message, setMessage] = useState('你好');
  const [response, setResponse] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const testStreamChat = async () => {
    setIsLoading(true);
    setResponse([]);
    
    try {
      await streamChat(
        { message },
        (data) => {
          console.log('Debug - Received data:', data);
          setResponse(prev => [...prev, JSON.stringify(data, null, 2)]);
        },
        (error) => {
          console.error('Debug - Received error:', error);
          setResponse(prev => [...prev, `ERROR: ${error}`]);
        }
      );
    } catch (error) {
      console.error('Debug - Exception:', error);
      setResponse(prev => [...prev, `EXCEPTION: ${error}`]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>Chat Debug Tool</h2>
      
      <div style={{ marginBottom: '40px' }}>
        <h3>简化聊天界面测试</h3>
        <SimpleAIChat />
      </div>
      <div style={{ marginBottom: '10px' }}>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          style={{ width: '300px', padding: '5px' }}
        />
        <button 
          onClick={testStreamChat} 
          disabled={isLoading}
          style={{ marginLeft: '10px', padding: '5px 10px' }}
        >
          {isLoading ? 'Testing...' : 'Test Stream Chat'}
        </button>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Streaming Response:</h3>
        <div style={{ 
          border: '1px solid #ccc', 
          padding: '10px', 
          minHeight: '200px', 
          backgroundColor: '#f9f9f9',
          whiteSpace: 'pre-wrap',
          fontSize: '12px'
        }}>
          {response.map((data, index) => (
            <div key={index} style={{ marginBottom: '5px', borderBottom: '1px solid #eee', paddingBottom: '5px' }}>
              {data}
            </div>
          ))}
          {response.length === 0 && !isLoading && 'No response yet...'}
          {isLoading && 'Waiting for response...'}
        </div>
      </div>
    </div>
  );
};

export default ChatDebug; 