'use client';

import React, { useState } from 'react';

interface HeaderAction {
  id: string;
  label: string;
  icon: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  disabled?: boolean;
  badge?: string | number;
}

interface EnhancedHeaderProps {
  title: string;
  subtitle?: string;
  onBack?: () => void;
  backLabel?: string;
  actions?: HeaderAction[];
  showGradient?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const EnhancedHeader: React.FC<EnhancedHeaderProps> = ({
  title,
  subtitle,
  onBack,
  backLabel = "返回",
  actions = [],
  showGradient = true,
  className = '',
  children
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get button styles based on variant
  const getButtonStyles = (variant: HeaderAction['variant'] = 'secondary') => {
    const baseStyles = `
      relative inline-flex items-center space-x-2 px-4 py-2 rounded-xl
      font-medium text-sm transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      transform hover:scale-105 active:scale-95
    `;

    switch (variant) {
      case 'primary':
        return `${baseStyles}
          bg-white/20 text-white border border-white/30
          hover:bg-white/30 hover:border-white/50
          focus:ring-white/50 backdrop-blur-sm
          shadow-lg hover:shadow-xl
        `;
      case 'ghost':
        return `${baseStyles}
          bg-transparent text-white/80 border border-transparent
          hover:bg-white/10 hover:text-white
          focus:ring-white/30
        `;
      default: // secondary
        return `${baseStyles}
          bg-white/10 text-white border border-white/20
          hover:bg-white/20 hover:border-white/40
          focus:ring-white/30 backdrop-blur-sm
        `;
    }
  };

  return (
    <header 
      className={`
        relative overflow-hidden
        ${showGradient 
          ? 'bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600' 
          : 'bg-primary-600'
        }
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated gradient overlay */}
        {showGradient && (
          <div className={`
            absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent
            transform transition-transform duration-1000
            ${isHovered ? 'translate-x-full' : '-translate-x-full'}
          `} />
        )}
        
        {/* Geometric patterns */}
        <div className="absolute top-0 right-0 w-64 h-64 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent rounded-full transform rotate-45 scale-150" />
        </div>
        <div className="absolute bottom-0 left-0 w-32 h-32 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-tr from-white to-transparent rounded-full transform -rotate-12" />
        </div>
      </div>

      {/* Header content */}
      <div className="relative z-10 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side - Back button and title */}
          <div className="flex items-center space-x-4">
            {/* Back button */}
            {onBack && (
              <button
                onClick={onBack}
                className={getButtonStyles('ghost')}
              >
                <span className="text-lg">←</span>
                <span>{backLabel}</span>
              </button>
            )}

            {/* Title section */}
            <div className="flex flex-col">
              <h1 className="text-2xl lg:text-3xl font-bold text-white leading-tight">
                {title}
              </h1>
              {subtitle && (
                <p className="text-sm lg:text-base text-white/80 mt-1 leading-relaxed">
                  {subtitle}
                </p>
              )}
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-3">
            {actions.map((action) => (
              <button
                key={action.id}
                onClick={action.onClick}
                disabled={action.disabled}
                className={getButtonStyles(action.variant)}
                title={action.label}
              >
                <span className="text-lg">{action.icon}</span>
                <span className="hidden sm:inline">{action.label}</span>
                
                {/* Badge */}
                {action.badge && (
                  <span className="
                    absolute -top-1 -right-1 min-w-[1.25rem] h-5
                    bg-error-500 text-white text-xs font-bold
                    rounded-full flex items-center justify-center
                    shadow-lg border-2 border-white
                  ">
                    {action.badge}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Additional content */}
        {children && (
          <div className="mt-4 border-t border-white/20 pt-4">
            {children}
          </div>
        )}
      </div>

      {/* Bottom border with gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent" />
    </header>
  );
};

export default EnhancedHeader; 