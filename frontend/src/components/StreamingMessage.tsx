'use client';

import React, { useEffect, useState, useRef } from 'react';
import { EnhancedMessage, MessageStatus } from '@/hooks/useChatStreaming';
import LoadingSpinner from './LoadingSpinner';
import MessageActions from './MessageActions';
import MarkdownRenderer from './MarkdownRenderer';
import ChartMessage from './ChartMessage';
import MessageBubble from './MessageBubble';
import { BadgeIndicator, StatusIndicator, DotIndicator } from './VisualIndicator';
import { preprocessMessageContent, preprocessStreamingContent } from '@/utils/messagePreprocessor';
import './StreamingMarkdown.css';

interface StreamingMessageProps {
  message: EnhancedMessage;
  conversationId?: string;
  isCurrentlyStreaming?: boolean;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
}

// Message status indicator component
const MessageStatusIndicator: React.FC<{ status: MessageStatus; retryCount?: number }> = ({ 
  status, 
  retryCount 
}) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'sending':
        return { indicator: <StatusIndicator status="sending" size="xs" />, text: '发送中', color: 'text-blue-600' };
      case 'sent':
        return { indicator: <StatusIndicator status="sent" size="xs" />, text: '已发送', color: 'text-green-600' };
      case 'streaming':
        return { indicator: <StatusIndicator status="streaming" size="xs" />, text: '接收中', color: 'text-blue-600' };
      case 'complete':
        return { indicator: <StatusIndicator status="complete" size="xs" />, text: '完成', color: 'text-green-600' };
      case 'failed':
        return { 
          indicator: <StatusIndicator status="failed" size="xs" />, 
          text: retryCount ? `失败 (重试 ${retryCount})` : '失败', 
          color: 'text-red-600' 
        };
      case 'cancelled':
        return { indicator: <StatusIndicator status="cancelled" size="xs" />, text: '已取消', color: 'text-gray-600' };
      case 'timeout':
        return { indicator: <StatusIndicator status="timeout" size="xs" />, text: '超时', color: 'text-orange-600' };
      default:
        return { indicator: <DotIndicator category="navigation" size="xs" />, text: status, color: 'text-gray-600' };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`flex items-center space-x-1 text-xs ${statusInfo.color}`}>
      <span>{statusInfo.indicator}</span>
      <span>{statusInfo.text}</span>
    </div>
  );
};

// Enhanced typewriter component that works with both plain text and markdown
const TypewriterMarkdown: React.FC<{ 
  text: string; 
  displayText?: string; 
  isComplete: boolean;
  isAssistant?: boolean;
  enableMarkdown?: boolean;
}> = ({ text, displayText, isComplete, isAssistant = false, enableMarkdown = true }) => {
  const [cursorVisible, setCursorVisible] = useState(true);
  const cursorInterval = useRef<NodeJS.Timeout | null>(null);

  // Handle cursor blinking for active typing
  useEffect(() => {
    if (!isComplete) {
      cursorInterval.current = setInterval(() => {
        setCursorVisible(prev => !prev);
      }, 500);
    } else {
      setCursorVisible(false);
    }

    return () => {
      if (cursorInterval.current) {
        clearInterval(cursorInterval.current);
      }
    };
  }, [isComplete]);

  const textToShow = displayText !== undefined ? displayText : text;

  // For assistant messages, use MarkdownRenderer if markdown is enabled
  if (isAssistant && enableMarkdown) {
    return (
      <div className="relative">
        <MarkdownRenderer 
          content={textToShow}
          enableCopy={isComplete}
          enableExport={isComplete && textToShow.length > 500}
          lazy={textToShow.length > 10000}
          className="streaming-markdown"
        />
        {!isComplete && cursorVisible && (
          <span className="inline-block ml-1 w-2 h-5 bg-current animate-pulse">|</span>
        )}
      </div>
    );
  }

  // For user messages or plain text, use simple whitespace-pre-wrap
  return (
    <div className="relative">
      <div className="whitespace-pre-wrap">{textToShow}</div>
      {!isComplete && cursorVisible && (
        <span className="inline-block ml-1 w-2 h-5 bg-current animate-pulse">|</span>
      )}
    </div>
  );
};

// Enhanced agent badge component with role identification
const AgentBadge: React.FC<{ agent?: string; showRole?: boolean }> = ({ agent, showRole = true }) => {
  if (!agent) return null;

  const getAgentInfo = (agentName: string) => {
    switch (agentName.toLowerCase()) {
      case 'researcher':
      case 'research_agent':
        return { 
          emoji: '🔍', 
          name: '研究员', 
          role: '数据研究',
          color: 'bg-blue-100 text-blue-800 border-blue-300',
          description: '负责市场数据收集和基础分析'
        };
      case 'analyst':
      case 'analysis_agent':
        return { 
          emoji: '📊', 
          name: '分析师', 
          role: '技术分析',
          color: 'bg-green-100 text-green-800 border-green-300',
          description: '负责技术指标分析和图表解读'
        };
      case 'programmer':
      case 'code_agent':
        return { 
          emoji: '💻', 
          name: '程序员', 
          role: '代码执行',
          color: 'bg-purple-100 text-purple-800 border-purple-300',
          description: '负责数据处理和算法实现'
        };
      case 'advisor':
      case 'investment_advisor':
        return { 
          emoji: '🎯', 
          name: '投资顾问', 
          role: '投资建议',
          color: 'bg-orange-100 text-orange-800 border-orange-300',
          description: '负责投资策略制定和风险评估'
        };
      case 'coordinator':
      case 'workflow_coordinator':
        return { 
          emoji: '🤝', 
          name: '协调员', 
          role: '流程协调',
          color: 'bg-indigo-100 text-indigo-800 border-indigo-300',
          description: '负责多智能体协作和任务分配'
        };
      case 'validator':
      case 'quality_validator':
        return { 
          emoji: '✅', 
          name: '验证员', 
          role: '质量验证',
          color: 'bg-teal-100 text-teal-800 border-teal-300',
          description: '负责结果验证和质量控制'
        };
      case 'risk_manager':
        return { 
          emoji: '⚠️', 
          name: '风险管理员', 
          role: '风险控制',
          color: 'bg-red-100 text-red-800 border-red-300',
          description: '负责风险识别和风险警告'
        };
      case 'portfolio_manager':
        return { 
          emoji: '💼', 
          name: '组合管理员', 
          role: '组合管理',
          color: 'bg-emerald-100 text-emerald-800 border-emerald-300',
          description: '负责投资组合构建和优化'
        };
      case 'market_analyst':
        return { 
          emoji: '📰', 
          name: '市场分析师', 
          role: '市场分析',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-300',
          description: '负责市场动态分析和趋势预测'
        };
      case 'ai_strategist':
        return { 
          emoji: '🧠', 
          name: 'AI策略师', 
          role: 'AI策略',
          color: 'bg-violet-100 text-violet-800 border-violet-300',
          description: '负责AI模型策略和智能决策'
        };
      default:
        return { 
          emoji: 'assistant', 
          name: agent, 
          role: '智能助手',
          color: 'bg-gray-100 text-gray-800 border-gray-300',
          description: '通用AI助手'
        };
    }
  };

  const agentInfo = getAgentInfo(agent);

  return (
    <div 
      className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium border ${agentInfo.color} hover:shadow-sm transition-shadow`}
      title={agentInfo.description}
    >
      <span className="text-sm">{agentInfo.emoji}</span>
      <span className="font-semibold">{agentInfo.name}</span>
      {showRole && (
        <span className="text-xs opacity-75">• {agentInfo.role}</span>
      )}
    </div>
  );
};

// Progress indicator for workflow steps - REMOVED per user request
/*
const WorkflowProgress: React.FC<{ metadata?: any }> = ({ metadata }) => {
  if (!metadata || !metadata.progress) return null;

  const progress = Math.min(100, Math.max(0, metadata.progress));

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
        <span>{metadata.step || '处理中'}</span>
        <span>{progress}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-1.5">
        <div 
          className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
      {metadata.substep && (
        <div className="text-xs text-gray-400 mt-1">
          {metadata.substep}
        </div>
      )}
    </div>
  );
};
*/

// Message type indicator
const MessageTypeIndicator: React.FC<{ message: EnhancedMessage }> = ({ message }) => {
  const getTypeInfo = () => {
    // Check for chart messages first
    if (message.type === 'chart' || message.metadata?.messageType === 'chart') {
      return { 
        icon: '📊', 
        label: '图表数据', 
        color: 'bg-blue-50 text-blue-700 border-blue-200',
        priority: 'high',
        category: 'visualization'
      };
    }
    
    // Check for metadata-based message types
    if (message.metadata?.messageType) {
      switch (message.metadata.messageType) {
        case 'analysis':
          return { 
            icon: '📈', 
            label: '分析报告', 
            color: 'bg-green-50 text-green-700 border-green-200',
            priority: 'high',
            category: 'analysis'
          };
        case 'recommendation':
          return { 
            icon: '💡', 
            label: '投资建议', 
            color: 'bg-yellow-50 text-yellow-700 border-yellow-200',
            priority: 'critical',
            category: 'advice'
          };
        case 'warning':
          return { 
            icon: '⚠️', 
            label: '风险提醒', 
            color: 'bg-red-50 text-red-700 border-red-200',
            priority: 'critical',
            category: 'alert'
          };
        case 'summary':
          return { 
            icon: '📋', 
            label: '总结', 
            color: 'bg-purple-50 text-purple-700 border-purple-200',
            priority: 'medium',
            category: 'summary'
          };
        case 'data':
          return { 
            icon: '📊', 
            label: '数据查询', 
            color: 'bg-indigo-50 text-indigo-700 border-indigo-200',
            priority: 'medium',
            category: 'data'
          };
        case 'code':
          return { 
            icon: '💻', 
            label: '代码执行', 
            color: 'bg-gray-50 text-gray-700 border-gray-200',
            priority: 'medium',
            category: 'technical'
          };
        case 'research':
          return { 
            icon: '🔍', 
            label: '研究报告', 
            color: 'bg-teal-50 text-teal-700 border-teal-200',
            priority: 'high',
            category: 'research'
          };
        case 'market_update':
          return { 
            icon: '📰', 
            label: '市场动态', 
            color: 'bg-orange-50 text-orange-700 border-orange-200',
            priority: 'medium',
            category: 'news'
          };
        case 'portfolio':
          return { 
            icon: '💼', 
            label: '投资组合', 
            color: 'bg-emerald-50 text-emerald-700 border-emerald-200',
            priority: 'high',
            category: 'portfolio'
          };
        case 'ai_insight':
          return { 
            icon: '🧠', 
            label: 'AI洞察', 
            color: 'bg-violet-50 text-violet-700 border-violet-200',
            priority: 'high',
            category: 'ai'
          };
        default:
          return null;
      }
    }

    // Check message content for automatic classification
    const content = message.content.toLowerCase();
    
    // Risk and warning keywords
    if (content.includes('风险') || content.includes('警告') || content.includes('注意') || 
        content.includes('danger') || content.includes('risk') || content.includes('warning')) {
      return { 
        icon: '⚠️', 
        label: '风险提醒', 
        color: 'bg-red-50 text-red-700 border-red-200',
        priority: 'critical',
        category: 'alert'
      };
    }
    
    // Investment recommendation keywords
    if (content.includes('建议') || content.includes('推荐') || content.includes('买入') || 
        content.includes('卖出') || content.includes('持有') || content.includes('recommend')) {
      return { 
        icon: '💡', 
        label: '投资建议', 
        color: 'bg-yellow-50 text-yellow-700 border-yellow-200',
        priority: 'critical',
        category: 'advice'
      };
    }
    
    // Analysis keywords
    if (content.includes('分析') || content.includes('技术') || content.includes('基本面') || 
        content.includes('analysis') || content.includes('technical') || content.includes('fundamental')) {
      return { 
        icon: '📈', 
        label: '分析报告', 
        color: 'bg-green-50 text-green-700 border-green-200',
        priority: 'high',
        category: 'analysis'
      };
    }
    
    // Summary keywords
    if (content.includes('总结') || content.includes('概述') || content.includes('summary') || 
        content.includes('overview') || content.includes('结论')) {
      return { 
        icon: '📋', 
        label: '总结', 
        color: 'bg-purple-50 text-purple-700 border-purple-200',
        priority: 'medium',
        category: 'summary'
      };
    }
    
    return null;
  };

  const typeInfo = getTypeInfo();
  if (!typeInfo) return null;

  // Priority indicator
  const getPriorityIndicator = (priority: string) => {
    switch (priority) {
      case 'critical':
        return '🔴';
      case 'high':
        return '🟡';
      case 'medium':
        return '🟢';
      default:
        return '';
    }
  };

  return (
    <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border ${typeInfo.color} mb-2`}>
      {typeInfo.priority && typeInfo.priority !== 'medium' && (
        <span className="mr-1">{getPriorityIndicator(typeInfo.priority)}</span>
      )}
      <span>{typeInfo.icon}</span>
      <span>{typeInfo.label}</span>
    </div>
  );
};

// Main streaming message component
const StreamingMessage: React.FC<StreamingMessageProps> = ({ 
  message,
  conversationId = 'default',
  isCurrentlyStreaming = false,
  onRetry,
  onEdit,
  onDelete,
  onReaction
}) => {
  // Use the new MessageBubble component for all message rendering
  return (
    <MessageBubble
      message={message}
      isStreaming={isCurrentlyStreaming}
      conversationId={conversationId}
      onRetry={onRetry}
      onEdit={onEdit}
      onDelete={onDelete}
      onReaction={onReaction}
    />
  );
};

export default StreamingMessage; 