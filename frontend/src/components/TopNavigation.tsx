import React from 'react';
import { AppMode } from '@/types';
import { DotIndicator } from './VisualIndicator';
import '../styles/visual-indicators.css';

interface NavigationItem {
  id: AppMode;
  iconCategory: string;
  title: string;
  description: string;
  features: string[];
  shortcut?: string;
}

interface TopNavigationProps {
  currentMode: AppMode;
  setMode: (mode: AppMode) => void;
  isHealthy: boolean | null;
  healthStatus: string;
  onHealthCheck: () => void;
  lastChecked?: string;
}

const TopNavigation: React.FC<TopNavigationProps> = ({
  currentMode,
  setMode,
  isHealthy,
  healthStatus,
  onHealthCheck,
  lastChecked
}) => {
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      iconCategory: 'navigation',
      title: '首页',
      description: '主仪表板和数据概览',
      features: ['实时数据', '快速概览', '状态监控']
    },
    {
      id: 'ai-agent',
      iconCategory: 'analysis',
      title: 'AI智能助手',
      description: '与AI智能助手对话，获取专业的投资建议和市场分析',
      features: ['实时对话', '智能分析', '投资建议'],
      shortcut: 'A'
    },
    {
      id: 'factor-management',
      iconCategory: 'chart',
      title: '因子管理',
      description: '管理和分析各种投资因子，创建自定义因子模型',
      features: ['因子分析', '自定义因子', 'AI生成'],
      shortcut: 'F'
    },
    {
      id: 'data-query',
      iconCategory: 'research',
      title: '数据查询',
      description: '查询和分析股票数据，获取详细的技术指标',
      features: ['股票查询', '技术指标', '数据导出'],
      shortcut: 'D'
    },
    {
      id: 'ml-models',
      iconCategory: 'data',
      title: '机器学习',
      description: '使用机器学习模型进行股票预测和分析',
      features: ['模型训练', '股票预测', '性能评估'],
      shortcut: 'M'
    },
    {
      id: 'stock-scoring',
      iconCategory: 'recommendation',
      title: '股票评分',
      description: '综合评分系统，为股票提供多维度评分',
      features: ['因子评分', 'ML评分', '综合排名'],
      shortcut: 'S'
    },
    {
      id: 'divergence-scanner',
      iconCategory: 'analysis',
      title: '背离扫描',
      description: '扫描市场中的MACD背离信号，发现投资机会',
      features: ['背离检测', '实时扫描', '历史记录'],
      shortcut: 'B'
    }
  ];

  // 键盘快捷键处理
  React.useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        const item = navigationItems.find(item => 
          item.shortcut && item.shortcut.toLowerCase() === event.key.toLowerCase()
        );
        if (item) {
          event.preventDefault();
          setMode(item.id);
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [setMode]);

  return (
    <div className="top-navigation">
      {/* 主导航栏 */}
      <nav className="main-nav">
        <div className="nav-container">
          {/* Logo 和标题 */}
          <div className="nav-brand" onClick={() => setMode('home')}>
            <div className="brand-icon">
              <DotIndicator category="success" size="lg" />
            </div>
            <div className="brand-text">
              <h1>金融投资助手</h1>
              <span>专业智能投资分析平台</span>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="nav-links">
            {navigationItems.slice(1).map((item) => (
              <button
                key={item.id}
                onClick={() => setMode(item.id)}
                className={`nav-link ${currentMode === item.id ? 'active' : ''}`}
                title={`${item.description}${item.shortcut ? ` (Ctrl+${item.shortcut})` : ''}`}
              >
                <span className="nav-icon">
                  <DotIndicator category={item.iconCategory} size="md" />
                </span>
                <span className="nav-title">{item.title}</span>
                {item.shortcut && (
                  <span className="nav-shortcut">Ctrl+{item.shortcut}</span>
                )}
              </button>
            ))}
          </div>

          {/* 状态和操作区域 */}
          <div className="nav-status">
            {/* 健康状态指示器 */}
            <div className="health-indicator">
              <div 
                className={`status-dot ${
                  isHealthy === true ? 'status-connected' : 
                  isHealthy === false ? 'status-disconnected' : 
                  'status-connecting'
                }`}
              />
              <div className="status-text">
                <span className={`status-label ${
                  isHealthy === true ? 'text-success' : 
                  isHealthy === false ? 'text-error' : 
                  'text-warning'
                }`}>
                  {healthStatus}
                </span>
                {lastChecked && (
                  <span className="status-time">
                    {lastChecked}
                  </span>
                )}
              </div>
              <button
                onClick={onHealthCheck}
                className="status-refresh"
                title="检查连接状态"
              >
                <DotIndicator category="navigation" size="sm" state="streaming" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 当前页面信息栏 */}
      {currentMode !== 'home' && (
        <div className="page-info">
          <div className="page-container">
            <div className="page-breadcrumb">
              <button 
                onClick={() => setMode('home')}
                className="breadcrumb-home"
              >
                <DotIndicator category="navigation" size="sm" /> 首页
              </button>
              <span className="breadcrumb-separator">›</span>
              <span className="breadcrumb-current">
                <DotIndicator 
                  category={navigationItems.find(item => item.id === currentMode)?.iconCategory || 'navigation'} 
                  size="sm" 
                />
                {' '}
                {navigationItems.find(item => item.id === currentMode)?.title}
              </span>
            </div>
            
            <div className="page-features">
              {navigationItems.find(item => item.id === currentMode)?.features.map((feature, index) => (
                <span key={index} className="feature-tag">
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopNavigation; 