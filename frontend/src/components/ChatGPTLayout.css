/* ===== ChatGPT-like Clean Interface ===== */

/* Main chat container */
.chat-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 768px;
  margin: 0 auto;
  background: #ffffff;
}

/* Header - minimal and clean */
.chat-header {
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 1.5rem;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.chat-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #202123;
  margin: 0;
  text-align: center;
}

/* Messages area - clean and spacious */
.chat-messages {
  padding: 1.5rem;
  background: #ffffff;
}



/* Welcome state - clean and centered */
.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 600;
  color: #202123;
  margin-bottom: 0.75rem;
}

.welcome-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 2rem;
  max-width: 32rem;
  line-height: 1.5;
}

/* Example prompts - clean grid */
.example-prompts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 0.75rem;
  max-width: 48rem;
  width: 100%;
}

.example-prompt {
  background: #f7f7f8;
  border: 1px solid #e5e5e5;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.4;
  color: #202123;
}

.example-prompt:hover {
  background: #f0f0f0;
  border-color: #d1d5db;
}

/* Message bubbles - ChatGPT style */
.message-group {
  margin-bottom: 1.5rem;
}

.message-row {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.message-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.message-avatar.user {
  background: #19c37d;
  color: white;
}

.message-avatar.assistant {
  background: #ab68ff;
  color: white;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  color: #202123;
  line-height: 1.5;
  font-size: 1rem;
}

.message-text.user {
  background: transparent;
  padding: 0;
}

.message-text.assistant {
  background: transparent;
  padding: 0;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  margin: 1rem 0;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing-bounce 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Input area - clean and modern */
.chat-input-area {
  border-top: 1px solid #e5e5e5;
  padding: 1rem 1.5rem 1.5rem;
  background: #ffffff;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.chat-input-container {
  position: relative;
  max-width: 48rem;
  margin: 0 auto;
}

.chat-input {
  width: 100%;
  min-height: 3rem;
  max-height: 8rem;
  padding: 0.75rem 4rem 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  resize: none;
  outline: none;
  background: #ffffff;
  color: #202123;
  font-family: inherit;
  transition: border-color 0.2s ease;
  overflow: hidden;
}



.chat-input:focus {
  border-color: #10a37f;
  box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
}

.chat-input::placeholder {
  color: #9ca3af;
}

.chat-send-button {
  position: absolute;
  right: 0.75rem;
  bottom: 0.5rem;
  width: 2rem;
  height: 2rem;
  border: none;
  background: #10a37f;
  color: white;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chat-send-button:hover:not(:disabled) {
  background: #0d8a6b;
}

.chat-send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

/* Error message */
.chat-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin: 1rem 1.5rem;
  color: #dc2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-icon {
  color: #ef4444;
}

.error-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.error-button {
  background: transparent;
  border: 1px solid #dc2626;
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-button:hover {
  background: #dc2626;
  color: white;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .chat-container {
    min-height: 100vh;
  }
  
  .chat-header {
    padding: 1rem;
  }
  
  .chat-messages {
    padding: 1rem;
  }
  
  .chat-input-area {
    padding: 1rem;
    position: sticky;
    bottom: 0;
  }
  
  .chat-input {
    padding: 0.75rem 3.5rem 0.75rem 1rem; /* 移动端减少右侧内边距 */
    font-size: 16px; /* 防止iOS缩放 */
    max-height: 6rem;
  }
  
  .chat-send-button {
    right: 0.5rem; /* 移动端减少距离 */
    width: 2.25rem; /* 稍微增大按钮便于点击 */
    height: 2.25rem;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
  
  .example-prompts {
    grid-template-columns: 1fr;
  }
  
  .example-prompt {
    padding: 0.875rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chat-container {
    background: #343541;
  }
  
  .chat-header {
    background: #343541;
    border-bottom-color: #4d4d4f;
  }
  
  .chat-title {
    color: #ffffff;
  }
  
  .chat-messages {
    background: #343541;
  }
  
  .welcome-title {
    color: #ffffff;
  }
  
  .welcome-subtitle {
    color: #b4b4b4;
  }
  
  .example-prompt {
    background: #40414f;
    border-color: #4d4d4f;
    color: #ffffff;
  }
  
  .example-prompt:hover {
    background: #4d4d4f;
  }
  
  .message-text {
    color: #ffffff;
  }
  
  .chat-input-area {
    background: #343541;
    border-top-color: #4d4d4f;
  }
  
  .chat-input {
    background: #40414f;
    border-color: #4d4d4f;
    color: #ffffff;
  }
  
  .chat-input::placeholder {
    color: #8e8ea0;
  }
  
  .chat-input:focus {
    border-color: #10a37f;
  }
}

/* Chart message styling for ChatGPT interface */
.chat-chart-message {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 1rem;
  margin: 0.5rem 0;
}

.chat-chart-message .message-header {
  margin-bottom: 1rem;
}

.chat-chart-message .chart-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-chart-message .chart-metadata {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  margin-top: 1rem;
}

.chat-chart-message .chart-error {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  color: #c53030;
}

.chat-chart-message .chart-controls button {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.chat-chart-message .chart-controls button:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

/* Dark mode for chart messages */
@media (prefers-color-scheme: dark) {
  .chat-chart-message {
    background: #1f2937;
    border-color: #374151;
  }
  
  .chat-chart-message .chart-metadata {
    background: #111827;
    border-color: #374151;
    color: #d1d5db;
  }
  
  .chat-chart-message .chart-controls button {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .chat-chart-message .chart-controls button:hover {
    background: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }
}

/* Workflow Progress Container */
.workflow-progress-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 0 20px;
}

.workflow-progress-container .workflow-progress {
  max-width: 600px;
  width: 100%;
}

/* Mobile responsive for workflow progress */
@media (max-width: 768px) {
  .workflow-progress-container {
    margin: 15px 0;
    padding: 0 15px;
  }
} 