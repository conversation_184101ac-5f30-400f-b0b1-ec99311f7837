'use client';

import React, { useState, useEffect } from 'react';
import { ErrorDetails, ErrorClassification } from '@/utils/errorClassification';
import LoadingSpinner from './LoadingSpinner';

interface ErrorDisplayProps {
  error: any;
  context?: {
    action?: string;
    details?: string;
    isStreaming?: boolean;
    messageId?: string;
  };
  onRetry?: () => void;
  onDismiss?: () => void;
  onClearError?: () => void;
  className?: string;
  inline?: boolean;
  showDetails?: boolean;
  autoRetry?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  context,
  onRetry,
  onDismiss,
  onClearError,
  className = '',
  inline = false,
  showDetails = false,
  autoRetry = true,
}) => {
  const [errorDetails, setErrorDetails] = useState<ErrorDetails | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [retryTimer, setRetryTimer] = useState<number | null>(null);
  const [showDetailsExpanded, setShowDetailsExpanded] = useState(showDetails);
  const [hasInternet, setHasInternet] = useState(navigator.onLine);

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setHasInternet(true);
    const handleOffline = () => setHasInternet(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Classify error and set up auto-retry
  useEffect(() => {
    if (!error) {
      setErrorDetails(null);
      return;
    }

    const classified = ErrorClassification.classify(error);
    setErrorDetails(classified);

    // Auto-retry logic
    if (autoRetry && ErrorClassification.shouldAutoRetry(classified, retryCount) && onRetry) {
      const delay = ErrorClassification.getRetryDelay(classified, retryCount);
      
      console.log(`[RETRY] Auto-retry scheduled in ${delay}ms (attempt ${retryCount + 1}/${classified.maxRetries})`);
      
      const timer = window.setTimeout(() => {
        setIsRetrying(true);
        setRetryCount(prev => prev + 1);
        onRetry();
        setIsRetrying(false);
      }, delay);

      setRetryTimer(timer);
    }

    return () => {
      if (retryTimer) {
        clearTimeout(retryTimer);
        setRetryTimer(null);
      }
    };
  }, [error, retryCount, autoRetry, onRetry]);

  // Reset retry count when error changes
  useEffect(() => {
    setRetryCount(0);
  }, [error?.message]);

  const handleManualRetry = () => {
    if (onRetry && !isRetrying) {
      setIsRetrying(true);
      setRetryCount(prev => prev + 1);
      onRetry();
      setTimeout(() => setIsRetrying(false), 1000);
    }
  };

  const handleDismiss = () => {
    if (retryTimer) {
      clearTimeout(retryTimer);
      setRetryTimer(null);
    }
    if (onDismiss) {
      onDismiss();
    } else if (onClearError) {
      onClearError();
    }
  };

  if (!errorDetails) return null;

  const contextualMessage = ErrorClassification.getContextualMessage(errorDetails, context);
  const suggestions = ErrorClassification.getRecoverySuggestions(errorDetails, {
    isStreaming: context?.isStreaming,
    hasInternet,
  });

  const colorClasses = {
    red: 'bg-red-50 border-red-200 text-red-700',
    orange: 'bg-orange-50 border-orange-200 text-orange-700',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    blue: 'bg-blue-50 border-blue-200 text-blue-700',
    gray: 'bg-gray-50 border-gray-200 text-gray-700',
  };

  const iconColorClasses = {
    red: 'text-red-500',
    orange: 'text-orange-500',
    yellow: 'text-yellow-500',
    blue: 'text-blue-500',
    gray: 'text-gray-500',
  };

  const buttonColorClasses = {
    red: 'text-red-600 hover:text-red-800 bg-red-100 hover:bg-red-200',
    orange: 'text-orange-600 hover:text-orange-800 bg-orange-100 hover:bg-orange-200',
    yellow: 'text-yellow-600 hover:text-yellow-800 bg-yellow-100 hover:bg-yellow-200',
    blue: 'text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200',
    gray: 'text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200',
  };

  const bgColorClass = colorClasses[errorDetails.color as keyof typeof colorClasses] || colorClasses.gray;
  const iconColorClass = iconColorClasses[errorDetails.color as keyof typeof iconColorClasses] || iconColorClasses.gray;
  const buttonColorClass = buttonColorClasses[errorDetails.color as keyof typeof buttonColorClasses] || buttonColorClasses.gray;

  if (inline) {
    return (
      <div className={`flex items-center space-x-2 p-2 border rounded-lg ${bgColorClass} ${className}`}>
        <span className={`text-lg ${iconColorClass}`}>{errorDetails.icon}</span>
        <span className="text-sm font-medium">{errorDetails.title}</span>
        {errorDetails.canRetry && onRetry && (
          <button
            onClick={handleManualRetry}
            disabled={isRetrying}
            className={`text-xs px-2 py-1 rounded ${buttonColorClass} transition-colors disabled:opacity-50`}
          >
                         {isRetrying ? <LoadingSpinner size="sm" /> : '重试'}
          </button>
        )}
        <button
          onClick={handleDismiss}
          className="text-gray-400 hover:text-gray-600 ml-auto"
        >
          ✕
        </button>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${bgColorClass} ${className}`}>
      {/* Header */}
      <div className="p-4">
        <div className="flex items-start space-x-3">
          <span className={`text-2xl flex-shrink-0 ${iconColorClass}`}>
            {errorDetails.icon}
          </span>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-base">{errorDetails.title}</h3>
              <button
                onClick={handleDismiss}
                className="text-gray-400 hover:text-gray-600 p-1"
                title="关闭错误提示"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            <p className="text-sm mb-3">{contextualMessage}</p>

            {/* Network status indicator */}
            {(errorDetails.type === 'network' || !hasInternet) && (
              <div className="flex items-center space-x-2 mb-3 text-xs">
                <div className={`w-2 h-2 rounded-full ${hasInternet ? 'bg-green-500' : 'bg-red-500'}`} />
                <span>{hasInternet ? '网络连接正常' : '网络连接断开'}</span>
              </div>
            )}

            {/* Retry info */}
            {retryCount > 0 && (
              <div className="text-xs opacity-75 mb-3">
                已重试 {retryCount} 次
                {errorDetails.maxRetries && ` / ${errorDetails.maxRetries}`}
              </div>
            )}

                         {/* Auto-retry countdown */}
             {retryTimer && autoRetry && (
               <div className="flex items-center space-x-2 text-xs mb-3">
                 <LoadingSpinner size="sm" />
                 <span>正在自动重试...</span>
               </div>
             )}
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-2 mt-4">
          {errorDetails.canRetry && onRetry && (
            <button
              onClick={handleManualRetry}
              disabled={isRetrying || Boolean(retryTimer)}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${buttonColorClass} disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2`}
            >
                             {isRetrying ? (
                 <>
                   <LoadingSpinner size="sm" />
                   <span>重试中...</span>
                 </>
               ) : retryTimer ? (
                <span>自动重试中...</span>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  <span>重试</span>
                </>
              )}
            </button>
          )}

          <button
            onClick={() => setShowDetailsExpanded(!showDetailsExpanded)}
            className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 bg-white border border-gray-300 rounded-md transition-colors"
          >
            {showDetailsExpanded ? '隐藏详情' : '显示详情'}
          </button>

          {onClearError && (
            <button
              onClick={onClearError}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 bg-white border border-gray-300 rounded-md transition-colors"
            >
              清除错误
            </button>
          )}
        </div>
      </div>

      {/* Expanded details */}
      {showDetailsExpanded && (
        <div className="border-t border-current border-opacity-20 p-4 space-y-3">
          {/* Recovery suggestions */}
          {suggestions.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-2">解决建议：</h4>
              <ul className="text-sm space-y-1 list-disc list-inside">
                {suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Technical details */}
          <div>
            <h4 className="font-medium text-sm mb-2">技术详情：</h4>
            <div className="text-xs font-mono bg-white bg-opacity-50 p-2 rounded border">
              <div><strong>错误代码:</strong> {errorDetails.code}</div>
              <div><strong>错误类型:</strong> {errorDetails.type}</div>
              <div><strong>严重级别:</strong> {errorDetails.severity}</div>
              <div><strong>原始消息:</strong> {errorDetails.message}</div>
              {context?.messageId && (
                <div><strong>消息ID:</strong> {context.messageId}</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ErrorDisplay; 