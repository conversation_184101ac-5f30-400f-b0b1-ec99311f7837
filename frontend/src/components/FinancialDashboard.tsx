'use client';

import React, { useState, useEffect } from 'react';
import Enhanced<PERSON>hart<PERSON>ontainer from './EnhancedChartContainer';
import EnhancedLoadingStates from './EnhancedLoadingStates';
import EnhancedButton from './EnhancedButton';
import { DotIndicator, BadgeIndicator, StatusIndicator } from './VisualIndicator';

interface DashboardData {
  marketIndices: Array<{
    name: string;
    value: number;
    change: number;
    changePercent: number;
  }>;
  topStocks: Array<{
    symbol: string;
    name: string;
    price: number;
    change: number;
    changePercent: number;
    volume: number;
  }>;
  portfolioMetrics: {
    totalValue: number;
    dayChange: number;
    dayChangePercent: number;
    totalReturn: number;
    totalReturnPercent: number;
  };
  recentAlerts: Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    timestamp: string;
  }>;
}

interface FinancialDashboardProps {
  className?: string;
}

const FinancialDashboard: React.FC<FinancialDashboardProps> = ({ className = '' }) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [selectedTimeframe, setSelectedTimeframe] = useState('1D');
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Mock data for demonstration
  const mockData: DashboardData = {
    marketIndices: [
      { name: '上证指数', value: 3234.56, change: 23.45, changePercent: 0.73 },
      { name: '深证成指', value: 10876.23, change: -45.67, changePercent: -0.42 },
      { name: '创业板指', value: 2345.67, change: 12.34, changePercent: 0.53 },
      { name: '科创50', value: 1023.45, change: -8.90, changePercent: -0.86 }
    ],
    topStocks: [
      { symbol: '000001', name: '平安银行', price: 12.45, change: 0.23, changePercent: 1.88, volume: 234567890 },
      { symbol: '000002', name: '万科A', price: 18.67, change: -0.45, changePercent: -2.35, volume: 123456789 },
      { symbol: '000858', name: '五粮液', price: 189.34, change: 3.21, changePercent: 1.72, volume: 98765432 },
      { symbol: '600036', name: '招商银行', price: 43.21, change: 0.89, changePercent: 2.10, volume: 156789012 },
      { symbol: '600519', name: '贵州茅台', price: 1789.56, change: -12.34, changePercent: -0.68, volume: 45678901 }
    ],
    portfolioMetrics: {
      totalValue: 1250000,
      dayChange: 15600,
      dayChangePercent: 1.27,
      totalReturn: 187500,
      totalReturnPercent: 17.65
    },
    recentAlerts: [
      { id: '1', type: 'success', message: 'MACD金叉信号：000001 平安银行', timestamp: '10:30' },
      { id: '2', type: 'warning', message: 'RSI超买警告：600519 贵州茅台', timestamp: '09:45' },
      { id: '3', type: 'info', message: '成交量异常：000858 五粮液', timestamp: '09:15' },
      { id: '4', type: 'error', message: '跌破支撑位：000002 万科A', timestamp: '09:00' }
    ]
  };

  useEffect(() => {
    // Simulate loading data
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      setDashboardData(mockData);
      setLastUpdate(new Date());
      setIsLoading(false);
    };

    loadData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Simulate real-time updates
      if (dashboardData) {
        const updatedData = {
          ...dashboardData,
          marketIndices: dashboardData.marketIndices.map(index => ({
            ...index,
            change: index.change + (Math.random() - 0.5) * 2,
            changePercent: index.changePercent + (Math.random() - 0.5) * 0.1
          }))
        };
        setDashboardData(updatedData);
        setLastUpdate(new Date());
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, dashboardData]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(2) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toFixed(2);
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-red-600' : 'text-green-600';
  };

  const getAlertIndicator = (type: string) => {
    switch (type) {
      case 'success': return <StatusIndicator status="complete" size="xs" />;
      case 'warning': return <DotIndicator category="warning" size="xs" />;
      case 'error': return <DotIndicator category="error" size="xs" />;
      default: return <DotIndicator category="navigation" size="xs" />;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 text-green-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'error': return 'bg-red-50 border-red-200 text-red-800';
      default: return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (isLoading || !dashboardData) {
    return (
      <div className={`financial-dashboard ${className}`}>
        <div className="dashboard-header mb-6">
          <h2 className="text-2xl font-bold text-gray-900">财务分析仪表盘</h2>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-sm text-gray-500">正在加载数据...</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="space-y-2">
                <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="w-32 h-8 bg-gray-200 rounded animate-pulse"></div>
                <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 relative h-64">
            <div className="absolute top-4 left-4 right-4">
              <div className="w-32 h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="w-48 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <DotIndicator category="chart" size="xl" />
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 relative h-64">
            <div className="absolute top-4 left-4 right-4">
              <div className="w-32 h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="w-48 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <DotIndicator category="chart" size="xl" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`financial-dashboard ${className}`}>
      {/* Dashboard Header */}
      <div className="dashboard-header flex justify-between items-center mb-8">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
            <DotIndicator category="chart" size="lg" />
            财务分析仪表盘
          </h2>
          <p className="text-gray-600">实时市场数据和投资组合分析</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            最后更新: {lastUpdate.toLocaleTimeString()}
          </div>
          
          <EnhancedButton
            variant={autoRefresh ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <DotIndicator 
              category="navigation" 
              size="sm" 
              state={autoRefresh ? 'default' : 'streaming'} 
            />
            {autoRefresh ? '暂停' : '开始'}
          </EnhancedButton>
          
          <EnhancedButton
            variant="ghost"
            size="sm"
            onClick={() => setLastUpdate(new Date())}
          >
            <DotIndicator category="navigation" size="sm" state="streaming" />
            刷新
          </EnhancedButton>
        </div>
      </div>

      {/* Market Indices Overview */}
      <div className="market-indices mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <DotIndicator category="analysis" size="md" />
          市场指数
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {dashboardData.marketIndices.map((index, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-gray-900">{index.name}</h4>
                <div className={`text-lg font-bold ${getChangeColor(index.change)}`}>
                  {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-2xl font-bold text-gray-900">
                  {index.value.toFixed(2)}
                </div>
                <div className={`text-sm font-medium ${getChangeColor(index.change)}`}>
                  {index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                </div>
              </div>
              <div className="mt-3 h-2 bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className={`h-full transition-all duration-300 ${
                    index.change >= 0 ? 'bg-red-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(Math.abs(index.changePercent) * 10, 100)}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Portfolio Overview */}
      <div className="portfolio-overview mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <DotIndicator category="success" size="md" />
          投资组合概览
        </h3>
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-1">总价值</div>
              <div className="text-3xl font-bold text-gray-900">
                ¥{formatNumber(dashboardData.portfolioMetrics.totalValue)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-1">今日盈亏</div>
              <div className={`text-3xl font-bold ${getChangeColor(dashboardData.portfolioMetrics.dayChange)}`}>
                {dashboardData.portfolioMetrics.dayChange >= 0 ? '+' : ''}¥{formatNumber(Math.abs(dashboardData.portfolioMetrics.dayChange))}
              </div>
              <div className={`text-sm ${getChangeColor(dashboardData.portfolioMetrics.dayChange)}`}>
                {dashboardData.portfolioMetrics.dayChangePercent >= 0 ? '+' : ''}{dashboardData.portfolioMetrics.dayChangePercent.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-1">总收益</div>
              <div className={`text-3xl font-bold ${getChangeColor(dashboardData.portfolioMetrics.totalReturn)}`}>
                {dashboardData.portfolioMetrics.totalReturn >= 0 ? '+' : ''}¥{formatNumber(Math.abs(dashboardData.portfolioMetrics.totalReturn))}
              </div>
              <div className={`text-sm ${getChangeColor(dashboardData.portfolioMetrics.totalReturn)}`}>
                {dashboardData.portfolioMetrics.totalReturnPercent >= 0 ? '+' : ''}{dashboardData.portfolioMetrics.totalReturnPercent.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-1">风险等级</div>
              <div className="text-3xl font-bold text-yellow-600">中等</div>
              <div className="text-sm text-gray-500">Beta: 1.2</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Top Stocks Performance */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <DotIndicator category="recommendation" size="md" />
                热门股票
              </h3>
              <div className="flex space-x-2">
                {['1D', '1W', '1M', '3M'].map(timeframe => (
                  <button
                    key={timeframe}
                    onClick={() => setSelectedTimeframe(timeframe)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      selectedTimeframe === timeframe
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {timeframe}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="space-y-4">
              {dashboardData.topStocks.map((stock, i) => (
                <div key={stock.symbol} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-bold text-blue-700">
                      {i + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{stock.name}</div>
                      <div className="text-sm text-gray-500">{stock.symbol}</div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-bold text-gray-900">¥{stock.price.toFixed(2)}</div>
                    <div className={`text-sm ${getChangeColor(stock.change)}`}>
                      {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)} ({stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%)
                    </div>
                  </div>
                  
                  <div className="text-right text-sm text-gray-500">
                    <div>成交量</div>
                    <div>{formatNumber(stock.volume)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Alerts and Notifications */}
        <div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <DotIndicator category="warning" size="md" />
                市场提醒
              </h3>
                              <EnhancedButton variant="ghost" size="sm">
                  <DotIndicator category="navigation" size="sm" />
                  设置
                </EnhancedButton>
            </div>
            
            <div className="space-y-3">
              {dashboardData.recentAlerts.map(alert => (
                <div key={alert.id} className={`p-3 rounded-lg border ${getAlertColor(alert.type)}`}>
                  <div className="flex items-start space-x-2">
                    <span className="text-sm">{getAlertIndicator(alert.type)}</span>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{alert.message}</div>
                      <div className="text-xs opacity-75 mt-1">{alert.timestamp}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <EnhancedButton 
              variant="ghost" 
              size="sm" 
              className="w-full mt-4"
              onClick={() => {}}
            >
              查看全部提醒
            </EnhancedButton>
          </div>
        </div>
      </div>

      {/* Market Heatmap Placeholder */}
      <div className="mt-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
            <DotIndicator category="chart" size="md" />
            市场热力图
          </h3>
          <div className="bg-gradient-to-r from-green-100 via-yellow-100 to-red-100 rounded-lg p-8 text-center">
            <div className="text-4xl mb-4">
              <DotIndicator category="chart" size="xl" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">市场热力图可视化</h4>
            <p className="text-gray-600 mb-4">显示各个行业和股票的表现情况</p>
            <EnhancedButton variant="primary">
              <DotIndicator category="recommendation" size="sm" />
              查看详细热力图
            </EnhancedButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialDashboard; 