'use client';

import React, { useState } from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import MarkdownRenderer from './MarkdownRenderer';
import ChartMessage from './ChartMessage';
import { preprocessMessageContent } from '@/utils/messagePreprocessor';
import { 
  User, 
  Bot, 
  Copy, 
  RotateCcw, 
  Trash2, 
  ThumbsUp, 
  ThumbsDown, 
  MoreHorizontal,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MessageBubbleProps {
  message: EnhancedMessage;
  isStreaming?: boolean;
  conversationId?: string;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  className?: string;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isStreaming = false,
  conversationId = 'default',
  onRetry,
  onEdit,
  onDelete,
  onReaction,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const isUser = message.type === 'user';
  const isAssistant = message.type === 'assistant';
  const isSystem = message.type === 'system';
  const isChart = message.type === 'chart' || message.chartData || message.metadata?.chartData;
  
  // Don't render system messages
  if (isSystem) return null;

  // Handle chart messages
  if (isChart && !isUser) {
    return (
      <div className={cn("flex justify-start mb-4", className)}>
        <div className="max-w-[95%]">
          <ChartMessage
            message={message}
            onEdit={onEdit}
            onDelete={onDelete}
            onReaction={onReaction}
            conversationId={conversationId}
          />
        </div>
      </div>
    );
  }

  // Get display content with preprocessing
  const rawContent = isAssistant 
    ? (message.displayContent || message.content || '') 
    : (message.content || '');
  
  const displayContent = isAssistant 
    ? preprocessMessageContent(rawContent)
    : rawContent;

  // Get status icon
  const getStatusIcon = () => {
    switch (message.status) {
      case 'complete':
      case 'sent':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'failed':
        return <XCircle className="w-3 h-3 text-red-500" />;
      case 'streaming':
        return <Clock className="w-3 h-3 text-blue-500 animate-pulse" />;
      case 'pending':
      case 'sending':
        return <Clock className="w-3 h-3 text-yellow-500" />;
      default:
        return null;
    }
  };

  // Get agent badge
  const getAgentBadge = () => {
    if (!isAssistant || !message.metadata?.agent) return null;
    
    return (
      <Badge variant="secondary" className="text-xs mb-2">
        {message.metadata.agent}
      </Badge>
    );
  };

  // Handle copy to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(displayContent);
      // Could add toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  // Handle reactions
  const handleReaction = (reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => {
    onReaction?.(message.id, reaction);
  };

  return (
    <div 
      className={cn(
        "flex mb-6 group",
        isUser ? "justify-end" : "justify-start",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={cn(
        "flex gap-3 max-w-[85%] md:max-w-[75%]",
        isUser ? "flex-row-reverse" : "flex-row"
      )}>
        {/* Avatar */}
        <Avatar className="w-8 h-8 flex-shrink-0 mt-1">
          <AvatarFallback className={cn(
            "text-xs font-medium",
            isUser 
              ? "bg-blue-500 text-white" 
              : "bg-purple-500 text-white"
          )}>
            {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
          </AvatarFallback>
        </Avatar>

        {/* Message content */}
        <div className={cn(
          "flex flex-col gap-1",
          isUser ? "items-end" : "items-start"
        )}>
          {/* Agent badge */}
          {getAgentBadge()}

          {/* Message bubble */}
          <Card className={cn(
            "relative transition-all duration-200",
            isUser 
              ? "bg-blue-500 text-white border-blue-500" 
              : "bg-card border-border hover:shadow-md",
            message.status === 'failed' && "border-red-300 bg-red-50"
          )}>
            <CardContent className="p-3 md:p-4">
              {/* Message content */}
              {isStreaming && !displayContent ? (
                // Loading skeleton for streaming
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ) : (
                <div className="relative">
                  {isAssistant ? (
                    <MarkdownRenderer 
                      content={displayContent}
                      className={cn(
                        "prose prose-sm max-w-none",
                        "prose-headings:text-foreground prose-p:text-foreground",
                        "prose-strong:text-foreground prose-code:text-foreground",
                        "prose-pre:bg-muted prose-pre:text-foreground"
                      )}
                      enableCopy={false}
                      enableExport={false}
                    />
                  ) : (
                    <div className="whitespace-pre-wrap break-words leading-relaxed text-sm md:text-base">
                      {displayContent}
                    </div>
                  )}

                  {/* Streaming cursor */}
                  {isAssistant && isStreaming && message.status === 'streaming' && (
                    <span className="inline-block w-0.5 h-4 bg-current ml-1 animate-pulse" />
                  )}
                </div>
              )}
            </CardContent>

            {/* Message actions */}
            {(isHovered || message.status === 'failed') && (
              <div className={cn(
                "absolute -top-2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",
                isUser ? "-left-2" : "-right-2"
              )}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-6 w-6 p-0 rounded-full shadow-sm"
                    >
                      <MoreHorizontal className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align={isUser ? "end" : "start"}>
                    <DropdownMenuItem onClick={handleCopy}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy
                    </DropdownMenuItem>
                    {message.status === 'failed' && onRetry && (
                      <DropdownMenuItem onClick={() => onRetry(message.id)}>
                        <RotateCcw className="w-4 h-4 mr-2" />
                        Retry
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <DropdownMenuItem 
                        onClick={() => onDelete(message.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    )}
                    {isAssistant && onReaction && (
                      <>
                        <DropdownMenuItem onClick={() => handleReaction('like')}>
                          <ThumbsUp className="w-4 h-4 mr-2" />
                          Helpful
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleReaction('dislike')}>
                          <ThumbsDown className="w-4 h-4 mr-2" />
                          Not helpful
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </Card>

          {/* Message metadata */}
          <div className={cn(
            "flex items-center gap-2 text-xs text-muted-foreground",
            isUser ? "flex-row-reverse" : "flex-row"
          )}>
            {/* Timestamp */}
            <span>
              {new Date(message.timestamp).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </span>
            
            {/* Status indicator */}
            {getStatusIcon()}
            
            {/* Retry count */}
            {message.retryCount && message.retryCount > 0 && (
              <Badge variant="outline" className="text-xs">
                Retry {message.retryCount}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageBubble; 