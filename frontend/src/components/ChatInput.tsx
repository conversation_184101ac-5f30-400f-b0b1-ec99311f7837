'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  Square, 
  Loader2, 
  Paperclip, 
  Mic, 
  Settings,
  Keyboard
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onCancelStream?: () => void;
  isStreaming?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showCharacterCount?: boolean;
  showAttachments?: boolean;
  showVoiceInput?: boolean;
  className?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  onCancelStream,
  isStreaming = false,
  isLoading = false,
  disabled = false,
  placeholder = "Ask me anything about financial markets, stocks, or investment strategies...",
  maxLength = 4000,
  showCharacterCount = true,
  showAttachments = false,
  showVoiceInput = false,
  className = ''
}) => {
  const [input, setInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 200; // Maximum height in pixels
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  }, []);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setInput(value);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: new line (default behavior)
        return;
      } else {
        // Enter: send message
        e.preventDefault();
        handleSendMessage();
      }
    }
  };

  // Handle send message
  const handleSendMessage = () => {
    const trimmedInput = input.trim();
    if (trimmedInput && !isLoading && !isStreaming) {
      onSendMessage(trimmedInput);
      setInput('');
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  // Handle cancel streaming
  const handleCancelStream = () => {
    onCancelStream?.();
  };

  // Auto-resize on input change
  useEffect(() => {
    adjustTextareaHeight();
  }, [input, adjustTextareaHeight]);

  // Focus management
  useEffect(() => {
    if (!isStreaming && !isLoading) {
      textareaRef.current?.focus();
    }
  }, [isStreaming, isLoading]);

  const canSend = input.trim().length > 0 && !isLoading && !disabled;
  const characterCount = input.length;
  const isNearLimit = characterCount > maxLength * 0.8;

  return (
    <Card className={cn("border-t rounded-t-none", className)}>
      <CardContent className="p-4">
        {/* Input area */}
        <div className="relative">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "min-h-[44px] max-h-[200px] resize-none pr-12",
              "focus-visible:ring-1 focus-visible:ring-ring",
              isFocused && "ring-1 ring-ring"
            )}
            style={{ height: 'auto' }}
          />

          {/* Send/Cancel button */}
          <div className="absolute bottom-2 right-2">
            {isStreaming ? (
              <Button
                size="sm"
                variant="destructive"
                onClick={handleCancelStream}
                className="h-8 w-8 p-0 rounded-full"
              >
                <Square className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleSendMessage}
                disabled={!canSend}
                className="h-8 w-8 p-0 rounded-full"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
          {/* Left side - shortcuts and features */}
          <div className="flex items-center gap-3">
            {/* Keyboard shortcut hint */}
            <div className="flex items-center gap-1">
              <Keyboard className="w-3 h-3" />
              <span>Enter to send, Shift+Enter for new line</span>
            </div>

            {/* Additional features */}
            <div className="flex items-center gap-2">
              {showAttachments && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                  disabled={disabled}
                >
                  <Paperclip className="w-3 h-3" />
                </Button>
              )}
              
              {showVoiceInput && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                  disabled={disabled}
                >
                  <Mic className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Right side - character count */}
          {showCharacterCount && (
            <div className="flex items-center gap-2">
              <Badge 
                variant={isNearLimit ? "destructive" : "secondary"}
                className="text-xs"
              >
                {characterCount}/{maxLength}
              </Badge>
            </div>
          )}
        </div>

        {/* Status indicators */}
        {(isStreaming || isLoading) && (
          <>
            <Separator className="my-2" />
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              {isStreaming ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>AI is thinking...</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancelStream}
                    className="ml-2 h-6 px-2 text-xs"
                  >
                    Cancel
                  </Button>
                </>
              ) : isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Thinking...</span>
                </>
              ) : null}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ChatInput; 