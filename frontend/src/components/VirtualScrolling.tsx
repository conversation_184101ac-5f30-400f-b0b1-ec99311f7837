'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import StreamingMessage from './StreamingMessage';

interface VirtualScrollingProps {
  messages: EnhancedMessage[];
  itemHeight?: number; // Average height of each message
  containerHeight?: number; // Height of the scrollable container
  overscan?: number; // Number of items to render outside visible area
  conversationId?: string;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  className?: string;
  autoScrollToBottom?: boolean;
}

interface VirtualItem {
  index: number;
  start: number;
  end: number;
  height: number;
}

interface VirtualRange {
  start: number;
  end: number;
  offsetBefore: number;
  offsetAfter: number;
}

// Hook for dynamic height measurement
const useItemHeights = (messages: EnhancedMessage[], defaultHeight: number = 150) => {
  const [heights, setHeights] = useState<Map<string, number>>(new Map());
  const measureRef = useRef<(id: string, height: number) => void>(() => {});

  measureRef.current = useCallback((id: string, height: number) => {
    setHeights(prev => {
      const newMap = new Map(prev);
      if (newMap.get(id) !== height) {
        newMap.set(id, height);
        return newMap;
      }
      return prev;
    });
  }, []);

  const getItemHeight = useCallback((index: number): number => {
    if (index >= messages.length) return defaultHeight;
    const message = messages[index];
    return heights.get(message.id) || defaultHeight;
  }, [messages, heights, defaultHeight]);

  const getTotalHeight = useCallback((): number => {
    return messages.reduce((total, message, index) => {
      return total + getItemHeight(index);
    }, 0);
  }, [messages, getItemHeight]);

  return { 
    heights, 
    getItemHeight, 
    getTotalHeight, 
    measureHeight: measureRef.current || ((id: string, height: number) => {}) 
  };
};

// Hook for virtual scrolling calculations
const useVirtualScrolling = (
  itemCount: number,
  getItemHeight: (index: number) => number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);

  const virtualItems = useMemo((): VirtualItem[] => {
    const items: VirtualItem[] = [];
    let start = 0;

    for (let i = 0; i < itemCount; i++) {
      const height = getItemHeight(i);
      items.push({
        index: i,
        start,
        end: start + height,
        height,
      });
      start += height;
    }

    return items;
  }, [itemCount, getItemHeight]);

  const totalHeight = useMemo(() => {
    return virtualItems.length > 0 ? virtualItems[virtualItems.length - 1].end : 0;
  }, [virtualItems]);

  const visibleRange = useMemo((): VirtualRange => {
    if (virtualItems.length === 0) {
      return { start: 0, end: 0, offsetBefore: 0, offsetAfter: 0 };
    }

    const viewportStart = scrollTop;
    const viewportEnd = scrollTop + containerHeight;

    // Find the first item that's partially or fully visible
    let startIndex = virtualItems.findIndex(item => item.end > viewportStart);
    if (startIndex === -1) startIndex = virtualItems.length - 1;

    // Find the last item that's partially or fully visible
    let endIndex = virtualItems.findIndex(item => item.start > viewportEnd);
    if (endIndex === -1) endIndex = virtualItems.length;

    // Apply overscan
    const startWithOverscan = Math.max(0, startIndex - overscan);
    const endWithOverscan = Math.min(virtualItems.length, endIndex + overscan);

    const offsetBefore = startWithOverscan > 0 ? virtualItems[startWithOverscan - 1].end : 0;
    const offsetAfter = endWithOverscan < virtualItems.length 
      ? totalHeight - virtualItems[endWithOverscan - 1].end 
      : 0;

    return {
      start: startWithOverscan,
      end: endWithOverscan,
      offsetBefore,
      offsetAfter,
    };
  }, [virtualItems, scrollTop, containerHeight, overscan, totalHeight]);

  return {
    virtualItems,
    visibleRange,
    totalHeight,
    setScrollTop,
  };
};

// Message item component with height measurement
interface VirtualMessageItemProps {
  message: EnhancedMessage;
  conversationId?: string;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  onHeightChange?: (id: string, height: number) => void;
}

const VirtualMessageItem: React.FC<VirtualMessageItemProps> = ({
  message,
  conversationId,
  onRetry,
  onEdit,
  onDelete,
  onReaction,
  onHeightChange,
}) => {
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (itemRef.current && onHeightChange) {
      const height = itemRef.current.offsetHeight;
      onHeightChange(message.id, height);
    }
  }, [message.content, message.status, onHeightChange, message.id]);

  // Use ResizeObserver for more accurate height tracking
  useEffect(() => {
    const element = itemRef.current;
    if (!element || !onHeightChange) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const height = entry.target.getBoundingClientRect().height;
        onHeightChange(message.id, height);
      }
    });

    resizeObserver.observe(element);

    return () => {
      resizeObserver.disconnect();
    };
  }, [message.id, onHeightChange]);

  return (
    <div ref={itemRef} className="virtual-message-item">
      <StreamingMessage
        message={message}
        conversationId={conversationId}
        onRetry={onRetry}
        onEdit={onEdit}
        onDelete={onDelete}
        onReaction={onReaction}
      />
    </div>
  );
};

// Main VirtualScrolling component
const VirtualScrolling: React.FC<VirtualScrollingProps> = ({
  messages,
  itemHeight = 150,
  containerHeight = 600,
  overscan = 5,
  conversationId,
  onRetry,
  onEdit,
  onDelete,
  onReaction,
  className = '',
  autoScrollToBottom = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const [isAutoScrolling, setIsAutoScrolling] = useState(false);
  
  const { heights, getItemHeight, getTotalHeight, measureHeight } = useItemHeights(messages, itemHeight);
  
  const {
    virtualItems,
    visibleRange,
    totalHeight,
    setScrollTop,
  } = useVirtualScrolling(messages.length, getItemHeight, containerHeight, overscan);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    setScrollTop(target.scrollTop);
  }, [setScrollTop]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScrollToBottom && scrollElementRef.current && !isAutoScrolling) {
      const element = scrollElementRef.current;
      const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 100;
      
      if (isNearBottom) {
        setIsAutoScrolling(true);
        element.scrollTop = element.scrollHeight;
        // Reset auto-scrolling flag after scroll completes
        setTimeout(() => setIsAutoScrolling(false), 100);
      }
    }
  }, [messages.length, autoScrollToBottom, isAutoScrolling]);

  // Scroll to specific message
  const scrollToMessage = useCallback((messageId: string) => {
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1 || !scrollElementRef.current) return;

    const targetItem = virtualItems[messageIndex];
    if (targetItem) {
      scrollElementRef.current.scrollTop = targetItem.start;
    }
  }, [messages, virtualItems]);

  // Scroll to bottom method
  const scrollToBottom = useCallback(() => {
    if (scrollElementRef.current) {
      setIsAutoScrolling(true);
      scrollElementRef.current.scrollTop = scrollElementRef.current.scrollHeight;
      setTimeout(() => setIsAutoScrolling(false), 100);
    }
  }, []);

  // Performance monitoring
  const renderedItemCount = visibleRange.end - visibleRange.start;
  const renderPercentage = Math.round((renderedItemCount / messages.length) * 100);

  const visibleMessages = useMemo(() => {
    return messages.slice(visibleRange.start, visibleRange.end);
  }, [messages, visibleRange.start, visibleRange.end]);

  return (
    <div 
      ref={containerRef}
      className={`virtual-scrolling ${className}`}
      style={{ height: containerHeight }}
    >
      {/* Performance indicator */}
      {messages.length > 50 && (
        <div className="virtual-scroll-indicator">
          <span className="performance-badge">
            ⚡ 虚拟滚动: {renderedItemCount}/{messages.length} ({renderPercentage}%)
          </span>
          <button
            onClick={scrollToBottom}
            className="scroll-to-bottom-btn"
            title="滚动到底部"
          >
            ⬇️
          </button>
        </div>
      )}

      {/* Virtual scrolling container */}
      <div
        ref={scrollElementRef}
        className="virtual-scroll-container"
        style={{ height: '100%', overflow: 'auto' }}
        onScroll={handleScroll}
      >
        {/* Total content height */}
        <div style={{ height: totalHeight, position: 'relative' }}>
          {/* Spacer before visible items */}
          {visibleRange.offsetBefore > 0 && (
            <div style={{ height: visibleRange.offsetBefore }} />
          )}
          
          {/* Visible items */}
          <div className="virtual-items">
            {visibleMessages.map((message, index) => (
              <VirtualMessageItem
                key={message.id}
                message={message}
                conversationId={conversationId}
                onRetry={onRetry}
                onEdit={onEdit}
                onDelete={onDelete}
                onReaction={onReaction}
                onHeightChange={measureHeight}
              />
            ))}
          </div>
          
          {/* Spacer after visible items */}
          {visibleRange.offsetAfter > 0 && (
            <div style={{ height: visibleRange.offsetAfter }} />
          )}
        </div>
      </div>
    </div>
  );
};

// Export utilities for use in other components
export const useVirtualScrollingHelpers = () => {
  return {
    scrollToMessage: (containerRef: React.RefObject<HTMLDivElement>, messageId: string, messages: EnhancedMessage[]) => {
      const container = containerRef.current;
      if (!container) return;
      
      const messageElement = container.querySelector(`[data-message-id="${messageId}"]`);
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    },
    
    scrollToBottom: (containerRef: React.RefObject<HTMLDivElement>) => {
      const container = containerRef.current;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    
    isNearBottom: (containerRef: React.RefObject<HTMLDivElement>, threshold: number = 100) => {
      const container = containerRef.current;
      if (!container) return false;
      
      return container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
    }
  };
};

export default VirtualScrolling; 