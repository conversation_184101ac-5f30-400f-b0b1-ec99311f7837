'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import { MessageSquare, Bot, User, Sparkles } from 'lucide-react';

interface ChatContainerProps {
  messages: EnhancedMessage[];
  isStreaming?: boolean;
  isLoading?: boolean;
  error?: string | null;
  children?: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  title?: string;
  subtitle?: string;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isStreaming = false,
  isLoading = false,
  error = null,
  children,
  className = '',
  showHeader = true,
  title = 'AI Assistant',
  subtitle = 'Intelligent financial analysis and insights'
}) => {

  // Get message statistics
  const messageStats = {
    total: messages.length,
    user: messages.filter(m => m.type === 'user').length,
    assistant: messages.filter(m => m.type === 'assistant').length,
    charts: messages.filter(m => m.type === 'chart' || m.chartData).length
  };

  return (
    <Card className={`flex flex-col h-full w-full ${className}`}>
      {/* Header */}
      {showHeader && (
        <>
          <CardHeader className="flex-shrink-0 pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">{title}</h2>
                  <p className="text-sm text-muted-foreground">{subtitle}</p>
                </div>
              </div>
              
              {/* Status indicators */}
              <div className="flex items-center gap-2">
                {isStreaming && (
                  <Badge variant="secondary" className="animate-pulse">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Thinking...
                  </Badge>
                )}
                {messageStats.total > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {messageStats.total} messages
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <Separator />
        </>
      )}

      {/* Messages Area */}
      <CardContent className="flex-1 p-0 overflow-y-auto">
        {messages.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <div className="flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
              <MessageSquare className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              Start a conversation
            </h3>
            <p className="text-sm text-muted-foreground max-w-sm">
              Ask me anything about financial markets, stock analysis, or investment strategies.
            </p>

            {/* Example prompts */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-6 w-full max-w-md">
              {[
                "Analyze AAPL stock performance",
                "What are the market trends today?",
                "Explain technical indicators",
                "Compare tech vs finance sectors"
              ].map((prompt, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="text-xs h-auto p-2 text-left justify-start whitespace-normal"
                >
                  {prompt}
                </Button>
              ))}
            </div>
          </div>
        ) : (
          // Messages list
          <div className="p-4 md:p-6 space-y-4">
            {children}
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className="p-4 border-t bg-destructive/5 border-destructive/20">
            <div className="flex items-center gap-2 text-sm text-destructive">
              <div className="w-2 h-2 rounded-full bg-destructive" />
              {error}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ChatContainer; 