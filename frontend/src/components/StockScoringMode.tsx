'use client';

import React from 'react';
import { AppMode } from '@/types';

interface StockScoringModeProps {
  scoringMode: string;
  setScoringMode: React.Dispatch<React.SetStateAction<any>>;
  scoreResults: any;
  setScoreResults: React.Dispatch<React.SetStateAction<any>>;
  rankingResults: any;
  setRankingResults: React.Dispatch<React.SetStateAction<any>>;
  rankingSymbols: string[];
  setRankingSymbols: React.Dispatch<React.SetStateAction<string[]>>;
  factorWeight: number;
  setFactorWeight: React.Dispatch<React.SetStateAction<number>>;
  mlWeight: number;
  setMlWeight: React.Dispatch<React.SetStateAction<number>>;
  isRanking: boolean;
  setIsRanking: React.Dispatch<React.SetStateAction<boolean>>;
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const StockScoringMode: React.FC<StockScoringModeProps> = ({
  setMode,
}) => {
  return (
    <div className="mode-container">
      <div className="mode-header">
        <button
          onClick={() => setMode('home')}
          className="back-button"
        >
          ← 返回首页
        </button>
        <h1>⭐ 股票评分</h1>
        <p>综合评分系统，为股票提供多维度评分</p>
      </div>
      
      <div className="scoring-content">
        <div className="text-center p-8">
          <div className="text-4xl mb-4">⭐</div>
          <h3 className="text-lg font-semibold mb-2">股票评分系统</h3>
          <p className="text-gray-600">综合评分和排名功能即将推出</p>
        </div>
      </div>
    </div>
  );
};

export default StockScoringMode; 