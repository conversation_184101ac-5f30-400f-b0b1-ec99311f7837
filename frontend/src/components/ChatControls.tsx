'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Trash2, 
  Download, 
  Settings, 
  MoreHorizontal,
  FileText,
  Copy,
  Share,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatControlsProps {
  // Stream control
  isStreaming?: boolean;
  isPaused?: boolean;
  onPauseStream?: () => void;
  onResumeStream?: () => void;
  onStopStream?: () => void;
  
  // Conversation management
  messageCount?: number;
  onClearConversation?: () => void;
  onRetryLastMessage?: () => void;
  onRefreshFromServer?: () => void;
  
  // Export functionality
  onExportConversation?: (format: 'json' | 'markdown' | 'txt') => void;
  onShareConversation?: () => void;
  
  // Settings
  onOpenSettings?: () => void;
  
  // UI
  className?: string;
  variant?: 'compact' | 'full';
}

const ChatControls: React.FC<ChatControlsProps> = ({
  isStreaming = false,
  isPaused = false,
  onPauseStream,
  onResumeStream,
  onStopStream,
  messageCount = 0,
  onClearConversation,
  onRetryLastMessage,
  onRefreshFromServer,
  onExportConversation,
  onShareConversation,
  onOpenSettings,
  className = '',
  variant = 'full'
}) => {
  const [showClearDialog, setShowClearDialog] = useState(false);

  // Handle clear conversation with confirmation
  const handleClearConversation = () => {
    onClearConversation?.();
    setShowClearDialog(false);
  };

  // Handle export conversation
  const handleExport = (format: 'json' | 'markdown' | 'txt') => {
    onExportConversation?.(format);
  };

  if (variant === 'compact') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {/* Stream controls */}
        {isStreaming && (
          <div className="flex items-center gap-1">
            {isPaused ? (
              <Button
                size="sm"
                variant="outline"
                onClick={onResumeStream}
                className="h-7 px-2"
              >
                <Play className="w-3 h-3 mr-1" />
                Resume
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={onPauseStream}
                className="h-7 px-2"
              >
                <Pause className="w-3 h-3 mr-1" />
                Pause
              </Button>
            )}
            <Button
              size="sm"
              variant="destructive"
              onClick={onStopStream}
              className="h-7 px-2"
            >
              <Square className="w-3 h-3" />
            </Button>
          </div>
        )}

        {/* More options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onRetryLastMessage && (
              <DropdownMenuItem onClick={onRetryLastMessage}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Retry Last
              </DropdownMenuItem>
            )}
            {onRefreshFromServer && (
              <DropdownMenuItem onClick={onRefreshFromServer}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowClearDialog(true)}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear Chat
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear confirmation dialog */}
        <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Clear Conversation</DialogTitle>
              <DialogDescription>
                This will permanently delete all messages in this conversation. This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowClearDialog(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleClearConversation}>
                Clear All
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center justify-between p-3 border-b bg-muted/30", className)}>
      {/* Left side - Stream controls */}
      <div className="flex items-center gap-2">
        {isStreaming ? (
          <>
            <Badge variant="secondary" className="animate-pulse">
              Streaming
            </Badge>
            <div className="flex items-center gap-1">
              {isPaused ? (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onResumeStream}
                  className="h-8"
                >
                  <Play className="w-4 h-4 mr-1" />
                  Resume
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onPauseStream}
                  className="h-8"
                >
                  <Pause className="w-4 h-4 mr-1" />
                  Pause
                </Button>
              )}
              <Button
                size="sm"
                variant="destructive"
                onClick={onStopStream}
                className="h-8"
              >
                <Square className="w-4 h-4 mr-1" />
                Stop
              </Button>
            </div>
          </>
        ) : (
          <div className="flex items-center gap-2">
            {messageCount > 0 && (
              <Badge variant="outline">
                {messageCount} messages
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Right side - Conversation controls */}
      <div className="flex items-center gap-2">
        {/* Quick actions */}
        {onRetryLastMessage && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onRetryLastMessage}
            className="h-8"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Retry
          </Button>
        )}

        {onRefreshFromServer && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onRefreshFromServer}
            className="h-8"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Refresh
          </Button>
        )}

        <Separator orientation="vertical" className="h-6" />

        {/* Export dropdown */}
        {onExportConversation && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="ghost" className="h-8">
                <Download className="w-4 h-4 mr-1" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleExport('markdown')}>
                <FileText className="w-4 h-4 mr-2" />
                Markdown
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('json')}>
                <Copy className="w-4 h-4 mr-2" />
                JSON
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('txt')}>
                <FileText className="w-4 h-4 mr-2" />
                Plain Text
              </DropdownMenuItem>
              {onShareConversation && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onShareConversation}>
                    <Share className="w-4 h-4 mr-2" />
                    Share Link
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Settings */}
        {onOpenSettings && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onOpenSettings}
            className="h-8"
          >
            <Settings className="w-4 h-4" />
          </Button>
        )}

        {/* Clear conversation */}
        <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
          <DialogTrigger asChild>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Clear
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Clear Conversation</DialogTitle>
              <DialogDescription>
                This will permanently delete all {messageCount} messages in this conversation. 
                This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowClearDialog(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleClearConversation}>
                Clear All Messages
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ChatControls; 