'use client';

import React from 'react';
import { AppMode } from '@/types';

interface DivergenceScannerModeProps {
  divergenceMode: string;
  setDivergenceMode: React.Dispatch<React.SetStateAction<any>>;
  selectedMarket: string;
  setSelectedMarket: React.Dispatch<React.SetStateAction<string>>;
  scanResults: any;
  setScanResults: React.Dispatch<React.SetStateAction<any>>;
  recentDivergences: any[];
  setRecentDivergences: React.Dispatch<React.SetStateAction<any[]>>;
  scanHistory: any[];
  setScanHistory: React.Dispatch<React.SetStateAction<any[]>>;
  isScanningDivergence: boolean;
  setIsScanningDivergence: React.Dispatch<React.SetStateAction<boolean>>;
  isScanning: boolean;
  setIsScanning: React.Dispatch<React.SetStateAction<boolean>>;
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const DivergenceScannerMode: React.FC<DivergenceScannerModeProps> = ({
  selectedMarket,
  setSelectedMarket,
  recentDivergences,
  setMode,
}) => {
  return (
    <div className="mode-container">
      <div className="mode-header">
        <button
          onClick={() => setMode('home')}
          className="back-button"
        >
          ← 返回首页
        </button>
        <h1>📈 背离扫描</h1>
        <p>扫描市场中的MACD背离信号，发现投资机会</p>
      </div>
      
      <div className="divergence-content">
        <div className="scanner-controls">
          <div className="form-group">
            <label>选择市场</label>
            <select
              value={selectedMarket}
              onChange={(e) => setSelectedMarket(e.target.value)}
              className="form-select"
            >
              <option value="US">美股</option>
              <option value="HK">港股</option>
              <option value="CN">A股</option>
            </select>
          </div>
          
          <button className="scan-button">
            开始扫描
          </button>
        </div>

        <div className="divergence-results">
          {recentDivergences.length > 0 ? (
            <div className="recent-divergences">
              <h3>最近发现的背离</h3>
              <div className="divergences-list">
                {recentDivergences.map((divergence, index) => (
                  <div key={index} className="divergence-card">
                    <h4>{divergence.symbol || `股票 ${index + 1}`}</h4>
                    <p>类型: {divergence.type || '未知'}</p>
                    <p>时间: {divergence.timestamp || '未知'}</p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="empty-state">
              <div className="text-center p-8">
                <div className="text-4xl mb-4">📈</div>
                <h3 className="text-lg font-semibold mb-2">开始扫描背离信号</h3>
                <p className="text-gray-600">选择市场并点击开始扫描来发现MACD背离机会</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DivergenceScannerMode; 