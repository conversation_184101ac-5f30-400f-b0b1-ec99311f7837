'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';

export interface ResponsiveLayoutConfig {
  // Layout modes
  viewMode: 'normal' | 'compact' | 'fullscreen';
  isMobile: boolean;
  isTablet: boolean;
  
  // Scrolling optimization
  virtualScrolling: boolean;
  virtualScrollThreshold: number; // Messages count to trigger virtual scrolling
  
  // Touch gestures
  enableTouchGestures: boolean;
  swipeThreshold: number;
  
  // Performance
  lazyLoadImages: boolean;
  compactMode: boolean;
}

interface ResponsiveLayoutManagerProps {
  children: React.ReactNode;
  messages: EnhancedMessage[];
  config?: Partial<ResponsiveLayoutConfig>;
  onConfigChange?: (config: ResponsiveLayoutConfig) => void;
  className?: string;
}

interface TouchState {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  isDragging: boolean;
  startTime: number;
}

export const DEFAULT_RESPONSIVE_CONFIG: ResponsiveLayoutConfig = {
  viewMode: 'normal',
  isMobile: false,
  isTablet: false,
  virtualScrolling: true,
  virtualScrollThreshold: 100,
  enableTouchGestures: true,
  swipeThreshold: 50,
  lazyLoadImages: true,
  compactMode: false,
};

// Hook for detecting device type
export const useDeviceDetection = () => {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 1024,
    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setDeviceInfo({
        isMobile: width <= 768,
        isTablet: width > 768 && width <= 1024,
        isDesktop: width > 1024,
        screenWidth: width,
        screenHeight: height,
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, []);

  return deviceInfo;
};

// Hook for touch gesture handling
export const useTouchGestures = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  onSwipeUp?: () => void,
  onSwipeDown?: () => void,
  threshold: number = 50
) => {
  const [touchState, setTouchState] = useState<TouchState | null>(null);
  const elementRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    const touch = e.touches[0];
    setTouchState({
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      isDragging: false,
      startTime: Date.now(),
    });
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchState) return;

    const touch = e.touches[0];
    setTouchState(prev => prev ? {
      ...prev,
      currentX: touch.clientX,
      currentY: touch.clientY,
      isDragging: true,
    } : null);
  }, [touchState]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchState) return;

    const deltaX = touchState.currentX - touchState.startX;
    const deltaY = touchState.currentY - touchState.startY;
    const deltaTime = Date.now() - touchState.startTime;

    // Only process swipes if they're fast enough and long enough
    if (deltaTime < 500 && (Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold)) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > threshold && onSwipeRight) {
          onSwipeRight();
        } else if (deltaX < -threshold && onSwipeLeft) {
          onSwipeLeft();
        }
      } else {
        // Vertical swipe
        if (deltaY > threshold && onSwipeDown) {
          onSwipeDown();
        } else if (deltaY < -threshold && onSwipeUp) {
          onSwipeUp();
        }
      }
    }

    setTouchState(null);
  }, [touchState, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  return { elementRef, touchState };
};

// Main ResponsiveLayoutManager component
const ResponsiveLayoutManager: React.FC<ResponsiveLayoutManagerProps> = ({
  children,
  messages,
  config: configProp,
  onConfigChange,
  className = '',
}) => {
  const deviceInfo = useDeviceDetection();
  const [config, setConfig] = useState<ResponsiveLayoutConfig>({
    ...DEFAULT_RESPONSIVE_CONFIG,
    isMobile: deviceInfo.isMobile,
    isTablet: deviceInfo.isTablet,
    ...configProp,
  });

  // Update device detection in config
  useEffect(() => {
    setConfig(prev => ({
      ...prev,
      isMobile: deviceInfo.isMobile,
      isTablet: deviceInfo.isTablet,
    }));
  }, [deviceInfo]);

  // Propagate config changes
  useEffect(() => {
    if (onConfigChange) {
      onConfigChange(config);
    }
  }, [config, onConfigChange]);

  const updateConfig = useCallback((updates: Partial<ResponsiveLayoutConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  // Touch gesture handlers
  const handleSwipeLeft = useCallback(() => {
    if (config.viewMode === 'fullscreen') {
      updateConfig({ viewMode: 'normal' });
    }
  }, [config.viewMode, updateConfig]);

  const handleSwipeRight = useCallback(() => {
    if (config.viewMode === 'normal') {
      updateConfig({ viewMode: 'fullscreen' });
    }
  }, [config.viewMode, updateConfig]);

  const handleSwipeUp = useCallback(() => {
    if (!config.compactMode) {
      updateConfig({ compactMode: true });
    }
  }, [config.compactMode, updateConfig]);

  const handleSwipeDown = useCallback(() => {
    if (config.compactMode) {
      updateConfig({ compactMode: false });
    }
  }, [config.compactMode, updateConfig]);

  const { elementRef: touchRef } = useTouchGestures(
    config.enableTouchGestures ? handleSwipeLeft : undefined,
    config.enableTouchGestures ? handleSwipeRight : undefined,
    config.enableTouchGestures ? handleSwipeUp : undefined,
    config.enableTouchGestures ? handleSwipeDown : undefined,
    config.swipeThreshold
  );

  // Virtual scrolling check
  const shouldUseVirtualScrolling = config.virtualScrolling && messages.length > config.virtualScrollThreshold;

  // CSS classes based on configuration
  const layoutClasses = [
    'responsive-layout-manager',
    className,
    `view-mode-${config.viewMode}`,
    config.isMobile ? 'mobile' : '',
    config.isTablet ? 'tablet' : '',
    config.compactMode ? 'compact' : '',
    shouldUseVirtualScrolling ? 'virtual-scroll' : '',
    config.enableTouchGestures ? 'touch-enabled' : '',
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={touchRef}
      className={layoutClasses}
      data-mobile={config.isMobile}
      data-tablet={config.isTablet}
      data-view-mode={config.viewMode}
      data-compact={config.compactMode}
      data-virtual-scroll={shouldUseVirtualScrolling}
    >
      {/* Layout controls for non-mobile devices */}
      {!config.isMobile && (
        <LayoutControls
          config={config}
          onConfigChange={updateConfig}
          messageCount={messages.length}
        />
      )}
      
      {/* Main content area */}
      <div className="layout-content">
        {children}
      </div>
      
      {/* Mobile touch hints */}
      {config.isMobile && config.enableTouchGestures && (
        <TouchHints config={config} />
      )}
    </div>
  );
};

// Layout controls component
interface LayoutControlsProps {
  config: ResponsiveLayoutConfig;
  onConfigChange: (updates: Partial<ResponsiveLayoutConfig>) => void;
  messageCount: number;
}

const LayoutControls: React.FC<LayoutControlsProps> = ({
  config,
  onConfigChange,
  messageCount,
}) => {
  return (
    <div className="layout-controls">
      <div className="layout-controls-group">
        {/* View mode buttons */}
        <div className="view-mode-controls">
          <button
            onClick={() => onConfigChange({ viewMode: 'normal' })}
            className={`control-button ${config.viewMode === 'normal' ? 'active' : ''}`}
            title="正常视图"
          >
            📱
          </button>
          <button
            onClick={() => onConfigChange({ viewMode: 'compact' })}
            className={`control-button ${config.viewMode === 'compact' ? 'active' : ''}`}
            title="紧凑视图"
          >
            📋
          </button>
          <button
            onClick={() => onConfigChange({ viewMode: 'fullscreen' })}
            className={`control-button ${config.viewMode === 'fullscreen' ? 'active' : ''}`}
            title="全屏视图"
          >
            🔳
          </button>
        </div>

        {/* Performance controls */}
        <div className="performance-controls">
          <button
            onClick={() => onConfigChange({ virtualScrolling: !config.virtualScrolling })}
            className={`control-button ${config.virtualScrolling ? 'active' : ''}`}
            title="虚拟滚动"
            disabled={messageCount < config.virtualScrollThreshold}
          >
            ⚡
          </button>
          <button
            onClick={() => onConfigChange({ lazyLoadImages: !config.lazyLoadImages })}
            className={`control-button ${config.lazyLoadImages ? 'active' : ''}`}
            title="懒加载图片"
          >
            🖼️
          </button>
        </div>

        {/* Touch gesture toggle */}
        <div className="gesture-controls">
          <button
            onClick={() => onConfigChange({ enableTouchGestures: !config.enableTouchGestures })}
            className={`control-button ${config.enableTouchGestures ? 'active' : ''}`}
            title="触摸手势"
          >
            👆
          </button>
        </div>
      </div>
    </div>
  );
};

// Touch hints component for mobile
interface TouchHintsProps {
  config: ResponsiveLayoutConfig;
}

const TouchHints: React.FC<TouchHintsProps> = ({ config }) => {
  const [showHints, setShowHints] = useState(false);

  useEffect(() => {
    // Show hints briefly when touch gestures are enabled
    if (config.enableTouchGestures) {
      setShowHints(true);
      const timer = setTimeout(() => setShowHints(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [config.enableTouchGestures]);

  if (!showHints) return null;

  return (
    <div className="touch-hints">
      <div className="hint-overlay">
        <div className="hint-item">
          <span className="hint-gesture">👈</span>
          <span className="hint-text">向左滑动退出全屏</span>
        </div>
        <div className="hint-item">
          <span className="hint-gesture">👉</span>
          <span className="hint-text">向右滑动进入全屏</span>
        </div>
        <div className="hint-item">
          <span className="hint-gesture">👆</span>
          <span className="hint-text">向上滑动紧凑模式</span>
        </div>
        <div className="hint-item">
          <span className="hint-gesture">👇</span>
          <span className="hint-text">向下滑动正常模式</span>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveLayoutManager; 