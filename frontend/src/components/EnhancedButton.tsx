'use client';

import React from 'react';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  className?: string;
  style?: React.CSSProperties;
  badge?: string | number;
  tooltip?: string;
}

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  className = '',
  style,
  badge,
  tooltip
}) => {
  // Base button styles
  const baseStyles = `
    relative inline-flex items-center justify-center
    font-medium rounded-xl transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    transform active:scale-95
    overflow-hidden
  `;

  // Size variants
  const sizeStyles = {
    xs: 'px-2 py-1 text-xs gap-1',
    sm: 'px-3 py-1.5 text-sm gap-1.5',
    md: 'px-4 py-2 text-sm gap-2',
    lg: 'px-6 py-3 text-base gap-2',
    xl: 'px-8 py-4 text-lg gap-3'
  };

  // Variant styles
  const variantStyles = {
    primary: `
      bg-gradient-to-r from-primary-500 to-primary-600 text-gray-900
      hover:from-primary-600 hover:to-primary-700 hover:shadow-lg
      focus:ring-primary-500 shadow-md hover:shadow-xl
      border border-primary-400/20
    `,
    secondary: `
      bg-gradient-to-r from-secondary-500 to-secondary-600 text-gray-900
      hover:from-secondary-600 hover:to-secondary-700 hover:shadow-lg
      focus:ring-secondary-500 shadow-md hover:shadow-xl
      border border-secondary-400/20
    `,
    success: `
      bg-gradient-to-r from-success-500 to-success-600 text-white
      hover:from-success-600 hover:to-success-700 hover:shadow-lg
      focus:ring-success-500 shadow-md hover:shadow-xl
      border border-success-400/20
    `,
    warning: `
      bg-gradient-to-r from-warning-500 to-warning-600 text-white
      hover:from-warning-600 hover:to-warning-700 hover:shadow-lg
      focus:ring-warning-500 shadow-md hover:shadow-xl
      border border-warning-400/20
    `,
    error: `
      bg-gradient-to-r from-error-500 to-error-600 text-white
      hover:from-error-600 hover:to-error-700 hover:shadow-lg
      focus:ring-error-500 shadow-md hover:shadow-xl
      border border-error-400/20
    `,
    ghost: `
      bg-transparent text-gray-700 hover:bg-gray-100
      focus:ring-gray-500 border border-transparent
      hover:border-gray-200
    `,
    outline: `
      bg-transparent text-primary-600 border-2 border-primary-500
      hover:bg-primary-50 hover:border-primary-600
      focus:ring-primary-500
    `
  };

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
  );

  // Ripple effect on click
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Create ripple effect
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;

    button.appendChild(ripple);
    setTimeout(() => ripple.remove(), 600);

    if (onClick) {
      onClick();
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      style={style}
      className={`
        ${baseStyles}
        ${sizeStyles[size]}
        ${variantStyles[variant]}
        ${fullWidth ? 'w-full' : ''}
        ${disabled || loading ? 'cursor-not-allowed' : 'hover:-translate-y-0.5'}
        ${className}
      `}
      title={tooltip}
    >
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 hover:opacity-100 transition-opacity duration-300" />

      {/* Content */}
      <div className="relative flex items-center justify-center gap-inherit">
        {/* Left icon or loading */}
        {(icon && iconPosition === 'left') && !loading && (
          <span className="text-current">{icon}</span>
        )}
        {loading && (
          <LoadingSpinner />
        )}

        {/* Button text */}
        <span className={loading ? 'opacity-70' : ''}>{children}</span>

        {/* Right icon */}
        {(icon && iconPosition === 'right') && !loading && (
          <span className="text-current">{icon}</span>
        )}
      </div>

      {/* Badge */}
      {badge && (
        <span className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 bg-error-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg border-2 border-white">
          {badge}
        </span>
      )}
    </button>
  );
};

export default EnhancedButton; 