'use client';

import React from 'react';
import { DotIndicator, BadgeIndicator, StatusIndicator, PriorityIndicator } from './VisualIndicator';

const IndicatorDemo: React.FC = () => {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 flex items-center gap-3">
        <DotIndicator category="chart" size="lg" />
        Visual Indicators Demo
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Category Indicators */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <DotIndicator category="navigation" size="md" />
            Category Indicators
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <DotIndicator category="analysis" size="md" />
              <span>Analysis (📈 replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="chart" size="md" />
              <span>Chart (📊 replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="research" size="md" />
              <span>Research (🔍 replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="recommendation" size="md" />
              <span>Recommendation (💡 replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="warning" size="md" />
              <span>Warning (⚠️ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="error" size="md" />
              <span>Error (❌ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="success" size="md" />
              <span>Success (✅ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="data" size="md" />
              <span>Data (📋 replacement)</span>
            </div>
          </div>
        </div>

        {/* Badge Indicators */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <BadgeIndicator category="navigation" label="Badges" size="sm" />
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <BadgeIndicator category="chart" label="图表" />
              <span>Chart Badge</span>
            </div>
            <div className="flex items-center gap-3">
              <BadgeIndicator category="analysis" label="分析师" />
              <span>Agent Badge</span>
            </div>
            <div className="flex items-center gap-3">
              <BadgeIndicator category="research" label="研究员" />
              <span>Researcher Badge</span>
            </div>
            <div className="flex items-center gap-3">
              <BadgeIndicator category="recommendation" label="建议" />
              <span>Recommendation Badge</span>
            </div>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <StatusIndicator status="streaming" size="md" showLabel />
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <StatusIndicator status="sending" size="sm" />
              <span>Sending (⏳ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <StatusIndicator status="streaming" size="sm" />
              <span>Streaming (modern indicator)</span>
            </div>
            <div className="flex items-center gap-3">
              <StatusIndicator status="complete" size="sm" />
              <span>Complete (✅ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <StatusIndicator status="failed" size="sm" />
              <span>Failed (❌ replacement)</span>
            </div>
            <div className="flex items-center gap-3">
              <StatusIndicator status="timeout" size="sm" />
              <span>Timeout (⏰ replacement)</span>
            </div>
          </div>
        </div>

        {/* Size Variations */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">Size Variations</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <DotIndicator category="chart" size="xs" />
              <DotIndicator category="chart" size="sm" />
              <DotIndicator category="chart" size="md" />
              <DotIndicator category="chart" size="lg" />
              <DotIndicator category="chart" size="xl" />
              <span>XS → XL</span>
            </div>
          </div>
        </div>

        {/* Interactive States */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">Interactive States</h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <DotIndicator category="analysis" size="md" state="loading" />
              <span>Loading State</span>
            </div>
            <div className="flex items-center gap-3">
              <DotIndicator category="analysis" size="md" state="streaming" />
              <span>Streaming State</span>
            </div>
            <div className="flex items-center gap-3">
              <PriorityIndicator priority="critical" />
              <span>Critical Priority</span>
            </div>
            <div className="flex items-center gap-3">
              <PriorityIndicator priority="high" />
              <span>High Priority</span>
            </div>
          </div>
        </div>

        {/* Before/After Comparison */}
        <div className="bg-white rounded-lg shadow-sm border p-6 md:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Before/After Comparison</h2>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-3 text-red-600">❌ Before (Emojis)</h3>
              <div className="space-y-2 text-sm">
                <div>📊 Chart</div>
                <div>📈 Analysis</div>
                <div>🔍 Research</div>
                <div>💡 Recommendation</div>
                <div>⚠️ Warning</div>
                <div>✅ Success</div>
                <div>Loading (old emoji style)</div>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-3 text-green-600">✨ After (Visual Indicators)</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <DotIndicator category="chart" size="sm" />
                  Chart
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="analysis" size="sm" />
                  Analysis
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="research" size="sm" />
                  Research
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="recommendation" size="sm" />
                  Recommendation
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="warning" size="sm" />
                  Warning
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="success" size="sm" />
                  Success
                </div>
                <div className="flex items-center gap-2">
                  <DotIndicator category="navigation" size="sm" state="streaming" />
                  Loading
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndicatorDemo; 