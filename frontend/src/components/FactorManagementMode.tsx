'use client';

import React from 'react';
import { AppMode, Factor } from '@/types';
import LoadingSpinner from './LoadingSpinner';

interface FactorManagementModeProps {
  factors: Factor[];
  setFactors: React.Dispatch<React.SetStateAction<Factor[]>>;
  selectedStock: string;
  setSelectedStock: React.Dispatch<React.SetStateAction<string>>;
  factorData: any;
  setFactorData: React.Dispatch<React.SetStateAction<any>>;
  factorMode: string;
  setFactorMode: React.Dispatch<React.SetStateAction<any>>;
  startDate: string;
  setStartDate: React.Dispatch<React.SetStateAction<string>>;
  endDate: string;
  setEndDate: React.Dispatch<React.SetStateAction<string>>;
  customFactorName: string;
  setCustomFactorName: React.Dispatch<React.SetStateAction<string>>;
  customFactorDesc: string;
  setCustomFactorDesc: React.Dispatch<React.SetStateAction<string>>;
  customFactorFormula: string;
  setCustomFactorFormula: React.Dispatch<React.SetStateAction<string>>;
  aiFactorPrompt: string;
  setAiFactorPrompt: React.Dispatch<React.SetStateAction<string>>;
  aiFactorResult: any;
  setAiFactorResult: React.Dispatch<React.SetStateAction<any>>;
  isGeneratingFactor: boolean;
  setIsGeneratingFactor: React.Dispatch<React.SetStateAction<boolean>>;
  factorAnalysisResults: any;
  setFactorAnalysisResults: React.Dispatch<React.SetStateAction<any>>;
  selectedStockForChart: string;
  setSelectedStockForChart: React.Dispatch<React.SetStateAction<string>>;
  factorStockData: any;
  setFactorStockData: React.Dispatch<React.SetStateAction<any>>;
  showFactorChart: boolean;
  setShowFactorChart: React.Dispatch<React.SetStateAction<boolean>>;
  selectedFactorsForAnalysis: string[];
  setSelectedFactorsForAnalysis: React.Dispatch<React.SetStateAction<string[]>>;
  factorChartData: any;
  setFactorChartData: React.Dispatch<React.SetStateAction<any>>;
  isPerformingAnalysis: boolean;
  setIsPerformingAnalysis: React.Dispatch<React.SetStateAction<boolean>>;
  analysisStockSuggestions: any[];
  setAnalysisStockSuggestions: React.Dispatch<React.SetStateAction<any[]>>;
  showAnalysisStockSuggestions: boolean;
  setShowAnalysisStockSuggestions: React.Dispatch<React.SetStateAction<boolean>>;
  analysisSearchInput: string;
  setAnalysisSearchInput: React.Dispatch<React.SetStateAction<string>>;
  analysisStocks: string[];
  setAnalysisStocks: React.Dispatch<React.SetStateAction<string[]>>;
  analysisSearchSuggestions: any[];
  setAnalysisSearchSuggestions: React.Dispatch<React.SetStateAction<any[]>>;
  showAnalysisSearchSuggestions: boolean;
  setShowAnalysisSearchSuggestions: React.Dispatch<React.SetStateAction<boolean>>;
  analysisTimeframe: string;
  setAnalysisTimeframe: React.Dispatch<React.SetStateAction<string>>;
  analysisMode: string;
  setAnalysisMode: React.Dispatch<React.SetStateAction<any>>;
  isAnalyzing: boolean;
  setIsAnalyzing: React.Dispatch<React.SetStateAction<boolean>>;
  analysisResults: any;
  setAnalysisResults: React.Dispatch<React.SetStateAction<any>>;
  correlationMatrix: any;
  setCorrelationMatrix: React.Dispatch<React.SetStateAction<any>>;
  returnsData: any;
  setReturnsData: React.Dispatch<React.SetStateAction<any>>;
  validityMetrics: any;
  setValidityMetrics: React.Dispatch<React.SetStateAction<any>>;
  riskMetrics: any;
  setRiskMetrics: React.Dispatch<React.SetStateAction<any>>;
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const FactorManagementMode: React.FC<FactorManagementModeProps> = ({
  factors,
  selectedStock,
  setSelectedStock,
  factorData,
  factorMode,
  setFactorMode,
  setMode,
}) => {
  return (
    <div className="mode-container">
      <div className="mode-header">
        <button
          onClick={() => setMode('home')}
          className="back-button"
        >
          ← 返回首页
        </button>
        <h1>📊 因子管理</h1>
        <p>管理和分析各种投资因子，创建自定义因子模型</p>
      </div>
      
      <div className="factor-content">
        <div className="factor-tabs">
          <button
            className={`tab ${factorMode === 'overview' ? 'active' : ''}`}
            onClick={() => setFactorMode('overview')}
          >
            因子概览
          </button>
          <button
            className={`tab ${factorMode === 'calculation' ? 'active' : ''}`}
            onClick={() => setFactorMode('calculation')}
          >
            因子计算
          </button>
          <button
            className={`tab ${factorMode === 'custom' ? 'active' : ''}`}
            onClick={() => setFactorMode('custom')}
          >
            自定义因子
          </button>
          <button
            className={`tab ${factorMode === 'analysis' ? 'active' : ''}`}
            onClick={() => setFactorMode('analysis')}
          >
            因子分析
          </button>
        </div>

        <div className="factor-body">
          {factorMode === 'overview' && (
            <div className="factors-overview">
              <h3>可用因子列表</h3>
              <div className="factors-grid">
                {factors.length > 0 ? (
                  factors.map((factor) => (
                    <div key={factor.id} className="factor-card">
                      <h4>{factor.name}</h4>
                      <p className="factor-category">{factor.category}</p>
                      <p className="factor-description">{factor.description}</p>
                      <div className="factor-status">
                        {factor.isActive ? (
                          <span className="status-active">已启用</span>
                        ) : (
                          <span className="status-inactive">未启用</span>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="empty-state">
                    <div className="text-center p-8">
                      <div className="text-4xl mb-4">📊</div>
                      <h3 className="text-lg font-semibold mb-2">暂无因子数据</h3>
                      <p className="text-gray-600">正在从后端加载因子信息...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {factorMode === 'calculation' && (
            <div className="factor-calculation">
              <h3>因子计算</h3>
              <div className="calculation-form">
                <div className="form-group">
                  <label>选择股票代码</label>
                  <input
                    type="text"
                    value={selectedStock}
                    onChange={(e) => setSelectedStock(e.target.value)}
                    placeholder="输入股票代码，如 AAPL"
                    className="form-input"
                  />
                </div>
                <button className="calculate-button">
                  计算因子
                </button>
              </div>
              
              {factorData && (
                <div className="calculation-results">
                  <h4>计算结果</h4>
                  <pre className="results-display">
                    {JSON.stringify(factorData, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}

          {factorMode === 'custom' && (
            <div className="custom-factor">
              <h3>自定义因子</h3>
              <p className="text-gray-600">创建您自己的投资因子</p>
              <div className="text-center p-8">
                <div className="text-4xl mb-4">🛠️</div>
                <h3 className="text-lg font-semibold mb-2">功能开发中</h3>
                <p className="text-gray-600">自定义因子功能即将推出</p>
              </div>
            </div>
          )}

          {factorMode === 'analysis' && (
            <div className="factor-analysis">
              <h3>因子分析</h3>
              <p className="text-gray-600">分析因子表现和相关性</p>
              <div className="text-center p-8">
                <div className="text-4xl mb-4">📈</div>
                <h3 className="text-lg font-semibold mb-2">功能开发中</h3>
                <p className="text-gray-600">因子分析功能即将推出</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FactorManagementMode; 