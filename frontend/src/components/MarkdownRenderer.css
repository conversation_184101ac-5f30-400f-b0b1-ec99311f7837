/* Markdown 内容容器 */
.markdown-content {
  line-height: 1.6;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Markdown 渲染器容器 */
.markdown-renderer-container {
  position: relative;
}

/* 操作按钮容器 */
.markdown-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e1e4e8;
}

/* 标题样式 */
.markdown-h1 {
  font-size: 2em;
  font-weight: 700;
  margin: 1.5em 0 0.8em 0;
  padding-bottom: 0.3em;
  border-bottom: 2px solid #e1e4e8;
  color: #1a1a1a;
  position: relative;
}

.markdown-h1:first-child {
  margin-top: 0;
}

.markdown-h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1.2em 0 0.6em 0;
  padding-bottom: 0.2em;
  border-bottom: 1px solid #e1e4e8;
  color: #2c3e50;
  position: relative;
}

.markdown-h2:first-child {
  margin-top: 0;
}

.markdown-h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  color: #34495e;
}

.markdown-h3:first-child {
  margin-top: 0;
}

.markdown-h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 0.8em 0 0.4em 0;
  color: #34495e;
}

.markdown-h4:first-child {
  margin-top: 0;
}

/* 段落样式 */
.markdown-p {
  margin: 0.8em 0;
  line-height: 1.7;
}

.markdown-p:first-child {
  margin-top: 0;
}

.markdown-p:last-child {
  margin-bottom: 0;
}

/* 块级段落样式 - 用于包含块级元素的段落 */
.markdown-p-block {
  margin: 0.8em 0;
  line-height: 1.7;
}

.markdown-p-block:first-child {
  margin-top: 0;
}

.markdown-p-block:last-child {
  margin-bottom: 0;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: #2c3e50;
}

.markdown-em {
  font-style: italic;
  color: #555;
}

/* 列表样式 */
.markdown-ul, .markdown-ol {
  margin: 0.8em 0;
  padding-left: 2em;
}

.markdown-li {
  margin: 0.3em 0;
  line-height: 1.6;
}

.markdown-ul .markdown-li {
  list-style-type: disc;
}

.markdown-ol .markdown-li {
  list-style-type: decimal;
}

/* 嵌套列表 */
.markdown-ul .markdown-ul .markdown-li {
  list-style-type: circle;
}

.markdown-ul .markdown-ul .markdown-ul .markdown-li {
  list-style-type: square;
}

/* 代码样式 */
.inline-code {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.9em;
  color: #d73a49;
}

/* 增强的代码块容器 */
.code-block-container {
  margin: 1em 0;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f6f8fa;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f3f4;
  border-bottom: 1px solid #e1e4e8;
  font-size: 0.85em;
}

.code-language {
  font-weight: 600;
  color: #586069;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-copy-button {
  background: none;
  border: 1px solid #d1d9e0;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 0.75em;
  color: #586069;
  cursor: pointer;
  transition: all 0.2s ease;
}

.code-copy-button:hover {
  background-color: #e1e4e8;
  border-color: #c6cbd1;
}

.code-block {
  background-color: #f6f8fa;
  padding: 1em;
  margin: 0;
  overflow-x: auto;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.9em;
  line-height: 1.45;
}

.code-block code {
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}

/* 带行号的代码表格 */
.code-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
}

.line-number {
  width: 40px;
  padding-right: 16px;
  text-align: right;
  color: #6a737d;
  user-select: none;
  border-right: 1px solid #e1e4e8;
  vertical-align: top;
}

.line-content {
  padding-left: 16px;
  white-space: pre-wrap;
  word-break: break-all;
  vertical-align: top;
}

/* 表格样式 */
.table-wrapper {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-table {
  border-collapse: collapse;
  width: 100%;
  background-color: #fff;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  padding: 0.75em 1em;
  text-align: left;
  font-weight: 600;
  color: #24292e;
}

.markdown-table td {
  border: 1px solid #e1e4e8;
  padding: 0.75em 1em;
  color: #586069;
}

.markdown-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.markdown-table tr:hover {
  background-color: #f1f8ff;
}

/* 链接样式 */
.markdown-link {
  color: #0366d6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  color: #0366d6;
  border-bottom-color: #0366d6;
  text-decoration: none;
}

.markdown-link:visited {
  color: #6f42c1;
}

/* 引用样式 */
.markdown-blockquote {
  border-left: 4px solid #dfe2e5;
  padding: 0 1em;
  margin: 1em 0;
  color: #6a737d;
  background-color: #f8f9fa;
  border-radius: 0 3px 3px 0;
}

.markdown-blockquote .markdown-p {
  margin: 0.5em 0;
}

/* 分割线样式 */
.markdown-hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, #e1e4e8, #f6f8fa, #e1e4e8);
  margin: 2em 0;
  border-radius: 1px;
}

/* 图片样式 */
.markdown-img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1em 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 数学公式样式 */
.katex {
  font-size: 1em;
}

.katex-display {
  margin: 1em 0;
  text-align: center;
}

.katex-error {
  color: #cc0000;
  background-color: #ffeaea;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  border: 1px solid #ffcccc;
}

/* 懒加载占位符 */
.lazy-markdown-placeholder {
  text-align: center;
  padding: 2em;
  background-color: #f8f9fa;
  border: 2px dashed #e1e4e8;
  border-radius: 6px;
  margin: 1em 0;
}

/* 任务列表样式 */
.markdown-content .task-list-item {
  list-style: none;
  margin-left: -1.5em;
}

.markdown-content .task-list-item input {
  margin-right: 0.5em;
}

/* 数学公式容器 */
.markdown-content .math {
  overflow-x: auto;
  padding: 0.5em 0;
}

/* 语法高亮覆盖 */
.markdown-content .hljs {
  background: transparent !important;
  color: inherit;
  padding: 0;
}

/* 标记文本 */
.markdown-content mark {
  background-color: #fff3cd;
  padding: 0.1em 0.2em;
  border-radius: 2px;
  color: #856404;
}

/* 键盘按键样式 */
.markdown-content kbd {
  background-color: #f6f8fa;
  border: 1px solid #c6cbd1;
  border-bottom-color: #959da5;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #959da5;
  color: #444d56;
  display: inline-block;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 0.85em;
  line-height: 1;
  padding: 0.2em 0.4em;
  vertical-align: middle;
}

/* 删除线 */
.markdown-content del {
  text-decoration: line-through;
  color: #6a737d;
}

/* 下标和上标 */
.markdown-content sub,
.markdown-content sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.markdown-content sub {
  bottom: -0.25em;
}

.markdown-content sup {
  top: -0.5em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 0.9em;
  }

  .markdown-h1 {
    font-size: 1.75em;
  }

  .markdown-h2 {
    font-size: 1.4em;
  }

  .markdown-h3 {
    font-size: 1.2em;
  }

  .code-block {
    padding: 0.8em;
    font-size: 0.85em;
  }

  .markdown-table {
    font-size: 0.85em;
  }

  .markdown-table th,
  .markdown-table td {
    padding: 0.5em 0.75em;
  }

  .markdown-actions {
    flex-direction: column;
    gap: 4px;
  }

  .code-block-header {
    padding: 6px 10px;
    font-size: 0.8em;
  }

  .line-number {
    width: 30px;
    padding-right: 12px;
  }

  .line-content {
    padding-left: 12px;
  }
}

/* ===== Enhanced Image Styles ===== */

/* Base64 image container */
.markdown-img-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  margin: 1em 0;
}

/* Base markdown image styles */
.markdown-img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

/* Enhanced styles for base64 images */
.markdown-img.base64-img {
  border: 2px solid #e5e7eb;
  background: #f9fafb;
  padding: 4px;
}

/* Hover effects for images */
.markdown-img:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Loading state for images */
.markdown-img.loading {
  opacity: 0;
  transform: scale(0.95);
}

.markdown-img-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #6b7280;
  backdrop-filter: blur(4px);
  z-index: 10;
}

/* Error state for images */
.markdown-img-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #fef2f2;
  border: 2px dashed #fca5a5;
  border-radius: 8px;
  color: #dc2626;
  font-size: 0.875rem;
  margin: 1em 0;
}

.markdown-img-error .img-alt-text {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive image sizing */
@media (max-width: 768px) {
  .markdown-img {
    max-width: 100%;
    border-radius: 6px;
  }
  
  .markdown-img-container {
    margin: 0.75em 0;
  }
}

/* Large image handling */
.markdown-img[width] {
  max-width: min(100%, 800px);
}

/* Image zoom on click (optional enhancement) */
.markdown-img.zoomed {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  z-index: 1000;
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border-radius: 12px;
}

/* Backdrop for zoomed images */
.image-zoom-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999;
  backdrop-filter: blur(4px);
}

/* ===== Existing Markdown Styles ===== */

/* Markdown content container */
.markdown-content {
  line-height: 1.6;
  color: #374151;
  font-size: 0.95rem;
}

/* Headers */
.markdown-h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.markdown-h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #1f2937;
}

.markdown-h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #374151;
}

.markdown-h4 {
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0.875rem 0 0.5rem 0;
  color: #374151;
}

/* Paragraphs */
.markdown-p {
  margin: 0.75rem 0;
  line-height: 1.7;
}

.markdown-p-block {
  margin: 0.75rem 0;
  line-height: 1.7;
}

/* Lists */
.markdown-ul {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.markdown-ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.markdown-li {
  margin: 0.25rem 0;
  line-height: 1.6;
}

/* Links */
.markdown-link {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

/* Blockquotes */
.markdown-blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
  background: #f9fafb;
  padding: 0.75rem 1rem;
  border-radius: 0 6px 6px 0;
}

/* Inline code */
.inline-code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  border: 1px solid #e5e7eb;
}

/* Code blocks */
.code-block-container {
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  background: #f8fafc;
}

.code-block-header {
  background: #f1f5f9;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.code-language {
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.code-copy-button {
  background: #e2e8f0;
  border: 1px solid #cbd5e1;
  color: #475569;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.code-copy-button:hover {
  background: #cbd5e1;
  color: #334155;
}

.code-block {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.code-table {
  width: 100%;
  border-collapse: collapse;
}

.line-number {
  color: #64748b;
  text-align: right;
  padding-right: 1rem;
  user-select: none;
  border-right: 1px solid #334155;
  min-width: 2rem;
}

.line-content {
  padding-left: 1rem;
  width: 100%;
}

/* Tables */
.table-wrapper {
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.markdown-table th {
  background: #f9fafb;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.markdown-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
}

.markdown-table tr:hover {
  background: #f9fafb;
}

/* Horizontal rule */
.markdown-hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
  margin: 2rem 0;
}

/* Emphasis */
.markdown-strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-em {
  font-style: italic;
  color: #4b5563;
}

/* Pre elements */
.markdown-pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 1rem 0;
  border: 1px solid #334155;
}

/* Action buttons */
.markdown-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 640px) {
  .markdown-content {
    font-size: 0.9rem;
  }
  
  .markdown-h1 {
    font-size: 1.5rem;
  }
  
  .markdown-h2 {
    font-size: 1.25rem;
  }
  
  .markdown-h3 {
    font-size: 1.125rem;
  }
  
  .code-block {
    font-size: 0.8rem;
  }
  
  .table-wrapper {
    font-size: 0.875rem;
  }
} 