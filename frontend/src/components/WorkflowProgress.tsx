import React from 'react';
import CircularProgress from './CircularProgress';
import ProgressIndicator from './ProgressIndicator';

interface WorkflowProgressProps {
  progress: number; // 0-100
  stepName: string;
  substepDescription?: string; // NEW: Current substep details
  totalSteps?: number;
  currentStepIndex?: number; // NEW: Current step index
  isActive?: boolean;
  canCancel?: boolean; // NEW: Can cancel workflow
  onCancel?: () => void; // NEW: Cancel callback
  onRetry?: () => void; // NEW: Retry callback
  status?: 'loading' | 'error' | 'success'; // NEW: Workflow status
  className?: string;
}

const WorkflowProgress: React.FC<WorkflowProgressProps> = ({
  progress,
  stepName,
  substepDescription,
  totalSteps = 8,
  currentStepIndex,
  isActive = true,
  canCancel = false,
  onCancel,
  onRetry,
  status = 'loading',
  className = ''
}) => {
  // Map common workflow steps to more user-friendly names
  const getDisplayStepName = (step: string): string => {
    const stepMappings: Record<string, string> = {
      'planning': '制定分析计划',
      'research': '数据收集与研究',
      'technical_analysis': '技术指标分析',
      'fundamental_analysis': '基本面分析',
      'sentiment_analysis': '市场情绪分析',
      'risk_assessment': '风险评估',
      'report_generation': '生成分析报告',
      'final_review': '最终审核',
      'chart_generation': '生成图表',
      'market_data_collection': '收集市场数据',
      'factor_analysis': '因子分析',
      'portfolio_optimization': '投资组合优化'
    };
    
    // Try to find a mapping, otherwise use the original step name
    const lowerStep = step.toLowerCase().replace(/[^a-z]/g, '_');
    return stepMappings[lowerStep] || step;
  };

  // Generate workflow steps for better progress visualization
  const generateWorkflowSteps = () => {
    const baseSteps = [
      { id: 'planning', name: '制定分析计划', status: 'completed' as const },
      { id: 'data_collection', name: '数据收集', status: 'completed' as const },
      { id: 'technical_analysis', name: '技术分析', status: 'active' as const },
      { id: 'fundamental_analysis', name: '基本面分析', status: 'pending' as const },
      { id: 'risk_assessment', name: '风险评估', status: 'pending' as const },
      { id: 'report_generation', name: '生成报告', status: 'pending' as const },
      { id: 'final_review', name: '最终审核', status: 'pending' as const },
      { id: 'complete', name: '完成', status: 'pending' as const }
    ];

    // Update step statuses based on current progress
    const stepProgress = Math.floor((progress / 100) * baseSteps.length);
    return baseSteps.map((step, index) => ({
      ...step,
      status: index < stepProgress ? 'completed' as const :
              index === stepProgress ? 'active' as const :
              status === 'error' && index === stepProgress ? 'error' as const : 'pending' as const,
      progress: index === stepProgress ? progress % (100 / baseSteps.length) * baseSteps.length : undefined
    }));
  };

  const workflowSteps = generateWorkflowSteps();
  const displayStepName = getDisplayStepName(stepName);

  return (
    <div className={`workflow-progress bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Use enhanced ProgressIndicator for better UX */}
      <ProgressIndicator
        variant="detailed"
        progress={progress}
        title="AI 投资分析进行中"
        currentStep={displayStepName}
        substepDescription={substepDescription}
        status={status}
        showPercentage={true}
        showETA={true}
        showCurrentStep={true}
        showSubsteps={true}
        steps={workflowSteps}
        canCancel={canCancel && isActive}
        canRetry={status === 'error'}
        onCancel={onCancel}
        onRetry={onRetry}
        animated={true}
        size="md"
        color={status === 'error' ? 'red' : status === 'success' ? 'green' : 'blue'}
      />

      {/* Legacy circular progress display - keeping for visual consistency */}
      <div className="mt-4 flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex-shrink-0">
          <CircularProgress 
            progress={progress} 
            size={60} 
            strokeWidth={4}
            showProgress={false}
          >
            <div className="flex flex-col items-center justify-center">
              <div className="text-sm font-bold text-blue-600">
                {Math.round(progress)}%
              </div>
              {totalSteps && currentStepIndex !== undefined && (
                <div className="text-xs text-gray-500">
                  {currentStepIndex + 1}/{totalSteps}
                </div>
              )}
            </div>
          </CircularProgress>
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="text-sm font-semibold text-gray-900 truncate">
              {displayStepName}
            </h4>
            {isActive && status === 'loading' && (
              <div className="flex space-x-1">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
            )}
          </div>
          
          {substepDescription && (
            <div className="text-xs text-gray-600 mb-2">
              {substepDescription}
            </div>
          )}
          
          <div className="text-xs text-gray-500">
            <span>{isActive ? '分析中...' : '已完成'}</span>
            <span className="ml-2">{progress === 100 ? '分析完成' : `${Math.round(progress)}% 完成`}</span>
          </div>
        </div>
      </div>
      
      {/* Status indicators */}
      {progress === 100 && status === 'success' && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-green-700 font-medium">分析完成！正在生成最终报告...</span>
          </div>
        </div>
      )}
      
      {status === 'error' && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <svg className="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <span className="text-sm text-red-700 font-medium">分析过程中出现错误</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowProgress; 