'use client';

import React from 'react';
import { StreamingControl } from '@/hooks/useChatStreaming';

interface StreamingControlsProps {
  isStreaming: boolean;
  streamingControl: StreamingControl;
  onControlChange: (control: StreamingControl) => void;
  networkStatus: 'online' | 'offline' | 'reconnecting';
  className?: string;
}

const StreamingControls: React.FC<StreamingControlsProps> = ({
  isStreaming,
  streamingControl,
  onControlChange,
  networkStatus,
  className = '',
}) => {
  if (!isStreaming) return null;

  const handlePauseResume = () => {
    onControlChange(streamingControl === 'pause' ? 'play' : 'pause');
  };

  const handleStop = () => {
    onControlChange('stop');
  };

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      {/* Network status indicator */}
      <div className="flex items-center space-x-1">
        <div 
          className={`w-2 h-2 rounded-full ${
            networkStatus === 'online' 
              ? 'bg-green-500' 
              : networkStatus === 'offline'
              ? 'bg-red-500'
              : 'bg-yellow-500 animate-pulse'
          }`} 
        />
        <span className="text-xs text-gray-500">
          {networkStatus === 'online' && '在线'}
          {networkStatus === 'offline' && '离线'}
          {networkStatus === 'reconnecting' && '重连中'}
        </span>
      </div>

      {/* Streaming controls */}
      <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 shadow-sm">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-700">正在接收响应</span>
        </div>

        <div className="flex items-center space-x-1">
          {/* Pause/Resume button */}
          <button
            onClick={handlePauseResume}
            className="p-1 text-gray-600 hover:text-blue-600 transition-colors"
            title={streamingControl === 'pause' ? '继续' : '暂停'}
          >
            {streamingControl === 'pause' ? (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
          </button>

          {/* Stop button */}
          <button
            onClick={handleStop}
            className="p-1 text-gray-600 hover:text-red-600 transition-colors"
            title="停止"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StreamingControls; 