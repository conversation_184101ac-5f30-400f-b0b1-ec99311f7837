'use client';

import React, { useCallback, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import type { EChartsOption } from 'echarts';

// Chart data interfaces
interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface ChartData {
  symbol: string;
  company_name: string;
  current_price: number;
  price_change: number;
  price_change_percent: number;
  dates: string[];
  kline_data: [number, number, number, number][]; // [open, close, low, high]
  volume_data: number[];
  last_update: string;
}

interface ChartRendererProps {
  chartData: ChartData;
  chartConfig?: any;
  height?: string | number;
  className?: string;
  enableInteraction?: boolean;
  enableDownload?: boolean;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({
  chartData,
  chartConfig,
  height = '500px',
  className = '',
  enableInteraction = true,
  enableDownload = true
}) => {
  // Generate ECharts configuration
  const getEChartsOption = useCallback((): any => {
    if (chartConfig) {
      return chartConfig;
    }

    if (!chartData || !chartData.kline_data || chartData.kline_data.length === 0) {
      return {
        title: {
          text: 'No Data Available',
          left: 'center',
          top: 'center',
          textStyle: { color: '#999', fontSize: 16 }
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: 'No chart data to display',
            fontSize: 14,
            fill: '#999'
          }
        }
      };
    }

    // Prepare volume data with colors
    const volumeData = (chartData.volume_data || []).map((volume, index) => {
      if (index < (chartData.kline_data || []).length) {
        const [open, close] = (chartData.kline_data || [])[index];
        const color = close >= open ? '#ec000080' : '#00da3c80';
        return { value: volume, itemStyle: { color } };
      }
      return { value: volume, itemStyle: { color: '#1f77b4' } };
    });

    // Price color based on change
    const getPriceColor = (change: number) => {
      if (change > 0) return '#ec0000';
      if (change < 0) return '#00da3c';
      return '#666666';
    };

    const option = {
      title: {
        text: `${chartData.symbol} - ${chartData.company_name}`,
        left: 'left',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        },
        subtext: `$${(chartData.current_price || 0).toFixed(2)} ${(chartData.price_change || 0) >= 0 ? '+' : ''}${(chartData.price_change || 0).toFixed(2)} (${(chartData.price_change_percent || 0) >= 0 ? '+' : ''}${(chartData.price_change_percent || 0).toFixed(2)}%)`,
        subtextStyle: {
          fontSize: 12,
          color: getPriceColor(chartData.price_change || 0)
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#000'
        },
        formatter: function (params: any) {
          const data = params[0];
          if (data && data.seriesName === 'K线') {
            const [open, close, low, high] = data.data;
            return `
              <div style="font-weight: bold;">${data.name}</div>
              <div style="margin: 5px 0;">
                <span style="color: #666;">开盘:</span> $${open.toFixed(2)}<br/>
                <span style="color: #666;">最高:</span> $${high.toFixed(2)}<br/>
                <span style="color: #666;">最低:</span> $${low.toFixed(2)}<br/>
                <span style="color: #666;">收盘:</span> $${close.toFixed(2)}<br/>
                ${params[1] ? `<span style="color: #666;">成交量:</span> ${params[1].value.toLocaleString()}` : ''}
              </div>
            `;
          }
          return '';
        }
      },
      legend: {
        data: ['K线', '成交量'],
        top: 30
      },
      grid: [
        {
          left: '10%',
          right: '8%',
          top: '20%',
          height: '50%'
        },
        {
          left: '10%',
          right: '8%',
          top: '75%',
          height: '15%'
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: chartData.dates || [],
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax',
          axisPointer: { z: 100 }
        },
        {
          type: 'category',
          gridIndex: 1,
          data: chartData.dates || [],
          scale: true,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitArea: { show: true }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 80,
          end: 100
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '90%',
          start: 80,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: chartData.kline_data || [],
          itemStyle: {
            color: '#ec0000',     // 阳线颜色
            color0: '#00da3c',   // 阴线颜色
            borderColor: '#8A0000',
            borderColor0: '#008F28'
          },
          markPoint: {
            data: [
              {
                name: '最高值',
                type: 'max',
                valueDim: 'highest',
                label: { formatter: '最高: {c}' },
                itemStyle: { color: '#ec0000' }
              },
              {
                name: '最低值',
                type: 'min',
                valueDim: 'lowest',
                label: { formatter: '最低: {c}' },
                itemStyle: { color: '#00da3c' }
              }
            ]
          }
        },
        {
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumeData
        }
      ],
      toolbox: enableInteraction ? {
        feature: {
          dataZoom: {
            yAxisIndex: false
          },
          brush: {
            type: ['lineX', 'clear']
          },
          saveAsImage: enableDownload ? {
            name: `${chartData.symbol}_chart`,
            title: '下载图片'
          } : undefined
        }
      } : undefined,
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    };

    return option;
  }, [chartData, chartConfig, enableInteraction, enableDownload]);

  // Chart event handlers
  const onChartClick = useCallback((params: any) => {
    if (params.componentType === 'series') {
      console.log('Chart data point clicked:', params);
    }
  }, []);

  const onDataZoom = useCallback((params: any) => {
    console.log('Chart zoom event:', params);
  }, []);

  // Memoized chart option to prevent unnecessary re-renders
  const chartOption = useMemo(() => getEChartsOption(), [getEChartsOption]);

  return (
    <div className={`chart-renderer ${className}`}>
      <ReactECharts
        option={chartOption}
        style={{ height, width: '100%' }}
        opts={{ renderer: 'canvas' }}
        onEvents={{
          click: onChartClick,
          datazoom: onDataZoom
        }}
        notMerge={true}
        lazyUpdate={true}
      />
      
      {chartData && chartData.kline_data && chartData.kline_data.length > 0 && (
        <div className="chart-info mt-2 text-sm text-gray-600 border-t pt-2">
          <div className="flex justify-between items-center">
            <span>数据点: {(chartData.kline_data || []).length}</span>
            <span>最后更新: {new Date(chartData.last_update || new Date()).toLocaleString()}</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            提示: 可以拖拽缩放图表，使用滚轮缩放，双击重置
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartRenderer; 