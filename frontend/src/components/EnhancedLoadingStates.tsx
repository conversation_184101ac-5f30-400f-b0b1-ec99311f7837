'use client';

import React from 'react';
import './AnimationEnhancements.css';
import { DotIndicator } from './VisualIndicator';

// 打字指示器组件
export const TypingIndicator: React.FC<{
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ className = '', size = 'md' }) => {
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className={`typing-indicator ${sizeClasses[size]} ${className}`}>
      <div className="typing-dot"></div>
      <div className="typing-dot"></div>
      <div className="typing-dot"></div>
    </div>
  );
};

// 消息骨架屏组件
export const MessageSkeleton: React.FC<{
  type?: 'user' | 'assistant';
  showAvatar?: boolean;
  className?: string;
}> = ({ type = 'assistant', showAvatar = true, className = '' }) => {
  return (
    <div className={`flex ${type === 'user' ? 'justify-end' : 'justify-start'} mb-4 ${className}`}>
      <div className={`max-w-[85%] ${type === 'user' ? 'order-2' : 'order-1'}`}>
        {/* Avatar skeleton */}
        {showAvatar && type === 'assistant' && (
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 skeleton rounded-full mr-2"></div>
            <div className="w-16 h-4 skeleton rounded"></div>
          </div>
        )}
        
        {/* Message bubble skeleton */}
        <div className={`
          ${type === 'user' ? 'message-skeleton-user' : 'message-skeleton-assistant'}
          p-4 space-y-2
        `}>
          <div className="text-skeleton text-skeleton-long"></div>
          <div className="text-skeleton text-skeleton-medium"></div>
          <div className="text-skeleton text-skeleton-short"></div>
        </div>
      </div>
    </div>
  );
};

// 图表骨架屏组件
export const ChartSkeleton: React.FC<{
  className?: string;
  showTitle?: boolean;
}> = ({ className = '', showTitle = true }) => {
  return (
    <div className={`chart-skeleton ${className}`}>
      {showTitle && (
        <div className="absolute top-4 left-4 right-4">
          <div className="w-32 h-6 skeleton rounded mb-2"></div>
          <div className="w-48 h-4 skeleton rounded"></div>
        </div>
      )}
      
      {/* Chart placeholder icon */}
      <div className="absolute inset-0 flex items-center justify-center">
        <DotIndicator category="chart" size="xl" />
      </div>
      
      {/* Bottom legend skeleton */}
      <div className="absolute bottom-4 left-4 right-4 flex space-x-4">
        <div className="w-16 h-3 skeleton rounded"></div>
        <div className="w-20 h-3 skeleton rounded"></div>
        <div className="w-12 h-3 skeleton rounded"></div>
      </div>
    </div>
  );
};

// 多种加载指示器
export const LoadingSpinner: React.FC<{
  type?: 'dots' | 'pulse' | 'spin' | 'bounce';
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'gray';
  className?: string;
}> = ({ type = 'dots', size = 'md', color = 'primary', className = '' }) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const colorClasses = {
    primary: 'text-primary-500',
    secondary: 'text-secondary-500',
    gray: 'text-gray-500'
  };

  if (type === 'dots') {
    return (
      <div className={`spinner-dots ${sizeClasses[size]} ${colorClasses[color]} ${className}`}>
        <div className="spinner-dot"></div>
        <div className="spinner-dot"></div>
        <div className="spinner-dot"></div>
      </div>
    );
  }

  if (type === 'pulse') {
    return (
      <div className={`pulse-loader w-8 h-8 rounded-full bg-current ${className}`}></div>
    );
  }

  if (type === 'spin') {
    return (
      <div className={`animate-spin w-6 h-6 border-2 border-current border-t-transparent rounded-full ${className}`}></div>
    );
  }

  if (type === 'bounce') {
    return (
      <div className={`flex space-x-1 ${className}`}>
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
    );
  }

  return null;
};

// 进度条组件
export const ProgressBar: React.FC<{
  progress?: number; // 0-100
  indeterminate?: boolean;
  className?: string;
  showPercentage?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}> = ({ 
  progress = 0, 
  indeterminate = false, 
  className = '', 
  showPercentage = false,
  color = 'primary'
}) => {
  const colorClasses = {
    primary: 'from-primary-500 to-primary-600',
    secondary: 'from-secondary-500 to-secondary-600',
    success: 'from-success-500 to-success-600',
    warning: 'from-warning-500 to-warning-600',
    error: 'from-error-500 to-error-600'
  };

  return (
    <div className={`progress-bar ${className}`}>
      <div 
        className={`
          progress-fill bg-gradient-to-r ${colorClasses[color]}
          ${indeterminate ? 'progress-indeterminate' : ''}
        `}
        style={!indeterminate ? { width: `${Math.min(100, Math.max(0, progress))}%` } : undefined}
      ></div>
      
      {showPercentage && !indeterminate && (
        <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
};

// 内容加载状态组件
export const ContentLoader: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  skeleton?: React.ReactNode;
  className?: string;
}> = ({ loading, children, skeleton, className = '' }) => {
  if (loading) {
    return (
      <div className={`content-reveal ${className}`}>
        {skeleton || <MessageSkeleton />}
      </div>
    );
  }

  return (
    <div className={`content-reveal ${className}`}>
      {children}
    </div>
  );
};

// 列表加载状态组件
export const ListLoader: React.FC<{
  loading: boolean;
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  renderSkeleton?: () => React.ReactNode;
  skeletonCount?: number;
  className?: string;
  staggered?: boolean;
}> = ({ 
  loading, 
  items, 
  renderItem, 
  renderSkeleton, 
  skeletonCount = 3, 
  className = '',
  staggered = true
}) => {
  if (loading) {
    return (
      <div className={className}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <div key={index} className={staggered ? 'stagger-item' : ''}>
            {renderSkeleton ? renderSkeleton() : <MessageSkeleton />}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={className}>
      {items.map((item, index) => (
        <div key={index} className={staggered ? 'stagger-item' : ''}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  );
};

// 页面加载状态组件
export const PageLoader: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  className?: string;
  message?: string;
}> = ({ loading, children, className = '', message = '加载中...' }) => {
  if (loading) {
    return (
      <div className={`flex flex-col items-center justify-center min-h-[400px] ${className}`}>
        <LoadingSpinner type="pulse" size="lg" className="mb-4" />
        <p className="text-gray-600 text-sm">{message}</p>
      </div>
    );
  }

  return <div className={`page-enter-active ${className}`}>{children}</div>;
};

// 按钮加载状态组件
export const ButtonLoader: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}> = ({ loading, children, loadingText = '处理中...', className = '' }) => {
  return (
    <div className={`relative ${className}`}>
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-current bg-opacity-10 rounded">
          <LoadingSpinner type="spin" size="sm" className="mr-2" />
          <span className="text-sm">{loadingText}</span>
        </div>
      )}
      <div className={loading ? 'opacity-50' : ''}>
        {children}
      </div>
    </div>
  );
};

// 图片加载状态组件
export const ImageLoader: React.FC<{
  src: string;
  alt: string;
  className?: string;
  skeletonClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
}> = ({ src, alt, className = '', skeletonClassName = '', onLoad, onError }) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);

  const handleLoad = () => {
    setLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setLoading(false);
    setError(true);
    onError?.();
  };

  return (
    <div className="relative">
      {loading && (
        <div className={`skeleton ${skeletonClassName} absolute inset-0`}></div>
      )}
      
      {error ? (
        <div className={`flex items-center justify-center bg-gray-100 text-gray-400 ${className}`}>
          <span>图片加载失败</span>
        </div>
      ) : (
        <img
          src={src}
          alt={alt}
          className={`${className} ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          onLoad={handleLoad}
          onError={handleError}
        />
      )}
    </div>
  );
};

// 导出所有组件
export default {
  TypingIndicator,
  MessageSkeleton,
  ChartSkeleton,
  LoadingSpinner,
  ProgressBar,
  ContentLoader,
  ListLoader,
  PageLoader,
  ButtonLoader,
  ImageLoader
}; 