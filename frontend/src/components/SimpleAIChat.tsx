'use client';

import React, { useState, useRef, useEffect } from 'react';
import { streamChat } from '@/utils/api';
import { AvatarIndicator } from './VisualIndicator';

const SimpleAIChat: React.FC = () => {
  const [messages, setMessages] = useState<Array<{id: string, type: 'user' | 'assistant', content: string}>>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage = {
      id: `user_${Date.now()}`,
      type: 'user' as const,
      content: input.trim()
    };

    const assistantMessage = {
      id: `assistant_${Date.now()}`,
      type: 'assistant' as const,
      content: ''
    };

    setMessages(prev => [...prev, userMessage, assistantMessage]);
    setInput('');
    setIsLoading(true);

    let assistantContent = '';

    try {
      await streamChat(
        { message: userMessage.content },
        (data) => {
          console.log('📡 Stream data:', data);
          if (data.content) {
            assistantContent += data.content;
            setMessages(prev => prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: assistantContent }
                : msg
            ));
          }
        },
        (error) => {
          console.error('❌ Stream error:', error);
          setMessages(prev => prev.map(msg => 
            msg.id === assistantMessage.id 
              ? { ...msg, content: `错误: ${error}` }
              : msg
          ));
        }
      );
    } catch (error) {
      console.error('💥 Send error:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, content: `发送失败: ${error}` }
          : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '600px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <div style={{ padding: '16px', borderBottom: '1px solid #eee', backgroundColor: '#f9f9f9' }}>
        <h2>Simple AI Chat Test</h2>
      </div>
      
      <div style={{ flex: 1, padding: '16px', overflowY: 'auto' }}>
        {messages.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#666', marginTop: '50px' }}>
            <p>发送消息开始对话...</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              style={{
                marginBottom: '16px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: message.type === 'user' ? '#e3f2fd' : '#f5f5f5',
                alignSelf: message.type === 'user' ? 'flex-end' : 'flex-start',
                maxWidth: '80%'
              }}
            >
              <div style={{ fontWeight: 'bold', marginBottom: '4px', color: message.type === 'user' ? '#1976d2' : '#424242', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <AvatarIndicator type={message.type === 'user' ? 'user' : 'assistant'} size="xs" />
                {message.type === 'user' ? '用户' : 'AI助手'}
              </div>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {message.content || (message.type === 'assistant' && isLoading ? '正在思考...' : '')}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <div style={{ padding: '16px', borderTop: '1px solid #eee' }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="输入您的问题..."
            style={{
              flex: 1,
              minHeight: '40px',
              maxHeight: '100px',
              padding: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              resize: 'vertical'
            }}
            disabled={isLoading}
          />
          <button
            onClick={sendMessage}
            disabled={isLoading || !input.trim()}
            style={{
              padding: '8px 16px',
              backgroundColor: isLoading || !input.trim() ? '#ccc' : '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isLoading || !input.trim() ? 'not-allowed' : 'pointer'
            }}
          >
            {isLoading ? '发送中...' : '发送'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleAIChat; 