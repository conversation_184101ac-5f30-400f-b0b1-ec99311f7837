'use client';

import React, { useState, useEffect, useRef } from 'react';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import { StatusIndicator } from './VisualIndicator';

interface FinancialNewsProps {
  className?: string;
}

const FinancialNews: React.FC<FinancialNewsProps> = ({ className = '' }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [newsLoaded, setNewsLoaded] = useState(false);
  const [calendarLoaded, setCalendarLoaded] = useState(false);
  const [mounted, setMounted] = useState(false);
  const newsIframeRef = useRef<HTMLIFrameElement>(null);
  const calendarIframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Set mounted state and initialize timestamp on client-side only
    setMounted(true);
    setLastRefresh(new Date());
    
    // Reset loading state when component mounts
    setIsLoading(true);
    setHasError(false);

    // Set a timeout to hide loading spinner if iframes take too long
    const timeout = setTimeout(() => {
      setIsLoading(false);
    }, 10000); // 10 seconds timeout

    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    // Mark as loaded when both iframes are loaded
    if (newsLoaded && calendarLoaded) {
      setIsLoading(false);
    }
  }, [newsLoaded, calendarLoaded]);

  const handleNewsLoad = () => {
    setNewsLoaded(true);
    setHasError(false);
  };

  const handleCalendarLoad = () => {
    setCalendarLoaded(true);
    setHasError(false);
  };

  const handleNewsError = () => {
    setNewsLoaded(true);
    setHasError(true);
  };

  const handleCalendarError = () => {
    setCalendarLoaded(true);
    setHasError(true);
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setHasError(false);
    setNewsLoaded(false);
    setCalendarLoaded(false);
    setLastRefresh(new Date());

    // Reload iframes by changing their src
    if (newsIframeRef.current) {
      const baseSrc = 'https://www.jin10.com/example/jin10.com.html?fontSize=14px&theme=white&sound=0&audio=false&mute=1&volume=0&notification=0';
      newsIframeRef.current.src = '';
      setTimeout(() => {
        if (newsIframeRef.current) {
          newsIframeRef.current.src = baseSrc + '&_t=' + Date.now();
        }
      }, 100);
    }

    if (calendarIframeRef.current) {
      const baseSrc = 'https://rili-d.jin10.com/open.php?fontSize=14px&scrolling=yes&theme=primary&sound=0&audio=false&mute=1&volume=0&notification=0';
      calendarIframeRef.current.src = '';
      setTimeout(() => {
        if (calendarIframeRef.current) {
          calendarIframeRef.current.src = baseSrc + '&_t=' + Date.now();
        }
      }, 100);
    }
  };

  const formatTime = (date: Date | null) => {
    if (!date) return '加载中...';
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className={`financial-news ${className}`}>
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">📰 实时财经资讯</h2>
          <p className="text-gray-600">Jin10金融新闻和财经日历</p>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            最后更新: {mounted ? formatTime(lastRefresh) : '加载中...'}
          </span>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" color="white" />
            ) : (
                              <StatusIndicator status="streaming" size="sm" />
            )}
            <span>刷新</span>
          </button>
        </div>
      </div>

      {/* Error Display */}
      {hasError && !isLoading && (
        <div className="mb-6">
          <ErrorDisplay 
            error="部分新闻内容加载失败，请检查网络连接或稍后重试"
            onRetry={handleRefresh}
          />
        </div>
      )}

      {/* News Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Jin10 News Feed */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <span className="mr-2">📈</span>
              实时财经新闻
            </h3>
            <p className="text-sm text-gray-600 mt-1">Jin10 金融资讯实时推送</p>
          </div>
          
          <div className="relative">
            {isLoading && !newsLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                <div className="text-center">
                  <LoadingSpinner size="lg" />
                  <p className="text-gray-600 mt-2">正在加载新闻内容...</p>
                </div>
              </div>
            )}
            
            <iframe
              ref={newsIframeRef}
              src="https://www.jin10.com/example/jin10.com.html?fontSize=14px&theme=white&sound=0&audio=false&mute=1&volume=0&notification=0"
              frameBorder="0"
              scrolling="yes"
              className="w-full h-96 md:h-[500px] lg:h-[600px]"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
              allow="autoplay 'none'; fullscreen 'self'; microphone 'none'; camera 'none'; speaker 'none'; midi 'none'"
              referrerPolicy="no-referrer-when-downgrade"
              loading="lazy"
              onLoad={handleNewsLoad}
              onError={handleNewsError}
              title="Jin10 Financial News"
            />
          </div>
        </div>

        {/* Jin10 Calendar */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <span className="mr-2">📅</span>
              财经日历
            </h3>
            <p className="text-sm text-gray-600 mt-1">重要经济数据和事件提醒</p>
          </div>
          
          <div className="relative">
            {isLoading && !calendarLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                <div className="text-center">
                  <LoadingSpinner size="lg" />
                  <p className="text-gray-600 mt-2">正在加载日历内容...</p>
                </div>
              </div>
            )}
            
            <iframe
              ref={calendarIframeRef}
              src="https://rili-d.jin10.com/open.php?fontSize=14px&scrolling=yes&theme=primary&sound=0&audio=false&mute=1&volume=0&notification=0"
              frameBorder="0"
              scrolling="yes"
              className="w-full h-96 md:h-[500px] lg:h-[580px]"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
              allow="autoplay 'none'; fullscreen 'self'; microphone 'none'; camera 'none'; speaker 'none'; midi 'none'"
              referrerPolicy="no-referrer-when-downgrade"
              loading="lazy"
              onLoad={handleCalendarLoad}
              onError={handleCalendarError}
              title="Jin10 Financial Calendar"
            />
          </div>
        </div>
      </div>

      {/* Mobile Responsive Notice */}
      <div className="xl:hidden mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800">
          💡 提示: 在大屏幕设备上，新闻和日历将并排显示以获得更好的浏览体验。
        </p>
      </div>
    </div>
  );
};

export default FinancialNews; 