/* Enhanced Data Query Mode Styles */
/* Complementary styles for the DataQueryMode component */

.enhanced-data-query {
  /* Base container styles */
  --query-border-radius: var(--radius-lg);
  --query-spacing: var(--spacing-lg);
  --query-shadow: var(--shadow-md);
}

/* Search section enhancements */
.search-suggestions-dropdown {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-xl);
  z-index: 50;
}

.search-suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-suggestions-dropdown::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: var(--radius-sm);
}

.search-suggestions-dropdown::-webkit-scrollbar-thumb {
  background: var(--color-border-secondary);
  border-radius: var(--radius-sm);
}

.search-suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-focus);
}

/* Tab navigation enhancements */
.data-query-tabs {
  position: relative;
}

.data-query-tab {
  position: relative;
  transition: all var(--transition-normal);
}

.data-query-tab:hover::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-primary-400),
    transparent
  );
  border-radius: var(--radius-full);
  opacity: 0.5;
}

.data-query-tab.active::before {
  opacity: 1;
  background: linear-gradient(
    90deg,
    var(--color-primary-500),
    var(--color-primary-600),
    var(--color-primary-500)
  );
}

/* Card hover effects */
.query-result-card {
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.query-result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-primary-500),
    transparent
  );
  transition: left var(--transition-slow);
}

.query-result-card:hover::before {
  left: 100%;
}

.query-result-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Stats grid animations */
.stats-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.stats-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.stats-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(
    135deg,
    var(--color-primary-500),
    var(--color-secondary-500)
  );
  opacity: 0.1;
  border-radius: 0 0 0 20px;
  transition: all var(--transition-normal);
}

.stats-card:hover::after {
  width: 40px;
  height: 40px;
  opacity: 0.2;
}

/* News items styling */
.news-item {
  position: relative;
  padding-left: 1rem;
  transition: all var(--transition-fast);
}

.news-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-primary-500);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.news-item:hover::before {
  width: 6px;
  background: var(--color-primary-600);
}

.news-item:hover {
  background-color: var(--color-background-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  margin: calc(-1 * var(--spacing-sm));
}

/* Watchlist styling */
.watchlist-item {
  position: relative;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.watchlist-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(
    180deg,
    var(--color-primary-500),
    var(--color-secondary-500)
  );
  transition: width var(--transition-normal);
}

.watchlist-item:hover::before {
  width: 3px;
}

.watchlist-item.active::before {
  width: 4px;
  background: linear-gradient(
    180deg,
    var(--color-primary-600),
    var(--color-secondary-600)
  );
}

/* Chart container enhancements */
.chart-container {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-primary-300),
    transparent
  );
  z-index: 1;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--color-background-secondary),
    var(--color-background-tertiary),
    var(--color-background-secondary)
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Export menu styling */
.export-menu {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-xl);
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.export-menu button {
  transition: all var(--transition-fast);
  position: relative;
}

.export-menu button::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 60%;
  background: var(--color-primary-500);
  transition: width var(--transition-fast);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.export-menu button:hover::before {
  width: 3px;
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .data-query-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .data-query-tabs::-webkit-scrollbar {
    display: none;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .news-item:hover {
    margin: 0;
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .search-suggestions-dropdown {
    background-color: rgba(31, 41, 55, 0.95);
    border-color: var(--color-border-secondary);
  }
  
  .export-menu {
    background-color: rgba(31, 41, 55, 0.95);
  }
  
  .loading-shimmer {
    background: linear-gradient(
      90deg,
      var(--color-gray-800),
      var(--color-gray-700),
      var(--color-gray-800)
    );
    background-size: 200% 100%;
  }
}

/* Print styles */
@media print {
  .enhanced-data-query {
    background: white !important;
    color: black !important;
  }
  
  .data-query-tabs,
  .search-suggestions-dropdown,
  .export-menu {
    display: none !important;
  }
  
  .query-result-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .data-query-tab:hover::before,
  .data-query-tab.active::before {
    background: currentColor;
  }
  
  .query-result-card::before {
    background: currentColor;
  }
  
  .stats-card::after {
    display: none;
  }
} 