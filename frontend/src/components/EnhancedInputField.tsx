'use client';

import React, { useState, useRef, useEffect } from 'react';

interface EnhancedInputFieldProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  minRows?: number;
  maxRows?: number;
  showCharacterCount?: boolean;
  showSubmitHint?: boolean;
  autoFocus?: boolean;
  className?: string;
  validationState?: 'default' | 'success' | 'warning' | 'error';
  validationMessage?: string;
  suggestions?: string[];
  onSuggestionSelect?: (suggestion: string) => void;
}

const EnhancedInputField: React.FC<EnhancedInputFieldProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = "输入您的问题...",
  disabled = false,
  maxLength = 2000,
  minRows = 1,
  maxRows = 6,
  showCharacterCount = true,
  showSubmitHint = true,
  autoFocus = false,
  className = '',
  validationState = 'default',
  validationMessage,
  suggestions = [],
  onSuggestionSelect
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = 24; // Approximate line height
      const minHeight = lineHeight * minRows;
      const maxHeight = lineHeight * maxRows;
      
      textarea.style.height = `${Math.min(Math.max(scrollHeight, minHeight), maxHeight)}px`;
    }
  }, [value, minRows, maxRows]);

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Allow new line
        return;
      } else {
        e.preventDefault();
        if (value.trim() && !disabled) {
          onSubmit();
        }
      }
    }
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 150);
  };

  // Handle suggestion selection
  const handleSuggestionClick = (suggestion: string) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    } else {
      onChange(suggestion);
    }
    setShowSuggestions(false);
    textareaRef.current?.focus();
  };

  // Get validation styles
  const getValidationStyles = () => {
    switch (validationState) {
      case 'success':
        return {
          border: 'border-success-300 focus:border-success-500',
          ring: 'focus:ring-success-200',
          bg: 'bg-success-50/30'
        };
      case 'warning':
        return {
          border: 'border-warning-300 focus:border-warning-500',
          ring: 'focus:ring-warning-200',
          bg: 'bg-warning-50/30'
        };
      case 'error':
        return {
          border: 'border-error-300 focus:border-error-500',
          ring: 'focus:ring-error-200',
          bg: 'bg-error-50/30'
        };
      default:
        return {
          border: 'border-gray-300 focus:border-primary-500',
          ring: 'focus:ring-primary-200',
          bg: 'bg-white'
        };
    }
  };

  const validationStyles = getValidationStyles();
  const characterCount = value.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <div className={`relative ${className}`}>
      {/* Input container */}
      <div className={`
        relative rounded-xl border-2 transition-all duration-200
        ${validationStyles.border} ${validationStyles.bg}
        ${isFocused ? `ring-4 ${validationStyles.ring} shadow-lg` : 'shadow-sm'}
        ${disabled ? 'opacity-60 cursor-not-allowed' : 'hover:shadow-md'}
      `}>
        {/* Textarea */}
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoFocus={autoFocus}
          className={`
            w-full px-4 py-3 bg-transparent border-none outline-none resize-none
            text-gray-800 placeholder-gray-400
            font-medium leading-relaxed
            ${disabled ? 'cursor-not-allowed' : ''}
          `}
          style={{
            minHeight: `${24 * minRows}px`,
            maxHeight: `${24 * maxRows}px`
          }}
        />

        {/* Input decorations */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Focus glow effect */}
          {isFocused && (
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/5 to-secondary-500/5 animate-pulse" />
          )}
          
          {/* Typing indicator */}
          {isFocused && value.length > 0 && (
            <div className="absolute top-2 right-2">
              <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse" />
            </div>
          )}
        </div>
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 z-50">
          <div className="bg-white border border-gray-200 rounded-xl shadow-xl max-h-48 overflow-y-auto">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 px-2 py-1">建议问题</div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="
                    w-full text-left px-3 py-2 text-sm text-gray-700
                    hover:bg-primary-50 hover:text-primary-700
                    rounded-lg transition-colors
                  "
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Bottom info bar */}
      <div className="flex items-center justify-between mt-2 px-1">
        {/* Left side - hints and validation */}
        <div className="flex items-center space-x-3">
          {showSubmitHint && (
            <span className="text-xs text-gray-500">
              <kbd className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs">Enter</kbd>
              <span className="ml-1">发送</span>
              <span className="mx-2">•</span>
              <kbd className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs">Shift+Enter</kbd>
              <span className="ml-1">换行</span>
            </span>
          )}
          
          {validationMessage && (
            <span className={`text-xs font-medium ${
              validationState === 'error' ? 'text-error-600' :
              validationState === 'warning' ? 'text-warning-600' :
              validationState === 'success' ? 'text-success-600' :
              'text-gray-600'
            }`}>
              {validationMessage}
            </span>
          )}
        </div>

        {/* Right side - character count */}
        {showCharacterCount && (
          <div className="flex items-center space-x-2">
            <span className={`text-xs font-medium ${
              isOverLimit ? 'text-error-600' :
              isNearLimit ? 'text-warning-600' :
              'text-gray-500'
            }`}>
              {characterCount.toLocaleString()}/{maxLength.toLocaleString()}
            </span>
            
            {/* Character count indicator */}
            <div className="w-12 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-300 ${
                  isOverLimit ? 'bg-error-500' :
                  isNearLimit ? 'bg-warning-500' :
                  'bg-primary-500'
                }`}
                style={{ width: `${Math.min((characterCount / maxLength) * 100, 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedInputField; 