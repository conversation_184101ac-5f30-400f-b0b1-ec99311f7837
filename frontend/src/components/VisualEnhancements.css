/* ===== Visual Enhancements for Task 2.6 ===== */

/* Modern Card Patterns */
.enhanced-card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-200;
  @apply hover:shadow-xl transition-all duration-300;
  @apply backdrop-blur-sm;
}

.enhanced-card-elevated {
  @apply enhanced-card;
  @apply shadow-xl hover:shadow-2xl;
  @apply transform hover:-translate-y-1;
}

.enhanced-card-interactive {
  @apply enhanced-card;
  @apply cursor-pointer;
  @apply hover:border-primary-300 hover:bg-gradient-to-br hover:from-white hover:to-primary-50/30;
}

/* Glass Morphism Effects */
.glass-card {
  @apply bg-white/80 backdrop-blur-lg border border-white/20;
  @apply shadow-xl rounded-2xl;
}

.glass-overlay {
  @apply bg-gradient-to-br from-white/10 to-white/5;
  @apply backdrop-blur-md border border-white/20;
  @apply rounded-xl;
}

/* Enhanced Shadows */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 30px -5px rgba(0, 0, 0, 0.05);
}

.shadow-strong {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 50px -10px rgba(0, 0, 0, 0.1);
}

.shadow-colored-primary {
  box-shadow: 0 4px 25px -5px rgba(37, 99, 235, 0.2), 0 10px 30px -5px rgba(37, 99, 235, 0.1);
}

.shadow-colored-success {
  box-shadow: 0 4px 25px -5px rgba(34, 197, 94, 0.2), 0 10px 30px -5px rgba(34, 197, 94, 0.1);
}

.shadow-colored-error {
  box-shadow: 0 4px 25px -5px rgba(239, 68, 68, 0.2), 0 10px 30px -5px rgba(239, 68, 68, 0.1);
}

/* Enhanced Borders */
.border-gradient-primary {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #3b82f6, #8b5cf6) border-box;
}

.border-gradient-success {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #10b981, #06b6d4) border-box;
}

.border-animated {
  position: relative;
  border: 2px solid transparent;
}

.border-animated::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  animation: border-spin 3s linear infinite;
}

@keyframes border-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Message Bubble Enhancements */
.message-bubble-user {
  @apply bg-gradient-to-br from-primary-500 to-primary-600 text-white;
  @apply rounded-2xl rounded-br-md shadow-lg hover:shadow-xl;
  @apply transform hover:-translate-y-0.5 transition-all duration-200;
  @apply border border-primary-400/20;
}

.message-bubble-assistant {
  @apply bg-gradient-to-br from-white to-gray-50 text-gray-800;
  @apply rounded-2xl rounded-bl-md shadow-lg hover:shadow-xl;
  @apply border border-gray-200 hover:border-gray-300;
  @apply transform hover:-translate-y-0.5 transition-all duration-200;
  @apply backdrop-blur-sm;
}

.message-bubble-system {
  @apply bg-gradient-to-r from-warning-50 to-warning-100 text-warning-800;
  @apply rounded-xl border border-warning-200 shadow-sm;
  @apply backdrop-blur-sm;
}

.message-bubble-error {
  @apply bg-gradient-to-br from-error-50 to-error-100 text-error-800;
  @apply rounded-2xl rounded-bl-md shadow-lg border border-error-200;
  @apply ring-1 ring-error-100;
}

/* Input Field Enhancements */
.enhanced-input {
  @apply relative rounded-xl border-2 transition-all duration-200;
  @apply border-gray-300 focus-within:border-primary-500 bg-white;
  @apply focus-within:ring-4 focus-within:ring-primary-200 focus-within:shadow-lg;
  @apply hover:shadow-md;
}

.enhanced-input-success {
  @apply border-success-300 focus-within:border-success-500 bg-success-50/30;
  @apply focus-within:ring-success-200;
}

.enhanced-input-warning {
  @apply border-warning-300 focus-within:border-warning-500 bg-warning-50/30;
  @apply focus-within:ring-warning-200;
}

.enhanced-input-error {
  @apply border-error-300 focus-within:border-error-500 bg-error-50/30;
  @apply focus-within:ring-error-200;
}

/* Button Enhancements */
.btn-enhanced {
  @apply relative inline-flex items-center justify-center;
  @apply font-medium rounded-xl transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply transform active:scale-95 overflow-hidden;
}

.btn-primary {
  @apply btn-enhanced;
  @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white;
  @apply hover:from-primary-600 hover:to-primary-700 hover:shadow-lg;
  @apply focus:ring-primary-500 shadow-md hover:shadow-xl;
  @apply border border-primary-400/20;
}

.btn-secondary {
  @apply btn-enhanced;
  @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white;
  @apply hover:from-secondary-600 hover:to-secondary-700 hover:shadow-lg;
  @apply focus:ring-secondary-500 shadow-md hover:shadow-xl;
  @apply border border-secondary-400/20;
}

.btn-ghost {
  @apply btn-enhanced;
  @apply bg-transparent text-gray-700 hover:bg-gray-100;
  @apply focus:ring-gray-500 border border-transparent;
  @apply hover:border-gray-200;
}

.btn-outline {
  @apply btn-enhanced;
  @apply bg-transparent text-primary-600 border-2 border-primary-500;
  @apply hover:bg-primary-50 hover:border-primary-600;
  @apply focus:ring-primary-500;
}

/* Header Enhancements */
.enhanced-header {
  @apply relative overflow-hidden;
  @apply bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600;
}

.header-decoration {
  @apply absolute inset-0 overflow-hidden;
}

.header-pattern-1 {
  @apply absolute top-0 right-0 w-64 h-64 opacity-10;
}

.header-pattern-2 {
  @apply absolute bottom-0 left-0 w-32 h-32 opacity-5;
}

/* Chart Container Enhancements */
.chart-container {
  @apply relative bg-white rounded-2xl shadow-lg border border-gray-200;
  @apply hover:shadow-xl transition-all duration-300;
}

.chart-header {
  @apply relative p-6 pb-4 border-b border-gray-100;
}

.chart-content {
  @apply relative p-6;
}

.chart-wrapper {
  @apply relative rounded-xl overflow-hidden bg-gradient-to-br from-gray-50 to-white border border-gray-100;
}

/* Loading States */
.loading-skeleton {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200;
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots > div {
  @apply w-2 h-2 bg-current rounded-full;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots > div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots > div:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Hover Effects */
.hover-lift {
  @apply transition-transform duration-200;
  @apply hover:-translate-y-1;
}

.hover-glow {
  @apply transition-all duration-200;
  @apply hover:shadow-lg hover:shadow-primary-500/25;
}

.hover-scale {
  @apply transition-transform duration-200;
  @apply hover:scale-105;
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset;
}

/* Status Indicators */
.status-online {
  @apply w-3 h-3 bg-success-500 rounded-full;
  @apply shadow-lg shadow-success-500/50;
  animation: status-pulse 2s infinite;
}

.status-offline {
  @apply w-3 h-3 bg-error-500 rounded-full;
  @apply shadow-lg shadow-error-500/50;
}

.status-warning {
  @apply w-3 h-3 bg-warning-500 rounded-full;
  @apply shadow-lg shadow-warning-500/50;
  animation: status-pulse 2s infinite;
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Badge Enhancements */
.badge-enhanced {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  @apply border backdrop-blur-sm;
}

.badge-primary {
  @apply badge-enhanced;
  @apply bg-primary-100 text-primary-800 border-primary-200;
}

.badge-success {
  @apply badge-enhanced;
  @apply bg-success-100 text-success-800 border-success-200;
}

.badge-warning {
  @apply badge-enhanced;
  @apply bg-warning-100 text-warning-800 border-warning-200;
}

.badge-error {
  @apply badge-enhanced;
  @apply bg-error-100 text-error-800 border-error-200;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .enhanced-card {
    @apply rounded-xl;
  }
  
  .message-bubble-user,
  .message-bubble-assistant {
    @apply rounded-xl;
  }
  
  .enhanced-input {
    @apply rounded-lg;
  }
  
  .btn-enhanced {
    @apply rounded-lg;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .enhanced-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .glass-card {
    @apply bg-gray-800/80 border-gray-700/20;
  }
  
  .message-bubble-assistant {
    @apply bg-gradient-to-br from-gray-800 to-gray-900 text-gray-100;
    @apply border-gray-700 hover:border-gray-600;
  }
  
  .enhanced-input {
    @apply bg-gray-800 border-gray-600 text-gray-100;
    @apply focus-within:border-primary-400;
  }
}

/* Print Styles */
@media print {
  .enhanced-card,
  .message-bubble-user,
  .message-bubble-assistant {
    @apply shadow-none border border-gray-300;
  }
  
  .btn-enhanced {
    @apply shadow-none;
  }
} 