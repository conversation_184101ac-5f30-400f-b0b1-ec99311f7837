import React, { useState, useCallback, memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeHighlight from 'rehype-highlight';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import 'highlight.js/styles/github.css';
import 'katex/dist/katex.min.css';
import './MarkdownRenderer.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  enableCopy?: boolean;
  enableExport?: boolean;
  maxHeight?: string;
  showLineNumbers?: boolean;
  lazy?: boolean;
}

// Copy button component
const CopyButton: React.FC<{ content: string; className?: string }> = memo(({ content, className = '' }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, [content]);

  return (
    <button
      onClick={handleCopy}
      className={`inline-flex items-center px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded transition-colors ${className}`}
      title="复制内容"
    >
      {copied ? (
        <>
          <span className="text-green-600">✓</span>
          <span className="ml-1">已复制</span>
        </>
      ) : (
        <>
          <span>📋</span>
          <span className="ml-1">复制</span>
        </>
      )}
    </button>
  );
});

CopyButton.displayName = 'CopyButton';

// Export button component
const ExportButton: React.FC<{ content: string; filename?: string; className?: string }> = memo(({ 
  content, 
  filename = 'markdown-content', 
  className = '' 
}) => {
  const exportAsMarkdown = useCallback(() => {
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [content, filename]);

  const exportAsHTML = useCallback(() => {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>${filename}</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 40px 20px; }
  </style>
</head>
<body>
  <div class="markdown-content">${content}</div>
</body>
</html>`;
    
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [content, filename]);

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      <button
        onClick={exportAsMarkdown}
        className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 border border-blue-300 rounded transition-colors"
        title="导出为 Markdown"
      >
        <span>📄</span>
        <span className="ml-1">MD</span>
      </button>
      <button
        onClick={exportAsHTML}
        className="inline-flex items-center px-2 py-1 text-xs bg-green-100 hover:bg-green-200 border border-green-300 rounded transition-colors"
        title="导出为 HTML"
      >
        <span>🌐</span>
        <span className="ml-1">HTML</span>
      </button>
    </div>
  );
});

ExportButton.displayName = 'ExportButton';

// Enhanced code block component with copy functionality
const CodeBlock: React.FC<{ 
  children: React.ReactNode; 
  className?: string; 
  inline?: boolean;
  showLineNumbers?: boolean;
}> = memo(({ children, className, inline, showLineNumbers }) => {
  const [copied, setCopied] = useState(false);
  const match = /language-(\w+)/.exec(className || '');
  const language = match?.[1] || '';
  const codeContent = String(children).replace(/\n$/, '');

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(codeContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制代码失败:', error);
    }
  }, [codeContent]);

  // Inline code is now handled in the components mapping
  if (inline) {
    return <code className="inline-code">{children}</code>;
  }

  const lines = codeContent.split('\n');

  return (
    <div className="code-block-container">
      <div className="code-block-header">
        <span className="code-language">{language || 'text'}</span>
        <button
          onClick={handleCopy}
          className="code-copy-button"
          title="复制代码"
        >
          {copied ? '✓ 已复制' : '📋 复制'}
        </button>
      </div>
      <pre className="code-block">
        <code className={className}>
          {showLineNumbers ? (
            <table className="code-table">
              <tbody>
                {lines.map((line, index) => (
                  <tr key={index}>
                    <td className="line-number">{index + 1}</td>
                    <td className="line-content">{line}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            children
          )}
        </code>
      </pre>
    </div>
  );
});

CodeBlock.displayName = 'CodeBlock';

// Lazy loading wrapper for large content
const LazyMarkdown: React.FC<{ 
  children: React.ReactNode; 
  threshold?: number 
}> = memo(({ children, threshold = 10000 }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const content = String(children);

  if (content.length < threshold) {
    return <>{children}</>;
  }

  if (!isLoaded) {
    return (
      <div className="lazy-markdown-placeholder">
        <p className="text-gray-600 text-center py-8">
          内容较大，点击加载完整内容
        </p>
        <button
          onClick={() => setIsLoaded(true)}
          className="mx-auto block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          加载内容 ({Math.round(content.length / 1000)}KB)
        </button>
      </div>
    );
  }

  return <>{children}</>;
});

LazyMarkdown.displayName = 'LazyMarkdown';

const MarkdownRenderer: React.FC<MarkdownRendererProps> = memo(({ 
  content, 
  className = '',
  enableCopy = false,
  enableExport = false,
  maxHeight,
  showLineNumbers = false,
  lazy = false
}) => {
  // Memoize the markdown components to avoid recreation on every render
  const components = useMemo(() => ({
    // Enhanced code block with copy functionality
    code({ node, inline, className, children, ...props }: any) {
      // For inline code, return a simple code element to avoid nesting issues
      if (inline) {
        return <code className="inline-code" {...props}>{children}</code>;
      }
      
      // For block code, ensure it's rendered as a block element
      // The CodeBlock component returns a div structure which is block-level
      return (
        <CodeBlock
          className={className}
          inline={false}
          showLineNumbers={showLineNumbers}
          {...props}
        >
          {children}
        </CodeBlock>
      );
    },
    // Enhanced table with responsive wrapper
    table({ children }: any) {
      return (
        <div className="table-wrapper">
          <table className="markdown-table">{children}</table>
        </div>
      );
    },
    // Enhanced links with security
    a({ href, children }: any) {
      return (
        <a 
          href={href} 
          target="_blank" 
          rel="noopener noreferrer"
          className="markdown-link"
        >
          {children}
        </a>
      );
    },
    // Custom heading components with anchor links
    h1({ children }: any) {
      const id = String(children).toLowerCase().replace(/\s+/g, '-');
      return <h1 id={id} className="markdown-h1">{children}</h1>;
    },
    h2({ children }: any) {
      const id = String(children).toLowerCase().replace(/\s+/g, '-');
      return <h2 id={id} className="markdown-h2">{children}</h2>;
    },
    h3({ children }: any) {
      const id = String(children).toLowerCase().replace(/\s+/g, '-');
      return <h3 id={id} className="markdown-h3">{children}</h3>;
    },
    h4({ children }: any) {
      const id = String(children).toLowerCase().replace(/\s+/g, '-');
      return <h4 id={id} className="markdown-h4">{children}</h4>;
    },
    // Enhanced list components
    ul({ children }: any) {
      return <ul className="markdown-ul">{children}</ul>;
    },
    ol({ children }: any) {
      return <ol className="markdown-ol">{children}</ol>;
    },
    li({ children }: any) {
      return <li className="markdown-li">{children}</li>;
    },
    // Enhanced blockquote
    blockquote({ children }: any) {
      return <blockquote className="markdown-blockquote">{children}</blockquote>;
    },
    // Enhanced paragraph - handle cases where children might contain block elements
    p({ children, ...props }: any) {
      // Check if children contains block-level elements that shouldn't be in a paragraph
      const hasBlockElements = React.Children.toArray(children).some((child: any) => {
        // Check for direct HTML elements
        if (child?.type === 'div' || child?.type === 'pre' || child?.type === 'table') {
          return true;
        }
        
        // Check for CodeBlock component
        if (child?.type === CodeBlock) {
          return true;
        }
        
        // Check for class names that indicate block content
        if (child?.props?.className && (
          child.props.className.includes('code-block') ||
          child.props.className.includes('table-wrapper')
        )) {
          return true;
        }
        
        // Check for React components that render as block elements
        if (typeof child?.type === 'function' && 
            (child.type.displayName === 'CodeBlock' || child.type.name === 'CodeBlock')) {
          return true;
        }
        
        return false;
      });
      
      // If it contains block elements, render as a div instead
      if (hasBlockElements) {
        return <div className="markdown-p-block" {...props}>{children}</div>;
      }
      
      return <p className="markdown-p" {...props}>{children}</p>;
    },
    // Enhanced emphasis
    strong({ children }: any) {
      return <strong className="markdown-strong">{children}</strong>;
    },
    em({ children }: any) {
      return <em className="markdown-em">{children}</em>;
    },
    // Enhanced horizontal rule
    hr() {
      return <hr className="markdown-hr" />;
    },
    // Custom image component with lazy loading and base64 support
    img({ src, alt, ...props }: any) {
      const [imageError, setImageError] = useState(false);
      const [imageLoading, setImageLoading] = useState(true);

      const handleImageLoad = () => {
        setImageLoading(false);
      };

      const handleImageError = () => {
        setImageError(true);
        setImageLoading(false);
      };

      // Handle base64 images
      const isBase64 = src && src.startsWith('data:image/');
      
      if (imageError) {
        return (
          <div className="markdown-img-error">
            <span>🖼️ Image failed to load</span>
            {alt && <span className="img-alt-text">Alt: {alt}</span>}
          </div>
        );
      }

      return (
        <div className="markdown-img-container">
          {imageLoading && (
            <div className="markdown-img-loading">
              <span>Loading image...</span>
            </div>
          )}
          <img
            src={src}
            alt={alt || 'Agent generated image'}
            className={`markdown-img ${isBase64 ? 'base64-img' : ''} ${imageLoading ? 'loading' : ''}`}
            loading="lazy"
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{ display: imageLoading ? 'none' : 'block' }}
            {...props}
          />
        </div>
      );
    },
    // Handle pre elements to ensure they don't get nested in paragraphs
    pre({ children, ...props }: any) {
      return <pre className="markdown-pre" {...props}>{children}</pre>;
    }
  }), [showLineNumbers]);

  const rendererStyle = maxHeight ? { maxHeight, overflowY: 'auto' as const } : {};

  const markdownContent = (
    <div 
      className={`markdown-content ${className}`}
      style={rendererStyle}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeHighlight, rehypeKatex, rehypeRaw]}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  );

  return (
    <div className="markdown-renderer-container">
      {/* Action buttons */}
      {(enableCopy || enableExport) && (
        <div className="markdown-actions">
          {enableCopy && <CopyButton content={content} />}
          {enableExport && <ExportButton content={content} />}
        </div>
      )}

      {/* Markdown content */}
      {lazy ? (
        <LazyMarkdown>{markdownContent}</LazyMarkdown>
      ) : (
        markdownContent
      )}
    </div>
  );
});

MarkdownRenderer.displayName = 'MarkdownRenderer';

export default MarkdownRenderer; 