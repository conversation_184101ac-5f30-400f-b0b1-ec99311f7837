'use client';

import React from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import MessageActions from './MessageActions';
import MarkdownRenderer from './MarkdownRenderer';
import { StatusIndicator } from './VisualIndicator';
import { preprocessMessageContent } from '@/utils/messagePreprocessor';

interface EnhancedMessageBubbleProps {
  message: EnhancedMessage;
  conversationId?: string;
  isCurrentlyStreaming?: boolean;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
}

const EnhancedMessageBubble: React.FC<EnhancedMessageBubbleProps> = ({
  message,
  conversationId = 'default',
  isCurrentlyStreaming = false,
  onRetry,
  onEdit,
  onDelete,
  onReaction
}) => {
  const isUser = message.type === 'user';
  const isAssistant = message.type === 'assistant';
  const isSystem = message.type === 'system';
  const isError = message.status === 'failed';
  const isComplete = message.status === 'complete' || message.status === 'sent';

  // Enhanced message bubble styles based on type and status
  const getBubbleStyles = () => {
    if (isUser) {
      return {
        container: 'justify-end',
        bubble: `
          bg-gradient-to-br from-primary-500 to-primary-600 text-white
          rounded-2xl rounded-br-md shadow-lg hover:shadow-xl
          transform hover:-translate-y-0.5 transition-all duration-200
          border border-primary-400/20
        `,
        maxWidth: 'max-w-[80%] lg:max-w-[70%]'
      };
    }
    
    if (isSystem) {
      return {
        container: 'justify-center',
        bubble: `
          bg-gradient-to-r from-warning-50 to-warning-100 text-warning-800
          rounded-xl border border-warning-200 shadow-sm
          backdrop-blur-sm
        `,
        maxWidth: 'max-w-[90%]'
      };
    }
    
    if (isError) {
      return {
        container: 'justify-start',
        bubble: `
          bg-gradient-to-br from-error-50 to-error-100 text-error-800
          rounded-2xl rounded-bl-md shadow-lg border border-error-200
          ring-1 ring-error-100
        `,
        maxWidth: 'max-w-[85%] lg:max-w-[75%]'
      };
    }
    
    // Assistant message (default)
    return {
      container: 'justify-start',
      bubble: `
        bg-gradient-to-br from-white to-gray-50 text-gray-800
        rounded-2xl rounded-bl-md shadow-lg hover:shadow-xl
        border border-gray-200 hover:border-gray-300
        transform hover:-translate-y-0.5 transition-all duration-200
        backdrop-blur-sm
      `,
      maxWidth: 'max-w-[85%] lg:max-w-[75%]'
    };
  };

  const styles = getBubbleStyles();

  // Message type indicator with enhanced styling
  const getMessageTypeIndicator = () => {
    if (isUser) return null;
    
    const indicators = {
      analysis: { icon: '📊', label: '分析', color: 'bg-blue-100 text-blue-700 border-blue-200' },
      recommendation: { icon: '💡', label: '建议', color: 'bg-green-100 text-green-700 border-green-200' },
      warning: { icon: '⚠️', label: '警告', color: 'bg-yellow-100 text-yellow-700 border-yellow-200' },
      error: { icon: '❌', label: '错误', color: 'bg-red-100 text-red-700 border-red-200' },
      chart: { icon: '📈', label: '图表', color: 'bg-purple-100 text-purple-700 border-purple-200' },
      summary: { icon: '📋', label: '总结', color: 'bg-indigo-100 text-indigo-700 border-indigo-200' },
    };

    const type = message.metadata?.messageType || 'analysis';
    const indicator = indicators[type as keyof typeof indicators] || indicators.analysis;

    return (
      <div className={`
        inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium
        border ${indicator.color} mb-2 shadow-sm backdrop-blur-sm
      `}>
        <span>{indicator.icon}</span>
        <span>{indicator.label}</span>
      </div>
    );
  };

  // Agent badge with enhanced styling
  const getAgentBadge = () => {
    if (!message.metadata?.agent || isUser) return null;

    const agents = {
      researcher: { emoji: '🔍', name: '研究员', color: 'bg-blue-100 text-blue-800 border-blue-300' },
      analyst: { emoji: '📊', name: '分析师', color: 'bg-green-100 text-green-800 border-green-300' },
      advisor: { emoji: '🎯', name: '顾问', color: 'bg-orange-100 text-orange-800 border-orange-300' },
      programmer: { emoji: '💻', name: '程序员', color: 'bg-purple-100 text-purple-800 border-purple-300' },
    };

    const agent = agents[message.metadata.agent as keyof typeof agents] || agents.analyst;

    return (
      <div className={`
        inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium
        border ${agent.color} mb-2 shadow-sm hover:shadow-md transition-shadow
        backdrop-blur-sm
      `}>
        <span className="text-sm">{agent.emoji}</span>
        <span className="font-semibold">{agent.name}</span>
      </div>
    );
  };

  return (
    <div className={`flex ${styles.container} mb-6 px-4`}>
      <div className={`${styles.maxWidth} space-y-2`}>
        {/* Agent badge and type indicator */}
        <div className="flex flex-wrap gap-2">
          {getAgentBadge()}
          {getMessageTypeIndicator()}
        </div>

        {/* Message bubble */}
        <div className={`
          relative group ${styles.bubble}
          px-4 py-3 lg:px-5 lg:py-4
        `}>
          {/* Message content */}
          <div className="relative">
            {isAssistant ? (
              <MarkdownRenderer 
                content={preprocessMessageContent(message.content)}
                className="prose prose-sm max-w-none"
                enableCopy={isComplete}
                enableExport={isComplete && message.content.length > 500}
              />
            ) : (
              <div className="whitespace-pre-wrap break-words leading-relaxed">
                {message.content}
              </div>
            )}

            {/* Streaming indicator */}
            {isCurrentlyStreaming && !isComplete && (
              <span className="inline-block w-2 h-4 bg-current opacity-75 animate-pulse ml-1">|</span>
            )}
          </div>

          {/* Message actions - Enhanced positioning */}
          <MessageActions
            message={message}
            conversationId={conversationId}
            onEdit={onEdit}
            onDelete={onDelete}
            onReaction={onReaction}
            className="
              absolute -top-2 -right-2 opacity-0 group-hover:opacity-100
              transition-all duration-200 transform scale-90 group-hover:scale-100
            "
          />

          {/* Error display */}
          {message.error && (
            <div className="mt-3 p-3 bg-error-50 border border-error-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <span className="text-error-500 text-sm">⚠️</span>
                <div className="text-sm text-error-700">
                  <div className="font-medium">错误信息</div>
                  <div className="mt-1">{message.error}</div>
                </div>
              </div>
            </div>
          )}

          {/* Message metadata */}
          <div className="flex items-center justify-between mt-3 pt-2 border-t border-current/10">
            <div className="flex items-center space-x-2 text-xs opacity-70">
              {/* Status indicator */}
              <div className="flex items-center space-x-1">
                {message.status === 'complete' && <span className="text-success-500">✓</span>}
                {message.status === 'failed' && <span className="text-error-500">✗</span>}
                {message.status === 'sending' && <span className="animate-spin">⟳</span>}
                <span>
                  {new Date(message.timestamp).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>

              {/* Word count for long messages */}
              {message.content.length > 500 && (
                <span className="px-2 py-0.5 bg-current/10 rounded-full">
                  {Math.round(message.content.length / 100) / 10}K 字
                </span>
              )}
            </div>

            {/* Retry button */}
            {message.status === 'failed' && onRetry && (
              <button
                onClick={() => onRetry(message.id)}
                className="
                  flex items-center space-x-1 px-2 py-1 text-xs font-medium
                  bg-primary-100 text-primary-700 rounded-full
                  hover:bg-primary-200 transition-colors
                "
              >
                <StatusIndicator status="streaming" size="xs" />
                <span>重试</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedMessageBubble; 