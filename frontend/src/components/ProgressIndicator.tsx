'use client';

import React, { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface ProgressIndicatorProps {
  // Progress values
  progress?: number; // 0-100
  isIndeterminate?: boolean;
  
  // Status and text
  status?: 'idle' | 'loading' | 'success' | 'error' | 'warning';
  title?: string;
  description?: string;
  currentStep?: string;
  substepDescription?: string;
  estimatedTimeRemaining?: number;
  steps?: Array<{
    id: string;
    name: string;
    status: 'pending' | 'active' | 'completed' | 'error';
    description?: string;
    progress?: number;
    substeps?: string[];
  }>;
  
  // Appearance
  size?: 'sm' | 'md' | 'lg';
  variant?: 'bar' | 'circle' | 'steps' | 'minimal' | 'detailed';
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'gray';
  showPercentage?: boolean;
  showETA?: boolean;
  showCurrentStep?: boolean;
  showSubsteps?: boolean;
  animated?: boolean;
  
  // Behavior
  autoHide?: boolean;
  hideDelay?: number; // milliseconds
  canCancel?: boolean;
  canRetry?: boolean;
  
  // Events
  onComplete?: () => void;
  onError?: () => void;
  onCancel?: () => void;
  onRetry?: () => void;
  
  className?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress = 0,
  isIndeterminate = false,
  status = 'idle',
  title,
  description,
  currentStep,
  substepDescription,
  estimatedTimeRemaining,
  steps,
  size = 'md',
  variant = 'bar',
  color = 'blue',
  showPercentage = true,
  showETA = false,
  showCurrentStep = false,
  showSubsteps = false,
  animated = true,
  autoHide = false,
  hideDelay = 3000,
  canCancel = false,
  canRetry = false,
  onComplete,
  onError,
  onCancel,
  onRetry,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [startTime] = useState(Date.now());
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null);

  // Calculate ETA
  useEffect(() => {
    if (showETA && progress > 0 && progress < 100 && !isIndeterminate) {
      const elapsed = Date.now() - startTime;
      const estimated = (elapsed / progress) * (100 - progress);
      setEstimatedTime(estimated);
    } else if (estimatedTimeRemaining) {
      // Use provided ETA if available
      setEstimatedTime(estimatedTimeRemaining);
    } else {
      setEstimatedTime(null);
    }
  }, [progress, showETA, isIndeterminate, startTime, estimatedTimeRemaining]);

  // Auto-hide on completion
  useEffect(() => {
    if (autoHide && (status === 'success' || progress >= 100)) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, hideDelay);
      return () => clearTimeout(timer);
    }
  }, [autoHide, status, progress, hideDelay]);

  // Trigger callbacks
  useEffect(() => {
    if (progress >= 100 && onComplete) {
      onComplete();
    }
  }, [progress, onComplete]);

  useEffect(() => {
    if (status === 'error' && onError) {
      onError();
    }
  }, [status, onError]);

  if (!isVisible) return null;

  // Size configurations
  const sizeConfigs = {
    sm: {
      bar: 'h-1',
      circle: 'w-8 h-8',
      text: 'text-xs',
      title: 'text-sm font-medium',
      description: 'text-xs'
    },
    md: {
      bar: 'h-2',
      circle: 'w-12 h-12',
      text: 'text-sm',
      title: 'text-base font-medium',
      description: 'text-sm'
    },
    lg: {
      bar: 'h-3',
      circle: 'w-16 h-16',
      text: 'text-base',
      title: 'text-lg font-semibold',
      description: 'text-base'
    }
  };

  // Color configurations
  const colorConfigs = {
    blue: {
      progress: 'bg-blue-600',
      background: 'bg-blue-100',
      text: 'text-blue-700',
      border: 'border-blue-200'
    },
    green: {
      progress: 'bg-green-600',
      background: 'bg-green-100',
      text: 'text-green-700',
      border: 'border-green-200'
    },
    red: {
      progress: 'bg-red-600',
      background: 'bg-red-100',
      text: 'text-red-700',
      border: 'border-red-200'
    },
    yellow: {
      progress: 'bg-yellow-600',
      background: 'bg-yellow-100',
      text: 'text-yellow-700',
      border: 'border-yellow-200'
    },
    gray: {
      progress: 'bg-gray-600',
      background: 'bg-gray-100',
      text: 'text-gray-700',
      border: 'border-gray-200'
    }
  };

  const sizeConfig = sizeConfigs[size];
  const colorConfig = colorConfigs[color];

  const formatTime = (ms: number): string => {
    const seconds = Math.ceil(ms / 1000);
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) {
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}小时${remainingMinutes}分钟`;
  };

  const getStatusIcon = (): React.JSX.Element | null => {
    switch (status) {
      case 'loading':
        return <LoadingSpinner size="sm" color={color === 'blue' ? 'primary' : 'gray'} />;
      case 'success':
        return (
          <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Get progress text with current step info
  const getProgressText = (): string => {
    let text = showPercentage && !isIndeterminate ? `${Math.round(progress)}%` : '';
    if (showCurrentStep && currentStep) {
      text = currentStep + (text ? ` (${text})` : '');
    }
    return text;
  };

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon()}
        {title && <span className={sizeConfig.text}>{title}</span>}
        {showPercentage && !isIndeterminate && (
          <span className={`${sizeConfig.text} ${colorConfig.text}`}>
            {Math.round(progress)}%
          </span>
        )}
      </div>
    );
  }

  if (variant === 'circle') {
    const circumference = 2 * Math.PI * 16; // radius = 16
    const strokeDasharray = circumference;
    const strokeDashoffset = isIndeterminate ? 0 : circumference - (progress / 100) * circumference;

    return (
      <div className={`flex flex-col items-center space-y-2 ${className}`}>
        <div className="relative">
          <svg className={sizeConfig.circle} viewBox="0 0 40 40">
            {/* Background circle */}
            <circle
              cx="20"
              cy="20"
              r="16"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className={colorConfig.background.replace('bg-', 'text-')}
            />
            {/* Progress circle */}
            <circle
              cx="20"
              cy="20"
              r="16"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className={`${colorConfig.progress.replace('bg-', 'text-')} transform -rotate-90 origin-center ${animated ? 'transition-all duration-300' : ''} ${isIndeterminate ? 'animate-spin' : ''}`}
              style={isIndeterminate ? { strokeDasharray: '25 75' } : {}}
            />
          </svg>
          {showPercentage && !isIndeterminate && (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`${sizeConfig.text} font-medium ${colorConfig.text}`}>
                {Math.round(progress)}%
              </span>
            </div>
          )}
        </div>
        {title && <div className={sizeConfig.title}>{title}</div>}
        {description && <div className={`${sizeConfig.description} text-gray-500 text-center`}>{description}</div>}
      </div>
    );
  }

  if (variant === 'steps' && steps) {
    return (
      <div className={`space-y-4 ${className}`}>
        {title && <div className={sizeConfig.title}>{title}</div>}
        
        <div className="space-y-2">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center space-x-3">
              {/* Step indicator */}
              <div className="flex-shrink-0">
                {step.status === 'completed' ? (
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                ) : step.status === 'error' ? (
                  <div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                ) : step.status === 'active' ? (
                  <div className={`w-6 h-6 ${colorConfig.progress} rounded-full flex items-center justify-center relative`}>
                    {isIndeterminate ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      <span className="text-white text-xs font-medium">{index + 1}</span>
                    )}
                  </div>
                ) : (
                  <div className={`w-6 h-6 ${colorConfig.background} border-2 ${colorConfig.border} rounded-full flex items-center justify-center`}>
                    <span className={`${colorConfig.text} text-xs font-medium`}>{index + 1}</span>
                  </div>
                )}
              </div>

              {/* Step content */}
              <div className="flex-1 min-w-0">
                <div className={`${sizeConfig.text} font-medium ${step.status === 'active' ? colorConfig.text : step.status === 'completed' ? 'text-green-700' : step.status === 'error' ? 'text-red-700' : 'text-gray-600'}`}>
                  {step.name}
                </div>
                {step.description && (
                  <div className={`${sizeConfig.description} text-gray-500`}>
                    {step.description}
                  </div>
                )}
              </div>

              {/* Step status */}
              {step.status === 'active' && currentStep === step.id && (
                <div className="flex-shrink-0">
                  {isIndeterminate ? (
                    <LoadingSpinner size="sm" color="gray" />
                  ) : (
                    <span className={`${sizeConfig.text} ${colorConfig.text}`}>
                      {Math.round(progress)}%
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Enhanced detailed variant
  if (variant === 'detailed') {
    return (
      <div className={`space-y-4 bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        {/* Header with title and controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <div>
              {title && <div className={sizeConfig.title}>{title}</div>}
              {description && (
                <div className={`${sizeConfig.description} text-gray-500`}>
                  {description}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {canCancel && onCancel && (
              <button 
                onClick={onCancel}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
              >
                取消
              </button>
            )}
            {canRetry && onRetry && status === 'error' && (
              <button 
                onClick={onRetry}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                重试
              </button>
            )}
          </div>
        </div>

        {/* Current step and substep info */}
        {(currentStep || substepDescription) && (
          <div className="space-y-1">
            {currentStep && (
              <div className={`${sizeConfig.text} font-medium ${colorConfig.text}`}>
                当前步骤: {currentStep}
              </div>
            )}
            {substepDescription && (
              <div className={`${sizeConfig.description} text-gray-600`}>
                {substepDescription}
              </div>
            )}
          </div>
        )}

        {/* Progress bar with enhanced info */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className={`${sizeConfig.text} font-medium`}>
              {getProgressText()}
            </span>
            {estimatedTime && (
              <span className={`${sizeConfig.description} text-gray-500`}>
                预计剩余: {formatTime(estimatedTime)}
              </span>
            )}
          </div>
          
          <div className={`${colorConfig.background} rounded-full ${sizeConfig.bar} overflow-hidden`}>
            <div
              className={`${colorConfig.progress} h-full rounded-full ${animated ? 'transition-all duration-500 ease-out' : ''} ${isIndeterminate ? 'animate-pulse' : ''}`}
              style={{
                width: isIndeterminate ? '100%' : `${Math.min(Math.max(progress, 0), 100)}%`,
              }}
            />
          </div>
        </div>

        {/* Steps breakdown if available */}
        {showSubsteps && steps && steps.length > 0 && (
          <div className="space-y-2">
            <div className={`${sizeConfig.text} font-medium text-gray-700`}>步骤详情:</div>
            <div className="space-y-1">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      step.status === 'completed' ? 'bg-green-500' :
                      step.status === 'active' ? 'bg-blue-500' :
                      step.status === 'error' ? 'bg-red-500' : 'bg-gray-300'
                    }`} />
                    <span className={step.status === 'active' ? 'font-medium' : ''}>
                      {step.name}
                    </span>
                  </div>
                  {step.status === 'active' && step.progress !== undefined && (
                    <span className="text-gray-500">{step.progress}%</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default bar variant
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Header */}
      {(title || description || currentStep) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <div>
              {title && <div className={sizeConfig.title}>{title}</div>}
              {(description || currentStep) && (
                <div className={`${sizeConfig.description} text-gray-500`}>
                  {currentStep && showCurrentStep ? (
                    <span className="font-medium text-gray-700">当前: {currentStep}</span>
                  ) : (
                    description
                  )}
                </div>
              )}
              {substepDescription && (
                <div className={`${sizeConfig.description} text-gray-500 text-sm`}>
                  {substepDescription}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-3 text-right">
            {showPercentage && !isIndeterminate && (
              <span className={`${sizeConfig.text} font-medium ${colorConfig.text}`}>
                {Math.round(progress)}%
              </span>
            )}
            {estimatedTime && (
              <span className={`${sizeConfig.description} text-gray-500`}>
                剩余 {formatTime(estimatedTime)}
              </span>
            )}
            {/* Action buttons for errors */}
            {canRetry && onRetry && status === 'error' && (
              <button 
                onClick={onRetry}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                重试
              </button>
            )}
            {canCancel && onCancel && status === 'loading' && (
              <button 
                onClick={onCancel}
                className="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
              >
                取消
              </button>
            )}
          </div>
        </div>
      )}

      {/* Progress bar */}
      <div className={`${colorConfig.background} rounded-full ${sizeConfig.bar} overflow-hidden`}>
        <div
          className={`${colorConfig.progress} h-full rounded-full ${animated ? 'transition-all duration-300 ease-out' : ''} ${isIndeterminate ? 'animate-pulse' : ''}`}
          style={{
            width: isIndeterminate ? '100%' : `${Math.min(Math.max(progress, 0), 100)}%`,
            ...(isIndeterminate && {
              background: `linear-gradient(90deg, transparent, ${colorConfig.progress.includes('blue') ? '#2563eb' : colorConfig.progress.includes('green') ? '#16a34a' : colorConfig.progress.includes('red') ? '#dc2626' : colorConfig.progress.includes('yellow') ? '#ca8a04' : '#4b5563'}, transparent)`,
              animation: 'progress-slide 2s infinite'
            })
          }}
        />
      </div>

      {/* Additional info */}
      {(status === 'error' || status === 'warning') && description && (
        <div className={`text-xs ${status === 'error' ? 'text-red-600' : 'text-yellow-600'}`}>
          {description}
        </div>
      )}
    </div>
  );
};

export default ProgressIndicator;

// Enhanced CSS styles for modern progress indicators
const progressIndicatorStyles = `
  @keyframes progress-slide {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(400%);
    }
  }

  .progress-indicator-enhanced {
    position: relative;
    overflow: hidden;
  }

  .progress-indicator-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progress-slide 2s infinite;
  }

  .progress-step-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .progress-step-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .progress-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .progress-button-ripple {
    position: relative;
    overflow: hidden;
  }

  .progress-button-ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  .progress-button-ripple:active::after {
    width: 120px;
    height: 120px;
  }
`;

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = progressIndicatorStyles;
  if (!document.head.querySelector('style[data-progress-indicator]')) {
    styleElement.setAttribute('data-progress-indicator', 'true');
    document.head.appendChild(styleElement);
  }
} 