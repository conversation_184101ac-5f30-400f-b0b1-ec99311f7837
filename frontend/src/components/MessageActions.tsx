'use client';

import React, { useState, useRef, useEffect } from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import { messageStorage } from '@/utils/messageStorage';

interface MessageActionsProps {
  message: EnhancedMessage;
  conversationId?: string;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  className?: string;
}

interface EditModalProps {
  message: EnhancedMessage;
  isOpen: boolean;
  onClose: () => void;
  onSave: (newContent: string, reason?: string) => void;
}

// Edit message modal component
const EditMessageModal: React.FC<EditModalProps> = ({ message, isOpen, onClose, onSave }) => {
  const [editContent, setEditContent] = useState(message.content);
  const [editReason, setEditReason] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.setSelectionRange(editContent.length, editContent.length);
    }
  }, [isOpen, editContent.length]);

  const handleSave = () => {
    if (editContent.trim() && editContent.trim() !== message.content) {
      onSave(editContent.trim(), editReason.trim() || undefined);
    }
    onClose();
  };

  const handleCancel = () => {
    setEditContent(message.content);
    setEditReason('');
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="border-b border-gray-200 px-6 py-4">
          <h3 className="text-lg font-semibold text-gray-900">编辑消息</h3>
        </div>

        <div className="p-6 space-y-4">
          {/* Content editor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              消息内容
            </label>
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows={Math.max(3, Math.min(10, editContent.split('\n').length + 1))}
              placeholder="输入消息内容..."
            />
            <div className="mt-1 text-xs text-gray-500">
              字符数: {editContent.length} | Ctrl+Enter 保存, Esc 取消
            </div>
          </div>

          {/* Edit reason */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              编辑原因 (可选)
            </label>
            <input
              type="text"
              value={editReason}
              onChange={(e) => setEditReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="说明编辑原因..."
              maxLength={100}
            />
          </div>

          {/* Edit history preview */}
          {message.editHistory && message.editHistory.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                编辑历史 ({message.editHistory.length} 次)
              </label>
              <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-gray-50">
                {message.editHistory.slice(-3).map((edit, index) => (
                  <div key={index} className="text-xs text-gray-600 mb-2 last:mb-0">
                    <div className="font-medium">
                      {new Date(edit.editedAt).toLocaleString()}
                      {edit.reason && ` - ${edit.reason}`}
                    </div>
                    <div className="mt-1 italic">"{edit.content.slice(0, 100)}..."</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-200 px-6 py-4 flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={!editContent.trim() || editContent.trim() === message.content}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

// Confirmation modal for message deletion
const DeleteConfirmModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (permanent: boolean) => void;
}> = ({ isOpen, onClose, onConfirm }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 text-xl">🗑️</span>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">删除消息</h3>
              <p className="text-sm text-gray-600">请选择删除方式</p>
            </div>
          </div>

          <div className="space-y-3">
            <button
              onClick={() => {
                onConfirm(false);
                onClose();
              }}
              className="w-full p-3 text-left border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium text-gray-900">软删除</div>
              <div className="text-sm text-gray-600">隐藏消息但保留在历史记录中</div>
            </button>
            
            <button
              onClick={() => {
                onConfirm(true);
                onClose();
              }}
              className="w-full p-3 text-left border border-red-300 rounded-lg hover:bg-red-50 transition-colors"
            >
              <div className="font-medium text-red-900">永久删除</div>
              <div className="text-sm text-red-600">完全删除消息，无法恢复</div>
            </button>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main message actions component
const MessageActions: React.FC<MessageActionsProps> = ({
  message,
  conversationId = 'default',
  onEdit,
  onDelete,
  onReaction,
  className = '',
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [copied, setCopied] = useState(false);

  const storage = messageStorage;
  const preferences = storage.getPreferences();

  // Copy message content to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  // Handle message editing
  const handleEdit = (newContent: string, reason?: string) => {
    const success = storage.editMessage(message.id, newContent, reason, conversationId);
    if (success && onEdit) {
      onEdit(message.id, newContent);
    }
  };

  // Handle message deletion
  const handleDelete = (permanent: boolean) => {
    const success = storage.deleteMessage(message.id, conversationId, permanent);
    if (success && onDelete) {
      onDelete(message.id);
    }
  };

  // Handle message reactions
  const handleReaction = (reactionType: 'like' | 'dislike' | 'helpful' | 'unhelpful') => {
    if (onReaction) {
      onReaction(message.id, reactionType);
    }
  };

  // Don't show actions for system messages or if user has disabled features
  if (message.type === 'system') return null;

  const canEdit = preferences.enableMessageEditing && message.type === 'user';
  const canDelete = preferences.enableMessageDeletion;

  return (
    <>
      <div className={`message-actions group ${className}`}>
        {/* Actions trigger */}
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="opacity-0 group-hover:opacity-100 transition-opacity p-1 text-gray-400 hover:text-gray-600 rounded"
            title="消息操作"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </button>

          {/* Actions dropdown */}
          {showActions && (
            <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
              {/* Copy message */}
              <button
                onClick={() => {
                  handleCopy();
                  setShowActions(false);
                }}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
              >
                <span>{copied ? '✓' : '📋'}</span>
                <span>{copied ? '已复制' : '复制'}</span>
              </button>

              {/* Edit message */}
              {canEdit && (
                <button
                  onClick={() => {
                    setShowEditModal(true);
                    setShowActions(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <span>✏️</span>
                  <span>编辑</span>
                </button>
              )}

              {/* Delete message */}
              {canDelete && (
                <button
                  onClick={() => {
                    setShowDeleteModal(true);
                    setShowActions(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                >
                  <span>🗑️</span>
                  <span>删除</span>
                </button>
              )}

              {/* Message info */}
              <div className="border-t border-gray-200 mt-1 pt-1">
                <div className="px-3 py-2 text-xs text-gray-500">
                  <div>ID: {message.id.slice(-8)}</div>
                  <div>时间: {new Date(message.timestamp).toLocaleTimeString()}</div>
                  {message.version && message.version > 1 && (
                    <div>版本: {message.version}</div>
                  )}
                  {message.retryCount && message.retryCount > 0 && (
                    <div>重试: {message.retryCount}</div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick reaction buttons */}
        {message.type === 'assistant' && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1 ml-2">
            <button
              onClick={() => handleReaction('helpful')}
              className="p-1 text-gray-400 hover:text-green-600 transition-colors"
              title="有用"
            >
              👍
            </button>
            <button
              onClick={() => handleReaction('unhelpful')}
              className="p-1 text-gray-400 hover:text-red-600 transition-colors"
              title="无用"
            >
              👎
            </button>
          </div>
        )}
      </div>

      {/* Edit modal */}
      <EditMessageModal
        message={message}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSave={handleEdit}
      />

      {/* Delete confirmation modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
      />

      {/* Click outside to close actions */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(false)}
        />
      )}
    </>
  );
};

export default MessageActions; 