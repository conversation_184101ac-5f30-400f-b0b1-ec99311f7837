'use client';

import React from 'react';
import { IndicatorConfig, IndicatorProps, getIndicatorClasses, getIndicatorAriaAttributes } from '@/utils/visualIndicators';

// Visual Indicator Component
const VisualIndicator: React.FC<IndicatorProps> = ({
  config,
  showLabel = false,
  interactive = false,
  ariaLabel,
  className = '',
  onClick,
  ...props
}) => {
  const indicatorClasses = getIndicatorClasses(config);
  const ariaAttributes = getIndicatorAriaAttributes(config);
  const finalAriaLabel = ariaLabel || config.label;

  // Override aria-label if provided
  if (finalAriaLabel) {
    ariaAttributes['aria-label'] = finalAriaLabel;
  }

  // Add interactive classes if needed
  const finalClasses = [
    indicatorClasses,
    interactive ? 'cursor-pointer hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500' : '',
    className
  ].filter(Boolean).join(' ');

  const Element = interactive ? 'button' : 'span';

  return (
    <span className="inline-flex items-center">
      <Element
        className={finalClasses}
        onClick={interactive ? onClick : undefined}
        tabIndex={interactive ? 0 : undefined}
        {...ariaAttributes}
        {...props}
      />
      {showLabel && config.label && (
        <span className="indicator-label">{config.label}</span>
      )}
    </span>
  );
};

// Convenience component for dot indicators
export const DotIndicator: React.FC<Omit<IndicatorProps, 'config'> & { 
  category: string; 
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  priority?: 'critical' | 'high' | 'medium' | 'low';
  state?: 'loading' | 'streaming' | 'default';
}> = ({ category, size = 'md', priority, state, ...props }) => {
  const config: IndicatorConfig = {
    type: 'dot',
    category,
    size,
    priority,
    state
  };

  return <VisualIndicator config={config} {...props} />;
};

// Convenience component for badge indicators
export const BadgeIndicator: React.FC<Omit<IndicatorProps, 'config'> & { 
  category: string; 
  label: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}> = ({ category, label, size = 'md', ...props }) => {
  const config: IndicatorConfig = {
    type: 'badge',
    category,
    size,
    label
  };

  return <VisualIndicator config={config} showLabel={true} {...props} />;
};

// Convenience component for shape indicators
export const ShapeIndicator: React.FC<Omit<IndicatorProps, 'config'> & { 
  category: string; 
  shape: 'circle' | 'square' | 'diamond' | 'triangle';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}> = ({ category, shape, size = 'md', ...props }) => {
  const config: IndicatorConfig = {
    type: 'shape',
    category,
    size,
    shape
  };

  return <VisualIndicator config={config} {...props} />;
};

// Status indicator with predefined configurations
export const StatusIndicator: React.FC<{
  status: 'sending' | 'sent' | 'streaming' | 'complete' | 'failed' | 'cancelled' | 'timeout';
  showLabel?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
} & Omit<IndicatorProps, 'config'>> = ({ status, size = 'sm', ...props }) => {
  const statusConfigs: Record<string, IndicatorConfig> = {
    sending: { type: 'dot', category: 'navigation', size, state: 'loading', label: '发送中' },
    sent: { type: 'dot', category: 'success', size, label: '已发送' },
    streaming: { type: 'dot', category: 'analysis', size, state: 'streaming', label: '接收中' },
    complete: { type: 'dot', category: 'success', size, label: '完成' },
    failed: { type: 'dot', category: 'error', size, label: '失败' },
    cancelled: { type: 'dot', category: 'navigation', size, label: '已取消' },
    timeout: { type: 'dot', category: 'warning', size, label: '超时' },
  };

  const config = statusConfigs[status];
  return <VisualIndicator config={config} {...props} />;
};

// Priority indicator with predefined configurations
export const PriorityIndicator: React.FC<{
  priority: 'critical' | 'high' | 'medium' | 'low';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
} & Omit<IndicatorProps, 'config'>> = ({ priority, size = 'sm', ...props }) => {
  const priorityConfigs: Record<string, IndicatorConfig> = {
    critical: { type: 'dot', category: 'error', size, priority: 'critical' },
    high: { type: 'dot', category: 'warning', size, priority: 'high' },
    medium: { type: 'dot', category: 'success', size, priority: 'medium' },
    low: { type: 'dot', category: 'navigation', size, priority: 'low' },
  };

  const config = priorityConfigs[priority];
  return <VisualIndicator config={config} {...props} />;
};

// Avatar indicator component for user/assistant identification
export const AvatarIndicator: React.FC<{
  type: 'user' | 'assistant' | 'system';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showLabel?: boolean;
}> = ({ type, size = 'md', className = '', showLabel = false }) => {
  const avatarClasses = {
    user: 'vi-avatar-user',
    assistant: 'vi-avatar-assistant', 
    system: 'vi-avatar-system'
  };
  
  const labels = {
    user: '用户',
    assistant: 'AI',
    system: '系统'
  };
  
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl'
  };
  
  const baseClasses = `vi-avatar ${avatarClasses[type]} ${sizeClasses[size]} rounded-lg flex items-center justify-center font-semibold shadow-sm`;
  
  return (
    <div className={`inline-flex items-center ${showLabel ? 'space-x-2' : ''}`}>
      <div className={`${baseClasses} ${className}`} title={labels[type]} aria-label={labels[type]}>
        <span className="vi-avatar-text">{labels[type][0]}</span>
      </div>
      {showLabel && <span className="text-sm text-gray-600">{labels[type]}</span>}
    </div>
  );
};

export default VisualIndicator;
export type { IndicatorConfig, IndicatorProps }; 