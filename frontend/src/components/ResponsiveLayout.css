/* ===== Responsive Layout Manager Styles ===== */
.responsive-layout-manager {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* ===== Layout Controls ===== */
.layout-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.layout-controls-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-mode-controls,
.performance-controls,
.gesture-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.control-button {
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.control-button:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.control-button.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== Layout Content ===== */
.layout-content {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

/* ===== View Mode Styles ===== */
.responsive-layout-manager.view-mode-normal .layout-content {
  /* Normal view - default styling */
}

.responsive-layout-manager.view-mode-compact .layout-content {
  /* Compact view optimizations */
}

.responsive-layout-manager.view-mode-compact .message {
  margin-bottom: 8px;
}

.responsive-layout-manager.view-mode-compact .message-content {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.4;
}

.responsive-layout-manager.view-mode-compact .message-header {
  font-size: 12px;
  margin-bottom: 4px;
}

.responsive-layout-manager.view-mode-compact .agent-badge {
  transform: scale(0.9);
}

.responsive-layout-manager.view-mode-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
}

.responsive-layout-manager.view-mode-fullscreen .layout-controls {
  top: 16px;
  right: 16px;
}

/* ===== Mobile Optimizations ===== */
.responsive-layout-manager.mobile {
  /* Mobile-specific optimizations */
}

.responsive-layout-manager.mobile .layout-controls {
  display: none; /* Hide layout controls on mobile */
}

.responsive-layout-manager.mobile .message-content {
  max-width: 92%;
  padding: 12px;
  border-radius: 12px;
}

.responsive-layout-manager.mobile .message-text {
  font-size: 16px; /* Larger text for mobile readability */
  line-height: 1.5;
}

.responsive-layout-manager.mobile .message-header {
  font-size: 14px;
}

.responsive-layout-manager.mobile .agent-badge {
  transform: scale(0.85);
}

.responsive-layout-manager.mobile .input-form {
  padding: 12px 16px;
}

.responsive-layout-manager.mobile .input-container {
  gap: 8px;
}

.responsive-layout-manager.mobile .message-input {
  font-size: 16px; /* Prevent zoom on iOS */
  padding: 10px 12px;
  border-radius: 10px;
}

.responsive-layout-manager.mobile .send-button {
  width: 40px;
  height: 40px;
  font-size: 16px;
}

/* ===== Tablet Optimizations ===== */
.responsive-layout-manager.tablet {
  /* Tablet-specific optimizations */
}

.responsive-layout-manager.tablet .message-content {
  max-width: 88%;
}

.responsive-layout-manager.tablet .layout-controls {
  top: 12px;
  right: 12px;
}

/* ===== Virtual Scrolling Styles ===== */
.virtual-scrolling {
  position: relative;
  width: 100%;
}

.virtual-scroll-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.performance-badge {
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.scroll-to-bottom-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  padding: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  backdrop-filter: blur(5px);
}

.scroll-to-bottom-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.virtual-scroll-container {
  position: relative;
}

.virtual-items {
  width: 100%;
}

.virtual-message-item {
  width: 100%;
}

/* ===== Touch Gesture Styles ===== */
.responsive-layout-manager.touch-enabled {
  touch-action: pan-y; /* Allow vertical scrolling but enable gesture detection */
}

.touch-hints {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.hint-overlay {
  background: white;
  border-radius: 16px;
  padding: 24px;
  max-width: 300px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.hint-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  text-align: left;
}

.hint-item:last-child {
  margin-bottom: 0;
}

.hint-gesture {
  font-size: 24px;
  width: 32px;
  text-align: center;
}

.hint-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== Chart Responsiveness ===== */
.responsive-layout-manager .chart-container {
  width: 100%;
  overflow-x: auto;
}

.responsive-layout-manager.mobile .chart-container {
  margin: 8px 0;
  padding: 12px;
}

.responsive-layout-manager.mobile .chart-container .echarts-for-react {
  min-width: 300px; /* Minimum width for chart readability */
}

.responsive-layout-manager.tablet .chart-container .echarts-for-react {
  min-width: 400px;
}

/* Chart fullscreen mode */
.chart-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1500;
  background: white;
  display: flex;
  flex-direction: column;
}

.chart-fullscreen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.chart-fullscreen-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.chart-fullscreen-close {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.chart-fullscreen-close:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* ===== Message Actions Mobile Optimization ===== */
.responsive-layout-manager.mobile .message-actions {
  position: relative; /* Change from absolute positioning on mobile */
  margin-top: 8px;
  display: flex;
  justify-content: center;
  opacity: 1; /* Always visible on mobile */
}

.responsive-layout-manager.mobile .message-actions-buttons {
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 4px 8px;
}

.responsive-layout-manager.mobile .action-button {
  padding: 6px 8px;
  font-size: 12px;
  min-width: 28px;
  height: 28px;
}

/* ===== Markdown Content Mobile Optimization ===== */
.responsive-layout-manager.mobile .markdown-content {
  font-size: 16px;
}

.responsive-layout-manager.mobile .markdown-content table {
  font-size: 14px;
  display: block;
  overflow-x: auto;
  white-space: nowrap;
}

.responsive-layout-manager.mobile .markdown-content pre {
  overflow-x: auto;
  font-size: 14px;
}

.responsive-layout-manager.mobile .markdown-content h1 {
  font-size: 24px;
}

.responsive-layout-manager.mobile .markdown-content h2 {
  font-size: 20px;
}

.responsive-layout-manager.mobile .markdown-content h3 {
  font-size: 18px;
}

.responsive-layout-manager.mobile .markdown-content h4 {
  font-size: 16px;
}

/* ===== Adaptive Scrollbar ===== */
.responsive-layout-manager .virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.responsive-layout-manager.mobile .virtual-scroll-container::-webkit-scrollbar {
  width: 4px;
}

.responsive-layout-manager .virtual-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.responsive-layout-manager .virtual-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.responsive-layout-manager .virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ===== Performance Indicators ===== */
.responsive-layout-manager[data-virtual-scroll="true"] .virtual-scroll-indicator {
  display: flex;
}

.responsive-layout-manager[data-virtual-scroll="false"] .virtual-scroll-indicator {
  display: none;
}

/* ===== Orientation Support ===== */
@media (orientation: landscape) and (max-width: 768px) {
  .responsive-layout-manager.mobile .chat-header {
    padding: 12px 16px;
  }
  
  .responsive-layout-manager.mobile .header-content h1 {
    font-size: 20px;
  }
  
  .responsive-layout-manager.mobile .input-form {
    padding: 8px 16px;
  }
}

/* ===== Accessibility Enhancements ===== */
.responsive-layout-manager .control-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.responsive-layout-manager .scroll-to-bottom-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .responsive-layout-manager * {
    transition: none !important;
    animation: none !important;
  }
}

/* ===== High Contrast Mode ===== */
@media (prefers-contrast: high) {
  .responsive-layout-manager .control-button {
    border-width: 2px;
  }
  
  .responsive-layout-manager .performance-badge {
    border: 2px solid white;
  }
  
  .responsive-layout-manager .message-content {
    border-width: 2px;
  }
}

/* ===== Print Styles ===== */
@media print {
  .responsive-layout-manager .layout-controls,
  .responsive-layout-manager .virtual-scroll-indicator,
  .responsive-layout-manager .touch-hints {
    display: none !important;
  }
  
  .responsive-layout-manager .message-actions {
    display: none !important;
  }
} 