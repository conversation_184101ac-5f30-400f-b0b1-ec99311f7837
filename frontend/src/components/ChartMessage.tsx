'use client';

import React, { useState, useCallback } from 'react';
import <PERSON><PERSON>enderer from './ChartRenderer';
import MessageActions from './MessageActions';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import { BadgeIndicator, DotIndicator } from './VisualIndicator';

interface ChartMessageProps {
  message: EnhancedMessage;
  onEdit?: (messageId: string, newContent: string, reason?: string) => void;
  onDelete?: (messageId: string, permanent?: boolean) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  conversationId?: string;
  className?: string;
}

const ChartMessage: React.FC<ChartMessageProps> = ({
  message,
  onEdit,
  onDelete,
  onReaction,
  conversationId,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Parse chart data from message metadata
  const getChartData = useCallback(() => {
    // First check if chartData is directly on the message object (from streaming)
    if (message.chartData) {
      return message.chartData;
    }
    
    // Then check metadata for chartData
    if (message.metadata?.chartData) {
      return message.metadata.chartData;
    }
    
    // Try to parse from content if it's JSON
    try {
      if (message.rawContent?.startsWith('{') && message.rawContent?.endsWith('}')) {
        const parsed = JSON.parse(message.rawContent);
        if (parsed.symbol && parsed.kline_data) {
          return parsed;
        }
      }
    } catch (error) {
      console.debug('Could not parse chart data from content:', error);
    }
    
    // Debug logging to understand what data is available
    console.log('🔍 Chart data debug - message:', {
      type: message.type,
      hasChartData: !!message.chartData,
      hasMetadataChartData: !!message.metadata?.chartData,
      hasRawContent: !!message.rawContent,
      chartDataKeys: message.chartData ? Object.keys(message.chartData) : [],
      metadataKeys: message.metadata ? Object.keys(message.metadata) : [],
      rawContentPreview: message.rawContent?.substring(0, 100)
    });
    
    return null;
  }, [message]);

  const chartData = getChartData();

  // Chart interaction handlers
  const handleChartDownload = useCallback(() => {
    if (chartData) {
      const filename = `${chartData.symbol}_chart_${new Date().toISOString().split('T')[0]}.png`;
      console.log('Downloading chart as:', filename);
    }
  }, [chartData]);

  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleCopyChartData = useCallback(async () => {
    if (chartData) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(chartData, null, 2));
        console.log('Chart data copied to clipboard');
      } catch (error) {
        console.error('Failed to copy chart data:', error);
      }
    }
  }, [chartData]);

  if (!chartData) {
    return (
      <div className={`chart-message error-state ${className}`}>
        <div className="message-header flex items-center justify-between">
          <div className="message-info">
            <BadgeIndicator category="chart" label="图表" />
            <span className="agent-name">图表生成器</span>
            <span className="timestamp">{new Date(message.timestamp).toLocaleTimeString()}</span>
          </div>
        </div>
        <div className="chart-error bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-red-600 font-medium flex items-center justify-center gap-2">
            <DotIndicator category="error" size="sm" />
            无法显示图表
          </div>
          <div className="text-red-500 text-sm mt-1">图表数据格式不正确或缺失</div>
          {message.rawContent && (
            <details className="mt-2 text-left">
              <summary className="text-xs text-gray-500 cursor-pointer">查看原始数据</summary>
              <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                {message.rawContent}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`chart-message ${className} ${isFullscreen ? 'fullscreen' : ''}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Message Header */}
      <div className="message-header flex items-center justify-between mb-3">
        <div className="message-info flex items-center gap-2">
          <BadgeIndicator category="chart" label="图表" />
          <span className="agent-name text-sm text-gray-600">图表生成器</span>
          <span className="timestamp text-xs text-gray-400">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
        </div>
        
        <div className="chart-controls flex items-center gap-2">
          <button
            onClick={handleFullscreenToggle}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title={isFullscreen ? "退出全屏" : "全屏显示"}
          >
            <DotIndicator category="navigation" size="sm" />
          </button>
          <button
            onClick={handleChartDownload}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title="下载图表"
          >
            <DotIndicator category="success" size="sm" />
          </button>
          <button
            onClick={handleCopyChartData}
            className="text-gray-500 hover:text-gray-700 p-1 rounded transition-colors"
            title="复制图表数据"
          >
            <DotIndicator category="data" size="sm" />
          </button>
        </div>
      </div>

      {/* Chart Title */}
      {message.displayContent && (
        <div className="chart-title mb-3">
          <h4 className="text-lg font-medium text-gray-800">{message.displayContent}</h4>
        </div>
      )}

      {/* Chart Renderer */}
      <div className="chart-container bg-white rounded-lg border shadow-sm">
        <ChartRenderer
          chartData={chartData}
          height={isFullscreen ? '80vh' : '500px'}
          enableInteraction={true}
          enableDownload={true}
          className="rounded-lg"
        />
      </div>

      {/* Chart Information */}
      <div className="chart-metadata mt-3 p-3 bg-gray-50 rounded-lg text-sm">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <span className="font-medium text-gray-700">股票代码:</span>
            <span className="ml-1 text-gray-900">{chartData.symbol}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">公司名称:</span>
            <span className="ml-1 text-gray-900">{chartData.company_name}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">当前价格:</span>
            <span className="ml-1 text-gray-900">${chartData.current_price?.toFixed(2)}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">涨跌幅:</span>
            <span className={`ml-1 ${chartData.price_change >= 0 ? 'text-red-600' : 'text-green-600'}`}>
              {chartData.price_change >= 0 ? '+' : ''}{chartData.price_change?.toFixed(2)} 
              ({chartData.price_change_percent >= 0 ? '+' : ''}{chartData.price_change_percent?.toFixed(2)}%)
            </span>
          </div>
        </div>
        
        <div className="mt-2 pt-2 border-t border-gray-200">
          <span className="font-medium text-gray-700">数据范围:</span>
          <span className="ml-1 text-gray-600">
            {chartData.dates?.length} 个交易日
            {chartData.dates && chartData.dates.length > 0 && (
              <span className="ml-2">
                ({chartData.dates[0]} 至 {chartData.dates[chartData.dates.length - 1]})
              </span>
            )}
          </span>
        </div>
      </div>

      {/* Message Actions */}
      {showActions && (
        <MessageActions
          message={message}
          onEdit={onEdit}
          onDelete={onDelete}
          onReaction={onReaction}
          conversationId={conversationId}
        />
      )}

      {/* Fullscreen overlay */}
      {isFullscreen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full h-full max-w-6xl max-h-full overflow-auto">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-medium">
                {chartData.symbol} - {chartData.company_name}
              </h3>
              <button
                onClick={handleFullscreenToggle}
                className="text-gray-500 hover:text-gray-700 p-2 rounded transition-colors"
              >
                ✕
              </button>
            </div>
            <div className="p-4">
              <ChartRenderer
                chartData={chartData}
                height="calc(100vh - 200px)"
                enableInteraction={true}
                enableDownload={true}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartMessage; 