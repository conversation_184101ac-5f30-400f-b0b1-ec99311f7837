/* ===== 流式 Markdown 渲染增强样式 ===== */

/* 流式 Markdown 渲染样式 */
.streaming-markdown {
  position: relative;
}

.streaming-markdown .markdown-actions {
  position: absolute;
  top: -8px;
  right: -8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.message-content:hover .streaming-markdown .markdown-actions {
  opacity: 1;
}

/* 图表描述样式 */
.chart-description .markdown-content {
  font-size: 0.9em;
  color: #4a5568;
}

.chart-description .markdown-content .markdown-p {
  margin: 0.5em 0;
}

/* 增强的消息类型指示器样式 */
.message-type-indicator {
  transition: all 0.2s ease;
}

.message-type-indicator:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 代理徽章悬停效果 */
.agent-badge {
  transition: all 0.2s ease;
}

.agent-badge:hover {
  transform: scale(1.05);
}

/* 数学公式在消息中的样式 */
.message-text .katex {
  font-size: 0.95em;
}

.message-text .katex-display {
  margin: 1em 0;
  text-align: center;
  overflow-x: auto;
}

/* 代码块在消息中的增强样式 */
.message-text .code-block-container {
  margin: 1em 0;
  border-radius: 8px;
  overflow: hidden;
}

.message-text .code-block-header {
  background: rgba(0, 0, 0, 0.05);
  font-size: 0.8em;
  padding: 6px 12px;
}

.message-text .code-copy-button {
  font-size: 0.7em;
  padding: 2px 6px;
}

/* 表格在消息中的响应式样式 */
.message-text .table-wrapper {
  margin: 1em 0;
  max-height: 400px;
  overflow-y: auto;
}

.message-text .markdown-table {
  font-size: 0.85em;
}

.message-text .markdown-table th,
.message-text .markdown-table td {
  padding: 0.5em 0.75em;
}

/* 引用块在消息中的样式 */
.message-text .markdown-blockquote {
  margin: 0.8em 0;
  padding: 0.5em 1em;
  font-size: 0.95em;
  border-left-width: 3px;
}

/* 链接在消息中的样式 */
.message-text .markdown-link {
  word-break: break-word;
}

/* 列表在消息中的样式 */
.message-text .markdown-ul,
.message-text .markdown-ol {
  margin: 0.8em 0;
  padding-left: 1.5em;
}

.message-text .markdown-li {
  margin: 0.2em 0;
}

/* 懒加载占位符在消息中的样式 */
.message-text .lazy-markdown-placeholder {
  margin: 1em 0;
  padding: 1.5em;
  font-size: 0.9em;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .streaming-markdown .markdown-actions {
    background: #1f2937;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .chart-description .markdown-content {
    color: #9ca3af;
  }
} 