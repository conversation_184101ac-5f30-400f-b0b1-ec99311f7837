'use client';

import React from 'react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { AppMode } from '@/types';

interface BreadcrumbsProps {
  currentMode: AppMode;
  customPath?: string[];
}

const modeLabels: Record<AppMode, string> = {
  'home': '首页',
  'ai-agent': 'AI 助手',
  'data-query': '数据查询',
  'factor-management': '因子管理',
  'ml-models': 'ML 模型',
  'stock-scoring': '股票评分',
  'divergence-scanner': '背离扫描',
  'markdown-test': 'Markdown 测试',
  'debug': '调试面板',
};

const modeCategories: Record<AppMode, string | null> = {
  'home': null,
  'ai-agent': '主要功能',
  'data-query': '主要功能',
  'factor-management': '分析工具',
  'ml-models': '分析工具',
  'stock-scoring': '分析工具',
  'divergence-scanner': '分析工具',
  'markdown-test': '开发工具',
  'debug': '开发工具',
};

export default function Breadcrumbs({ currentMode, customPath }: BreadcrumbsProps) {
  const category = modeCategories[currentMode];
  const currentLabel = modeLabels[currentMode];

  // If custom path is provided, use it instead of default mode-based breadcrumbs
  if (customPath && customPath.length > 0) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">首页</BreadcrumbLink>
          </BreadcrumbItem>
          {customPath.map((item, index) => (
            <React.Fragment key={index}>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                {index === customPath.length - 1 ? (
                  <BreadcrumbPage>{item}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href="#">{item}</BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Default mode-based breadcrumbs
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/">首页</BreadcrumbLink>
        </BreadcrumbItem>
        
        {/* Show category if not on home page and category exists */}
        {currentMode !== 'home' && category && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="#">{category}</BreadcrumbLink>
            </BreadcrumbItem>
          </>
        )}
        
        {/* Show current page if not on home */}
        {currentMode !== 'home' && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{currentLabel}</BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
} 