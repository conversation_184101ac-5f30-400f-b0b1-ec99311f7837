'use client';

import React, { useState } from 'react';

interface RefreshButtonProps {
  onRefresh: () => void;
  lastHydrationTime?: number;
  className?: string;
  showConfirmation?: boolean;
}

const RefreshButton: React.FC<RefreshButtonProps> = ({
  onRefresh,
  lastHydrationTime = 0,
  className = '',
  showConfirmation = true,
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleRefreshClick = () => {
    if (showConfirmation) {
      setShowConfirmDialog(true);
    } else {
      onRefresh();
    }
  };

  const handleConfirm = () => {
    setShowConfirmDialog(false);
    onRefresh();
  };

  const handleCancel = () => {
    setShowConfirmDialog(false);
  };

  const formatTime = (timestamp: number) => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <>
      <button
        onClick={handleRefreshClick}
        className={`
          inline-flex items-center space-x-2 px-3 py-1.5
          text-sm font-medium text-gray-600 hover:text-gray-800
          bg-gray-100 hover:bg-gray-200
          border border-gray-300 rounded-md
          transition-all duration-200
          hover:shadow-sm
          ${className}
        `}
        title={`刷新对话 (上次加载: ${formatTime(lastHydrationTime)})`}
      >
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        <span>从服务器刷新</span>
      </button>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              确认刷新对话
            </h3>
            <p className="text-gray-600 mb-6">
              此操作将清除本地缓存的对话内容，并从服务器重新加载。当前显示的消息将被清空。
            </p>
            {lastHydrationTime > 0 && (
              <p className="text-sm text-gray-500 mb-4">
                上次加载时间: {formatTime(lastHydrationTime)}
              </p>
            )}
            <div className="flex space-x-3 justify-end">
              <button
                onClick={handleCancel}
                className="
                  px-4 py-2 text-sm font-medium text-gray-700
                  bg-gray-100 hover:bg-gray-200 
                  border border-gray-300 rounded-md
                  transition-colors duration-200
                "
              >
                取消
              </button>
              <button
                onClick={handleConfirm}
                className="
                  px-4 py-2 text-sm font-medium text-white
                  bg-blue-500 hover:bg-blue-600
                  border border-blue-500 rounded-md
                  transition-colors duration-200
                "
              >
                确认刷新
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RefreshButton; 