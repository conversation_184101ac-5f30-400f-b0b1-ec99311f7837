/* ===== Animation and Interaction Design Enhancements ===== */
/* Task 2.7: Animation and Interaction Design Implementation */

/* ===== Smooth Animations and Micro-interactions ===== */

/* Page transition animations */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Message appear animation */
.message-appear {
  animation: messageSlideIn 0.4s ease-out;
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Typing indicator animation */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.4;
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.4;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Button click ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Ripple animation keyframes */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Smooth hover transitions */
.smooth-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* ===== Loading States and Skeleton Screens ===== */

/* Skeleton loading animation */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Message skeleton */
.message-skeleton {
  @apply skeleton rounded-lg;
  height: 60px;
  margin-bottom: 16px;
}

.message-skeleton-user {
  @apply message-skeleton ml-auto;
  width: 70%;
}

.message-skeleton-assistant {
  @apply message-skeleton mr-auto;
  width: 85%;
}

/* Text skeleton lines */
.text-skeleton {
  @apply skeleton rounded;
  height: 16px;
  margin-bottom: 8px;
}

.text-skeleton-short { width: 60%; }
.text-skeleton-medium { width: 80%; }
.text-skeleton-long { width: 95%; }

/* Chart skeleton */
.chart-skeleton {
  @apply skeleton rounded-xl;
  height: 300px;
  position: relative;
}

.chart-skeleton::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #8B5CF6;
  opacity: 0.3;
}

/* Loading spinner variations */
.spinner-dots {
  display: inline-flex;
  gap: 4px;
}

.spinner-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  animation: spinnerPulse 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) { animation-delay: -0.16s; }
.spinner-dot:nth-child(2) { animation-delay: -0.08s; }
.spinner-dot:nth-child(3) { animation-delay: 0s; }

@keyframes spinnerPulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse loading effect */
.pulse-loader {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* ===== Modern Scrollbar Styling ===== */

/* Custom scrollbar for webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Thin scrollbar variant */
.thin-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}

/* Smooth scrolling behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* ===== Hover and Focus Animations ===== */

/* Enhanced button hover effects */
.btn-hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-hover-lift:active {
  transform: translateY(-1px);
  transition-duration: 0.1s;
}

/* Glow effect on hover */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Scale effect on hover */
.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.98);
}

/* Focus ring animations */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

/* Input field focus animations */
.input-focus-slide {
  position: relative;
}

.input-focus-slide::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.input-focus-slide:focus-within::after {
  width: 100%;
}

/* ===== UI State Transitions ===== */

/* Fade in/out transitions */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-out;
}

/* Slide transitions */
.slide-up-enter {
  transform: translateY(100%);
}

.slide-up-enter-active {
  transform: translateY(0);
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-exit {
  transform: translateY(0);
}

.slide-up-exit-active {
  transform: translateY(100%);
  transition: transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modal backdrop animation */
.modal-backdrop {
  background: rgba(0, 0, 0, 0);
  transition: background 0.3s ease;
}

.modal-backdrop.show {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* ===== Progressive Loading Animations ===== */

/* Chart loading animation */
.chart-loading {
  position: relative;
  overflow: hidden;
}

.chart-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: chartShimmer 2s infinite;
}

@keyframes chartShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Content reveal animation */
.content-reveal {
  animation: contentFadeIn 0.6s ease-out;
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for lists */
.stagger-item {
  animation: staggerFadeIn 0.5s ease-out;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== Interactive Element Enhancements ===== */

/* Card hover effects */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.interactive-card:active {
  transform: translateY(-2px);
  transition-duration: 0.1s;
}

/* Toggle switch animation */
.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  background: #e5e7eb;
  border-radius: 12px;
  transition: background 0.3s ease;
  cursor: pointer;
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active {
  background: #3b82f6;
}

.toggle-switch.active::after {
  transform: translateX(24px);
}

/* Progress bar animation */
.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-indeterminate {
  width: 30%;
  animation: progressSlide 2s infinite;
}

@keyframes progressSlide {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* ===== Accessibility and Reduced Motion ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .skeleton {
    animation: none;
    background: #f0f0f0;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .hover-glow:hover {
    box-shadow: 0 0 0 2px currentColor;
  }
  
  .focus-ring:focus {
    box-shadow: 0 0 0 3px currentColor;
  }
}

/* ===== Dark Mode Animations ===== */
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* ===== Performance Optimizations ===== */

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimize repaints */
.optimize-repaint {
  contain: layout style paint;
}

/* ===== Utility Classes ===== */

/* Animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-500 { animation-delay: 0.5s; }

/* Animation durations */
.duration-fast { animation-duration: 0.15s; }
.duration-normal { animation-duration: 0.3s; }
.duration-slow { animation-duration: 0.5s; }

/* Easing functions */
.ease-smooth { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
.ease-bounce { transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55); }
.ease-elastic { transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275); } 