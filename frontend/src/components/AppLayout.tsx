'use client';

import React from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import AppSidebar from './AppSidebar';
import AppHeader from './AppHeader';
import { AppMode } from '@/types';

interface AppLayoutProps {
  children: React.ReactNode;
  currentMode: AppMode;
  onModeChange: (mode: AppMode) => void;
  title?: string;
  subtitle?: string;
  customBreadcrumbPath?: string[];
}

export default function AppLayout({ 
  children, 
  currentMode, 
  onModeChange,
  title,
  subtitle,
  customBreadcrumbPath
}: AppLayoutProps) {
  return (
    <SidebarProvider>
      <AppSidebar 
        currentMode={currentMode} 
        onModeChange={onModeChange} 
      />
      <SidebarInset>
        <AppHeader 
          currentMode={currentMode}
          title={title}
          subtitle={subtitle}
          customBreadcrumbPath={customBreadcrumbPath}
        />
        <main className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            {children}
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
} 