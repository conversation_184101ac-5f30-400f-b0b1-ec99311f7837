'use client';

import React, { useState, useMemo } from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import StreamingMessage from './StreamingMessage';

interface MessageGroup {
  id: string;
  type: 'conversation' | 'agent' | 'category' | 'time' | 'priority';
  title: string;
  messages: EnhancedMessage[];
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  icon: string;
  color: string;
  isCollapsed: boolean;
  timestamp: number;
  messageCount: number;
}

interface MessageGroupingProps {
  messages: EnhancedMessage[];
  groupBy: 'none' | 'agent' | 'category' | 'priority' | 'time';
  conversationId?: string;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, reaction: 'like' | 'dislike' | 'helpful' | 'unhelpful') => void;
  showGroupHeaders?: boolean;
  allowCollapse?: boolean;
  maxMessagesPerGroup?: number;
}

// Helper function to classify message type and priority
const classifyMessage = (message: EnhancedMessage): { category: string; priority: string; icon: string; color: string } => {
  // Chart messages
  if (message.type === 'chart' || message.metadata?.messageType === 'chart') {
    return { category: 'visualization', priority: 'high', icon: '📊', color: 'blue' };
  }

  // Metadata-based classification
  if (message.metadata?.messageType) {
    switch (message.metadata.messageType) {
      case 'analysis':
        return { category: 'analysis', priority: 'high', icon: '📈', color: 'green' };
      case 'recommendation':
        return { category: 'advice', priority: 'critical', icon: '💡', color: 'yellow' };
      case 'warning':
        return { category: 'alert', priority: 'critical', icon: '⚠️', color: 'red' };
      case 'summary':
        return { category: 'summary', priority: 'medium', icon: '📋', color: 'purple' };
      case 'data':
        return { category: 'data', priority: 'medium', icon: '📊', color: 'indigo' };
      case 'code':
        return { category: 'technical', priority: 'medium', icon: '💻', color: 'gray' };
      case 'research':
        return { category: 'research', priority: 'high', icon: '🔍', color: 'teal' };
      case 'market_update':
        return { category: 'news', priority: 'medium', icon: '📰', color: 'orange' };
      case 'portfolio':
        return { category: 'portfolio', priority: 'high', icon: '💼', color: 'emerald' };
      case 'ai_insight':
        return { category: 'ai', priority: 'high', icon: '🧠', color: 'violet' };
    }
  }

  // Content-based classification
  const content = message.content.toLowerCase();
  
  // Risk and warning keywords
  if (content.includes('风险') || content.includes('警告') || content.includes('注意') || 
      content.includes('danger') || content.includes('risk') || content.includes('warning')) {
    return { category: 'alert', priority: 'critical', icon: '⚠️', color: 'red' };
  }
  
  // Investment recommendation keywords
  if (content.includes('建议') || content.includes('推荐') || content.includes('买入') || 
      content.includes('卖出') || content.includes('持有') || content.includes('recommend')) {
    return { category: 'advice', priority: 'critical', icon: '💡', color: 'yellow' };
  }
  
  // Analysis keywords
  if (content.includes('分析') || content.includes('技术') || content.includes('基本面') || 
      content.includes('analysis') || content.includes('technical') || content.includes('fundamental')) {
    return { category: 'analysis', priority: 'high', icon: '📈', color: 'green' };
  }

  // Default classification
  if (message.type === 'user') {
    return { category: 'user', priority: 'medium', icon: 'user', color: 'blue' };
  }

  return { category: 'general', priority: 'medium', icon: '💬', color: 'gray' };
};

// Group messages by different criteria
const groupMessages = (messages: EnhancedMessage[], groupBy: string): MessageGroup[] => {
  if (groupBy === 'none') {
    return [{
      id: 'all',
      type: 'conversation',
      title: '所有消息',
      messages,
      priority: 'medium' as const,
      category: 'all',
      icon: '💬',
      color: 'gray',
      isCollapsed: false,
      timestamp: Date.now(),
      messageCount: messages.length,
    }];
  }

  const groups: { [key: string]: MessageGroup } = {};

  messages.forEach(message => {
    let groupKey: string;
    let groupTitle: string;
    let groupIcon: string;
    let groupColor: string;
    let groupPriority: 'critical' | 'high' | 'medium' | 'low' = 'medium';

    const classification = classifyMessage(message);

    switch (groupBy) {
      case 'agent':
        const agent = message.metadata?.agent || (message.type === 'user' ? 'user' : 'assistant');
        groupKey = agent;
        groupTitle = agent === 'user' ? '用户消息' : `${agent}智能体`;
        groupIcon = agent === 'user' ? 'user' : 'assistant';
        groupColor = agent === 'user' ? 'blue' : 'purple';
        break;

      case 'category':
        groupKey = classification.category;
        groupTitle = getCategoryTitle(classification.category);
        groupIcon = classification.icon;
        groupColor = classification.color;
        groupPriority = classification.priority as any;
        break;

      case 'priority':
        groupKey = classification.priority;
        groupTitle = getPriorityTitle(classification.priority);
        groupIcon = getPriorityIcon(classification.priority);
        groupColor = getPriorityColor(classification.priority);
        groupPriority = classification.priority as any;
        break;

      case 'time':
        const timeGroup = getTimeGroup(message.timestamp);
        groupKey = timeGroup.key;
        groupTitle = timeGroup.title;
        groupIcon = timeGroup.icon;
        groupColor = 'blue';
        break;

      default:
        groupKey = 'default';
        groupTitle = '默认分组';
        groupIcon = '💬';
        groupColor = 'gray';
    }

    if (!groups[groupKey]) {
      groups[groupKey] = {
        id: groupKey,
        type: groupBy as any,
        title: groupTitle,
        messages: [],
        priority: groupPriority,
        category: groupKey,
        icon: groupIcon,
        color: groupColor,
        isCollapsed: false,
        timestamp: message.timestamp,
        messageCount: 0,
      };
    }

    groups[groupKey].messages.push(message);
    groups[groupKey].messageCount++;
    groups[groupKey].timestamp = Math.max(groups[groupKey].timestamp, message.timestamp);
  });

  return Object.values(groups).sort((a, b) => {
    // Sort by priority first, then by timestamp
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    const aPriority = priorityOrder[a.priority] || 2;
    const bPriority = priorityOrder[b.priority] || 2;
    
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }
    
    return b.timestamp - a.timestamp;
  });
};

// Helper functions for group titles and icons
const getCategoryTitle = (category: string): string => {
  const titles: { [key: string]: string } = {
    analysis: '分析报告',
    advice: '投资建议',
    alert: '风险提醒',
    summary: '总结报告',
    data: '数据查询',
    visualization: '图表展示',
    technical: '技术内容',
    research: '研究报告',
    news: '市场动态',
    portfolio: '投资组合',
    ai: 'AI洞察',
    user: '用户消息',
    general: '常规消息',
  };
  return titles[category] || category;
};

const getPriorityTitle = (priority: string): string => {
  const titles: { [key: string]: string } = {
    critical: '紧急重要',
    high: '高优先级',
    medium: '中等优先级',
    low: '低优先级',
  };
  return titles[priority] || priority;
};

const getPriorityIcon = (priority: string): string => {
  const icons: { [key: string]: string } = {
    critical: '🔴',
    high: '🟡',
    medium: '🟢',
    low: '⚪',
  };
  return icons[priority] || '💬';
};

const getPriorityColor = (priority: string): string => {
  const colors: { [key: string]: string } = {
    critical: 'red',
    high: 'yellow',
    medium: 'green',
    low: 'gray',
  };
  return colors[priority] || 'gray';
};

const getTimeGroup = (timestamp: number): { key: string; title: string; icon: string } => {
  const now = Date.now();
  const diff = now - timestamp;
  const minutes = diff / (1000 * 60);
  const hours = minutes / 60;
  const days = hours / 24;

  if (minutes < 5) {
    return { key: 'recent', title: '刚刚 (5分钟内)', icon: '🕐' };
  } else if (minutes < 30) {
    return { key: 'last30min', title: '最近30分钟', icon: '🕕' };
  } else if (hours < 1) {
    return { key: 'lasthour', title: '最近1小时', icon: '🕒' };
  } else if (hours < 6) {
    return { key: 'last6hours', title: '最近6小时', icon: '🕘' };
  } else if (days < 1) {
    return { key: 'today', title: '今天', icon: '📅' };
  } else if (days < 7) {
    return { key: 'week', title: '本周', icon: '📊' };
  } else {
    return { key: 'older', title: '更早', icon: '📜' };
  }
};

// Group header component
const GroupHeader: React.FC<{
  group: MessageGroup;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  allowCollapse: boolean;
}> = ({ group, isCollapsed, onToggleCollapse, allowCollapse }) => {
  const colorClasses = {
    red: 'bg-red-50 border-red-200 text-red-800',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',
    teal: 'bg-teal-50 border-teal-200 text-teal-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
    emerald: 'bg-emerald-50 border-emerald-200 text-emerald-800',
    violet: 'bg-violet-50 border-violet-200 text-violet-800',
    gray: 'bg-gray-50 border-gray-200 text-gray-800',
  };

  const colorClass = colorClasses[group.color as keyof typeof colorClasses] || colorClasses.gray;

  return (
    <div
      className={`flex items-center justify-between p-3 border rounded-lg ${colorClass} ${
        allowCollapse ? 'cursor-pointer hover:opacity-80' : ''
      } mb-2`}
      onClick={allowCollapse ? onToggleCollapse : undefined}
    >
      <div className="flex items-center space-x-2">
        <span className="text-lg">{group.icon}</span>
        <span className="font-medium">{group.title}</span>
        <span className="text-xs opacity-75">({group.messageCount} 消息)</span>
      </div>
      {allowCollapse && (
        <span className="text-sm">
          {isCollapsed ? '▶️' : '▼️'}
        </span>
      )}
    </div>
  );
};

// Main message grouping component
const MessageGrouping: React.FC<MessageGroupingProps> = ({
  messages,
  groupBy,
  conversationId = 'default',
  onRetry,
  onEdit,
  onDelete,
  onReaction,
  showGroupHeaders = true,
  allowCollapse = true,
  maxMessagesPerGroup = 50,
}) => {
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());

  const messageGroups = useMemo(() => {
    return groupMessages(messages, groupBy);
  }, [messages, groupBy]);

  const toggleGroupCollapse = (groupId: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  if (groupBy === 'none' || !showGroupHeaders) {
    return (
      <div className="space-y-2">
        {messages.map((message) => (
          <StreamingMessage
            key={message.id}
            message={message}
            conversationId={conversationId}
            onRetry={onRetry}
            onEdit={onEdit}
            onDelete={onDelete}
            onReaction={onReaction}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {messageGroups.map((group) => {
        const isCollapsed = collapsedGroups.has(group.id);
        const displayMessages = group.messages.slice(0, maxMessagesPerGroup);
        const hasMoreMessages = group.messages.length > maxMessagesPerGroup;

        return (
          <div key={group.id} className="message-group">
            <GroupHeader
              group={group}
              isCollapsed={isCollapsed}
              onToggleCollapse={() => toggleGroupCollapse(group.id)}
              allowCollapse={allowCollapse}
            />
            
            {!isCollapsed && (
              <div className="space-y-2 ml-4">
                {displayMessages.map((message) => (
                  <StreamingMessage
                    key={message.id}
                    message={message}
                    conversationId={conversationId}
                    onRetry={onRetry}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onReaction={onReaction}
                  />
                ))}
                
                {hasMoreMessages && (
                  <div className="text-center py-2">
                    <span className="text-sm text-gray-500">
                      还有 {group.messages.length - maxMessagesPerGroup} 条消息...
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default MessageGrouping; 