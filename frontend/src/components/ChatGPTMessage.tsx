'use client';

import React from 'react';
import { EnhancedMessage } from '@/hooks/useChatStreaming';
import MarkdownRenderer from './MarkdownRenderer';
import ChartMessage from './ChartMessage';
import { AvatarIndicator } from './VisualIndicator';
import { preprocessMessageContent } from '@/utils/messagePreprocessor';
import './ChatGPTLayout.css';

interface ChatGPTMessageProps {
  message: EnhancedMessage;
  isStreaming?: boolean;
}

const ChatGPTMessage: React.FC<ChatGPTMessageProps> = ({ message, isStreaming }) => {
  const isUser = message.type === 'user';
  const isAssistant = message.type === 'assistant';

  // Don't render system messages in ChatGPT style
  if (message.type === 'system') return null;

  // Check if this message contains chart data or is a chart message type
  const hasChartData = message.chartData || 
                       message.metadata?.chartData || 
                       (message.metadata?.messageType === 'chart') ||
                       message.type === 'chart';

  // If this is a chart message, render the ChartMessage component
  if (hasChartData) {
    return (
      <div className="message-group">
        <div className="message-row">
          <div className="message-content">
            <ChartMessage 
              message={message}
              className="chat-chart-message"
            />
          </div>
        </div>
      </div>
    );
  }

  // Get the appropriate content for display with preprocessing
  const rawContent = isAssistant 
    ? (message.displayContent || message.content || '') 
    : (message.content || '');
    
  const displayText = isAssistant 
    ? preprocessMessageContent(rawContent)
    : rawContent;

  // Debug logging for content display
  if (isAssistant) {
    console.log('💬 ChatGPTMessage rendering assistant message:', {
      messageId: message.id,
      status: message.status,
      isStreaming,
      contentLength: message.content?.length || 0,
      displayContentLength: message.displayContent?.length || 0,
      usingDisplayText: displayText.length,
      hasContent: !!displayText
    });
  }

  return (
    <div className="message-group">
      <div className="message-row">
        {/* Avatar */}
        <div className={`message-avatar ${isUser ? 'user' : 'assistant'}`}>
          <AvatarIndicator type={isUser ? 'user' : 'assistant'} size="sm" />
        </div>
        
        {/* Message content */}
        <div className="message-content">
          <div className={`message-text ${isUser ? 'user' : 'assistant'}`}>
            {isAssistant ? (
              <MarkdownRenderer 
                content={displayText}
                className="prose prose-gray max-w-none"
                enableCopy={false}
                enableExport={false}
              />
            ) : (
              <div>{displayText}</div>
            )}
            
            {/* Streaming cursor - only show when actually streaming */}
            {isAssistant && isStreaming && message.status === 'streaming' && (
              <span className="inline-block w-2 h-5 bg-gray-400 ml-1 animate-pulse">|</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatGPTMessage; 