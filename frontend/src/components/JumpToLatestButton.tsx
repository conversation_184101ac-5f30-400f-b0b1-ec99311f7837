'use client';

import React from 'react';

interface JumpToLatestButtonProps {
  show: boolean;
  onClick: () => void;
  className?: string;
}

const JumpToLatestButton: React.FC<JumpToLatestButtonProps> = ({
  show,
  onClick,
  className = '',
}) => {
  if (!show) return null;

  return (
    <div
      className={`
        fixed bottom-20 right-6 z-50 
        transform transition-all duration-300 ease-in-out
        ${show ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0 pointer-events-none'}
        ${className}
      `}
    >
      <button
        onClick={onClick}
        className="
          bg-blue-500 hover:bg-blue-600 text-white
          px-4 py-2 rounded-full shadow-lg
          flex items-center space-x-2
          font-medium text-sm
          transition-all duration-200
          hover:shadow-xl hover:scale-105
          active:scale-95
          border border-blue-400
        "
        aria-label="Jump to latest message"
      >
        <span>跳转到最新</span>
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </button>
    </div>
  );
};

export default JumpToLatestButton; 