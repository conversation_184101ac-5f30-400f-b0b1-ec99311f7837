/* 顶部导航主容器 */
.top-navigation {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #4A90E2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 64px; /* 确保最小高度 */
}

/* 主导航栏 */
.main-nav {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden; /* 防止内容溢出 */
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem; /* 减少左右内边距 */
  min-height: 64px;
  gap: 0.5rem; /* 添加元素间间距 */
  flex-wrap: nowrap; /* 防止换行导致布局问题 */
}

/* Logo 和品牌区域 */
.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: none;
  flex-shrink: 0;
  min-width: 200px; /* 确保品牌区域最小宽度 */
  max-width: 250px; /* 限制最大宽度 */
}

.nav-brand:hover {
  transform: none;
  opacity: 1;
}

.brand-icon {
  font-size: 1.8rem;
  animation: none;
  flex-shrink: 0;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.brand-text h1 {
  font-size: 1.1rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: none;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.brand-text span {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 导航链接区域 */
.nav-links {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  flex: 1;
  justify-content: center;
  margin: 0 0.5rem;
  overflow-x: auto; /* 添加横向滚动 */
  overflow-y: hidden;
  scrollbar-width: none; /* Firefox 隐藏滚动条 */
  -ms-overflow-style: none; /* IE 隐藏滚动条 */
  min-width: 0; /* 允许收缩 */
}

/* 隐藏 Webkit 浏览器滚动条 */
.nav-links::-webkit-scrollbar {
  display: none;
}

.nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
  padding: 0.4rem 0.6rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: white;
  text-decoration: none;
  transition: background 0.2s ease, transform 0.2s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  min-width: 65px; /* 减小最小宽度 */
  max-width: 80px; /* 限制最大宽度 */
  flex-shrink: 0; /* 防止收缩 */
  white-space: nowrap; /* 防止文字换行 */
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.3s;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: transparent;
  transform: none;
  box-shadow: none;
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  border-color: transparent;
  box-shadow: none;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #A8D8EA;
  border-radius: 1px;
}

.nav-icon {
  font-size: 1.1rem;
  margin-bottom: 0.1rem;
  flex-shrink: 0;
}

.nav-title {
  font-size: 0.6rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.nav-shortcut {
  font-size: 0.45rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(0, 0, 0, 0.1);
  padding: 0.05rem 0.15rem;
  border-radius: 2px;
  margin-top: 0.05rem;
  white-space: nowrap;
}

/* 状态区域 */
.nav-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  min-width: 140px; /* 确保状态区域最小宽度 */
  max-width: 200px; /* 限制最大宽度 */
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  border: none;
  min-width: 0; /* 允许收缩 */
  flex: 1;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.status-connected {
  background: #4CAF50;
  animation: none;
}

.status-disconnected {
  background: #F44336;
  animation: none;
}

.status-connecting {
  background: #FFC107;
  animation: none;
}

@keyframes pulse-green {
  0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  50% { box-shadow: 0 0 0 8px rgba(16, 185, 129, 0); }
}

@keyframes pulse-red {
  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }
}

@keyframes pulse-yellow {
  0%, 100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7); }
  50% { box-shadow: 0 0 0 8px rgba(245, 158, 11, 0); }
}

.status-text {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许收缩 */
  flex: 1;
}

.status-label {
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-success { color: #4CAF50; }
.text-error { color: #F44336; }
.text-warning { color: #FFC107; }

.status-time {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-refresh {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.2rem;
  border-radius: 3px;
  transition: background 0.2s ease;
  font-size: 0.8rem;
  flex-shrink: 0;
}

.status-refresh:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(180deg);
}

/* 页面信息栏 */
.page-info {
  background: rgba(74, 144, 226, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden; /* 防止内容溢出 */
}

.page-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.4rem 1rem;
  gap: 1rem;
  min-height: 40px;
}

.page-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  flex-shrink: 0;
}

.breadcrumb-home {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0.15rem 0.4rem;
  border-radius: 3px;
  transition: all 0.1s ease;
  font-size: 0.8rem;
  white-space: nowrap;
}

.breadcrumb-home:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
}

.breadcrumb-current {
  font-weight: 500;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.page-features {
  display: flex;
  gap: 0.4rem;
  flex-wrap: wrap;
  justify-content: flex-end;
  min-width: 0; /* 允许收缩 */
}

.feature-tag {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  padding: 0.2rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  flex-shrink: 0;
}

/* 响应式设计 - 1200px 断点 */
@media (max-width: 1200px) {
  .nav-container {
    padding: 0 0.75rem;
    gap: 0.25rem;
  }

  .nav-brand {
    min-width: 180px;
    max-width: 200px;
  }

  .brand-text h1 {
    font-size: 1rem;
  }

  .brand-text span {
    font-size: 0.65rem;
  }

  .nav-links {
    gap: 0.1rem;
    margin: 0 0.25rem;
  }
  
  .nav-link {
    min-width: 55px;
    max-width: 65px;
    padding: 0.35rem 0.5rem;
  }
  
  .nav-title {
    font-size: 0.55rem;
  }

  .nav-shortcut {
    font-size: 0.4rem;
  }

  .nav-status {
    min-width: 120px;
    max-width: 160px;
  }

  .health-indicator {
    padding: 0.25rem 0.5rem;
  }

  .status-label {
    font-size: 0.65rem;
  }
}

/* 响应式设计 - 1000px 断点 */
@media (max-width: 1000px) {
  .nav-container {
    padding: 0 0.5rem;
    gap: 0.2rem;
  }

  .nav-brand {
    min-width: 160px;
    max-width: 180px;
  }

  .brand-text h1 {
    font-size: 0.95rem;
  }

  .nav-links {
    margin: 0 0.2rem;
    gap: 0.05rem;
  }

  .nav-link {
    min-width: 50px;
    max-width: 60px;
    padding: 0.3rem 0.4rem;
  }

  .nav-icon {
    font-size: 1rem;
  }

  .nav-title {
    font-size: 0.5rem;
  }

  .nav-shortcut {
    display: none; /* 隐藏快捷键 */
  }

  .nav-status {
    min-width: 100px;
    max-width: 140px;
  }

  .status-label {
    font-size: 0.6rem;
  }

  .status-time {
    font-size: 0.55rem;
  }
}

/* 响应式设计 - 900px 断点 */
@media (max-width: 900px) {
  .nav-container {
    padding: 0 0.5rem;
    height: auto;
    min-height: 56px;
  }
  
  .nav-brand {
    min-width: 140px;
    max-width: 160px;
  }

  .brand-text h1 {
    font-size: 0.9rem;
  }
  
  .brand-text span {
    font-size: 0.6rem;
  }
  
  .nav-links {
    gap: 0;
    margin: 0 0.1rem;
  }
  
  .nav-link {
    min-width: 45px;
    max-width: 55px;
    padding: 0.3rem 0.3rem;
  }
  
  .nav-icon {
    font-size: 0.95rem;
  }
  
  .nav-title {
    font-size: 0.45rem;
    line-height: 1;
  }
  
  .health-indicator {
    padding: 0.25rem 0.4rem;
    gap: 0.3rem;
  }
  
  .status-label {
    font-size: 0.6rem;
  }

  .nav-status {
    min-width: 90px;
    max-width: 120px;
  }
}

/* 响应式设计 - 768px 断点 */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    height: auto;
    padding: 0.5rem;
    gap: 0.5rem;
    align-items: stretch;
  }

  .nav-brand {
    min-width: auto;
    max-width: none;
    justify-content: center;
  }
  
  .nav-links {
    margin: 0;
    flex-wrap: wrap;
    justify-content: center;
    overflow-x: visible;
    gap: 0.3rem;
  }

  .nav-link {
    min-width: 70px;
    max-width: 80px;
  }

  .nav-status {
    min-width: auto;
    max-width: none;
    justify-content: center;
  }
  
  .page-container {
    flex-direction: column;
    gap: 0.4rem;
    align-items: flex-start;
    padding: 0.5rem;
  }
  
  .page-features {
    align-self: stretch;
    justify-content: flex-start;
  }
}

/* 响应式设计 - 480px 断点 */
@media (max-width: 480px) {
  .nav-container {
    padding: 0.4rem;
    gap: 0.4rem;
  }

  .nav-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.3rem;
    width: 100%;
  }
  
  .nav-link {
    min-width: unset;
    max-width: none;
  }
  
  .health-indicator {
    flex-direction: column;
    text-align: center;
    gap: 0.2rem;
    padding: 0.3rem;
  }

  .status-text {
    align-items: center;
  }

  .page-container {
    padding: 0.4rem;
  }

  .page-features {
    gap: 0.2rem;
  }

  .feature-tag {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .top-navigation {
    background: #2C3E50;
  }
  
  .page-info {
    background: #34495E;
  }

  .status-time,
  .breadcrumb-home {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
  }

  .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .nav-link.active::after {
    background: #60A5FA;
  }
}

/* 无障碍优化 */
.nav-link:focus-visible,
.breadcrumb-home:focus-visible,
.status-refresh:focus-visible {
  outline: 2px solid #90CAF9;
  outline-offset: 2px;
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .nav-link,
  .status-refresh,
  .breadcrumb-home,
  .nav-brand {
    transition: none !important;
  }
  
  .brand-icon,
  .status-dot {
    animation: none !important;
  }
  
  .nav-link::before {
    display: none !important;
  }
} 