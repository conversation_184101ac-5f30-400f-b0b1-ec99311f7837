#!/bin/bash
# 停止完整系统脚本

echo "🛑 停止AI金融分析系统"
echo "===================="

# 函数：停止指定PID文件中的进程
stop_process_by_pid() {
    local name=$1
    local pid_file=$2
    local pkill_patterns=("${@:3}")

    echo "🔄 尝试停止 $name 服务..."

    # 1. 尝试通过PID文件停止
    if [ -f "$pid_file" ]; then
        PID=$(cat "$pid_file")
        if [ -n "$PID" ] && ps -p $PID > /dev/null 2>&1; then
            echo "🎯 从PID文件停止 $name 服务 (PID: $PID)..."
            kill $PID
            sleep 3 # 给予更多时间让进程自行终止

            if ps -p $PID > /dev/null 2>&1; then
                echo "强制停止 $name 服务 (PID: $PID)..."
                kill -9 $PID
                sleep 1
            fi
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "✅ $name 服务 (PID: $PID) 已停止"
            else
                echo "❌ 警告：$name 服务 (PID: $PID) 可能未能完全停止，请手动检查。"
            fi
        else
            echo "⚠️   $name 服务PID文件存在但进程未运行或PID无效。"
        fi
        rm -f "$pid_file" # 无论如何都移除PID文件
    else
        echo "⚠️  未找到 $name 的PID文件。"
    fi

    # 2. 尝试通过进程名强制停止 (兜底机制)
    echo "🧹 尝试通过进程名清理 $name 进程..."
    local any_killed=false
    for pattern in "${pkill_patterns[@]}"; do
        if pgrep -f "$pattern" > /dev/null; then
            echo "   强制停止匹配 '$pattern' 的 $name 进程..."
            pkill -9 -f "$pattern" 2>/dev/null || true
            sleep 1
            if pgrep -f "$pattern" > /dev/null; then
                echo "   ❌ 警告：匹配 '$pattern' 的 $name 进程仍在运行，可能需要手动干预。"
            else
                echo "   ✅ 匹配 '$pattern' 的 $name 进程已停止"
                any_killed=true
            fi
        else
            echo "   未找到匹配 '$pattern' 的 $name 进程."
        fi
    done

    if [ "$any_killed" = true ]; then
        echo "✅ $name 进程清理完成。"
    elif [ ! -f "$pid_file" ] && ! pgrep -f "${pkill_patterns[0]}" > /dev/null; then
        # 如果PID文件不存在，且第一个pkill模式也没有找到，则认为已经停止
        echo "ℹ️ $name 服务似乎未在运行或已停止。"
    fi
    echo ""
}

# 停止后端服务 - 更新匹配模式以匹配新的启动命令
backend_patterns=(
    "uvicorn.*backend.server:app"
    "python.*backend/server.py"
    "uvicorn.*server:app"
)
stop_process_by_pid "后端" ".backend.pid" "${backend_patterns[@]}"

# 停止前端服务 - 更新匹配模式
frontend_patterns=(
    "npm.*run.*dev"
    "next.*dev"
    "node.*next.*dev"
)
stop_process_by_pid "前端" ".frontend.pid" "${frontend_patterns[@]}"

# 最终检查并清理所有可能残留的通用进程
echo "🔍 最终检查并清理所有可能残留的通用进程..."
PROCESSES_TO_CHECK=(
    "uvicorn.*backend.server:app"
    "python.*backend/server.py"
    "npm.*run.*dev"
    "next.*dev"
    "node.*next.*dev"
)
ALL_CLEAN=true
for proc in "${PROCESSES_TO_CHECK[@]}"; do
    if pgrep -f "$proc" > /dev/null; then
        echo "❌ 发现残留进程: $proc - 尝试再次强制停止..."
        pkill -9 -f "$proc" 2>/dev/null || true
        sleep 1
        if pgrep -f "$proc" > /dev/null; then
            echo "   ⚠️  警告：进程 $proc 仍然存在，请手动检查。"
            ALL_CLEAN=false
        else
            echo "   ✅ 进程 $proc 已清除。"
        fi
    fi
done

# 额外检查端口占用
echo "🔍 检查端口占用情况..."
if lsof -ti:8000 > /dev/null 2>&1; then
    echo "⚠️  端口 8000 仍被占用，尝试释放..."
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    sleep 1
    if ! lsof -ti:8000 > /dev/null 2>&1; then
        echo "✅ 端口 8000 已释放"
    else
        echo "❌ 端口 8000 仍被占用，请手动检查"
        ALL_CLEAN=false
    fi
fi

if lsof -ti:3000 > /dev/null 2>&1; then
    echo "⚠️  端口 3000 仍被占用，尝试释放..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 1
    if ! lsof -ti:3000 > /dev/null 2>&1; then
        echo "✅ 端口 3000 已释放"
    else
        echo "❌ 端口 3000 仍被占用，请手动检查"
        ALL_CLEAN=false
    fi
fi

if [ "$ALL_CLEAN" = true ]; then
    echo "✅ 所有已知相关进程和端口已成功停止并清理。"
else
    echo "❌ 警告：部分进程或端口可能未能完全停止，请手动检查系统进程。"
    echo "   可以使用以下命令检查："
    echo "   - 检查后端进程: ps aux | grep -E 'uvicorn|backend'"
    echo "   - 检查前端进程: ps aux | grep -E 'npm|next|node'"
    echo "   - 检查端口占用: lsof -i:8000 -i:3000"
fi

echo ""
echo "✅ 系统已完全停止"
echo ""
echo "📝 日志文件仍然保留："
if [ -f "backend.log" ]; then
    echo "   - backend.log (后端日志)"
fi
if [ -f "frontend.log" ]; then
    echo "   - frontend.log (前端日志)"
fi

echo ""
echo "🚀 要重新启动系统，请运行："
echo "   ./start_full_system.sh" 