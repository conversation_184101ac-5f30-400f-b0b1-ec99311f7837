---
description:
globs:
alwaysApply: false
---
# 项目结构指南

`deer-flow` 项目的主要入口点包括 `main.py` 和 `server.py`。

核心逻辑位于 `src/` 目录下：

- `src/graph/`: 定义了主要的代理工作流图 (`build_graph` 函数在 [src/graph/builder.py](mdc:src/graph/builder.py) 中)。
- `src/agents/`: 包含用于创建代理的逻辑 ([src/agents/agents.py](mdc:src/agents/agents.py))。
- `src/tools/`: 定义了代理可以使用的各种工具，例如搜索工具 ([src/tools/search.py](mdc:src/tools/search.py)) 和爬虫工具 ([src/tools/crawl.py](mdc:src/tools/crawl.py))。
- `src/config/`: 存放项目的各种配置，包括代理配置 ([src/config/agents.py](mdc:src/config/agents.py)) 和工具配置 ([src/config/tools.py](mdc:src/config/tools.py))。
- `src/prompts/`: 存储用于代理的各种提示模板 (`get_prompt_template` 和 `apply_prompt_template` 函数在 [src/prompts/template.py](mdc:src/prompts/template.py) 中)。
- `src/server/`: 包含了 FastAPI 服务器的实现 ([src/server/app.py](mdc:src/server/app.py))，用于提供 API 接口。
- `src/workflow.py`: 提供了运行代理工作流的异步函数。

其他重要目录和文件包括：

- `web/`: 包含前端代码。
- `conf.yaml`: 项目的主要配置文件。
- `Dockerfile` 和 `docker-compose.yml`: 用于构建和运行 Docker 容器。
- `bootstrap.sh` 和 `bootstrap.bat`: 项目的启动脚本。
