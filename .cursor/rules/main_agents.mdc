
---
description:
globs:
alwaysApply: false
---
# 主要代理角色和职责

`deer-flow` 项目使用一系列专业代理协作完成任务。主要代理包括：

- **协调者 (Coordinator)**: 处理用户初始请求，进行问候或小范围交流，并将研究任务分配给规划者。详见 [src/prompts/coordinator.md](mdc:src/prompts/coordinator.md)。
- **规划者 (Planner)**: 接收协调者分配的任务，生成详细的研究计划，包括研究步骤和处理步骤。详见 [src/prompts/planner.md](mdc:src/prompts/planner.md) 和 [src/graph/nodes.py](mdc:src/graph/nodes.py)。
- **研究员 (Researcher)**: 根据规划者制定的研究步骤，使用搜索工具和爬虫工具收集信息。详见 [src/prompts/researcher.md](mdc:src/prompts/researcher.md) 和 [src/graph/nodes.py](mdc:src/graph/nodes.py)。
- **编码员 (Coder)**: 根据规划者制定的处理步骤，执行 Python 代码进行数据分析或计算。详见 [src/prompts/coder.md](mdc:src/prompts/coder.md) 和 [src/graph/nodes.py](mdc:src/graph/nodes.py)。
- **报告员 (Reporter)**: 在所有研究和处理步骤完成后，根据收集到的信息撰写最终报告。详见 [src/prompts/reporter.md](mdc:src/prompts/reporter.md) 和 [src/graph/nodes.py](mdc:src/graph/nodes.py)。

这些代理通过 `langgraph` 构建的工作流相互协作，共同完成用户请求。工作流的定义在 [src/graph/builder.py](mdc:src/graph/builder.py) 中。
