---
description: 
globs: 
alwaysApply: false
---
# API 服务器 (`src/server/app.py`)

项目的后端 API 服务器使用 FastAPI 实现，主要负责接收前端请求并与 `langgraph` 工作流进行交互。

主要功能包括：

- **聊天流 (`/api/chat/stream`)**: 接收用户聊天消息，初始化或恢复代理工作流，并将工作流的输出以 Server-Sent Events (SSE) 的形式流式传输回前端。这个端点使用了带有记忆功能的 `langgraph` 工作流 (`build_graph_with_memory` 函数在 [src/graph/builder.py](mdc:src/graph/builder.py) 中)。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `chat_stream` 函数。
- **TTS (`/api/tts`)**: 将文本转换为语音，调用 Volcengine TTS API。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `text_to_speech` 函数和 [src/tools/tts.py](mdc:src/tools/tts.py)。
- **生成播客 (`/api/podcast/generate`)**: 根据报告内容生成播客音频，调用播客生成工作流。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `generate_podcast` 函数和 [src/podcast/graph/builder.py](mdc:src/podcast/graph/builder.py)。
- **生成 PPT (`/api/ppt/generate`)**: 根据报告内容生成 PPT 文件，调用 PPT 生成工作流。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `generate_ppt` 函数和 [src/ppt/graph/builder.py](mdc:src/ppt/graph/builder.py)。
- **生成散文 (`/api/prose/generate`)**: 根据用户输入和选项生成或修改文本，调用散文生成工作流。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `generate_prose` 函数和 [src/prose/graph/builder.py](mdc:src/prose/graph/builder.py)。
- **MCP 服务器元数据 (`/api/mcp/server/metadata`)**: 获取 MCP 服务器的元数据和可用工具信息。详见 [src/server/app.py](mdc:src/server/app.py) 中的 `mcp_server_metadata` 函数和 [src/server/mcp_utils.py](mdc:src/server/mcp_utils.py)。

FastAPI 应用的定义在 [src/server/app.py](mdc:src/server/app.py) 文件中。
