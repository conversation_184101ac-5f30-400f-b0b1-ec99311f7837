---
description: 
globs: 
alwaysApply: false
---
### 个股新闻

接口: stock_news_em

目标地址: https://so.eastmoney.com/news/s

描述: 东方财富指定个股的新闻资讯数据

限量: 指定 symbol 当日最近 100 条新闻资讯数据

输入参数

| 名称     | 类型  | 描述                          |
|--------|-----|-----------------------------|
| symbol | str | symbol="300059"; 股票代码或其他关键词 |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 关键词  | object | -   |
| 新闻标题 | object | -   |
| 新闻内容 | object | -   |
| 发布时间 | object | -   |
| 文章来源 | object | -   |
| 新闻链接 | object | -   |

接口示例

```python
import akshare as ak

stock_news_em_df = ak.stock_news_em(symbol="300059")
print(stock_news_em_df)
```

数据示例

```
       关键词  ...                                               新闻链接
0   300059  ...  http://finance.eastmoney.com/a/202405103073124...
1   300059  ...  http://finance.eastmoney.com/a/202405103073132...
2   300059  ...  http://finance.eastmoney.com/a/202404243057785...
3   300059  ...  http://finance.eastmoney.com/a/202404163047193...
4   300059  ...  http://finance.eastmoney.com/a/202404263060328...
..     ...  ...                                                ...
95  300059  ...  http://finance.eastmoney.com/a/202403213019923...
96  300059  ...  http://finance.eastmoney.com/a/202403143012507...
97  300059  ...  http://finance.eastmoney.com/a/202403073004835...
98  300059  ...  http://finance.eastmoney.com/a/202402282997385...
99  300059  ...  http://finance.eastmoney.com/a/202402282997353...
[100 rows x 6 columns]
```

### 财经内容精选

接口: stock_news_main_cx

目标地址: https://cxdata.caixin.com/pc/

描述: 财新网-财新数据通-内容精选

限量: 返回所有历史新闻数据

输入参数

| 名称 | 类型 | 描述 |
|----|----|----|
| -  | -  | -  |

输出参数

| 名称            | 类型     | 描述 |
|---------------|--------|----|
| tag           | object | -  |
| summary       | object | -  |
| interval_time | object | -  |
| pub_time      | object | -  |
| url           | object | -  |

接口示例

```python
import akshare as ak

stock_news_main_cx_df = ak.stock_news_main_cx()
print(stock_news_main_cx_df)
```

数据示例

```
                     tag  ...                                                url
0                 8月5日收盘  ...  https://stock.caixin.com/m/market?cxapp_link=true
1      高盛上调美国衰退预期但认为风险有限  ...  https://database.caixin.com/2024-08-05/1022233...
2       分析人士：股市逢低买入还为时过早  ...  https://database.caixin.com/2024-08-05/1022233...
3        巴菲特加速减持苹果是重大信号吗  ...  https://www.caixin.com/2024-08-04/102223237.ht...
4                 今日股市热点  ...  https://database.caixin.com/2024-08-05/1022233...
...                  ...  ...                                                ...
12729     （接上条）广电的5G牌怎么打  ...  https://cxdata.caixin.com/twotopic/20200925153...
12730           5G建设最新数据  ...  https://www.caixin.com/2020-11-26/101632865.htm...
12731       银行股尾盘爆发有何道理？  ...  https://database.caixin.com/2020-11-22/10163123...
12732  鸿海或将部分苹果产品生产线迁出中国  ...  https://database.caixin.com/2020-11-27/10163291...
12733          “小酒”股今日普跌  ...  https://database.caixin.com/2020-11-26/10163250...
[12734 rows x 5 columns]
```

### 财报发行

接口: news_report_time_baidu

目标地址: https://gushitong.baidu.com/calendar

描述: 百度股市通-财报发行

限量: 单次获取指定 date 的财报发行, 提供港股的财报发行数据

输入参数

| 名称   | 类型  | 描述              |
|------|-----|-----------------|
| date | str | date="20241107" |

输出参数

| 名称   | 类型     | 描述  |
|------|--------|-----|
| 股票代码 | object |     |
| 交易所  | object |     |
| 股票简称 | object |     |
| 财报期  | object |     |

接口示例

```python
import akshare as ak

news_report_time_baidu_df = ak.news_report_time_baidu(date="20241107")
print(news_report_time_baidu_df)
```

数据示例

```
     股票代码 交易所                       股票简称               财报期
0   00945  HK                         宏利金融-S          2024年三季报
1   00981  HK                           中芯国际          2024年三季报
2   01347  HK                          华虹半导体          2024年三季报
3   01665  HK                           槟杰科达          2024年三季报
4   08476  HK                         大洋环球控股           2024年中报
..    ...  ..                            ...               ...
95   XRAY  US                         登士柏西诺德    美东时间发布2024年三季报
96   UPST  US         Upstart Holdings, Inc.  美东时间盘后发布2024年三季报
97    MUR  US                           墨菲石油    美东时间发布2024年三季报
98     SG  US       Sweetgreen, Inc. Class A    美东时间发布2024年三季报
99   STEP  US  StepStone Group, Inc. Class A   美东时间发布24/25年二季报
[100 rows x 4 columns]
```