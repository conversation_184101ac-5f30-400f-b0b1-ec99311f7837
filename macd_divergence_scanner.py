#!/usr/bin/env python3
"""
📊 MACD背离扫描器
一键扫描纳斯达克100、恒生科技、中证300指数成分股的MACD背离情况
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
import argparse

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.market_scanner import get_market_scanner
from backend.index_components import get_all_index_names, get_index_stocks

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MACDDivergenceScanner:
    """MACD背离扫描器"""
    
    def __init__(self, tushare_token: str):
        self.tushare_token = tushare_token
        self.scanner = get_market_scanner(tushare_token)
    
    async def scan_all_indexes(self, divergence_types: list = None):
        """扫描所有支持的指数"""
        if divergence_types is None:
            divergence_types = ['bullish', 'bearish']
        
        print("=" * 80)
        print("📊 MACD背离扫描器 - 全指数扫描")
        print("=" * 80)
        
        # 获取所有支持的指数（除了'ALL'）
        indexes = [idx for idx in get_all_index_names() if idx != 'ALL']
        
        all_results = {}
        total_divergences = 0
        total_stocks = 0
        
        for index_name in indexes:
            print(f"\n🔍 正在扫描 {self._get_index_display_name(index_name)}...")
            
            try:
                result = await self.scanner.scan_index(index_name, divergence_types)
                all_results[index_name] = result
                
                divergence_count = len(result['divergences'])
                total_divergences += divergence_count
                total_stocks += result['total_stocks']
                
                print(f"✅ {self._get_index_display_name(index_name)} 扫描完成")
                print(f"   📈 成分股数量: {result['total_stocks']}")
                print(f"   🎯 成功扫描: {result['scanned_stocks']}")
                print(f"   🚨 发现背离: {divergence_count}")
                print(f"   ⏱️  耗时: {result['scan_duration']:.2f}秒")
                
                if result['errors']:
                    print(f"   ⚠️  错误数量: {len(result['errors'])}")
                
            except Exception as e:
                print(f"❌ {self._get_index_display_name(index_name)} 扫描失败: {e}")
                logger.error(f"扫描{index_name}失败: {e}")
        
        # 汇总结果
        print("\n" + "=" * 80)
        print("📊 扫描汇总结果")
        print("=" * 80)
        print(f"🎯 总计扫描股票: {total_stocks}")
        print(f"🚨 总计发现背离: {total_divergences}")
        
        # 显示各指数背离详情
        for index_name, result in all_results.items():
            if result['divergences']:
                print(f"\n📈 {self._get_index_display_name(index_name)} 背离详情:")
                self._display_divergences(result['divergences'])
        
        return all_results
    
    async def scan_single_index(self, index_name: str, divergence_types: list = None):
        """扫描单个指数"""
        if divergence_types is None:
            divergence_types = ['bullish', 'bearish']
        
        print("=" * 80)
        print(f"📊 MACD背离扫描器 - {self._get_index_display_name(index_name)}")
        print("=" * 80)
        
        # 显示指数信息
        stock_count = self.scanner.get_index_stock_count(index_name)
        print(f"📈 指数: {self._get_index_display_name(index_name)}")
        print(f"📊 成分股数量: {stock_count}")
        print(f"🔍 扫描类型: {', '.join(divergence_types)}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            result = await self.scanner.scan_index(index_name, divergence_types)
            
            print(f"\n✅ 扫描完成!")
            print(f"📈 成分股数量: {result['total_stocks']}")
            print(f"🎯 成功扫描: {result['scanned_stocks']}")
            print(f"🚨 发现背离: {len(result['divergences'])}")
            print(f"⏱️  扫描耗时: {result['scan_duration']:.2f}秒")
            
            if result['errors']:
                print(f"⚠️  扫描错误: {len(result['errors'])}")
                print("错误详情:")
                for error in result['errors'][:5]:  # 只显示前5个错误
                    print(f"   - {error}")
                if len(result['errors']) > 5:
                    print(f"   ... 还有 {len(result['errors']) - 5} 个错误")
            
            # 显示背离详情
            if result['divergences']:
                print(f"\n🚨 发现的背离信号:")
                self._display_divergences(result['divergences'])
            else:
                print(f"\n✅ 未发现MACD背离信号")
            
            return result
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            logger.error(f"扫描{index_name}失败: {e}")
            return None
    
    def _get_index_display_name(self, index_name: str) -> str:
        """获取指数显示名称"""
        display_names = {
            'NASDAQ_100': '纳斯达克100',
            'HANG_SENG_TECH': '恒生科技',
            'CSI_300': '中证300',
            'ALL': '全部指数'
        }
        return display_names.get(index_name, index_name)
    
    def _display_divergences(self, divergences: list):
        """显示背离信号详情"""
        if not divergences:
            return
        
        # 按类型分组
        bullish_divs = [d for d in divergences if d['type'] == 'bullish']
        bearish_divs = [d for d in divergences if d['type'] == 'bearish']
        
        if bullish_divs:
            print(f"\n🟢 底背离信号 ({len(bullish_divs)}个):")
            for div in bullish_divs:
                print(f"   📈 {div['symbol']}: 强度{div['strength']:.2f}, 置信度{div['confidence']:.2f}")
                print(f"      价格区间: {div['price_range']}, MACD区间: {div['macd_range']}")
        
        if bearish_divs:
            print(f"\n🔴 顶背离信号 ({len(bearish_divs)}个):")
            for div in bearish_divs:
                print(f"   📉 {div['symbol']}: 强度{div['strength']:.2f}, 置信度{div['confidence']:.2f}")
                print(f"      价格区间: {div['price_range']}, MACD区间: {div['macd_range']}")
    
    def show_supported_indexes(self):
        """显示支持的指数列表"""
        print("=" * 80)
        print("📊 支持的指数列表")
        print("=" * 80)
        
        indexes = [idx for idx in get_all_index_names() if idx != 'ALL']
        
        for index_name in indexes:
            stock_count = self.scanner.get_index_stock_count(index_name)
            display_name = self._get_index_display_name(index_name)
            print(f"📈 {display_name} ({index_name}): {stock_count} 只成分股")
            
            # 显示部分成分股示例
            stocks = get_index_stocks(index_name)[:5]
            print(f"   示例成分股: {', '.join(stocks)}")
            if len(get_index_stocks(index_name)) > 5:
                print(f"   ... 等共 {stock_count} 只")
            print()

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MACD背离扫描器')
    parser.add_argument('--index', '-i', type=str, 
                       help='指定要扫描的指数 (NASDAQ_100, HANG_SENG_TECH, CSI_300, ALL)')
    parser.add_argument('--type', '-t', type=str, nargs='+', 
                       choices=['bullish', 'bearish'], 
                       default=['bullish', 'bearish'],
                       help='指定背离类型 (bullish=底背离, bearish=顶背离)')
    parser.add_argument('--list', '-l', action='store_true',
                       help='显示支持的指数列表')
    
    args = parser.parse_args()
    
    # Tushare Token
    TUSHARE_TOKEN = "d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c"
    
    try:
        scanner = MACDDivergenceScanner(TUSHARE_TOKEN)
        
        if args.list:
            scanner.show_supported_indexes()
            return
        
        if args.index:
            if args.index == 'ALL':
                await scanner.scan_all_indexes(args.type)
            else:
                await scanner.scan_single_index(args.index, args.type)
        else:
            # 默认扫描所有指数
            await scanner.scan_all_indexes(args.type)
            
    except KeyboardInterrupt:
        print("\n⏹️  扫描已被用户中断")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        logger.error(f"程序运行出错: {e}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main()) 