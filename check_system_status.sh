#!/bin/bash
# 系统状态检查脚本

echo "🔍 AI金融分析系统状态检查"
echo "========================"

# 检查当前目录
echo "📁 当前目录: $(pwd)"
if [ ! -f "package.json" ] && [ ! -d "backend" ]; then
    echo "❌ 不在项目根目录，请切换到项目根目录运行"
else
    echo "✅ 在正确的项目根目录"
fi

echo ""

# 检查Python环境
echo "🐍 Python环境检查:"
if [ -d "venv" ]; then
    echo "✅ 虚拟环境存在"
    if [[ "$VIRTUAL_ENV" == *"venv"* ]]; then
        echo "✅ 虚拟环境已激活"
    else
        echo "⚠️  虚拟环境未激活，请运行: source venv/bin/activate"
    fi
else
    echo "❌ 虚拟环境不存在"
fi

echo ""

# 检查环境变量
echo "🔧 环境变量检查:"
if [ -f ".env" ]; then
    echo "✅ .env文件存在"
    if grep -q "GEMINI_API_KEY" .env; then
        echo "✅ GEMINI_API_KEY已配置"
    else
        echo "❌ GEMINI_API_KEY未配置"
    fi
    if grep -q "TUSHARE_TOKEN" .env; then
        echo "✅ TUSHARE_TOKEN已配置"
    else
        echo "⚠️  TUSHARE_TOKEN未配置（可选）"
    fi
else
    echo "❌ .env文件不存在，请运行: ./setup_environment.sh"
fi

echo ""

# 检查端口占用
echo "🌐 端口占用检查:"
if lsof -ti:8000 > /dev/null 2>&1; then
    echo "⚠️  端口 8000 已被占用"
    echo "   占用进程: $(lsof -ti:8000 | xargs ps -p | tail -n +2)"
else
    echo "✅ 端口 8000 空闲"
fi

if lsof -ti:3000 > /dev/null 2>&1; then
    echo "⚠️  端口 3000 已被占用"
    echo "   占用进程: $(lsof -ti:3000 | xargs ps -p | tail -n +2)"
else
    echo "✅ 端口 3000 空闲"
fi

echo ""

# 检查进程状态
echo "🔄 进程状态检查:"
if pgrep -f "uvicorn.*backend.server:app" > /dev/null; then
    echo "✅ 后端服务正在运行"
    echo "   PID: $(pgrep -f 'uvicorn.*backend.server:app')"
else
    echo "❌ 后端服务未运行"
fi

if pgrep -f "npm.*run.*dev\|next.*dev" > /dev/null; then
    echo "✅ 前端服务正在运行"
    echo "   PID: $(pgrep -f 'npm.*run.*dev\|next.*dev')"
else
    echo "❌ 前端服务未运行"
fi

echo ""

# 检查PID文件
echo "📄 PID文件检查:"
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        echo "✅ 后端PID文件有效 (PID: $BACKEND_PID)"
    else
        echo "⚠️  后端PID文件存在但进程无效 (PID: $BACKEND_PID)"
    fi
else
    echo "❌ 后端PID文件不存在"
fi

if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        echo "✅ 前端PID文件有效 (PID: $FRONTEND_PID)"
    else
        echo "⚠️  前端PID文件存在但进程无效 (PID: $FRONTEND_PID)"
    fi
else
    echo "❌ 前端PID文件不存在"
fi

echo ""

# 检查服务连通性
echo "🌐 服务连通性检查:"
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务响应正常"
else
    echo "❌ 后端服务无响应"
fi

if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务响应正常"
else
    echo "❌ 前端服务无响应"
fi

echo ""

# 检查日志文件
echo "📝 日志文件检查:"
if [ -f "backend.log" ]; then
    echo "✅ 后端日志存在 ($(wc -l < backend.log) 行)"
    echo "   最后几行:"
    tail -3 backend.log | sed 's/^/     /'
else
    echo "❌ 后端日志不存在"
fi

if [ -f "frontend.log" ]; then
    echo "✅ 前端日志存在 ($(wc -l < frontend.log) 行)"
    echo "   最后几行:"
    tail -3 frontend.log | sed 's/^/     /'
else
    echo "❌ 前端日志不存在"
fi

echo ""

# 检查依赖
echo "📦 依赖检查:"
if [ -f "requirements.txt" ]; then
    echo "✅ Python依赖文件存在"
else
    echo "❌ Python依赖文件不存在"
fi

if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
    echo "✅ 前端依赖文件存在"
    if [ -d "frontend/node_modules" ]; then
        echo "✅ 前端依赖已安装"
    else
        echo "⚠️  前端依赖未安装，请运行: cd frontend && npm install"
    fi
else
    echo "❌ 前端依赖文件不存在"
fi

echo ""
echo "========================"
echo "🎯 建议操作:"

# 给出建议
if ! lsof -ti:8000 > /dev/null 2>&1 && ! lsof -ti:3000 > /dev/null 2>&1; then
    if [ -f ".env" ] && [ -d "venv" ]; then
        echo "   系统看起来准备就绪，可以运行: ./start_full_system.sh"
    else
        echo "   请先运行: ./setup_environment.sh"
    fi
else
    echo "   端口被占用，请先运行: ./stop_full_system.sh"
fi 