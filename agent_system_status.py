#!/usr/bin/env python3
"""
多智能体系统状态检查脚本
提供详细的系统运行状况分析和性能评估
"""

import asyncio
import sys
import time
import json
from pathlib import Path
from typing import Dict, Any, List
import traceback

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def log(msg, level="INFO"):
    """带级别的日志输出"""
    timestamp = time.strftime('%H:%M:%S')
    icons = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌", "TITLE": "🔍"}
    icon = icons.get(level, "📝")
    print(f"[{timestamp}] {icon} {msg}")

async def check_dependencies():
    """检查依赖项"""
    log("检查系统依赖项...", "TITLE")
    
    deps = {
        "langchain_core": "Lang<PERSON>hain 核心库",
        "langgraph": "LangGraph 工作流库", 
        "pandas": "数据处理库",
        "numpy": "数值计算库",
        "akshare": "金融数据库",
        "httpx": "HTTP 客户端",
        "asyncio": "异步编程库"
    }
    
    missing = []
    for dep, desc in deps.items():
        try:
            __import__(dep)
            log(f"{desc} - 已安装", "SUCCESS")
        except ImportError:
            log(f"{desc} - 缺失", "ERROR")
            missing.append(dep)
    
    return len(missing) == 0

async def analyze_llm_performance():
    """分析 LLM 性能"""
    log("分析 LLM 性能...", "TITLE")
    
    try:
        from backend.ai import LLMManager
        
        llm = LLMManager()
        
        # 测试响应时间
        start_time = time.time()
        response = await llm.get_completion("请简单介绍一下技术分析", temperature=0.3)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        log(f"响应时间: {response_time:.2f} 秒", "INFO")
        log(f"响应长度: {len(response)} 字符", "INFO")
        
        # 性能评级
        if response_time < 3:
            log("性能评级: 优秀 (< 3秒)", "SUCCESS")
        elif response_time < 8:
            log("性能评级: 良好 (3-8秒)", "WARNING")
        else:
            log("性能评级: 需要优化 (> 8秒)", "ERROR")
            
        # 测试意图分析
        intent_result = await llm.analyze_user_intent("分析苹果公司的股票技术指标")
        log(f"意图分析功能: {'正常' if intent_result else '异常'}", 
            "SUCCESS" if intent_result else "ERROR")
        
        return True
        
    except Exception as e:
        log(f"LLM 性能测试失败: {e}", "ERROR")
        return False

async def check_agents_capability():
    """检查智能体能力"""
    log("检查智能体能力...", "TITLE")
    
    try:
        from backend.ai import AgentManager
        
        agent_manager = AgentManager()
        tools = agent_manager.available_tools
        
        # 工具分类统计
        tool_categories = {
            "数据获取": ["get_stock_data", "get_financial_data", "web_search"],
            "技术分析": ["calculate_technical_indicators", "macd_divergence_analysis", "technical_analyst"],
            "代码执行": ["python_repl"],
            "网络爬虫": ["crawl"],
            "新闻分析": ["yahoo_finance_news", "get_stock_news"]
        }
        
        for category, category_tools in tool_categories.items():
            available = [tool for tool in category_tools if tool in tools]
            log(f"{category}: {len(available)}/{len(category_tools)} 可用", 
                "SUCCESS" if len(available) == len(category_tools) else "WARNING")
            if available:
                log(f"  可用工具: {', '.join(available)}", "INFO")
        
        # 测试特定智能体
        agents_to_test = ["technical_analyst", "researcher", "coder"]
        for agent_type in agents_to_test:
            if agent_type in tools:
                log(f"{agent_type} 智能体: 可用", "SUCCESS")
            else:
                log(f"{agent_type} 智能体: 不可用", "WARNING")
        
        return True
        
    except Exception as e:
        log(f"智能体能力检查失败: {e}", "ERROR")
        return False

async def test_workflow_paths():
    """测试工作流路径"""
    log("测试工作流路径...", "TITLE")
    
    try:
        from backend.ai import AIWorkflowManager
        
        workflow = AIWorkflowManager()
        
        # 测试不同类型的查询
        test_queries = [
            ("问候查询", "你好"),
            ("技术分析", "分析AAPL的MACD指标"),
            ("股票信息", "告诉我苹果公司的基本信息"),
            ("计算请求", "计算1+1等于多少")
        ]
        
        for query_type, query in test_queries:
            log(f"测试 {query_type}: {query}", "INFO")
            
            try:
                response_count = 0
                start_time = time.time()
                
                async for response in workflow.process_user_query(query):
                    response_count += 1
                    response_type = response.get('type', 'unknown')
                    log(f"  响应 {response_count}: {response_type}", "INFO")
                    
                    if response_count >= 3:  # 限制响应数量
                        break
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                log(f"  处理时间: {processing_time:.2f}秒, 响应数: {response_count}", "SUCCESS")
                
            except Exception as e:
                log(f"  查询失败: {e}", "ERROR")
        
        return True
        
    except Exception as e:
        log(f"工作流路径测试失败: {e}", "ERROR")
        return False

async def analyze_system_architecture():
    """分析系统架构"""
    log("分析系统架构...", "TITLE")
    
    try:
        from backend.ai.graph import build_graph
        
        graph = build_graph()
        
        # 获取图信息
        try:
            graph_info = graph.get_graph()
            if hasattr(graph_info, 'nodes'):
                nodes = list(graph_info.nodes.keys())
                log(f"图节点总数: {len(nodes)}", "INFO")
                log(f"节点列表: {', '.join(nodes)}", "INFO")
                
                # 分析节点类型
                node_types = {
                    "协调节点": ["coordinator"],
                    "分析节点": ["technical_analyst", "researcher", "background_investigator"],
                    "执行节点": ["coder", "planner", "research_team"],
                    "输出节点": ["reporter"],
                    "交互节点": ["human_feedback"]
                }
                
                for node_type, type_nodes in node_types.items():
                    available = [node for node in type_nodes if node in nodes]
                    log(f"{node_type}: {available}", "SUCCESS" if available else "WARNING")
            else:
                log("无法获取节点详细信息", "WARNING")
        except Exception as e:
            log(f"图信息获取失败: {e}", "WARNING")
        
        # 检查内存管理
        try:
            from backend.ai.graph import build_graph_with_memory
            memory_graph = build_graph_with_memory()
            log("内存持久化: 支持", "SUCCESS")
        except Exception:
            log("内存持久化: 不支持", "WARNING")
        
        return True
        
    except Exception as e:
        log(f"系统架构分析失败: {e}", "ERROR")
        return False

async def performance_benchmark():
    """性能基准测试"""
    log("执行性能基准测试...", "TITLE")
    
    try:
        from backend.ai import AIWorkflowManager
        
        workflow = AIWorkflowManager()
        
        # 并发测试
        async def process_query(query_id):
            start_time = time.time()
            response_count = 0
            
            async for response in workflow.process_user_query(f"测试查询 {query_id}"):
                response_count += 1
                if response_count >= 2:
                    break
            
            end_time = time.time()
            return end_time - start_time
        
        # 运行并发测试
        concurrent_tasks = 3
        log(f"运行 {concurrent_tasks} 个并发查询...", "INFO")
        
        tasks = [process_query(i) for i in range(concurrent_tasks)]
        times = await asyncio.gather(*tasks)
        
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        log(f"平均响应时间: {avg_time:.2f}秒", "INFO")
        log(f"最大响应时间: {max_time:.2f}秒", "INFO")
        log(f"最小响应时间: {min_time:.2f}秒", "INFO")
        
        # 性能评级
        if avg_time < 5:
            log("并发性能: 优秀", "SUCCESS")
        elif avg_time < 10:
            log("并发性能: 良好", "WARNING")
        else:
            log("并发性能: 需要优化", "ERROR")
        
        return True
        
    except Exception as e:
        log(f"性能基准测试失败: {e}", "ERROR")
        return False

async def main():
    """主函数"""
    log("多智能体系统状态检查开始", "TITLE")
    print("="*60)
    
    checks = [
        ("依赖项检查", check_dependencies),
        ("LLM 性能分析", analyze_llm_performance),
        ("智能体能力检查", check_agents_capability),
        ("工作流路径测试", test_workflow_paths),
        ("系统架构分析", analyze_system_architecture),
        ("性能基准测试", performance_benchmark),
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        print(f"\n{'-'*50}")
        try:
            success = await check_func()
            results[check_name] = "通过" if success else "失败"
        except Exception as e:
            log(f"{check_name} 发生异常: {e}", "ERROR")
            results[check_name] = "异常"
    
    # 汇总报告
    print(f"\n{'='*60}")
    log("系统状态汇总报告", "TITLE")
    print("="*60)
    
    for check_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌" if result == "失败" else "⚠️"
        print(f"{status_icon} {check_name}: {result}")
    
    # 总体评价
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    print(f"\n📊 总体状况: {passed}/{total} 项检查通过")
    
    if passed == total:
        log("🎉 系统运行状况优秀！", "SUCCESS")
    elif passed >= total * 0.8:
        log("👍 系统运行状况良好", "SUCCESS")
    elif passed >= total * 0.6:
        log("⚠️ 系统运行状况一般，建议检查", "WARNING")
    else:
        log("🚨 系统存在较多问题，需要修复", "ERROR")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n检查被中断")
    except Exception as e:
        print(f"检查运行错误: {e}")
        traceback.print_exc() 