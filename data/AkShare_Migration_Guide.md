# AkShare数据源迁移指南

## 概述

本项目已从Tushare数据源迁移到AkShare数据源，以解决股票搜索推荐功能中股票信息不完整的问题。AkShare提供了更全面的股票数据，且无需API Token即可使用。

## 主要变化

### 1. 数据源优先级调整
- **主要数据源**: AkShare（无需Token）
- **备用数据源**: Tushare（可选，需要Token）

### 2. 支持的市场
- **中国A股**: 使用AkShare的`stock_zh_a_spot_em`和`stock_zh_a_hist`接口
- **美股**: 使用AkShare的`stock_us_famous_spot_em`和`stock_us_daily`接口  
- **港股**: 使用AkShare的`stock_hk_famous_spot_em`和`stock_hk_hist`接口

### 3. API接口变化
- `tushare_token`参数现在是可选的
- 所有数据API优先使用AkShare
- 保持向后兼容性

## 安装要求

确保已安装akshare库：
```bash
pip install akshare
```

## 股票推荐功能修复

### 问题原因
之前股票推荐功能缺失很多股票名称是因为：
1. 数据库中股票基本信息不完整
2. 依赖Tushare的股票列表可能有覆盖限制
3. 数据初始化不充分

### 解决方案
1. **使用AkShare获取完整股票列表**
   - A股：从东方财富网获取全部沪深京A股列表
   - 美股：获取知名美股列表
   - 港股：获取知名港股列表

2. **改进的数据初始化流程**
   - 自动识别股票类型（A股/美股/港股）
   - 批量下载股票基本信息
   - 建立完整的股票推荐数据库

## 使用方法

### 1. 初始化股票推荐数据
```bash
cd backend
python init_stock_recommendations.py
```

### 2. 测试AkShare功能
```bash
cd backend  
python test_akshare_migration.py
```

### 3. API调用示例

#### 获取股票列表（不需要Token）
```python
# POST /data/stocks
{
    "tushare_token": null  # 可选
}
```

#### 初始化数据（不需要Token）
```python
# POST /data/init  
{
    "tushare_token": null,  # 可选
    "stock_codes": ["000001", "AAPL", "00700.HK"],
    "start_date": "20230101"
}
```

## 股票代码格式

### A股
- 6位数字：`000001`, `600000`
- 带交易所后缀：`000001.SZ`, `600000.SH`

### 美股
- 字母代码：`AAPL`, `MSFT`, `GOOGL`

### 港股
- 5位数字：`00700`
- 带.HK后缀：`00700.HK`, `09988.HK`

## 数据质量改进

### AkShare vs Tushare对比

| 特性 | AkShare | Tushare |
|------|---------|---------|
| API Token | 不需要 | 需要 |
| A股覆盖 | 全部沪深京A股 | 需要权限 |
| 美股覆盖 | 知名美股 | 有限制 |
| 港股覆盖 | 知名港股 | 需要权限 |
| 访问限制 | 较少 | 较多 |
| 数据质量 | 高 | 高 |

### 股票推荐覆盖范围
- **A股**: 5000+ 只股票（全市场覆盖）
- **美股**: 100+ 只知名股票
- **港股**: 100+ 只知名股票

## 迁移检查清单

- [ ] 确认akshare库已安装
- [ ] 运行初始化脚本
- [ ] 验证股票推荐功能
- [ ] 测试各市场数据下载
- [ ] 检查前端搜索功能

## 故障排除

### 问题1: AkShare库未安装
```bash
pip install akshare
```

### 问题2: 网络连接问题
- 检查网络连接
- 尝试使用代理
- 重试初始化

### 问题3: 股票推荐为空
```bash
# 重新初始化推荐数据
python backend/init_stock_recommendations.py
```

### 问题4: 特定股票数据无法获取
- 检查股票代码格式
- 确认股票在对应市场存在
- 查看错误日志

## 性能优化建议

1. **定期更新股票列表**
   - 建议每月运行一次初始化脚本
   - 监控新股上市情况

2. **数据缓存策略**
   - 股票基本信息缓存较长时间
   - 价格数据缓存较短时间

3. **错误处理**
   - 网络超时重试机制
   - 数据源切换机制

## 向后兼容性

- 现有的Tushare API调用仍然支持
- `tushare_token`参数保持可选
- 数据库结构保持不变
- 前端接口无需修改

## 联系支持

如果在迁移过程中遇到问题，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查网络连接
4. 验证akshare库版本

---

**迁移完成后，您的股票搜索推荐功能将拥有更全面的股票覆盖和更稳定的数据来源！** 🎉 