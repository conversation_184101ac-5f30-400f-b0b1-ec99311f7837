个股信息查询-雪球
接口: stock_individual_basic_info_xq

目标地址: https://xueqiu.com/snowman/S/SH601127/detail#/GSJJ

描述: 雪球财经-个股-公司概况-公司简介

限量: 单次返回指定 symbol 的个股信息

输入参数

名称	类型	描述
symbol	str	symbol="SH601127"; 股票代码
token	str	token=None;
timeout	float	timeout=None; 默认不设置超时参数
输出参数

名称	类型	描述
item	object	-
value	object	-
接口示例

import akshare as ak

stock_individual_basic_info_xq_df = ak.stock_individual_basic_info_xq(symbol="SH601127")
print(stock_individual_basic_info_xq_df)
数据示例

                            item                                              value
0                         org_id                                         T000071215
1                    org_name_cn                                        赛力斯集团股份有限公司
2              org_short_name_cn                                                赛力斯
3                    org_name_en                               Seres Group Co.,Ltd.
4              org_short_name_en                                              SERES
5        main_operation_business      新能源汽车及核心三电(电池、电驱、电控)、传统汽车及核心部件总成的研发、制造、销售及服务。
6                operating_scope  　　一般项目：制造、销售：汽车零部件、机动车辆零部件、普通机械、电器机械、电器、电子产品（不...
7                district_encode                                             500106
8            org_cn_introduction  赛力斯始创于1986年，是以新能源汽车为核心业务的技术科技型汽车企业。现有员工1.6万人，A...
9           legal_representative                                                张正萍
10               general_manager                                                张正萍
11                     secretary                                                 申薇
12              established_date                                      1178812800000
13                     reg_asset                                       1509782193.0
14                     staff_num                                              16102
15                     telephone                                     86-23-65179666
16                      postcode                                             401335
17                           fax                                     86-23-65179777
18                         email                                    <EMAIL>
19                   org_website                                   www.seres.com.cn
20                reg_address_cn                                      重庆市沙坪坝区五云湖路7号
21                reg_address_en                                               None
22             office_address_cn                                      重庆市沙坪坝区五云湖路7号
23             office_address_en                                               None
24               currency_encode                                             019001
25                      currency                                                CNY
26                   listed_date                                      1465920000000
27               provincial_name                                                重庆市
28             actual_controller                                       张兴海 (13.79%)
29                   classi_name                                               民营企业
30                   pre_name_cn                                     重庆小康工业集团股份有限公司
31                      chairman                                                张正萍
32               executives_nums                                                 20
33              actual_issue_vol                                        142500000.0
34                   issue_price                                               5.81
35             actual_rc_net_amt                                        *********.0
36              pe_after_issuing                                              18.19
37  online_success_rate_of_issue                                           0.110176
38            affiliate_industry         {'ind_code': 'BK0025', 'ind_name': '汽车整车'}


历史行情数据-新浪
接口: stock_zh_a_daily

P.S. 建议切换为 stock_zh_a_hist 接口使用(该接口数据质量高, 访问无限制)

目标地址: https://finance.sina.com.cn/realstock/company/sh600006/nc.shtml(示例)

描述: 新浪财经-沪深京 A 股的数据, 历史数据按日频率更新; 注意其中的 sh689009 为 CDR, 请 通过 ak.stock_zh_a_cdr_daily 接口获取

限量: 单次返回指定沪深京 A 股上市公司指定日期间的历史行情日频率数据, 多次获取容易封禁 IP

输入参数

名称	类型	描述
symbol	str	symbol='sh600000'; 股票代码可以在 ak.stock_zh_a_spot() 中获取
start_date	str	start_date='20201103'; 开始查询的日期
end_date	str	end_date='20201116'; 结束查询的日期
adjust	str	默认返回不复权的数据; qfq: 返回前复权后的数据; hfq: 返回后复权后的数据; hfq-factor: 返回后复权因子; qfq-factor: 返回前复权因子
股票数据复权

1.为何要复权：由于股票存在配股、分拆、合并和发放股息等事件，会导致股价出现较大的缺口。 若使用不复权的价格处理数据、计算各种指标，将会导致它们失去连续性，且使用不复权价格计算收益也会出现错误。 为了保证数据连贯性，常通过前复权和后复权对价格序列进行调整。

2.前复权：保持当前价格不变，将历史价格进行增减，从而使股价连续。 前复权用来看盘非常方便，能一眼看出股价的历史走势，叠加各种技术指标也比较顺畅，是各种行情软件默认的复权方式。 这种方法虽然很常见，但也有两个缺陷需要注意。

2.1 为了保证当前价格不变，每次股票除权除息，均需要重新调整历史价格，因此其历史价格是时变的。 这会导致在不同时点看到的历史前复权价可能出现差异。

2.2 对于有持续分红的公司来说，前复权价可能出现负值。

3.后复权：保证历史价格不变，在每次股票权益事件发生后，调整当前的股票价格。 后复权价格和真实股票价格可能差别较大，不适合用来看盘。 其优点在于，可以被看作投资者的长期财富增长曲线，反映投资者的真实收益率情况。

4.在量化投资研究中普遍采用后复权数据。

输出参数-历史行情数据

名称	类型	描述
date	object	交易日
open	float64	开盘价
high	float64	最高价
low	float64	最低价
close	float64	收盘价
volume	float64	成交量; 注意单位: 股
amount	float64	成交额; 注意单位: 元
outstanding_share	float64	流动股本; 注意单位: 股
turnover	float64	换手率=成交量/流动股本
接口示例-历史行情数据(前复权)

import akshare as ak

stock_zh_a_daily_qfq_df = ak.stock_zh_a_daily(symbol="sz000001", start_date="19910403", end_date="20231027", adjust="qfq")
print(stock_zh_a_daily_qfq_df)
数据示例-历史行情数据(前复权)

           date   open   high  ...        amount  outstanding_share  turnover
0    1991-04-03   0.39   0.39  ...  5.000000e+03       3.710000e+07  0.000003
1    1991-04-04   0.39   0.39  ...  1.500000e+04       3.710000e+07  0.000008
2    1991-04-05   0.38   0.38  ...  1.000000e+04       3.710000e+07  0.000005
3    1991-04-08   0.38   0.38  ...  1.000000e+04       3.710000e+07  0.000005
4    1991-04-09   0.38   0.38  ...  1.900000e+04       3.710000e+07  0.000011
         ...    ...    ...  ...           ...                ...       ...
7730 2023-10-23  10.59  10.60  ...  5.570322e+08       1.940555e+10  0.002733
7731 2023-10-24  10.54  10.61  ...  8.015284e+08       1.940555e+10  0.003920
7732 2023-10-25  10.51  10.54  ...  1.470972e+09       1.940555e+10  0.007273
7733 2023-10-26  10.31  10.42  ...  6.219153e+08       1.940555e+10  0.003092
7734 2023-10-27  10.38  10.48  ...  9.575875e+08       1.940555e+10  0.004740
[7735 rows x 9 columns]

沪深京 A 股
接口: stock_zh_a_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#hs_a_board

描述: 东方财富网-沪深京 A 股-实时行情数据

限量: 单次返回所有沪深京 A 股上市公司的实时行情数据

输入参数

名称	类型	描述
-	-	-
输出参数

名称	类型	描述
序号	int64	-
代码	object	-
名称	object	-
最新价	float64	-
涨跌幅	float64	注意单位: %
涨跌额	float64	-
成交量	float64	注意单位: 手
成交额	float64	注意单位: 元
振幅	float64	注意单位: %
最高	float64	-
最低	float64	-
今开	float64	-
昨收	float64	-
量比	float64	-
换手率	float64	注意单位: %
市盈率-动态	float64	-
市净率	float64	-
总市值	float64	注意单位: 元
流通市值	float64	注意单位: 元
涨速	float64	-
5分钟涨跌	float64	注意单位: %
60日涨跌幅	float64	注意单位: %
年初至今涨跌幅	float64	注意单位: %
接口示例

import akshare as ak

stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()
print(stock_zh_a_spot_em_df)
数据示例

        序号 代码    名称    最新价  ...   涨速 5分钟涨跌  60日涨跌幅  年初至今涨跌幅
0        1  836149  旭杰科技   7.31  ...  0.00   0.00   28.47    -9.53
1        2  300767  震安科技   9.28  ...  0.00   0.00   -9.55   -46.61
2        3  300125  ST聆达   4.67  ...  0.00   0.00  177.98   -62.97
3        4  301027  华蓝集团   8.93  ...  0.00   0.00   17.81   -28.56
4        5  300584  海辰药业  23.26  ...  0.00   0.00   42.44     0.13
...    ...     ...   ...    ...  ...   ...    ...     ...      ...
5630  5631  300531   优博讯  12.45  ... -0.16  -0.24   25.50   -18.52
5631  5632  301302  华如科技  13.47  ...  0.07  -0.07  -28.88   -46.36
5632  5633  300496  中科创达  32.30  ... -0.09  -0.12  -44.60   -59.53
5633  5634  300050  世纪鼎利   5.04  ... -0.20   0.60   72.01     6.55
5634  5635  832175  东方碳素   6.48  ...  0.00   0.00  -21.64   -47.57
[5635 rows x 23 columns]


历史行情数据-新浪
接口: stock_us_daily

目标地址: http://finance.sina.com.cn/stock/usstock/sector.shtml

描述: 美股历史行情数据，设定 adjust="qfq" 则返回前复权后的数据，默认 adjust="", 则返回未复权的数据，历史数据按日频率更新

限量: 单次返回指定上市公司的指定 adjust 后的所有历史行情数据

输入参数

名称	类型	描述
symbol	str	美股代码, 可以通过 ak.get_us_stock_name() 函数返回所有美股代码, 由于美股数据量大, 建议按需要获取
adjust	str	adjust="qfq" 则返回前复权后的数据，默认 adjust="", 则返回未复权的数据
ak.get_us_stock_name(): will return a pandas.DataFrame, which contains name, cname and symbol, you should use symbol!

输出参数-历史数据

名称	类型	描述
date	datetime64	-
open	float64	开盘价
high	float64	最高价
low	float64	最低价
close	float64	收盘价
volume	float64	成交量
输出参数-前复权因子

名称	类型	描述
date	datetime64	日期
qfq_factor	float	前复权因子
adjust	float	由于前复权会出现负值, 该值为调整因子
P.S. 复权计算公式: 未复权数据 * qfq_factor + adjust
接口示例-前复权调整后的数据

import akshare as ak

stock_us_daily_df = ak.stock_us_daily(symbol="AAPL", adjust="qfq")
print(stock_us_daily_df)
数据示例-前复权调整后的数据

 date          open      high       low     close       volume
1980-12-12   -4.8164   -4.8158   -4.8164   -4.8164    2093900.0
1980-12-15   -4.8230   -4.8225   -4.8230   -4.8230     785200.0
1980-12-16   -4.8321   -4.8314   -4.8321   -4.8321     472000.0
1980-12-17   -4.8293   -4.8286   -4.8293   -4.8293     385900.0
1980-12-18   -4.8258   -4.8253   -4.8258   -4.8258     327900.0
              ...       ...       ...       ...          ...
2021-01-04  133.5200  133.6116  126.7600  129.4100  134308607.0
2021-01-05  128.8900  131.7400  128.4300  131.0100   90749416.0
2021-01-06  127.7200  131.0499  126.3820  126.6000  139351145.0
2021-01-07  128.3600  131.6300  127.8600  130.9200  101546989.0
2021-01-08  132.4300  132.6300  130.2300  132.0500   98373014.0


知名美股
接口: stock_us_famous_spot_em

目标地址: http://quote.eastmoney.com/center/gridlist.html#us_wellknown

描述: 美股-知名美股的实时行情数据

限量: 单次返回指定 symbol 的行情数据

输入参数

名称	类型	描述
symbol	str	symbol="科技类"; choice of {'科技类', '金融类', '医药食品类', '媒体类', '汽车能源类', '制造零售类'}
输出参数

名称	类型	描述
序号	int64	-
名称	object	-
最新价	float64	注意单位: 美元
涨跌额	float64	注意单位: 美元
涨跌幅	float64	注意单位: %
开盘价	float64	注意单位: 美元
最高价	float64	注意单位: 美元
最低价	float64	注意单位: 美元
昨收价	float64	注意单位: 美元
总市值	float64	注意单位: 美元
市盈率	float64	-
代码	object	注意: 用来获取历史数据的代码
接口示例

import akshare as ak

stock_us_famous_spot_em_df = ak.stock_us_famous_spot_em(symbol='科技类')
print(stock_us_famous_spot_em_df)
数据示例

    序号              名称           最新价  ...         总市值     市盈率        代码
0    1  Silvergate Capital Corp-A   116.34  ...     3085409047   61.93    106.SI
1    2  Opendoor Technologies Inc    18.94  ...    11451903533  -19.65  105.OPEN
2    3                     阿勒格尼技术    17.55  ...     2233160842   -1.82   106.ATI
3    4                Yandex NV-A    78.71  ...    28129406798  131.13  105.YNDX
4    5                        爱立信    11.83  ...    39443015025   16.44  105.ERIC
5    6                        诺基亚     5.91  ...    33269348763  -14.11   106.NOK
6    7              Groupon Inc-A    22.16  ...      654264338   73.65  105.GRPN
7    8                         推特    62.46  ...    49840992399  129.65  106.TWTR
8    9             Facebook Inc-A   378.00  ...  1065750021378   27.36    105.FB
9   10                         惠普    28.21  ...    32512553599    7.98   106.HPQ
10  11                       谷歌-C  2898.27  ...  1932435735616   16.84  105.GOOG
11  12                      阿卡迈技术   113.38  ...    18461647373   31.60  105.AKAM
12  13                      超威半导体   106.15  ...   128756264684   37.47   105.AMD
13  14                         思科    58.60  ...   247159324736   23.34  105.CSCO
14  15                       中华电信    40.05  ...    31068573413   24.84   106.CHT
15  16                       德州仪器   188.47  ...   173997383234   25.87   105.TXN
16  17                        奥多比   661.68  ...   315224352000   56.47  105.ADBE
17  18                        高知特    76.48  ...    40197673856   23.91  105.CTSH
18  19                        英特尔    53.40  ...   216643800000   11.68  105.INTC
19  20                     美国电话电报    27.42  ...   195778800000 -100.55     106.T
20  21                         高通   141.58  ...   159702240000   17.35  105.QCOM
21  22                         苹果   154.07  ...  2546802675620   29.34  105.AAPL
22  23              IBM国际商业机器(US)   137.74  ...   123459126717   23.15   106.IBM
23  24                         陶氏    60.28  ...    44955123381   10.96   106.DOW
24  25                        思爱普   145.68  ...   171841118251   23.58   106.SAP
25  26                        英伟达   221.77  ...   554425000000   78.33  105.NVDA
26  27                      威瑞森通讯    54.44  ...   225387915421   11.28    106.VZ
27  28                         微软   297.25  ...  2233801423468   36.46  105.MSFT
28  29                   摩托罗拉解决方案   244.00  ...    41315202400   35.80   106.MSI
29  30                        亚马逊  3484.16  ...  1764519802163   59.94  105.AMZN
30  31                         易趣    73.00  ...    47454073765    3.68  105.EBAY
31  32                    沃达丰(US)    16.61  ...    46152628937  351.55   105.VOD
32  33                Zynga Inc-A     8.28  ...     9040854574  -53.06  105.ZNGA
33  34          SentinelOne Inc-A    66.04  ...    16930759265  -85.17     106.S


个股信息查询-雪球
接口: stock_individual_basic_info_hk_xq

目标地址: https://xueqiu.com/S/00700

描述: 雪球-个股-公司概况-公司简介

限量: 单次返回指定 symbol 的个股信息

输入参数

名称	类型	描述
symbol	str	symbol="02097"; 股票代码
token	str	token=None;
timeout	float	timeout=None; 默认不设置超时参数
输出参数

名称	类型	描述
item	object	-
value	object	-
接口示例

import akshare as ak

stock_individual_basic_info_hk_xq_df = ak.stock_individual_basic_info_hk_xq(symbol="02097")
print(stock_individual_basic_info_hk_xq_df)
数据示例

           item                                              value
0       comunic                                        231269720.0
1     comcnname                                         蜜雪冰城股份有限公司
2     comenname                                        MIXUE Group
3       incdate                                    1209484800000.0
4        rgiofc                 中国河南省郑州市金水区北三环南、文化路东瀚海北金商业中心16004室
5    hofclctmbu                 中国河南省郑州市金水区北三环南、文化路东瀚海北金商业中心16004室
6      chairman                                                张红超
7           mbu                                             现制饮品企业
8       comintr  我们是一家领先的现制饮品企业,聚焦为广大消费者提供单价约6元人民币(约1美元)的高质平价的现...
9     refccomty                                                1.0
10     numtissh                                         17059900.0
11         ispr                                              202.5
12         nrfd                                       3291000000.0
13  nation_name                                                 中国
14          tel                                      0371-89834090
15          fax                                      0371-89916887
16        email                                <EMAIL>
17     web_site                                http://www.mxbc.com
18    lsdateipo                                    1740931200000.0
19   mainholder                                                张红超


历史行情数据-东财
接口: stock_hk_hist

目标地址: https://quote.eastmoney.com/hk/08367.html

描述: 港股-历史行情数据, 可以选择返回复权后数据, 更新频率为日频

限量: 单次返回指定上市公司的历史行情数据

输入参数

名称	类型	描述
symbol	str	symbol="00593"; 港股代码,可以通过 ak.stock_hk_spot_em() 函数返回所有港股代码
period	str	period='daily'; choice of {'daily', 'weekly', 'monthly'}
start_date	str	start_date="19700101"; 开始日期
end_date	str	end_date="22220101"; 结束日期
adjust	str	adjust="": 返回未复权的数据, 默认; qfq: 返回前复权数据; hfq: 返回后复权数据;
输出参数

名称	类型	描述
日期	object	-
开盘	float64	注意单位: 港元
收盘	float64	注意单位: 港元
最高	float64	注意单位: 港元
最低	float64	注意单位: 港元
成交量	int64	注意单位: 股
成交额	float64	注意单位: 港元
振幅	float64	注意单位: %
涨跌幅	float64	注意单位: %
涨跌额	float64	注意单位: 港元
换手率	float64	注意单位: %
输出参数-前复权

名称	类型	描述
日期	object	-
开盘	float64	注意单位: 港元
收盘	float64	注意单位: 港元
最高	float64	注意单位: 港元
最低	float64	注意单位: 港元
成交量	int64	注意单位: 股
成交额	float64	注意单位: 港元
振幅	float64	注意单位: %
涨跌幅	float64	注意单位: %
涨跌额	float64	注意单位: 港元
换手率	float64	注意单位: %
接口示例-前复权

import akshare as ak

stock_hk_hist_qfq_df = ak.stock_hk_hist(symbol="00593", period="daily", start_date="19700101", end_date="22220101", adjust="qfq")
print(stock_hk_hist_qfq_df)
数据示例-前复权

       日期     开盘     收盘     最高  ...     振幅    涨跌幅   涨跌额   换手率
0     1998-01-02  1.713  1.713  1.863  ...   0.00   0.00  0.00  0.45
1     1998-01-05  1.663  1.463  1.663  ...  11.68 -14.59 -0.25  1.47
2     1998-01-06  1.513  1.513  1.663  ...  10.25   3.42  0.05  0.69
3     1998-01-07  1.513  1.413  1.513  ...   6.61  -6.61 -0.10  1.05
4     1998-01-08  1.463  1.413  1.463  ...  10.62   0.00  0.00  0.53
          ...    ...    ...    ...  ...    ...    ...   ...   ...
4770  2023-11-13  1.800  1.390  1.800  ...  31.65 -12.03 -0.19  0.02
4771  2023-11-14  1.310  1.260  1.580  ...  34.53  -9.35 -0.13  5.09
4772  2023-11-15  1.400  1.350  1.400  ...  13.49   7.14  0.09  5.35
4773  2023-11-16  1.350  1.380  1.390  ...   3.70   2.22  0.03  4.00
4774  2023-11-17  1.380  1.470  1.470  ...   6.52   6.52  0.09  1.74
[4775 rows x 11 columns]

知名港股
接口: stock_hk_famous_spot_em

目标地址: https://quote.eastmoney.com/center/gridlist.html#hk_wellknown

描述: 东方财富网-行情中心-港股市场-知名港股实时行情数据

限量: 单次返回全部行情数据

输入参数

名称	类型	描述
-	-	-
输出参数

名称	类型	描述
序号	int64	-
代码	object	-
名称	object	-
最新价	float64	注意单位: 港元
涨跌额	float64	注意单位: 港元
涨跌幅	float64	注意单位: %
今开	float64	注意单位: 港元
最高	float64	注意单位: 港元
最低	float64	注意单位: 港元
昨收	float64	注意单位: 港元
成交量	float64	注意单位: 股
成交额	float64	注意单位: 港元
接口示例

import akshare as ak

stock_hk_famous_spot_em_df = ak.stock_hk_famous_spot_em()
print(stock_hk_famous_spot_em_df)
数据示例

      序号 代码       名称    最新价  ...    最低     昨收    成交量           成交额
0      1  01918      融创中国   2.04  ...   1.91   1.91  633638656.0  1.295074e+09
1      2  00763      中兴通讯  34.65  ...  31.50  32.85   90643056.0  3.088137e+09
2      3  00753      中国国航   4.56  ...   4.25   4.33   34639744.0  1.560062e+08
3      4  01928  金沙中国有限公司  18.08  ...  17.20  17.18   37260253.0  6.721171e+08
4      5  03900      绿城中国  10.38  ...   9.89   9.91   33031905.0  3.421672e+08
..   ...    ...       ...    ...  ...    ...    ...          ...           ...
113  114  02400      心动公司  32.15  ...  31.60  34.20    4769000.0  1.544492e+08
114  115  01833     平安好医生   8.68  ...   8.55   9.31   53910271.0  4.845739e+08
115  116  02269      药明生物  23.65  ...  23.10  26.00  170040773.0  4.052070e+09
116  117  02359      药明康德  62.90  ...  62.40  70.00   20403989.0  1.314193e+09
117  118  09698   万国数据-SW  42.70  ...  41.30  48.30   24768786.0  1.068253e+09
[118 rows x 12 columns]