#!/bin/bash
# 环境变量设置脚本

echo "🔧 配置AI智能体Backend环境变量"
echo "================================"

# 检查是否已有.env文件
if [ -f ".env" ]; then
    echo "⚠️  发现现有.env文件，是否要备份？(y/N)"
    read -r backup_choice
    if [[ $backup_choice =~ ^[Yy]$ ]]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        echo "✅ 已备份为 .env.backup.$(date +%Y%m%d_%H%M%S)"
    fi
fi

echo ""
echo "请输入您的API密钥和Token："

# 获取Gemini API Key
echo -n "🤖 Gemini API Key (用于AI智能体功能): "
read -r gemini_key

# 获取Tushare Token
echo -n "📊 Tushare Token (用于股票数据): "
read -r tushare_token

# 获取可选配置
echo -n "🌐 Gemini Base URL (可选，留空使用默认): "
read -r gemini_base_url
if [ -z "$gemini_base_url" ]; then
    gemini_base_url="https://generativelanguage.googleapis.com/v1beta"
fi

echo -n "🧠 Gemini Model (可选，留空使用gemini-2.0-flash): "
read -r gemini_model
if [ -z "$gemini_model" ]; then
    gemini_model="gemini-2.0-flash"
fi

# 创建.env文件
cat > .env << EOF
# AI智能体配置
GEMINI_API_KEY=$gemini_key
GEMINI_BASE_URL=$gemini_base_url
GEMINI_MODEL=$gemini_model

# 数据源配置
TUSHARE_TOKEN=$tushare_token

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000

# 日志配置
LOG_LEVEL=INFO
EOF

echo ""
echo "✅ 环境变量配置完成！"
echo ""
echo "📝 创建的配置文件内容："
echo "================================"
cat .env | sed 's/GEMINI_API_KEY=.*/GEMINI_API_KEY=***hidden***/' | sed 's/TUSHARE_TOKEN=.*/TUSHARE_TOKEN=***hidden***/'
echo "================================"
echo ""
echo "💡 接下来请运行："
echo "   source .env  # 加载环境变量"
echo "   ./start_backend_integrated.sh  # 启动后端服务"
echo ""
echo "🔒 安全提醒：请妥善保管您的API密钥，不要提交到版本控制系统" 