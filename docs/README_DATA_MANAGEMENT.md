# 📊 数据管理系统使用指南

## 🎯 系统概述

数据管理系统是金融投资助手平台的核心组件，负责：
- 📥 **数据下载**: 通过Tushare API下载股票OHLCV数据
- 💾 **数据存储**: 使用SQLite数据库高效存储
- 🧮 **因子计算**: 自动计算36个量化因子
- 🔄 **数据同步**: 支持增量更新和实时计算
- 📡 **API接口**: 提供统一的数据访问接口

## 🗄️ 数据库设计

### 表结构

```sql
-- 股票基本信息表
CREATE TABLE stock_info (
    ts_code TEXT PRIMARY KEY,        -- 股票代码
    symbol TEXT NOT NULL,            -- 交易代码
    name TEXT,                       -- 股票名称
    market TEXT,                     -- 市场（NASDAQ/NYSE等）
    industry TEXT,                   -- 行业分类
    created_at TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP             -- 更新时间
);

-- 日线数据表
CREATE TABLE daily_data (
    id INTEGER PRIMARY KEY,
    ts_code TEXT NOT NULL,           -- 股票代码
    trade_date TEXT NOT NULL,        -- 交易日期（YYYYMMDD）
    open REAL,                       -- 开盘价
    high REAL,                       -- 最高价
    low REAL,                        -- 最低价
    close REAL,                      -- 收盘价
    pre_close REAL,                  -- 昨收价
    change_amount REAL,              -- 涨跌额
    pct_change REAL,                 -- 涨跌幅(%)
    vol REAL,                        -- 成交量
    amount REAL,                     -- 成交额
    vwap REAL,                       -- 平均价
    turnover_ratio REAL,             -- 换手率
    total_mv REAL,                   -- 总市值
    pe REAL,                         -- 市盈率
    pb REAL,                         -- 市净率
    created_at TIMESTAMP,            -- 入库时间
    UNIQUE(ts_code, trade_date)      -- 唯一约束
);

-- 因子数据表
CREATE TABLE factor_data (
    id INTEGER PRIMARY KEY,
    ts_code TEXT NOT NULL,           -- 股票代码
    trade_date TEXT NOT NULL,        -- 交易日期
    factor_name TEXT NOT NULL,       -- 因子名称
    factor_value REAL,               -- 因子值
    calculation_date TIMESTAMP,      -- 计算时间
    UNIQUE(ts_code, trade_date, factor_name)  -- 唯一约束
);

-- 数据更新记录表
CREATE TABLE data_update_log (
    id INTEGER PRIMARY KEY,
    ts_code TEXT NOT NULL,           -- 股票代码
    data_type TEXT NOT NULL,         -- 数据类型
    start_date TEXT,                 -- 开始日期
    end_date TEXT,                   -- 结束日期
    record_count INTEGER,            -- 记录数量
    status TEXT,                     -- 状态（success/failed）
    error_message TEXT,              -- 错误信息
    update_time TIMESTAMP            -- 更新时间
);
```

### 索引优化

```sql
-- 主要索引（自动创建）
CREATE INDEX idx_daily_data_ts_code ON daily_data(ts_code);
CREATE INDEX idx_daily_data_trade_date ON daily_data(trade_date);
CREATE INDEX idx_daily_data_ts_code_date ON daily_data(ts_code, trade_date);
CREATE INDEX idx_factor_data_composite ON factor_data(ts_code, trade_date, factor_name);
```

## 🚀 快速开始

### 1. 准备工作

```bash
# 1. 获取Tushare Token
# 访问 https://tushare.pro 注册账号并获取token

# 2. 安装依赖
pip install tushare pandas numpy sqlite3

# 3. 创建数据目录
mkdir -p data
```

### 2. 使用示例

```python
from data_manager import init_data_manager

# 初始化数据管理器
TUSHARE_TOKEN = "your_token_here"
data_manager = init_data_manager(TUSHARE_TOKEN)

# 下载股票数据
stock_codes = ['AAPL', 'MSFT', 'GOOGL']
start_date = '20240101'  # 开始日期
data_manager.initialize_data(stock_codes, start_date)

# 获取股票数据
df = data_manager.get_stock_data('AAPL')
print(f"获取到 {len(df)} 天的数据")

# 获取因子数据
factors = data_manager.get_factor_data('AAPL')
print(f"获取到 {len(factors)} 个因子")
```

### 3. 运行示例脚本

```bash
cd backend
python data_example.py
```

## 📡 API接口文档

### 数据初始化

```http
POST /data/init
Content-Type: application/json

{
    "tushare_token": "your_token",
    "stock_codes": ["AAPL", "MSFT", "GOOGL"],
    "start_date": "20240101"
}
```

### 数据更新

```http
POST /data/update
Content-Type: application/json

{
    "stock_codes": ["AAPL", "MSFT"]
}
```

### 获取股票数据

```http
POST /data/stock/data
Content-Type: application/json

{
    "ts_code": "AAPL",
    "start_date": "20240101",
    "end_date": "20241231"
}
```

### 获取因子数据

```http
POST /data/factors
Content-Type: application/json

{
    "ts_code": "AAPL",
    "factor_names": ["rsi", "macd", "bollinger"]
}
```

### 查看数据库状态

```http
GET /data/database/info
```

## 🧮 支持的因子

### 技术面因子 (Technical)
- **RSI**: 相对强弱指标
- **MACD**: 指数平滑移动平均线
- **Bollinger**: 布林带位置
- **MA20/MA50**: 移动平均线
- **ATR**: 平均真实波动率
- **Volume_MA**: 成交量移动平均
- **Price_Change**: 价格变化率
- **Volatility**: 波动率

### 基本面因子 (Fundamental)
- **PE_Ratio**: 市盈率
- **PB_Ratio**: 市净率
- **ROE**: 净资产收益率
- **ROA**: 总资产收益率
- **Debt_Ratio**: 资产负债率

### 资金面因子 (Capital)
- **Money_Flow**: 资金流向
- **Big_Order_Ratio**: 大单比例
- **Net_Inflow**: 资金净流入
- **Institution_Ratio**: 机构持仓比例

## 🔧 高级功能

### 1. 自定义因子计算

```python
def calculate_custom_factor(df: pd.DataFrame) -> float:
    """自定义因子计算函数"""
    # 你的计算逻辑
    return factor_value

# 注册自定义因子
from factors import FACTOR_CALCULATORS
FACTOR_CALCULATORS['custom_factor'] = calculate_custom_factor
```

### 2. 数据缓存机制

```python
# 数据管理器会自动缓存：
# - 股票价格数据（5分钟缓存）
# - 因子计算结果（数据库持久化）
# - API请求结果（内存缓存）

# 清理缓存
data_manager.factor_calculator.cache.clear()
```

### 3. 批量操作

```python
# 批量计算因子
stock_codes = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
data_manager.factor_calculator.batch_calculate_factors(stock_codes)

# 批量更新数据
data_manager.update_data(stock_codes)
```

## 📊 性能优化

### 1. 数据库优化
- 使用复合索引提高查询性能
- UNIQUE约束避免重复数据
- 分区存储大量历史数据

### 2. 缓存策略
- **L1缓存**: 内存缓存热点数据
- **L2缓存**: 数据库缓存因子结果
- **L3缓存**: 文件缓存原始数据

### 3. 并发处理
- 后台任务处理大批量操作
- 异步API减少响应时间
- 任务队列管理长时间运行的作业

## 🔍 故障排除

### 常见问题

1. **Tushare API调用失败**
   ```
   解决方案：
   - 检查Token是否正确
   - 确认API权限和积分
   - 使用模拟数据作为备用方案
   ```

2. **数据库锁定错误**
   ```
   解决方案：
   - 确保正确关闭数据库连接
   - 使用上下文管理器
   - 检查并发访问情况
   ```

3. **因子计算错误**
   ```
   解决方案：
   - 检查数据完整性
   - 验证计算公式
   - 添加异常处理和默认值
   ```

### 调试技巧

```python
# 开启调试日志
import logging
logging.getLogger().setLevel(logging.DEBUG)

# 检查数据库状态
with data_manager.db_manager.get_connection() as conn:
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print("数据库表:", tables)

# 验证因子计算
factors = data_manager.factor_calculator.calculate_all_factors('AAPL')
print("计算的因子:", list(factors.keys()))
```

## 📈 数据质量保证

### 1. 数据验证
- 价格数据合理性检查
- 成交量异常值过滤
- 因子值范围验证

### 2. 数据修复
- 自动填充缺失值
- 异常值平滑处理
- 数据一致性检查

### 3. 监控指标
- 数据更新成功率
- 因子计算准确性
- API响应时间

## 🔄 数据更新策略

### 1. 全量更新
```python
# 适用于初始化或重建数据
data_manager.initialize_data(stock_codes, start_date='20200101')
```

### 2. 增量更新
```python
# 适用于日常数据维护
data_manager.update_data(stock_codes)  # 自动获取最近7天数据
```

### 3. 实时计算
```python
# 适用于实时分析需求
factors = factor_calculator.calculate_all_factors('AAPL')  # 不使用缓存
```

## 📝 最佳实践

1. **定期备份数据库**
   ```bash
   cp data/financial_data.db data/backup_$(date +%Y%m%d).db
   ```

2. **监控数据质量**
   ```python
   # 检查数据完整性
   def check_data_quality(symbol):
       df = data_manager.get_stock_data(symbol)
       missing_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
       return missing_ratio < 0.05  # 缺失率小于5%
   ```

3. **优化API调用**
   ```python
   # 批量获取，减少API调用次数
   symbols = ['AAPL', 'MSFT', 'GOOGL']
   for symbol in symbols:
       time.sleep(0.1)  # 避免频率限制
       data_manager.tushare_manager.download_daily_data(symbol)
   ```

---

**注意**: 请确保遵守Tushare的使用条款和API限制。本系统仅用于学习和研究目的，不构成投资建议。 