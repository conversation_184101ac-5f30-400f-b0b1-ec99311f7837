# 💰 金融投资助手 Web 界面

金融投资助手现在提供了类似 Claude 的现代化 Web 界面，支持流式输出和实时交互。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

1. **启动后端服务器**：
   ```bash
   ./start_backend.sh
   ```

2. **启动前端应用**（新开一个终端）：
   ```bash
   ./start_frontend.sh
   ```

### 方法二：手动启动

1. **启动后端**：
   ```bash
   # 确保在虚拟环境中
   source venv/bin/activate  # 或 source .venv/bin/activate
   
   # 安装后端依赖
   pip install fastapi uvicorn
   
   # 启动后端服务器
   cd backend
   python server.py --host 127.0.0.1 --port 8000 --reload
   ```

2. **启动前端**（新开终端）：
   ```bash
   cd frontend
   npm install  # 首次运行需要安装依赖
   npm start
   ```

## 🌐 访问地址

- **前端界面**：http://localhost:3000
- **后端 API**：http://127.0.0.1:8000
- **API 文档**：http://127.0.0.1:8000/docs

## ✨ 功能特性

### 🎨 用户界面
- **类似 Claude 的现代化设计**：简洁、优雅的聊天界面
- **响应式布局**：支持桌面和移动设备
- **流式输出**：实时显示 AI 回复，就像真人打字一样
- **智能体标识**：显示当前回复来自哪个智能体（研究员、编程员、技术分析师等）

### 🤖 多智能体系统
- **协调器**：负责理解用户需求并分配任务
- **规划师**：制定详细的研究计划
- **研究员**：进行信息搜集和分析
- **编程员**：执行数据分析和计算任务
- **技术分析师**：专门进行股票技术分析
- **报告员**：生成最终的综合报告

### 🔄 实时交互
- **Server-Sent Events (SSE)**：实现真正的流式输出
- **实时状态显示**：显示当前处理状态和进度
- **错误处理**：友好的错误提示和重试机制

## 🎯 使用示例

### 内置问题示例
界面提供了几个预设问题，点击即可快速开始：

1. **📈 股票技术分析**：
   ```
   分析苹果公司AAPL的股票技术面表现
   ```

2. **🏥 行业研究**：
   ```
   人工智能在医疗保健领域的最新发展
   ```

3. **🔬 技术研究**：
   ```
   量子计算对密码学的影响
   ```

4. **🌱 趋势分析**：
   ```
   可再生能源技术的最新趋势
   ```

### 自定义问题
您也可以输入任何问题，系统会自动：
1. 分析问题类型
2. 制定研究计划
3. 调用相应的智能体
4. 生成综合报告

## 🛠️ 技术架构

### 后端 (FastAPI)
- **异步处理**：支持高并发请求
- **流式响应**：实时推送处理结果
- **CORS 支持**：允许跨域访问
- **错误处理**：完善的异常处理机制

### 前端 (React + TypeScript)
- **TypeScript**：类型安全的开发体验
- **React Hooks**：现代化的状态管理
- **CSS Grid/Flexbox**：响应式布局
- **Fetch API**：原生的网络请求

### 多智能体工作流
- **LangGraph**：智能体编排和状态管理
- **流式处理**：实时输出中间结果
- **工具集成**：搜索、爬虫、数据分析等工具

## 🔧 配置选项

### 后端配置
在 `backend/server.py` 中可以调整：
- 服务器地址和端口
- CORS 设置
- 日志级别
- 工作流参数

### 前端配置
在 `frontend/src/App.tsx` 中可以调整：
- API 端点地址
- 请求参数
- UI 样式和布局

## 🐛 故障排除

### 常见问题

1. **后端启动失败**：
   - 检查虚拟环境是否激活
   - 确认依赖是否安装：`pip install fastapi uvicorn`
   - 检查端口 8000 是否被占用

2. **前端启动失败**：
   - 确认 Node.js 版本 >= 14
   - 删除 `node_modules` 重新安装：`rm -rf node_modules && npm install`
   - 检查端口 3000 是否被占用

3. **连接错误**：
   - 确认后端服务器正在运行
   - 检查防火墙设置
   - 确认 CORS 配置正确

4. **流式输出不工作**：
   - 检查浏览器是否支持 Server-Sent Events
   - 确认网络连接稳定
   - 查看浏览器开发者工具的网络面板

### 日志查看
- **后端日志**：在后端终端查看实时日志
- **前端日志**：打开浏览器开发者工具的控制台
- **网络请求**：在开发者工具的网络面板查看 API 调用

## 🎉 开始使用

1. 按照上述步骤启动前后端服务
2. 打开浏览器访问 http://localhost:3000
3. 在输入框中输入您的问题
4. 享受流式的 AI 回复体验！

---

**提示**：首次使用时，系统可能需要一些时间来初始化各种工具和模型。请耐心等待，后续使用会更加流畅。 