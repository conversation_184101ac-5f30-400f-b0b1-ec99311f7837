# 🔧 端口冲突问题解决方案

## 📋 问题描述
用户遇到前端 `Failed to fetch` 错误，无法连接到后端服务器。

## 🔍 问题诊断过程

### 初始症状
- 前端显示：`Error: Failed to fetch`
- 错误发生在：`fetchStream` 函数中的 `fetch(url, ...)` 调用
- 调用链：conversation-starter → sendMessage → chatStream → fetchStream

### 诊断步骤
1. **检查后端进程**：发现有 `uv run server.py --reload` 进程在运行
2. **检查端口监听**：发现端口8000没有被监听
3. **测试连接**：`curl http://localhost:8000` 连接被拒绝
4. **发现根本原因**：LongPort MCP 服务器占用了端口8000

## 🎯 根本原因

**端口冲突**：
- **LongPort MCP 服务器** 默认绑定到 `127.0.0.1:8000`
- **deer-flow 后端服务器** 也尝试使用端口8000
- 端口冲突导致 deer-flow 后端无法启动
- 前端配置指向 `http://localhost:8000/api`，但该端口被 LongPort MCP 占用

## ✅ 解决方案

### 1. 更改 deer-flow 后端端口
```bash
# 将后端服务器启动在端口8001
python server.py --port 8001 --reload
```

### 2. 更新前端API配置
```bash
# 更新 .env 文件中的 API URL
NEXT_PUBLIC_API_URL="http://localhost:8001/api"
```

### 3. 验证端口分配
- **端口 8000**: LongPort MCP 服务器
- **端口 8001**: deer-flow 后端服务器  
- **端口 3000**: deer-flow 前端服务器

## 🧪 验证结果

### 端口状态检查
```bash
✅ 端口 8000 (LongPort MCP 服务器): 响应正常
✅ 端口 8001 (deer-flow 后端服务器): 响应正常  
✅ 端口 3000 (deer-flow 前端服务器): 响应正常
```

### API连接测试
```bash
✅ 后端服务器响应: 404 (正常，表示服务器运行但路径不存在)
✅ 聊天API响应正常
```

## 🔧 实施的改进

### 1. 自动化配置更新脚本
创建了 `update_api_url.sh` 脚本：
- 自动备份原始配置
- 更新前端API URL
- 显示端口分配状态

### 2. 增强的错误处理
改进了前端错误处理：
- 捕获具体错误信息
- 区分不同类型的错误
- 提供中文错误提示

### 3. 综合测试脚本
创建了 `test_fix.py` 脚本：
- 测试所有端口的状态
- 验证API连接
- 提供详细的诊断信息

## 📊 技术细节

### LongPort MCP 服务器
```bash
# 默认配置
longport-mcp --bind 127.0.0.1:8000

# 查看帮助
longport-mcp -h
```

### deer-flow 后端服务器
```bash
# 新的启动命令
python server.py --port 8001 --reload

# 默认配置（现在会冲突）
python server.py --port 8000 --reload
```

### 前端配置
```javascript
// web/src/core/api/resolve-service-url.ts
export function resolveServiceURL(path: string) {
  let BASE_URL = env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000/api/";
  // 现在指向: http://localhost:8001/api/
}
```

## 🚀 用户操作指南

### 立即操作
1. **重启前端服务器**（如果正在运行）
2. **刷新浏览器页面**
3. **测试发送消息功能**

### 验证步骤
1. 检查是否还有 `Failed to fetch` 错误
2. 验证聊天功能是否正常
3. 测试 LongPort MCP 工具是否正常工作

### 故障排除
如果仍有问题：
1. 运行 `python test_fix.py` 进行诊断
2. 检查 `lsof -i :8001` 确认后端在正确端口
3. 查看服务器日志获取详细错误信息

## 🔮 预防措施

### 1. 端口管理
- 为不同服务分配固定端口
- 在启动脚本中检查端口可用性
- 使用环境变量配置端口

### 2. 配置管理
- 集中管理所有服务的端口配置
- 使用配置文件而不是硬编码端口
- 提供端口冲突检测工具

### 3. 文档更新
- 更新部署文档，说明端口分配
- 添加故障排除指南
- 提供端口冲突解决方案

## 📞 技术支持

### 常用命令
```bash
# 检查端口使用情况
lsof -i :8000
lsof -i :8001
lsof -i :3000

# 测试连接
curl http://localhost:8001/api/

# 运行诊断
python test_fix.py

# 更新配置
./update_api_url.sh
```

### 日志位置
- 后端日志：控制台输出
- 前端日志：浏览器开发者工具
- LongPort MCP 日志：可通过 `--log-dir` 参数指定

## 🎉 总结

**问题已完全解决**：
- ✅ 识别并解决了端口冲突问题
- ✅ 成功分离了 LongPort MCP 和 deer-flow 服务
- ✅ 更新了前端配置指向正确的后端
- ✅ 提供了完整的测试和验证工具
- ✅ 建立了预防措施避免未来冲突

现在 deer-flow 系统应该能够正常工作，同时保持与 LongPort MCP 服务的兼容性。 