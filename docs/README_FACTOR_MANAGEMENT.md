# ⚙️ 因子管理系统

金融投资助手的因子管理系统是一个专业的量化分析工具，提供36个内置因子的计算、自定义因子创建和因子分析功能。

## 🚀 功能特性

### 📊 因子总览
- **36个内置因子**：涵盖技术面、基本面、资金面、筹码面四大类别
- **实时统计**：显示总因子数、激活因子数、自定义因子数
- **分类管理**：按类别组织和管理因子
- **开关控制**：可独立启用或禁用每个因子

### 🧮 因子计算
- **多股票支持**：支持美股主要股票的因子计算
- **批量计算**：一次计算多个激活的因子
- **实时数据**：基于最新市场数据进行计算
- **结果展示**：清晰的计算结果展示界面

### ✏️ 自定义因子
- **公式编辑器**：支持自定义因子公式编写
- **公式验证**：自动验证公式语法的正确性
- **因子管理**：查看、编辑、删除自定义因子
- **公式示例**：提供常用公式模板和示例

### 📈 因子分析
- **有效性验证**：评估因子的预测能力
- **相关性分析**：分析因子间的相关关系
- **收益分析**：评估因子的历史收益表现
- **风险分析**：评估因子的风险特征

## 📋 因子分类详解

### 📈 技术面因子（9个）

| 因子名称 | 英文代码 | 描述 | 计算方法 |
|---------|---------|------|---------|
| RSI相对强弱指标 | rsi | 衡量股价超买超卖情况 | 基于价格涨跌幅计算强弱指数 |
| MACD指标 | macd | 趋势跟踪动量指标 | EMA12 - EMA26 |
| 布林带 | bollinger | 价格通道指标 | (当前价-下轨)/(上轨-下轨) |
| 20日移动平均线 | sma20 | 短期趋势指标 | 20日收盘价简单平均 |
| 50日移动平均线 | sma50 | 中期趋势指标 | 50日收盘价简单平均 |
| 200日移动平均线 | sma200 | 长期趋势指标 | 200日收盘价简单平均 |
| 成交量移动平均 | volume_sma | 成交量趋势 | 20日成交量简单平均 |
| 平均真实波动率 | atr | 波动性指标 | 真实波动幅度的移动平均 |
| 随机指标 | stoch | 超买超卖指标 | (收盘价-最低价)/(最高价-最低价) |

### 📊 基本面因子（9个）

| 因子名称 | 英文代码 | 描述 | 数据来源 |
|---------|---------|------|---------|
| 市盈率 | pe_ratio | 估值指标 | 股价/每股收益 |
| 市净率 | pb_ratio | 估值指标 | 股价/每股净资产 |
| 市销率 | ps_ratio | 估值指标 | 市值/销售收入 |
| 净资产收益率 | roe | 盈利能力指标 | 净利润/净资产 |
| 总资产收益率 | roa | 盈利能力指标 | 净利润/总资产 |
| 资产负债率 | debt_ratio | 偿债能力指标 | 总负债/总资产 |
| 流动比率 | current_ratio | 流动性指标 | 流动资产/流动负债 |
| 营收增长率 | revenue_growth | 成长性指标 | 营收同比增长率 |
| EPS增长率 | eps_growth | 成长性指标 | 每股收益同比增长率 |

### 💰 资金面因子（9个）

| 因子名称 | 英文代码 | 描述 | 特征 |
|---------|---------|------|------|
| 资金流向 | money_flow | 主力资金动向 | 基于价量关系计算 |
| 大单比例 | big_order_ratio | 大额交易占比 | 大单成交量占比 |
| 资金净流入 | net_inflow | 资金进出情况 | 流入资金-流出资金 |
| 机构持仓比例 | institution_ratio | 机构投资者持股情况 | 机构持股数/总股本 |
| 融资余额 | margin_balance | 杠杆资金情况 | 融资买入金额 |
| 融券余额 | short_balance | 做空资金情况 | 融券卖出金额 |
| ETF资金流 | etf_flow | 被动投资资金流向 | ETF申购赎回净额 |
| 外资流向 | foreign_flow | 外资买卖情况 | 外资净买入金额 |
| 大宗交易 | block_trade | 大宗交易活跃度 | 大宗交易成交金额 |

### 🔢 筹码面因子（9个）

| 因子名称 | 英文代码 | 描述 | 计算方法 |
|---------|---------|------|---------|
| 换手率 | turnover_rate | 筹码活跃度 | 成交量/流通股本 |
| 筹码集中度 | concentration | 筹码分布集中程度 | 基于价格波动性计算 |
| 平均成本 | avg_cost | 市场平均持股成本 | 加权平均价格 |
| 获利盘比例 | profit_ratio | 盈利筹码占比 | 高于成本价的筹码比例 |
| 锁定盘比例 | locked_ratio | 长期持有筹码占比 | 长期不交易的筹码比例 |
| 浮筹比例 | floating_ratio | 短期交易筹码占比 | 1 - 锁定盘比例 |
| 成本分布 | cost_distribution | 筹码成本分布情况 | 成本价格的分散程度 |
| 筹码峰值 | chip_peak | 主要成本区间 | 筹码最密集的价格区间 |
| 支撑压力 | support_pressure | 基于筹码的支撑压力位 | 筹码密集区形成的关键位 |

## 🔧 自定义因子公式

### 支持的函数和操作符

#### 基础数学操作
- `+`, `-`, `*`, `/`: 四则运算
- `()`: 括号，控制运算优先级
- `abs()`: 绝对值
- `log()`: 自然对数
- `sqrt()`: 平方根

#### 技术分析函数
- `sma(data, period)`: 简单移动平均
- `ema(data, period)`: 指数移动平均
- `max(data, period)`: 最大值
- `min(data, period)`: 最小值
- `std(data, period)`: 标准差
- `corr(data1, data2, period)`: 相关系数

#### 数据引用
- `close`: 收盘价
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `volume`: 成交量

### 公式示例

#### 1. 价格相对强度
```
(close - sma(close, 20)) / sma(close, 20)
```
计算当前价格相对于20日均线的偏离程度。

#### 2. 量价背离指标
```
corr(close, volume, 10)
```
计算10日内价格和成交量的相关性，负值可能表示量价背离。

#### 3. 波动率因子
```
std(close, 20) / sma(close, 20)
```
计算价格波动率相对于均价的比例。

#### 4. 动量因子
```
(close - close[5]) / close[5]
```
计算5日价格动量。

#### 5. 均线多头排列
```
(sma(close, 5) > sma(close, 10)) and (sma(close, 10) > sma(close, 20))
```
判断短中长期均线是否呈多头排列。

## 🎯 使用指南

### 1. 查看因子总览
1. 进入因子管理系统
2. 点击"因子总览"标签
3. 查看各类别因子的统计信息
4. 使用开关控制因子的启用状态

### 2. 计算股票因子
1. 点击"因子计算"标签
2. 选择要分析的股票代码
3. 确保相关因子已启用
4. 点击"计算因子"按钮
5. 查看计算结果

### 3. 创建自定义因子
1. 点击"自定义因子"标签
2. 输入因子名称
3. 编写计算公式
4. 点击"创建因子"
5. 在因子列表中查看和管理

### 4. 分析因子有效性
1. 点击"因子分析"标签
2. 选择要分析的因子
3. 查看相关性分析结果
4. 评估因子的预测能力

## ⚠️ 注意事项

### 数据来源
- **技术面因子**：基于实时股价和成交量数据计算
- **基本面因子**：来源于公司财务报表，更新频率相对较低
- **资金面因子**：部分为模拟数据，实际应用需专业数据源
- **筹码面因子**：基于价量数据推算，实际筹码分布需专业数据

### 使用建议
1. **因子组合**：建议组合使用多个不同类别的因子
2. **参数调整**：根据不同市场环境调整因子参数
3. **定期更新**：定期重新计算因子值，保持数据时效性
4. **风险控制**：结合风险指标使用因子信号

### 技术限制
- 自定义因子公式支持基础数学运算和技术分析函数
- 部分高级技术指标需要足够的历史数据支持
- 计算结果受数据质量和网络状况影响

## 🔮 未来功能

### 计划开发
- [ ] 因子回测功能
- [ ] 因子组合优化
- [ ] 机器学习因子挖掘
- [ ] 实时因子监控
- [ ] 因子报告生成
- [ ] 更多专业数据源接入

### 技术优化
- [ ] 因子计算性能优化
- [ ] 缓存机制改进
- [ ] 并行计算支持
- [ ] 更多技术指标
- [ ] 高频因子支持

---

**提示**：因子管理系统是专业的量化分析工具，建议结合投资知识和风险管理原则使用。投资有风险，决策需谨慎。 