# 🚀 AI金融分析平台 - 快速启动指南

## 📋 系统说明

系统现已配置为使用 **Google Gemini 2.0 Flash** 模型：
1. ✅ **已从 OpenAI 迁移到 Gemini API** - 更快的响应速度
2. ✅ **支持流式响应** - 实时AI交互体验
3. ✅ **完整的中文支持** - 专为中文金融分析优化

## ✅ 解决方案

### 第一步：配置环境变量
```bash
# 运行环境配置脚本
./setup_environment.sh
```

这个脚本会引导您输入：
- 🤖 **Gemini API Key** - 用于AI智能体功能
- 📊 **Tushare Token** - 用于股票数据获取
- 🌐 **Gemini Base URL** (可选)
- 🧠 **Gemini Model** (可选)

### 第二步：启动完整系统
```bash
# 启动前端+后端的完整系统
./start_full_system.sh
```

这个脚本会自动：
- ✅ 检查并安装所有依赖
- ✅ 运行AI集成测试
- ✅ 启动后端服务 (端口8000)
- ✅ 启动前端服务 (端口3000)
- ✅ 测试API接口

### 第三步：验证系统
系统启动后，您可以访问：

#### 🌐 前端界面
- **用户界面**: http://localhost:3000

#### 📊 后端API
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

#### 🤖 测试AI聊天
```bash
curl -X POST "http://localhost:8000/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{"message": "分析苹果公司的股票技术面"}'
```

## 🔧 如果仍有问题

### 1. 前端问题解决
如果前端仍有Node.js错误：
```bash
# 清理前端缓存和依赖
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
cd ..
```

### 2. 后端问题解决
```bash
# 重新安装Python依赖
source venv/bin/activate
pip install --upgrade -r requirements.txt

# 运行AI集成测试
python test_ai_integration.py
```

### 3. 环境变量问题
```bash
# 检查环境变量是否正确加载
source .env
echo $GEMINI_API_KEY  # 应该显示您的API密钥（部分）
echo $TUSHARE_TOKEN   # 应该显示您的Token
```

### 4. 只启动后端（如果不需要前端）
```bash
# 加载环境变量
source .env

# 启动后端
./start_backend_integrated.sh
```

## 🛑 停止系统

```bash
# 停止所有服务
./stop_full_system.sh
```

## 📝 查看日志

```bash
# 查看后端日志
tail -f backend.log

# 查看前端日志（如果有）
tail -f frontend.log
```

## 🎯 使用示例

### AI股票分析
```bash
curl -X POST "http://localhost:8000/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{"message": "分析特斯拉(TSLA)的技术面和基本面"}'
```

### 因子计算
```bash
curl -X POST "http://localhost:8000/factors/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "AAPL",
    "tushare_token": "your_token",
    "factors": ["rsi", "macd", "pe_ratio"]
  }'
```

### 股票数据查询
```bash
curl -X POST "http://localhost:8000/stocks/query" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "AAPL",
    "tushare_token": "your_token",
    "period": "1y"
  }'
```

## 🎉 功能特性

### ✅ AI智能体功能
- 🤖 多智能体协作分析
- 📈 技术分析师智能体
- 📊 基本面分析师智能体
- ⚠️ 风险分析师智能体
- 🎯 综合投资建议

### ✅ 数据分析功能
- 📊 实时股票数据获取
- 🔢 技术指标计算 (RSI, MACD, 布林带等)
- 📈 因子分析和评分
- 📉 MACD背离检测
- 🔍 智能选股推荐

### ✅ 开发者功能
- 🛠️ RESTful API
- 📖 自动生成API文档
- 🔄 流式响应支持
- 🧪 完整的测试框架

## 🔐 安全提醒

1. **保护API密钥**：请勿将`.env`文件提交到版本控制系统
2. **费用控制**：注意Gemini API的使用费用
3. **数据隐私**：敏感数据不会发送给第三方LLM服务

## 📞 支持

如果遇到问题，请检查：
1. 📝 日志文件 (`backend.log`, `frontend.log`)
2. 🔧 环境变量配置 (`.env`文件)
3. 📦 依赖安装状态
4. 🌐 网络连接

---

**现在您的AI金融分析平台已经完全集成并可以使用！** 🎉 