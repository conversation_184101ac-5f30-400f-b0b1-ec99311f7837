# 🧠🏆 机器学习模型与股票评分系统

金融投资助手新增了两个强大的量化分析模块：**机器学习模型**和**股票评分系统**，提供了完整的从因子分析到模型训练、预测、评分和选股的一体化解决方案。

## 🎯 系统架构

```
金融投资助手
├── 🤖 AI智能分析 (多智能体协作)
├── ⚙️ 因子管理系统 (36个内置因子)
├── 🧠 机器学习模型 (5种算法)
└── 🏆 股票评分系统 (综合评分排名)
```

## 🧠 机器学习模型模块

### 📋 支持的算法

| 算法名称 | 英文标识 | 特点 | 适用场景 |
|---------|---------|------|---------|
| 线性回归 | linear_regression | 简单快速 | 线性关系建模 |
| 岭回归 | ridge_regression | 抗过拟合 | 特征相关性高 |
| Lasso回归 | lasso_regression | 特征选择 | 稀疏特征 |
| 随机森林 | random_forest | 高精度 | 非线性关系 |
| 梯度提升 | gradient_boosting | 强泛化 | 复杂数据 |
| XGBoost | xgboost | 高性能 | 竞赛级精度 |
| LightGBM | lightgbm | 快速训练 | 大数据集 |
| 神经网络 | neural_network | 深度学习 | 复杂模式 |

### 🏋️ 模型训练功能

#### 训练流程
1. **数据收集**: 从多只股票获取36维因子数据
2. **数据预处理**: 缺失值处理、特征缩放
3. **模型训练**: 使用选定算法训练模型
4. **性能评估**: 计算MSE、RMSE、MAE、R²等指标
5. **模型保存**: 自动保存训练好的模型

#### 支持的评估指标
- **MSE** (均方误差): 预测误差的平方均值
- **RMSE** (均方根误差): MSE的平方根
- **MAE** (平均绝对误差): 预测误差绝对值的均值
- **R²** (决定系数): 模型解释方差的比例
- **MAPE** (平均绝对百分比误差): 相对误差百分比

### 🔮 股票预测功能

#### 预测能力
- **单股预测**: 对单只股票进行未来收益预测
- **批量预测**: 同时预测多只股票的表现
- **置信度**: 提供预测结果的可信度评估
- **特征重要性**: 分析各因子对预测的贡献度

#### 预测结果解读
```json
{
  "model_used": "random_forest",
  "predictions": {
    "AAPL": {
      "predicted_return": 0.0523,
      "confidence": 85.2
    }
  }
}
```

### 📊 模型比较功能

#### 比较维度
- **预测精度**: R²分数排名
- **训练时间**: 模型训练耗时
- **特征重要性**: 不同模型的特征权重
- **稳定性**: 交叉验证结果方差

## 🏆 股票评分系统

### ⚙️ 因子评分

#### 评分原理
基于36个量化因子的加权评分系统，每个因子都有专门的标准化函数：

**技术面因子标准化**:
- RSI: 30以下(超卖好) → 80-100分
- MACD: 正值好，负值差
- 布林带: 中间位置(0.2-0.8)最佳

**基本面因子标准化**:
- 市盈率: 10-20倍最佳 → 80-100分
- ROE: 越高越好，20%对应100分
- 负债率: 20%-50%较好

**资金面因子标准化**:
- 资金流向: 正值好
- 大单比例: 0.4-0.7较好
- 使用相对排名法

**筹码面因子标准化**:
- 换手率: 1-3倍较好
- 筹码集中度: 0.6-0.8较好
- 获利盘比例: 0.3-0.7较好

#### 权重配置
```python
factor_weights = {
    'technical': {
        'macd': 0.20,      # MACD权重最高
        'rsi': 0.15,       # RSI次之
        'sma50': 0.15,     # 中期均线
        # ... 其他因子
    },
    'fundamental': {
        'roe': 0.20,       # ROE权重最高
        'pe_ratio': 0.15,  # 市盈率次之
        # ... 其他因子
    }
    # ... 其他类别
}
```

### 🧠 ML评分

#### 集成学习方法
使用多个机器学习模型的预测结果进行集成：

1. **模型选择**: 默认使用Random Forest、XGBoost、LightGBM
2. **预测归一化**: 将预测收益率转换为0-100分数
3. **集成策略**: 多模型平均 + 标准差衡量不确定性
4. **评分等级**: excellent(80+) > good(70+) > average(50+) > poor(30+) > very_poor(<30)

### 🎯 综合评分

#### 评分公式
```
综合评分 = 因子评分 × 因子权重 + ML评分 × ML权重
```

默认权重配置:
- 因子权重: 60%
- ML权重: 40%

用户可以根据投资风格调整权重比例。

### 🏆 选股排名

#### 排名流程
1. **批量计算**: 同时分析多只股票的因子和ML评分
2. **相对排名**: 基于同批次股票的相对表现排名
3. **综合排序**: 按综合评分从高到低排序
4. **分类推荐**: 自动生成不同类型的投资建议

#### 推荐分类
- **顶级选择** (Top Picks): 综合评分85+的股票
- **价值型** (Value Plays): 因子评分显著高于ML评分
- **成长动量型** (Growth Momentum): ML评分显著高于因子评分  
- **均衡型** (Balanced): 因子和ML评分都较高且接近

## 🚀 使用指南

### 1. 机器学习模型训练

#### 步骤1: 模型管理
```bash
GET /ml/models  # 获取可用模型列表
```

#### 步骤2: 训练模型
```bash
POST /ml/train
{
  "symbols": ["AAPL", "MSFT", "GOOGL"],
  "model_id": "random_forest",
  "test_size": 0.2
}
```

#### 步骤3: 预测股票
```bash
POST /ml/predict
{
  "symbols": ["AAPL", "TSLA"],
  "model_id": "random_forest"
}
```

### 2. 股票评分计算

#### 因子评分
```bash
POST /scoring/factor
{
  "symbol": "AAPL"
}
```

#### ML评分
```bash
POST /scoring/ml
{
  "symbol": "AAPL"
}
```

#### 综合评分
```bash
POST /scoring/comprehensive
{
  "symbol": "AAPL"
}
```

### 3. 股票排名选股

```bash
POST /scoring/ranking
{
  "symbols": ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"],
  "max_stocks": 20,
  "factor_weight": 0.6,
  "ml_weight": 0.4
}
```

## 📊 API接口文档

### 机器学习模型API

| 接口路径 | 方法 | 功能 | 参数 |
|---------|------|------|------|
| `/ml/models` | GET | 获取可用模型列表 | 无 |
| `/ml/models/{model_id}` | GET | 获取模型详细信息 | model_id |
| `/ml/train` | POST | 训练模型 | symbols, model_id, test_size |
| `/ml/predict` | POST | 预测股票 | symbols, model_id |
| `/ml/compare` | POST | 比较模型性能 | symbols, test_size |

### 股票评分API

| 接口路径 | 方法 | 功能 | 参数 |
|---------|------|------|------|
| `/scoring/factor` | POST | 因子评分 | symbol |
| `/scoring/ml` | POST | ML评分 | symbol |
| `/scoring/comprehensive` | POST | 综合评分 | symbol |
| `/scoring/ranking` | POST | 股票排名 | symbols, max_stocks, weights |

## 🎯 实战案例

### 案例1: 构建科技股预测模型

```python
# 1. 选择科技股票池
tech_stocks = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META"]

# 2. 训练随机森林模型
train_request = {
    "symbols": tech_stocks,
    "model_id": "random_forest",
    "test_size": 0.2
}

# 3. 预测未来表现
predict_request = {
    "symbols": ["AAPL", "TSLA"],
    "model_id": "random_forest"
}
```

### 案例2: 综合评分选股策略

```python
# 1. 设置股票池
stock_universe = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", 
                  "NVDA", "META", "NFLX", "AMD", "CRM"]

# 2. 配置评分权重 (偏向因子分析)
ranking_request = {
    "symbols": stock_universe,
    "max_stocks": 5,
    "factor_weight": 0.7,
    "ml_weight": 0.3
}

# 3. 获取Top 5推荐
# 结果将按综合评分排序，并提供分类推荐
```

## ⚠️ 注意事项

### 模型训练
- **数据质量**: 确保股票数据完整，避免停牌或退市股票
- **样本数量**: 建议至少10只股票进行训练
- **过拟合风险**: 监控训练集与测试集性能差异

### 评分系统
- **参考意义**: 评分结果仅供参考，不构成投资建议
- **时效性**: 基于历史数据，市场环境变化可能影响预测准确性
- **权重调整**: 根据市场风格和个人偏好调整权重配置

### 技术限制
- **计算时间**: 大量股票的排名计算可能需要较长时间
- **模型依赖**: ML评分需要先训练相应模型
- **数据来源**: 部分因子(如资金面)使用模拟数据

## 🔮 未来规划

### 短期目标
- [ ] 增加更多机器学习算法(LSTM、Transformer)
- [ ] 实现模型自动调参功能
- [ ] 添加模型性能监控和预警
- [ ] 优化评分算法的准确性

### 中期目标  
- [ ] 集成更多专业数据源
- [ ] 实现实时模型更新
- [ ] 添加回测框架
- [ ] 构建投资组合优化功能

### 长期目标
- [ ] 开发强化学习交易策略
- [ ] 集成替代数据源(新闻、社交媒体)
- [ ] 实现多市场、多资产类别支持
- [ ] 构建专业级风险管理系统

---

**免责声明**: 本系统提供的所有分析、评分和预测结果仅供参考，不构成投资建议。投资有风险，入市需谨慎。请结合自身风险承受能力和投资目标做出决策。 