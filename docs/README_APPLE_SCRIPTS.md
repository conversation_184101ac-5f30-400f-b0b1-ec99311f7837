# 苹果股票数据获取脚本说明

本项目提供了多个脚本来获取苹果公司（AAPL）的股票数据，使用不同的数据源和复权方式。

## 可用脚本

### 1. `apple_sina_data.py` - 主要脚本（推荐）
- **数据源**: 新浪财经
- **复权方式**: 不复权（默认）
- **特点**: 显示实际历史价格，日期正确
- **用途**: 查看真实的历史交易价格

```bash
python apple_sina_data.py
```

### 2. `apple_sina_data_qfq.py` - 前复权数据
- **数据源**: 新浪财经  
- **复权方式**: 前复权
- **特点**: 考虑分红、拆股等因素，价格连续性好
- **用途**: 分析长期价格趋势和收益率

```bash
python apple_sina_data_qfq.py
```

### 3. `apple_sina_data_no_adjust.py` - 不复权数据（备用）
- **数据源**: 新浪财经
- **复权方式**: 不复权
- **特点**: 与主脚本功能相同
- **用途**: 备用脚本

```bash
python apple_sina_data_no_adjust.py
```

### 4. `apple_history_akshare.py` - 东方财富数据
- **数据源**: 东方财富网
- **复权方式**: 不复权
- **特点**: 先搜索股票代码，再获取数据
- **用途**: 使用东方财富数据源

```bash
python apple_history_akshare.py
```

## 数据字段说明

所有脚本返回的数据包含以下字段：

- `date`: 交易日期
- `open`: 开盘价（美元）
- `high`: 最高价（美元）
- `low`: 最低价（美元）
- `close`: 收盘价（美元）
- `volume`: 成交量（股）

## 复权方式对比

### 不复权数据
- ✅ 显示实际历史价格
- ✅ 适合查看真实价格走势
- ❌ 价格跳跃可能反映拆股等公司行为
- ❌ 不适合计算长期收益率

### 前复权数据
- ✅ 价格连续性好，适合技术分析
- ✅ 适合计算长期收益率
- ❌ 可能显示负值（历史拆股影响）
- ❌ 不是实际历史价格

## 推荐使用

1. **日常查看**: 使用 `apple_sina_data.py`（不复权，显示真实价格）
2. **技术分析**: 使用 `apple_sina_data_qfq.py`（前复权，价格连续）
3. **备用数据源**: 使用 `apple_history_akshare.py`（东方财富）

## 注意事项

- 新浪财经数据可能有15分钟延迟
- 数据仅供参考，投资需谨慎
- 前复权数据中的负值是正常现象
- 建议对比多个数据源以确保准确性

## 数据统计功能

所有脚本都提供以下统计信息：
- 最新/最早收盘价
- 总涨跌幅
- 期间最高/最低价
- 价格波动幅度
- 最近10个交易日详细数据 