# 🎙️ 播客功能TTS配置指南

## 问题描述
当前播客生成功能失败，错误信息：`An error occurred while generating podcast. Please try again.`

## 根本原因
播客功能依赖火山引擎TTS（文本转语音）服务，但API凭据未正确配置。

## 解决方案

### 方案1: 配置火山引擎TTS（推荐）

1. **注册火山引擎账户**
   - 访问：https://console.volcengine.com/
   - 注册并完成实名认证

2. **开通语音技术服务**
   - 在控制台中找到"语音技术"服务
   - 开通TTS（文本转语音）功能
   - 创建应用并获取API凭据

3. **获取API凭据**
   - APP ID: 应用的唯一标识符
   - Access Token: API访问令牌

4. **更新配置文件**
   编辑 `.env` 文件，将以下占位符替换为真实凭据：
   ```bash
   VOLCENGINE_TTS_APPID=your_real_app_id_here
   VOLCENGINE_TTS_ACCESS_TOKEN=your_real_access_token_here
   ```

5. **重启服务**
   ```bash
   # 重启后端服务
   python server.py
   ```

### 方案2: 使用其他TTS服务

如果不想使用火山引擎TTS，可以考虑：

1. **OpenAI TTS**
   - 修改代码使用OpenAI的TTS API
   - 需要OpenAI API Key

2. **Azure Speech Services**
   - 使用微软Azure的语音服务
   - 需要Azure订阅

3. **Google Cloud Text-to-Speech**
   - 使用Google Cloud的TTS服务
   - 需要Google Cloud账户

### 方案3: 暂时禁用播客功能

如果暂时不需要播客功能，可以：

1. **隐藏播客按钮**
   - 在前端代码中注释掉播客生成按钮
   - 或添加条件判断，只在TTS配置正确时显示

2. **添加友好提示**
   - 在UI中显示"播客功能需要配置TTS服务"的提示

## 测试配置

配置完成后，可以通过以下方式测试：

1. **后端测试**
   ```bash
   # 在项目根目录运行
   python -c "
   import os
   from src.tools.tts import VolcengineTTS
   
   app_id = os.getenv('VOLCENGINE_TTS_APPID')
   token = os.getenv('VOLCENGINE_TTS_ACCESS_TOKEN')
   
   if app_id and app_id != 'xxx':
       print('✅ TTS配置正确')
   else:
       print('❌ TTS配置需要更新')
   "
   ```

2. **前端测试**
   - 完成一个研究任务
   - 点击播客生成按钮
   - 检查是否能成功生成音频

## 费用说明

- 火山引擎TTS服务按使用量计费
- 通常有免费额度供测试使用
- 具体费用请查看火山引擎官网定价

## 技术支持

如果遇到配置问题，可以：
1. 查看火山引擎TTS官方文档
2. 检查服务器日志获取详细错误信息
3. 联系火山引擎技术支持

## 替代方案

如果不想使用付费TTS服务，可以考虑：
1. 使用开源TTS解决方案（如Coqui TTS）
2. 集成免费的TTS API
3. 简化播客功能，只生成文本脚本而不生成音频 