# 🏗️ 系统架构文档

## 📋 系统概览

金融投资助手是一个完整的量化投资分析平台，集成了AI智能分析、因子管理、机器学习、股票评分、回测和风险管理等功能模块。

## 🎯 核心架构

```
┌─────────────────────────────────────────────────────────────┐
│                    金融投资助手平台                          │
├─────────────────────────────────────────────────────────────┤
│  前端界面层 (React + TypeScript)                            │
│  ├── 🤖 AI智能分析界面                                      │
│  ├── ⚙️ 因子管理界面                                        │
│  ├── 🧠 机器学习界面                                        │
│  ├── 🏆 股票评分界面                                        │
│  ├── 📊 回测分析界面                                        │
│  └── ⚠️ 风险监控界面                                        │
├─────────────────────────────────────────────────────────────┤
│  API接口层 (FastAPI)                                        │
│  ├── 聊天流式接口                                           │
│  ├── 因子计算接口                                           │
│  ├── 机器学习接口                                           │
│  ├── 评分系统接口                                           │
│  ├── 数据源接口                                             │
│  ├── 回测引擎接口                                           │
│  └── 风险管理接口                                           │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Python)                                        │
│  ├── 🤖 多智能体工作流                                      │
│  ├── ⚙️ 因子计算引擎                                        │
│  ├── 🧠 机器学习模型                                        │
│  ├── 🏆 评分系统                                            │
│  ├── 📊 回测引擎                                            │
│  └── ⚠️ 风险管理                                            │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                     │
│  ├── 📈 价格数据 (yfinance)                                │
│  ├── 📊 财务数据 (yfinance)                                │
│  ├── 📰 新闻情感数据                                        │
│  ├── 💾 模型存储 (joblib)                                  │
│  └── 📋 配置数据                                            │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心模块详解

### 1. 🤖 AI智能分析模块

**技术栈**: LangGraph + 多智能体架构
**功能**: 
- 流式对话处理
- 多智能体协作分析
- 股票技术分析
- 市场研究报告

**核心文件**:
- `src/workflow.py` - 工作流引擎
- `src/graph.py` - 智能体图构建
- `src/agents/` - 各类智能体

### 2. ⚙️ 因子管理系统

**技术栈**: pandas + numpy + yfinance
**功能**:
- 36个内置量化因子
- 自定义因子创建
- 因子计算和分析
- 因子有效性验证

**核心文件**:
- `backend/factors.py` - 因子计算引擎
- 支持4大类因子：技术面、基本面、资金面、筹码面

### 3. 🧠 机器学习模块

**技术栈**: scikit-learn + XGBoost + LightGBM
**功能**:
- 8种机器学习算法
- 模型训练和评估
- 股票预测
- 模型比较分析

**核心文件**:
- `backend/ml_models.py` - ML模型管理器
- 支持算法：线性回归、随机森林、XGBoost、LightGBM、神经网络等

### 4. 🏆 股票评分系统

**技术栈**: 多因子模型 + 机器学习集成
**功能**:
- 因子评分
- ML评分
- 综合评分
- 股票排名选股

**核心文件**:
- `backend/scoring_system.py` - 评分系统
- 智能权重配置和评分算法

### 5. 📊 回测框架

**技术栈**: pandas + numpy + 事件驱动架构
**功能**:
- 策略回测
- 性能分析
- 风险评估
- 交易统计

**核心文件**:
- `backend/backtesting.py` - 回测引擎
- 支持多种策略和完整的回测报告

### 6. ⚠️ 风险管理系统

**技术栈**: scipy + 风险模型
**功能**:
- VaR/CVaR计算
- 风险指标监控
- 风险预警
- 投资组合分析

**核心文件**:
- `backend/risk_management.py` - 风险管理器
- 专业的风险计算和监控功能

### 7. 📡 数据源管理

**技术栈**: yfinance + 多数据源集成
**功能**:
- 实时价格数据
- 财务数据
- 新闻情感数据
- 数据缓存和管理

**核心文件**:
- `backend/data_sources.py` - 数据源管理器
- 统一的数据接口和缓存机制

## 🌐 API接口架构

### 核心接口分类

| 模块 | 接口前缀 | 主要功能 |
|------|----------|----------|
| AI分析 | `/chat/` | 流式对话、智能分析 |
| 因子管理 | `/factors/` | 因子计算、元数据 |
| 机器学习 | `/ml/` | 模型训练、预测 |
| 评分系统 | `/scoring/` | 各类评分、排名 |
| 数据源 | `/data/` | 股票数据、情感数据 |
| 回测 | `/backtest/` | 策略回测、性能分析 |
| 风险管理 | `/risk/` | 风险分析、监控 |

### 接口设计原则

1. **RESTful设计**: 遵循REST架构原则
2. **异步处理**: 支持流式和异步响应
3. **错误处理**: 统一的错误处理和日志记录
4. **参数验证**: 使用Pydantic进行参数验证
5. **CORS支持**: 支持跨域请求

## 💾 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   外部数据   │───▶│  数据源管理  │───▶│   数据缓存   │
│  (yfinance) │    │   模块      │    │            │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   因子计算   │◀───│   业务逻辑   │───▶│   模型训练   │
│            │    │    处理     │    │            │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   评分排名   │◀───│   结果整合   │───▶│   风险分析   │
│            │    │            │    │            │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │  前端展示    │
                   │            │
                   └─────────────┘
```

## 🔒 安全架构

### 1. 数据安全
- API密钥管理
- 数据传输加密
- 敏感信息脱敏

### 2. 接口安全
- CORS配置
- 请求频率限制
- 参数验证和过滤

### 3. 系统安全
- 错误信息过滤
- 日志安全记录
- 异常处理机制

## 📈 性能优化

### 1. 数据层优化
- **缓存机制**: LRU缓存减少重复计算
- **数据预处理**: 批量处理提高效率
- **异步IO**: 并发数据获取

### 2. 计算层优化
- **向量化计算**: 使用numpy/pandas优化
- **模型缓存**: 训练好的模型持久化
- **并行处理**: 多进程/多线程计算

### 3. 接口层优化
- **流式响应**: 实时数据传输
- **压缩传输**: gzip压缩减少带宽
- **连接池**: 数据库连接复用

## 🚀 部署架构

### 开发环境
```
┌─────────────┐    ┌─────────────┐
│  前端开发    │    │  后端开发    │
│ (React Dev) │    │ (FastAPI)   │
│ Port: 3000  │    │ Port: 8000  │
└─────────────┘    └─────────────┘
```

### 生产环境
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   负载均衡   │───▶│   Web服务    │───▶│   数据库    │
│  (Nginx)    │    │ (Gunicorn)  │    │ (Redis)     │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │   文件存储   │
                   │ (模型/日志)  │
                   └─────────────┘
```

## 🔧 技术栈总览

### 前端技术栈
- **框架**: React 18 + TypeScript
- **样式**: CSS3 + 响应式设计
- **状态管理**: React Hooks
- **HTTP客户端**: Fetch API
- **构建工具**: Create React App

### 后端技术栈
- **Web框架**: FastAPI + Uvicorn
- **数据处理**: pandas + numpy
- **机器学习**: scikit-learn + XGBoost + LightGBM
- **数据源**: yfinance + 自定义API
- **AI框架**: LangGraph + LangChain
- **风险计算**: scipy + 自定义算法

### 数据和存储
- **数据格式**: JSON + CSV + Parquet
- **模型存储**: joblib + pickle
- **缓存**: 内存缓存 + 文件缓存
- **配置**: YAML + 环境变量

## 📊 监控和日志

### 1. 系统监控
- API响应时间监控
- 内存和CPU使用率
- 错误率统计

### 2. 业务监控
- 因子计算成功率
- 模型预测准确率
- 用户行为分析

### 3. 日志管理
- 结构化日志记录
- 日志级别分类
- 日志轮转和归档

## 🔮 扩展性设计

### 1. 模块化架构
- 松耦合设计
- 插件化扩展
- 微服务就绪

### 2. 数据源扩展
- 多数据源适配器
- 统一数据接口
- 实时数据流支持

### 3. 算法扩展
- 策略插件系统
- 自定义因子框架
- 模型热插拔

## 📝 开发规范

### 1. 代码规范
- Python: PEP 8
- TypeScript: ESLint + Prettier
- 注释和文档完整

### 2. 测试规范
- 单元测试覆盖
- 集成测试验证
- 性能测试基准

### 3. 版本控制
- Git工作流
- 语义化版本
- 变更日志维护

---

**注意**: 本系统仅供学习和研究使用，所有分析结果不构成投资建议。投资有风险，入市需谨慎。 