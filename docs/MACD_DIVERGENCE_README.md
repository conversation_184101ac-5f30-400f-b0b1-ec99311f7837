# MACD背离检测功能使用说明

## 🎯 功能概述

MACD背离检测是一个专业的技术分析工具，能够自动扫描市场中的MACD顶背离和底背离信号，帮助投资者发现潜在的买卖机会。

## ✨ 主要特性

### 🔍 智能扫描
- **一键扫描**：支持美股、港股、A股三个市场的一键扫描
- **背离类型**：检测顶背离（看跌信号）和底背离（看涨信号）
- **数据范围**：分析最近100根K线的数据
- **并发处理**：异步并发扫描，提高效率

### 📊 专业算法
- **MACD计算**：标准的12、26、9参数MACD指标
- **峰值识别**：智能识别价格和MACD的峰值谷值
- **背离检测**：精确判断价格与MACD的背离关系
- **置信度评估**：为每个背离信号提供置信度评分

### 📈 可视化展示
- **K线图生成**：自动生成包含MACD指标的K线图
- **背离标记**：在图表上清晰标记背离位置
- **中文支持**：完整的中文界面和图表标注
- **多格式输出**：支持PNG格式的高清图表

### 💾 数据管理
- **数据库存储**：专门的SQLite数据库存储扫描结果
- **历史记录**：完整的扫描历史和背离信号记录
- **快速查询**：支持按市场、时间等条件查询

## 🚀 快速开始

### 1. 启动服务

```bash
# 启动后端服务
python -m backend.server

# 启动前端服务
cd frontend && npm start
```

### 2. 访问界面

打开浏览器访问：http://localhost:3000

### 3. 使用步骤

1. **选择模块**：在首页点击"MACD背离扫描"
2. **配置参数**：
   - 输入Tushare Token
   - 选择市场（美股/港股/A股）
   - 选择背离类型（顶背离/底背离）
3. **开始扫描**：点击"开始扫描"按钮
4. **查看结果**：
   - 扫描统计信息
   - 背离信号列表
   - K线图展示

## 📋 支持的市场

### 美股 (US)
- 包含50只主要美股：AAPL, MSFT, GOOGL, AMZN, TSLA等
- 涵盖科技、金融、消费等主要板块

### 港股 (HK)
- 包含30只主要港股：00700.HK, 00941.HK等
- 涵盖腾讯、阿里巴巴等知名公司

### A股 (CN)
- 包含30只主要A股：000001.SZ, 600519.SH等
- 涵盖银行、白酒、科技等主要板块

## 🔧 API接口

### 扫描市场背离
```http
POST /divergence/scan
Content-Type: application/json

{
  "market": "US",
  "tushare_token": "your_token",
  "divergence_types": ["bullish", "bearish"]
}
```

### 获取最近背离信号
```http
GET /divergence/recent/{market}?hours=24
```

### 获取扫描历史
```http
GET /divergence/history/{market}?limit=10
```

### 获取支持的市场
```http
GET /divergence/markets
```

## 📊 背离信号解读

### 底背离（Bullish Divergence）
- **定义**：价格创新低，但MACD不创新低
- **信号**：看涨信号，可能的买入机会
- **颜色**：绿色标识

### 顶背离（Bearish Divergence）
- **定义**：价格创新高，但MACD不创新高
- **信号**：看跌信号，可能的卖出机会
- **颜色**：红色标识

## 🎯 使用技巧

### 1. 参数设置
- **Token配置**：确保Tushare Token有效且有足够权限
- **市场选择**：根据投资偏好选择相应市场
- **背离类型**：可同时选择两种类型进行全面扫描

### 2. 结果分析
- **置信度**：优先关注置信度较高的信号
- **强度指标**：结合强度指标判断背离的显著性
- **时间范围**：注意背离发生的时间范围

### 3. 风险控制
- **多重确认**：结合其他技术指标进行确认
- **止损设置**：设置合理的止损位
- **仓位管理**：控制单笔投资的仓位大小

## 🔧 技术架构

### 后端模块
- `divergence_detector.py`：核心算法实现
- `market_scanner.py`：市场扫描器
- `server.py`：API接口服务

### 前端模块
- `App.tsx`：主界面组件
- `App.css`：样式定义

### 数据库
- SQLite数据库存储
- 背离信号表和扫描历史表

## 📝 注意事项

1. **数据依赖**：需要有效的Tushare Token
2. **网络要求**：需要稳定的网络连接
3. **计算时间**：大市场扫描可能需要较长时间
4. **字体警告**：中文字体警告不影响功能使用
5. **投资风险**：背离信号仅供参考，投资需谨慎

## 🆘 常见问题

### Q: 扫描时间过长怎么办？
A: 系统采用并发扫描，正常情况下几十秒内完成。如果时间过长，请检查网络连接。

### Q: 没有发现背离信号？
A: 这是正常现象，背离信号并不常见。可以尝试不同的市场或时间段。

### Q: 图表显示中文乱码？
A: 这是字体问题，不影响功能使用。图表数据和标记都是正确的。

### Q: API返回错误？
A: 请检查Tushare Token是否有效，网络连接是否正常。

## 📞 技术支持

如有问题，请检查：
1. 服务器是否正常启动
2. Token是否有效
3. 网络连接是否稳定
4. 浏览器控制台是否有错误信息

---

**免责声明**：本工具仅供技术分析参考，不构成投资建议。投资有风险，决策需谨慎。 