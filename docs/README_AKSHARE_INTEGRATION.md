# 📈 Akshare 集成功能使用指南

## 🎯 功能概述

本项目已成功集成 **Akshare** 数据源，实现了智能的美股数据获取功能。系统会根据股票代码自动选择最合适的数据源：

- 🇺🇸 **美股数据**：优先使用 **Akshare**（新浪财经接口）
- 🇨🇳 **中国股票**：继续使用 **Tushare**
- 🧠 **智能缓存**：优先从数据库读取，避免重复下载

## ✨ 主要特性

### 1. 智能数据源选择
```python
# 系统会自动识别股票类型并选择合适的数据源
data_manager.get_stock_data_intelligent('AAPL')    # 使用 Akshare
data_manager.get_stock_data_intelligent('000001.SZ')  # 使用 Tushare
```

### 2. 完整历史数据
- **Akshare** 提供从上市开始的完整历史数据
- **NVDA** 示例：从 1999年1月22日 到 2025年5月30日，共 5440 条记录
- 数据包含：开盘价、最高价、最低价、收盘价、成交量等完整信息

### 3. 智能缓存机制
```python
# 第一次调用：从 Akshare 下载数据并保存到数据库
df1 = data_manager.get_stock_data_intelligent('AAPL', start_date='20240101')

# 第二次调用：直接从数据库读取，速度更快
df2 = data_manager.get_stock_data_intelligent('AAPL', start_date='20240101')
```

### 4. 自动因子计算
- 下载数据后自动计算 29 个量化因子
- 包括技术指标、价格因子、成交量因子等
- 因子数据同步保存到数据库

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install akshare pandas numpy
```

### 2. 基本使用
```python
from backend.data_manager import init_data_manager

# 初始化数据管理器
data_manager = init_data_manager("your_tushare_token")

# 获取美股数据（自动使用 Akshare）
aapl_data = data_manager.get_stock_data_intelligent('AAPL')
print(f"获取到 {len(aapl_data)} 条 AAPL 数据")

# 获取中国股票数据（自动使用 Tushare）
ping_an_data = data_manager.get_stock_data_intelligent('000001.SZ')
print(f"获取到 {len(ping_an_data)} 条平安银行数据")
```

### 3. 批量初始化
```python
# 批量下载多只股票的数据
stock_codes = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', '000001.SZ', '600519.SH']
data_manager.initialize_data(stock_codes, start_date='20240101')
```

## 📊 数据格式

### 返回的 DataFrame 包含以下列：
- `trade_date`: 交易日期 (YYYYMMDD 格式)
- `open`: 开盘价
- `high`: 最高价  
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量
- `amount`: 成交额
- `pct_change`: 涨跌幅(%)
- `pre_close`: 昨收价
- `change`: 涨跌额

### 示例数据：
```
  trade_date   open   high    low  close     volume
0   20250530  135.0  136.5  134.2  135.13  45234567
1   20250529  134.8  135.9  133.5  134.95  38765432
2   20250528  133.2  135.1  132.8  134.75  42156789
```

## 🔧 高级功能

### 1. 强制重新下载
```python
# 强制从数据源重新下载，忽略缓存
df = data_manager.get_stock_data_intelligent(
    'AAPL', 
    start_date='20240101', 
    force_download=True
)
```

### 2. 指定日期范围
```python
# 获取指定时间范围的数据
df = data_manager.get_stock_data_intelligent(
    'AAPL',
    start_date='20240101',
    end_date='20241231'
)
```

### 3. 获取因子数据
```python
# 获取计算好的因子数据
factors = data_manager.get_factor_data('AAPL', '20241201')
print(f"AAPL 的因子数据：{factors}")
```

## 📈 支持的股票类型

### 美股 (使用 Akshare)
- **格式**：纯字母代码，如 `AAPL`、`MSFT`、`GOOGL`
- **数据源**：新浪财经
- **特点**：完整历史数据，从上市开始

### 中国股票 (使用 Tushare)  
- **A股格式**：`000001.SZ`、`600519.SH`
- **港股格式**：`00700.HK`
- **数据源**：Tushare
- **特点**：实时更新，专业金融数据

## 🛠️ 故障排除

### 1. Akshare 库未安装
```bash
pip install akshare
```

### 2. 网络连接问题
- Akshare 依赖网络连接获取数据
- 如果网络不稳定，系统会自动回退到 Tushare 或模拟数据

### 3. 数据获取失败
```python
# 检查股票代码是否正确
df = data_manager.get_stock_data_intelligent('INVALID_CODE')
if df.empty:
    print("股票代码无效或数据获取失败")
```

### 4. 查看日志
```python
import logging
logging.basicConfig(level=logging.INFO)
# 运行代码，查看详细日志信息
```

## 📝 测试验证

运行测试脚本验证功能：

```bash
# 基本集成测试
python test_akshare_integration.py

# Akshare 下载功能测试
python test_akshare_download.py
```

## 🔄 数据更新策略

### 自动更新逻辑：
1. **首次获取**：从数据源下载完整历史数据
2. **后续获取**：检查数据库中的数据完整性
3. **智能判断**：如果数据覆盖率 > 80%，直接从数据库读取
4. **增量更新**：只下载缺失的日期范围

### 手动更新：
```python
# 更新所有股票的最新数据
data_manager.update_data()

# 更新指定股票
data_manager.update_data(['AAPL', 'MSFT'])
```

## 📊 性能优化

### 1. 数据库缓存
- 所有下载的数据自动保存到 SQLite 数据库
- 后续查询直接从数据库读取，速度提升 10-100 倍

### 2. 智能去重
- 自动检测重复数据，避免重复下载
- 使用 `INSERT OR REPLACE` 确保数据一致性

### 3. 批量处理
- 支持批量下载多只股票
- 自动计算和保存因子数据

## 🎯 最佳实践

1. **首次使用**：建议先下载少量股票测试功能
2. **定期更新**：建议每日运行 `update_data()` 获取最新数据
3. **监控日志**：关注日志输出，及时发现问题
4. **备份数据**：定期备份 `data/financial_data.db` 文件

---

**注意**：本功能仅用于学习和研究目的，不构成投资建议。请遵守相关数据源的使用条款。 