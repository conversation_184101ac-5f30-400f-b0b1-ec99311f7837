# Alpha Vantage 工具文档

## 概述

Alpha Vantage 工具是一个强大的金融数据API工具，可以获取股票、外汇、数字货币和经济指标等各种金融数据。该工具基于 [Alpha Vantage API](https://www.alphavantage.co/) 构建。

## 功能特性

### 🏢 股票数据
- **时间序列数据**：日内、日线、周线、月线 OHLCV 数据
- **实时报价**：获取实时股价信息
- **股票搜索**：根据关键词搜索股票代码
- **基本面数据**：公司概览、财报、收益等

### 📊 技术分析
- **技术指标**：60+ 种技术指标（SMA、EMA、RSI、MACD、STOCH等）
- **图表分析**：支持各种技术分析工具

### 📰 Alpha Intelligence™
- **新闻情感分析**：分析新闻对股价的情感影响
- **市场动态**：热门股票、涨跌幅榜单等

### 💱 外汇数据
- **汇率信息**：实时和历史汇率数据
- **外汇时间序列**：日内、日线、周线、月线外汇数据

### 🪙 数字货币
- **加密货币数据**：主流数字货币的价格和历史数据

### 📈 经济指标
- **宏观经济**：GDP、失业率、通胀率、利率等经济指标

## 安装与配置

### 1. 获取API密钥

访问 [Alpha Vantage](https://www.alphavantage.co/support/#api-key) 注册免费账户并获取API密钥。

### 2. 配置环境变量

在项目根目录的 `.env` 文件中添加：

```bash
ALPHA_VANTAGE_API_KEY=your_api_key_here
```

### 3. 安装依赖

工具依赖已包含在项目的 `pyproject.toml` 中：

```bash
# 主要依赖
requests  # HTTP请求
pydantic  # 数据验证
langchain-core  # LangChain核心组件
```

## 使用方法

### 基本用法

```python
from src.tools.alpha_vantage import alpha_vantage_tool

# 获取股票日线数据
result = alpha_vantage_tool.invoke({
    "function": "TIME_SERIES_DAILY",
    "symbol": "AAPL",
    "outputsize": "compact"
})
```

### 参数说明

#### 必需参数

- **function**: API函数名称，例如：
  - `TIME_SERIES_DAILY` - 股票日线数据
  - `GLOBAL_QUOTE` - 实时股价
  - `SYMBOL_SEARCH` - 股票搜索
  - `NEWS_SENTIMENT` - 新闻情感分析

#### 可选参数

- **symbol**: 股票代码（如 AAPL, MSFT, TSLA）
- **interval**: 时间间隔（1min, 5min, 15min, 30min, 60min）
- **outputsize**: 输出大小（compact 或 full）
- **datatype**: 数据格式（json 或 csv）
- **keywords**: 搜索关键词
- **tickers**: 股票代码列表（逗号分隔）
- **time_period**: 技术指标时间周期
- **series_type**: 价格类型（open, high, low, close）
- **additional_params**: 其他API参数字典

## 使用示例

### 1. 获取股票日线数据

```python
result = alpha_vantage_tool.invoke({
    "function": "TIME_SERIES_DAILY",
    "symbol": "AAPL",
    "outputsize": "compact"
})
```

### 2. 获取实时股价

```python
result = alpha_vantage_tool.invoke({
    "function": "GLOBAL_QUOTE",
    "symbol": "MSFT"
})
```

### 3. 搜索股票

```python
result = alpha_vantage_tool.invoke({
    "function": "SYMBOL_SEARCH",
    "keywords": "Apple"
})
```

### 4. 新闻情感分析

```python
result = alpha_vantage_tool.invoke({
    "function": "NEWS_SENTIMENT",
    "tickers": "AAPL,MSFT"
})
```

### 5. 技术指标 - 移动平均线

```python
result = alpha_vantage_tool.invoke({
    "function": "SMA",
    "symbol": "AAPL",
    "interval": "daily",
    "time_period": 20,
    "series_type": "close"
})
```

### 6. 公司基本面信息

```python
result = alpha_vantage_tool.invoke({
    "function": "OVERVIEW",
    "symbol": "AAPL"
})
```

### 7. 货币汇率

```python
result = alpha_vantage_tool.invoke({
    "function": "CURRENCY_EXCHANGE_RATE",
    "additional_params": {
        "from_currency": "USD",
        "to_currency": "CNY"
    }
})
```

### 8. 经济指标

```python
result = alpha_vantage_tool.invoke({
    "function": "REAL_GDP",
    "additional_params": {
        "interval": "quarterly"
    }
})
```

## 常用API函数列表

### 股票数据

| 函数名 | 描述 | 免费版本 |
|--------|------|----------|
| `TIME_SERIES_INTRADAY` | 日内数据 | ❌ 限制 |
| `TIME_SERIES_DAILY` | 日线数据 | ✅ |
| `TIME_SERIES_WEEKLY` | 周线数据 | ✅ |
| `TIME_SERIES_MONTHLY` | 月线数据 | ✅ |
| `GLOBAL_QUOTE` | 实时报价 | ✅ |
| `SYMBOL_SEARCH` | 股票搜索 | ✅ |

### 基本面数据

| 函数名 | 描述 | 免费版本 |
|--------|------|----------|
| `OVERVIEW` | 公司概览 | ❌ |
| `EARNINGS` | 收益数据 | ❌ |
| `INCOME_STATEMENT` | 损益表 | ❌ |
| `BALANCE_SHEET` | 资产负债表 | ❌ |
| `CASH_FLOW` | 现金流量表 | ❌ |

### 技术指标

| 函数名 | 描述 | 免费版本 |
|--------|------|----------|
| `SMA` | 简单移动平均 | ❌ |
| `EMA` | 指数移动平均 | ❌ |
| `RSI` | 相对强弱指数 | ❌ |
| `MACD` | 平滑异同移动平均线 | ❌ |
| `STOCH` | 随机指标 | ❌ |

### 外汇数据

| 函数名 | 描述 | 免费版本 |
|--------|------|----------|
| `CURRENCY_EXCHANGE_RATE` | 汇率 | ✅ 限制 |
| `FX_INTRADAY` | 外汇日内 | ❌ |
| `FX_DAILY` | 外汇日线 | ❌ |

## API限制

### 免费版本限制

- **调用频率**：每分钟5次调用，每天100次
- **数据范围**：部分历史数据有限制
- **功能限制**：高级功能需要付费版本

### 付费版本优势

- **更高频率**：每分钟30-1200次调用
- **实时数据**：真正实时的市场数据
- **完整功能**：所有API功能无限制访问
- **更多数据**：完整的历史数据

## 错误处理

工具内置了完善的错误处理机制：

```python
# 错误类型
{
    "error": "ALPHA_VANTAGE_API_KEY 环境变量未设置"
}

{
    "error": "Alpha Vantage API 错误: Invalid API call"
}

{
    "error": "Alpha Vantage API 限制: Thank you for using Alpha Vantage!"
}

{
    "error": "网络请求失败: Connection timeout"
}
```

## 最佳实践

### 1. API调用频率控制

```python
import time

# 在连续调用之间添加延迟
alpha_vantage_tool.invoke(params1)
time.sleep(12)  # 免费版本建议12秒间隔
alpha_vantage_tool.invoke(params2)
```

### 2. 数据缓存

```python
# 缓存频繁使用的数据
cache = {}

def get_stock_data(symbol):
    if symbol in cache:
        return cache[symbol]
    
    result = alpha_vantage_tool.invoke({
        "function": "TIME_SERIES_DAILY",
        "symbol": symbol
    })
    cache[symbol] = result
    return result
```

### 3. 错误重试

```python
import time
import json

def robust_api_call(params, max_retries=3):
    for attempt in range(max_retries):
        result = alpha_vantage_tool.invoke(params)
        data = json.loads(result)
        
        if "error" not in data:
            return result
            
        if "API 限制" in data.get("error", ""):
            # API限制，等待更长时间
            time.sleep(60)
            continue
            
        # 其他错误直接返回
        return result
    
    return {"error": "达到最大重试次数"}
```

## 常见问题

### Q: 为什么返回API限制错误？

A: 免费版本有调用频率限制（每分钟5次）。建议：
- 在调用之间添加延迟
- 考虑升级到付费版本
- 使用数据缓存减少重复调用

### Q: 如何获取中国股票数据？

A: Alpha Vantage支持全球市场，中国股票需要添加交易所后缀：
```python
# 上海证券交易所
"symbol": "000001.SHG"
# 深圳证券交易所  
"symbol": "000001.SHE"
```

### Q: 技术指标返回空数据？

A: 技术指标需要足够的历史数据进行计算。确保：
- 使用 `outputsize: "full"` 获取更多历史数据
- 时间周期设置合理（如20日均线需要至少20个数据点）

## 运行示例

项目提供了完整的使用示例：

```bash
# 运行示例
python examples/alpha_vantage_examples.py
```

示例包含：
- 股票日线数据获取
- 实时报价查询
- 股票代码搜索
- 新闻情感分析
- 技术指标计算
- 基本面数据查询
- 外汇数据获取
- 经济指标查询

## 支持与反馈

如果遇到问题或有改进建议，请：

1. 检查API密钥配置
2. 查看Alpha Vantage API文档
3. 提交Issue或Pull Request

## 参考链接

- [Alpha Vantage官方网站](https://www.alphavantage.co/)
- [Alpha Vantage API文档](https://www.alphavantage.co/documentation/)
- [免费API密钥注册](https://www.alphavantage.co/support/#api-key)
- [定价信息](https://www.alphavantage.co/premium/) 