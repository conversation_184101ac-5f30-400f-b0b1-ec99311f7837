# 📊 MACD背离扫描器使用指南

## 🎯 功能概述

MACD背离扫描器是一个专门用于扫描股票市场MACD技术指标背离情况的工具。相比扫描整个市场，本工具专注于扫描三大重要指数的成分股，大大提高了扫描效率和实用性。

### 支持的指数
- 📈 **纳斯达克100** (NASDAQ_100): 50只主要科技股
- 🇭🇰 **恒生科技** (HANG_SENG_TECH): 30只港股科技股
- 🇨🇳 **中证300** (CSI_300): 100只A股核心股票

### 背离类型
- 🟢 **底背离** (bullish): 价格创新低但MACD不创新低，通常预示上涨
- 🔴 **顶背离** (bearish): 价格创新高但MACD不创新高，通常预示下跌

## 🚀 快速开始

### 1. 最简单的使用方式
```bash
python quick_macd_scan.py
```
这会快速扫描所有三个指数，显示发现的背离信号。

### 2. 完整功能扫描器
```bash
python macd_divergence_scanner.py
```
默认扫描所有指数的所有背离类型。

## 📋 详细使用方法

### 查看支持的指数
```bash
python macd_divergence_scanner.py --list
```

### 扫描特定指数
```bash
# 扫描纳斯达克100
python macd_divergence_scanner.py --index NASDAQ_100

# 扫描恒生科技
python macd_divergence_scanner.py --index HANG_SENG_TECH

# 扫描中证300
python macd_divergence_scanner.py --index CSI_300

# 扫描所有指数
python macd_divergence_scanner.py --index ALL
```

### 扫描特定背离类型
```bash
# 只扫描底背离（买入信号）
python macd_divergence_scanner.py --type bullish

# 只扫描顶背离（卖出信号）
python macd_divergence_scanner.py --type bearish

# 扫描所有类型（默认）
python macd_divergence_scanner.py --type bullish bearish
```

### 组合使用
```bash
# 扫描纳斯达克100的底背离
python macd_divergence_scanner.py --index NASDAQ_100 --type bullish

# 扫描恒生科技的顶背离
python macd_divergence_scanner.py --index HANG_SENG_TECH --type bearish
```

## 📊 输出结果说明

### 扫描进度信息
```
🔍 正在扫描 纳斯达克100...
✅ 纳斯达克100 扫描完成
   📈 成分股数量: 50
   🎯 成功扫描: 45
   🚨 发现背离: 3
   ⏱️  耗时: 25.6秒
   ⚠️  错误数量: 5
```

### 背离信号详情
```
🟢 底背离信号 (2个):
   📈 AAPL: 强度0.85, 置信度0.92
      价格区间: 150.20 - 145.80, MACD区间: -0.0234 - -0.0189

🔴 顶背离信号 (1个):
   📉 TSLA: 强度0.73, 置信度0.88
      价格区间: 245.60 - 252.30, MACD区间: 0.0156 - 0.0134
```

### 结果字段说明
- **强度 (strength)**: 背离信号的强度，0-1之间，越高越强
- **置信度 (confidence)**: 信号的可靠性，0-1之间，越高越可靠
- **价格区间**: 背离期间的价格范围
- **MACD区间**: 背离期间的MACD值范围

## ⚡ 性能优化

### 扫描速度对比
| 扫描范围 | 股票数量 | 预计耗时 | 推荐场景 |
|---------|---------|---------|---------|
| 整个市场 | 4000+ | 2-3小时 | 深度研究 |
| 三大指数 | 180只 | 3-5分钟 | 日常监控 |
| 单个指数 | 30-100只 | 1-2分钟 | 快速检查 |

### 并发优化
- 纳斯达克100: 8个并发连接
- 恒生科技: 5个并发连接  
- 中证300: 6个并发连接

## 🔧 配置说明

### 数据源配置
- **美股数据**: 优先使用 Akshare，回退到 Tushare
- **港股数据**: 使用 Tushare
- **A股数据**: 使用 Tushare

### 扫描参数
- **数据周期**: 最近6个月的日线数据
- **最小数据量**: 100个交易日
- **MACD参数**: 快线12，慢线26，信号线9

## 📁 文件结构

```
deer-flow/
├── macd_divergence_scanner.py     # 完整功能扫描器
├── quick_macd_scan.py             # 快速扫描器
├── backend/
│   ├── market_scanner.py          # 市场扫描核心逻辑
│   ├── index_components.py        # 指数成分股定义
│   ├── divergence_detector.py     # MACD背离检测算法
│   └── data_manager.py            # 数据管理
└── data/
    └── financial_data.db           # 本地数据库
```

## 🛠️ 故障排除

### 常见问题

1. **网络连接超时**
   ```
   解决方案: 检查网络连接，或稍后重试
   ```

2. **数据获取失败**
   ```
   解决方案: 确认Tushare token有效，检查API调用限制
   ```

3. **内存不足**
   ```
   解决方案: 减少并发数量，或分批扫描
   ```

### 日志查看
```bash
# 查看详细日志
python macd_divergence_scanner.py --index NASDAQ_100 2>&1 | tee scan.log
```

## 📈 使用建议

### 最佳实践
1. **定期扫描**: 建议每日收盘后扫描一次
2. **结合其他指标**: MACD背离应与其他技术指标结合使用
3. **风险控制**: 背离信号仅供参考，不构成投资建议

### 扫描策略
- **早盘前**: 扫描前一日的背离信号
- **盘中**: 快速扫描关注的指数
- **收盘后**: 全面扫描所有指数

### 信号过滤
- 优先关注强度 > 0.7 的信号
- 置信度 > 0.8 的信号更可靠
- 结合成交量确认信号有效性

## 🔄 更新日志

### v1.0.0 (2024-12-XX)
- ✅ 支持三大指数成分股扫描
- ✅ 异步并发扫描，大幅提升速度
- ✅ 智能数据源选择
- ✅ 详细的扫描结果展示
- ✅ 命令行参数支持

## 📞 技术支持

如有问题或建议，请查看项目文档或提交Issue。

---

**免责声明**: 本工具仅用于技术分析学习和研究，不构成投资建议。投资有风险，决策需谨慎。 