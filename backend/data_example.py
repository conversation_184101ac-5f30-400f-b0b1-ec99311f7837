#!/usr/bin/env python3
"""
数据管理系统使用示例
演示如何使用数据管理器下载数据、计算因子
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_manager import init_data_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    
    # 1. 配置参数
    # 注意：请在这里填入你的Tushare Token
    TUSHARE_TOKEN = "d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c"  # 请替换为你的真实token
    
    # 测试股票列表
    TEST_STOCKS = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    # 数据开始日期（获取最近半年数据）
    START_DATE = (datetime.now() - timedelta(days=180)).strftime('%Y%m%d')
    
    print("=" * 60)
    print("🚀 数据管理系统使用示例")
    print("=" * 60)
    
    try:
        # 2. 初始化数据管理器
        print("\n📊 初始化数据管理器...")
        data_manager = init_data_manager(TUSHARE_TOKEN)
        print("✅ 数据管理器初始化成功")
        
        # 3. 初始化数据（下载股票数据和计算因子）
        print(f"\n📥 开始下载 {len(TEST_STOCKS)} 只股票的数据...")
        print(f"📅 数据起始日期: {START_DATE}")
        print(f"📋 股票列表: {', '.join(TEST_STOCKS)}")
        
        data_manager.initialize_data(TEST_STOCKS, START_DATE)
        print("✅ 数据初始化完成")
        
        # 4. 查看数据库状态
        print("\n📈 查看数据库状态...")
        with data_manager.db_manager.get_connection() as conn:
            # 查询各表记录数
            stock_count = conn.execute("SELECT COUNT(*) FROM stock_info").fetchone()[0]
            daily_count = conn.execute("SELECT COUNT(*) FROM daily_data").fetchone()[0]
            factor_count = conn.execute("SELECT COUNT(*) FROM factor_data").fetchone()[0]
            
            print(f"  📊 股票信息: {stock_count} 条记录")
            print(f"  📊 日线数据: {daily_count} 条记录")
            print(f"  📊 因子数据: {factor_count} 条记录")
            
            # 查询数据日期范围
            date_range = conn.execute("""
                SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date 
                FROM daily_data
            """).fetchone()
            
            if date_range[0]:
                print(f"  📅 数据日期范围: {date_range[0]} ~ {date_range[1]}")
        
        # 5. 测试数据获取功能
        print("\n🔍 测试数据获取功能...")
        test_symbol = TEST_STOCKS[0]
        
        # 获取股票价格数据
        print(f"  📈 获取 {test_symbol} 的价格数据...")
        price_data = data_manager.get_stock_data(test_symbol)
        if not price_data.empty:
            print(f"    ✅ 获取到 {len(price_data)} 天的数据")
            print(f"    📊 最新价格: {price_data['close'].iloc[-1]:.2f}")
        else:
            print(f"    ❌ 未获取到 {test_symbol} 的数据")
        
        # 获取因子数据
        print(f"  🧮 获取 {test_symbol} 的因子数据...")
        factor_data = data_manager.get_factor_data(test_symbol)
        if factor_data:
            print(f"    ✅ 获取到 {len(factor_data)} 个因子")
            # 显示前几个因子
            for i, (factor_name, value) in enumerate(list(factor_data.items())[:5]):
                print(f"    📊 {factor_name}: {value:.4f}")
            if len(factor_data) > 5:
                print(f"    ... 还有 {len(factor_data) - 5} 个因子")
        else:
            print(f"    ❌ 未获取到 {test_symbol} 的因子数据")
        
        # 6. 测试实时因子计算
        print(f"\n🧮 测试实时因子计算...")
        factor_calculator = data_manager.factor_calculator
        
        # 计算特定因子
        test_factors = ['rsi', 'macd', 'bollinger', 'ma20', 'pe_ratio']
        
        # 获取最新的股票数据用于实时计算
        latest_stock_data = data_manager.get_stock_data(test_symbol)

        if not latest_stock_data.empty:
            realtime_factors = factor_calculator.calculate_all_factors(latest_stock_data, test_factors)
            
            if realtime_factors:
                print(f"  ✅ 实时计算了 {len(realtime_factors)} 个因子")
                for factor_name, value in realtime_factors.items():
                    print(f"    📊 {factor_name}: {value:.4f}")
            else:
                print(f"  ❌ 实时因子计算失败")
        else:
            print(f"  ❌ 未获取到 {test_symbol} 的最新股票数据，无法进行实时因子计算")
        
        # 7. 获取可用股票列表
        print(f"\n📋 获取数据库中的可用股票...")
        available_stocks = data_manager.get_available_stocks()
        print(f"  ✅ 数据库中有 {len(available_stocks)} 只股票")
        print(f"  📊 股票列表: {', '.join(available_stocks)}")
        
        # 8. 测试数据更新功能
        print(f"\n🔄 测试数据更新功能...")
        print("  📥 更新最新数据（最近7天）...")
        data_manager.update_data(TEST_STOCKS[:2])  # 只更新前两只股票
        print("  ✅ 数据更新完成")
        
        print("\n" + "=" * 60)
        print("🎉 数据管理系统示例运行完成！")
        print("=" * 60)
        
        # 使用提示
        print("\n💡 使用提示:")
        print("1. 数据库文件位置: data/financial_data.db")
        print("2. 可以通过 API 接口访问数据管理功能")
        print("3. 因子数据会自动缓存，提高查询性能")
        print("4. 支持增量数据更新，避免重复下载")
        
    except Exception as e:
        logger.error(f"示例运行出错: {e}")
        print(f"\n❌ 运行出错: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 检查 Tushare Token 是否正确设置")
        print("2. 确保网络连接正常")
        print("3. 检查是否有足够的磁盘空间")

if __name__ == "__main__":
    main() 