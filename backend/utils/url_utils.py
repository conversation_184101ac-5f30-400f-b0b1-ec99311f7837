import urllib.parse
from typing import Dict, Optional
import re


def encode_chinese_params(url: str) -> str:
    """编码URL中的中文参数"""
    try:
        parsed_url = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        
        # 重新编码所有参数，特别处理中文
        encoded_params = {}
        for key, values in query_params.items():
            encoded_params[key] = [urllib.parse.quote(value, safe='') for value in values]
        
        # 重建查询字符串
        encoded_query = urllib.parse.urlencode(encoded_params, doseq=True)
        
        # 重建完整URL
        return urllib.parse.urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            encoded_query,
            parsed_url.fragment
        ))
    except Exception as e:
        # 如果解析失败，返回原始URL
        return url


def decode_chinese_params(url: str) -> str:
    """解码URL中的中文参数"""
    try:
        parsed_url = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        
        # 解码所有参数
        decoded_params = {}
        for key, values in query_params.items():
            decoded_params[key] = [urllib.parse.unquote(value) for value in values]
        
        # 重建查询字符串（不编码，保持中文）
        decoded_parts = []
        for key, values in decoded_params.items():
            for value in values:
                decoded_parts.append(f"{key}={value}")
        decoded_query = "&".join(decoded_parts)
        
        # 重建完整URL
        return urllib.parse.urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            decoded_query,
            parsed_url.fragment
        ))
    except Exception as e:
        # 如果解析失败，返回原始URL
        return url


def parse_tushare_news_url(url: str) -> Dict[str, Optional[str]]:
    """解析Tushare新闻URL获取参数"""
    result = {
        'source': None,
        'keyword': None,
        'encoded_keyword': None,
        'is_valid': False
    }
    
    try:
        # 检查是否为Tushare新闻URL
        if not url.startswith('https://tushare.pro/news/'):
            return result
        
        parsed_url = urllib.parse.urlparse(url)
        
        # 提取新闻源（sina, eastmoney等）
        path_parts = parsed_url.path.strip('/').split('/')
        if len(path_parts) >= 2 and path_parts[0] == 'news':
            result['source'] = path_parts[1]
        
        # 提取搜索关键词
        query_params = urllib.parse.parse_qs(parsed_url.query)
        if 's' in query_params and query_params['s']:
            encoded_keyword = query_params['s'][0]
            result['encoded_keyword'] = encoded_keyword
            result['keyword'] = urllib.parse.unquote(encoded_keyword)
            result['is_valid'] = True
        
        return result
    except Exception as e:
        return result


def is_tushare_news_url(input_str: str) -> bool:
    """检测是否为Tushare新闻URL格式"""
    if not input_str:
        return False
    
    # 移除可能的@前缀
    clean_input = input_str.strip()
    if clean_input.startswith('@'):
        clean_input = clean_input[1:]
    
    # 检查URL格式
    tushare_patterns = [
        r'https://tushare\.pro/news/[^/]+\?s=.+',
        r'tushare\.pro/news/[^/]+\?s=.+'
    ]
    
    for pattern in tushare_patterns:
        if re.match(pattern, clean_input):
            return True
    
    return False


def extract_keyword_from_tushare_url(input_str: str) -> Optional[str]:
    """从Tushare URL格式的输入中提取关键词"""
    if not is_tushare_news_url(input_str):
        return None
    
    try:
        # 移除@前缀
        clean_input = input_str.strip()
        if clean_input.startswith('@'):
            clean_input = clean_input[1:]
        
        # 确保URL有协议
        if not clean_input.startswith('http'):
            clean_input = 'https://' + clean_input
        
        parsed_data = parse_tushare_news_url(clean_input)
        return parsed_data.get('keyword')
    except Exception:
        return None 