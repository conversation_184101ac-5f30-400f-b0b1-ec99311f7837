#!/usr/bin/env python3
"""
指数成分股定义
包含纳斯达克100、恒生科技和中证300指数的成分股
"""

# 纳斯达克100指数成分股（前50只主要股票）
NASDAQ_100_STOCKS = [
    # 科技巨头
    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'META', 'TSLA', 'NVDA',
    'NFLX', 'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO',
    'TXN', 'CSCO', 'IBM', 'INTU', 'NOW', 'AMAT', 'MU', 'ADI',
    'LRCX', 'KLAC', 'MRVL', 'SNPS', 'CDNS', 'FTNT', 'TEAM', 'WDAY',
    
    # 生物科技和医疗
    'GILD', 'AMGN', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'MDLZ',
    
    # 消费和服务
    'COST', 'SBUX', 'ATVI', 'EA', 'NTES', 'JD', 'BIDU', 'PDD',
    'ZOOM', 'DOCU', 'OKTA', 'ZM', 'DDOG', 'CRWD', 'ZS', 'PANW'
]

# 恒生科技指数成分股（30只）
HANG_SENG_TECH_STOCKS = [
    # 腾讯系
    '00700.HK',  # 腾讯控股
    '01024.HK',  # 快手-W
    '06618.HK',  # 京东健康
    
    # 阿里系
    '09988.HK',  # 阿里巴巴-SW
    '09618.HK',  # 京东集团-SW
    '03690.HK',  # 美团-W
    
    # 小米生态
    '01810.HK',  # 小米集团-W
    '02015.HK',  # 理想汽车-W
    '09868.HK',  # 小鹏汽车-W
    '09866.HK',  # 蔚来-SW
    
    # 教育科技
    '09901.HK',  # 新东方-S
    '01797.HK',  # 新东方在线
    
    # 游戏娱乐
    '00175.HK',  # 吉利汽车
    '02382.HK',  # 舜宇光学科技
    '01093.HK',  # 石药集团
    
    # 金融科技
    '06060.HK',  # 众安在线
    '03888.HK',  # 金山软件
    '01833.HK',  # 平安好医生
    
    # 电商物流
    '01772.HK',  # 赣锋锂业
    '02269.HK',  # 药明生物
    '01918.HK',  # 融创中国
    
    # 其他科技股
    '00268.HK',  # 金蝶国际
    '00992.HK',  # 联想集团
    '01177.HK',  # 中国生物制药
    '01299.HK',  # 友邦保险
    '02020.HK',  # 安踏体育
    '01211.HK',  # 比亚迪股份
    '01972.HK',  # 太古地产
    '00883.HK',  # 中国海洋石油
    '01658.HK'   # 邮储银行
]

# 中证300指数成分股（前100只主要股票）
CSI_300_STOCKS = [
    # 银行股
    '600036.SH',  # 招商银行
    '000001.SZ',  # 平安银行
    '600000.SH',  # 浦发银行
    '601166.SH',  # 兴业银行
    '000002.SZ',  # 万科A
    '600519.SH',  # 贵州茅台
    '000858.SZ',  # 五粮液
    '600276.SH',  # 恒瑞医药
    '000725.SZ',  # 京东方A
    '000776.SZ',  # 广发证券
    
    # 科技股
    '000063.SZ',  # 中兴通讯
    '002415.SZ',  # 海康威视
    '300014.SZ',  # 亿纬锂能
    '300015.SZ',  # 爱尔眼科
    '300059.SZ',  # 东方财富
    '300124.SZ',  # 汇川技术
    '300142.SZ',  # 沃森生物
    '300750.SZ',  # 宁德时代
    '688036.SH',  # 传音控股
    '688111.SH',  # 金山办公
    
    # 消费股
    '600887.SH',  # 伊利股份
    '600030.SH',  # 中信证券
    '002594.SZ',  # 比亚迪
    '002230.SZ',  # 科大讯飞
    '002304.SZ',  # 洋河股份
    '600104.SH',  # 上汽集团
    '600585.SH',  # 海螺水泥
    '600690.SH',  # 海尔智家
    '600703.SH',  # 三安光电
    '600745.SH',  # 闻泰科技
    
    # 医药股
    '300760.SZ',  # 迈瑞医疗
    '000661.SZ',  # 长春高新
    '002821.SZ',  # 凯莱英
    '300347.SZ',  # 泰格医药
    '300122.SZ',  # 智飞生物
    '300601.SZ',  # 康泰生物
    '603259.SH',  # 药明康德
    '688169.SH',  # 石头科技
    '688187.SH',  # 时代电气
    '688223.SH',  # 晶科能源
    
    # 新能源
    '300274.SZ',  # 阳光电源
    '002460.SZ',  # 赣锋锂业
    '300438.SZ',  # 鹏辉能源
    '002129.SZ',  # 中环股份
    '300316.SZ',  # 晶盛机电
    '002709.SZ',  # 天赐材料
    '300073.SZ',  # 当升科技
    '002466.SZ',  # 天齐锂业
    '300014.SZ',  # 亿纬锂能
    '300750.SZ',  # 宁德时代
    
    # 地产建筑
    '000002.SZ',  # 万科A
    '600048.SH',  # 保利发展
    '001979.SZ',  # 招商蛇口
    '600606.SH',  # 绿地控股
    '000069.SZ',  # 华侨城A
    
    # 基础设施
    '601318.SH',  # 中国平安
    '601398.SH',  # 工商银行
    '601939.SH',  # 建设银行
    '600028.SH',  # 中国石化
    '600050.SH',  # 中国联通
    '600900.SH',  # 长江电力
    '601857.SH',  # 中国石油
    '601988.SH',  # 中国银行
    '601668.SH',  # 中国建筑
    '600837.SH',  # 海通证券
    
    # 制造业
    '000858.SZ',  # 五粮液
    '002142.SZ',  # 宁波银行
    '600309.SH',  # 万华化学
    '600438.SH',  # 通威股份
    '600741.SH',  # 华域汽车
    '002008.SZ',  # 大族激光
    '002027.SZ',  # 分众传媒
    '002049.SZ',  # 紫光国微
    '002120.SZ',  # 韵达股份
    '002352.SZ',  # 顺丰控股
    
    # 其他重要股票
    '600196.SH',  # 复星医药
    '600256.SH',  # 广汇能源
    '600346.SH',  # 恒力石化
    '600406.SH',  # 国电南瑞
    '600570.SH',  # 恒生电子
    '600763.SH',  # 通策医疗
    '600809.SH',  # 山西汾酒
    '600893.SH',  # 航发动力
    '600958.SH',  # 东方证券
    '601012.SH',  # 隆基绿能
    '601066.SH',  # 中信建投
    '601138.SH',  # 工业富联
    '601236.SH',  # 红塔证券
    '601288.SH',  # 农业银行
    '601319.SH',  # 中国人保
    '601336.SH',  # 新华保险
    '601601.SH',  # 中国太保
    '601628.SH',  # 中国人寿
    '601688.SH',  # 华泰证券
    '601816.SH',  # 京沪高铁
    '601888.SH',  # 中国中免
    '601899.SH',  # 紫金矿业
    '603501.SH',  # 韦尔股份
    '603986.SH',  # 兆易创新
    '688008.SH',  # 澜起科技
    '688012.SH',  # 中微公司
    '688981.SH'   # 中芯国际
]

# 所有指数成分股的汇总
ALL_INDEX_STOCKS = {
    'NASDAQ_100': NASDAQ_100_STOCKS,
    'HANG_SENG_TECH': HANG_SENG_TECH_STOCKS,
    'CSI_300': CSI_300_STOCKS
}

def get_index_stocks(index_name: str) -> list:
    """
    获取指定指数的成分股列表
    
    Args:
        index_name: 指数名称 ('NASDAQ_100', 'HANG_SENG_TECH', 'CSI_300', 'ALL')
        
    Returns:
        股票代码列表
    """
    if index_name == 'ALL':
        # 返回所有指数的成分股（去重）
        all_stocks = []
        for stocks in ALL_INDEX_STOCKS.values():
            all_stocks.extend(stocks)
        return list(set(all_stocks))
    
    return ALL_INDEX_STOCKS.get(index_name, [])

def get_all_index_names() -> list:
    """获取所有支持的指数名称"""
    return list(ALL_INDEX_STOCKS.keys()) + ['ALL'] 