#!/usr/bin/env python3
"""
回测框架
支持策略回测、性能分析和风险评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Callable
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class OrderType(Enum):
    """订单类型"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"

@dataclass
class Order:
    """订单类"""
    symbol: str
    order_type: OrderType
    quantity: int
    price: float
    timestamp: datetime
    status: OrderStatus = OrderStatus.PENDING
    commission: float = 0.0

@dataclass
class Position:
    """持仓类"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    
    @property
    def market_value(self) -> float:
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        return (self.current_price - self.avg_cost) * self.quantity
    
    @property
    def unrealized_pnl_pct(self) -> float:
        if self.avg_cost == 0:
            return 0
        return (self.current_price - self.avg_cost) / self.avg_cost

@dataclass
class Trade:
    """交易记录"""
    symbol: str
    entry_date: datetime
    exit_date: datetime
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    pnl_pct: float
    commission: float
    hold_days: int

class Portfolio:
    """投资组合"""
    
    def __init__(self, initial_capital: float = 100000, commission_rate: float = 0.001):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.commission_rate = commission_rate
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Trade] = []
        self.daily_values: List[Dict] = []
        
    def add_order(self, symbol: str, order_type: OrderType, quantity: int, price: float, timestamp: datetime):
        """添加订单"""
        commission = abs(quantity * price * self.commission_rate)
        order = Order(symbol, order_type, quantity, price, timestamp, commission=commission)
        self.orders.append(order)
        return order
    
    def execute_order(self, order: Order, current_prices: Dict[str, float]):
        """执行订单"""
        if order.status != OrderStatus.PENDING:
            return False
        
        symbol = order.symbol
        current_price = current_prices.get(symbol, order.price)
        
        if order.order_type == OrderType.BUY:
            total_cost = order.quantity * current_price + order.commission
            if self.cash >= total_cost:
                self.cash -= total_cost
                
                if symbol in self.positions:
                    # 更新现有持仓
                    pos = self.positions[symbol]
                    total_quantity = pos.quantity + order.quantity
                    total_cost_basis = pos.quantity * pos.avg_cost + order.quantity * current_price
                    pos.avg_cost = total_cost_basis / total_quantity
                    pos.quantity = total_quantity
                else:
                    # 新建持仓
                    self.positions[symbol] = Position(symbol, order.quantity, current_price, current_price)
                
                order.status = OrderStatus.FILLED
                return True
        
        elif order.order_type == OrderType.SELL:
            if symbol in self.positions and self.positions[symbol].quantity >= order.quantity:
                pos = self.positions[symbol]
                
                # 记录交易
                pnl = (current_price - pos.avg_cost) * order.quantity - order.commission
                pnl_pct = (current_price - pos.avg_cost) / pos.avg_cost if pos.avg_cost > 0 else 0
                
                trade = Trade(
                    symbol=symbol,
                    entry_date=order.timestamp,  # 简化处理
                    exit_date=order.timestamp,
                    entry_price=pos.avg_cost,
                    exit_price=current_price,
                    quantity=order.quantity,
                    pnl=pnl,
                    pnl_pct=pnl_pct,
                    commission=order.commission,
                    hold_days=1  # 简化处理
                )
                self.trades.append(trade)
                
                # 更新现金和持仓
                self.cash += order.quantity * current_price - order.commission
                pos.quantity -= order.quantity
                
                if pos.quantity == 0:
                    del self.positions[symbol]
                
                order.status = OrderStatus.FILLED
                return True
        
        return False
    
    def update_positions(self, current_prices: Dict[str, float]):
        """更新持仓价格"""
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position.current_price = current_prices[symbol]
    
    def get_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """计算投资组合总价值"""
        self.update_positions(current_prices)
        portfolio_value = self.cash
        for position in self.positions.values():
            portfolio_value += position.market_value
        return portfolio_value
    
    def record_daily_value(self, date: datetime, current_prices: Dict[str, float]):
        """记录每日价值"""
        portfolio_value = self.get_portfolio_value(current_prices)
        
        daily_record = {
            'date': date,
            'portfolio_value': portfolio_value,
            'cash': self.cash,
            'positions_value': portfolio_value - self.cash,
            'daily_return': 0.0,
            'cumulative_return': (portfolio_value - self.initial_capital) / self.initial_capital
        }
        
        if len(self.daily_values) > 0:
            prev_value = self.daily_values[-1]['portfolio_value']
            daily_record['daily_return'] = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0
        
        self.daily_values.append(daily_record)

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, commission_rate: float = 0.001):
        self.portfolio = Portfolio(initial_capital, commission_rate)
        self.strategy_func: Optional[Callable] = None
        self.price_data: Dict[str, pd.DataFrame] = {}
        self.factor_data: Dict[str, pd.DataFrame] = {}
        
    def add_price_data(self, symbol: str, data: pd.DataFrame):
        """添加价格数据"""
        self.price_data[symbol] = data.copy()
    
    def add_factor_data(self, symbol: str, data: pd.DataFrame):
        """添加因子数据"""
        self.factor_data[symbol] = data.copy()
    
    def set_strategy(self, strategy_func: Callable):
        """设置策略函数"""
        self.strategy_func = strategy_func
    
    def run_backtest(self, start_date: str, end_date: str) -> Dict:
        """运行回测"""
        if not self.strategy_func:
            raise ValueError("请先设置策略函数")
        
        if not self.price_data:
            raise ValueError("请先添加价格数据")
        
        # 获取日期范围
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 获取所有交易日
        all_dates = set()
        for data in self.price_data.values():
            dates = data.index[(data.index >= start_dt) & (data.index <= end_dt)]
            all_dates.update(dates)
        
        trading_days = sorted(list(all_dates))
        
        # 逐日回测
        for current_date in trading_days:
            # 获取当日价格
            current_prices = {}
            for symbol, data in self.price_data.items():
                if current_date in data.index:
                    current_prices[symbol] = data.loc[current_date, 'close']
            
            # 获取当日因子数据
            current_factors = {}
            for symbol, data in self.factor_data.items():
                if current_date in data.index:
                    current_factors[symbol] = data.loc[current_date].to_dict()
            
            # 执行策略
            signals = self.strategy_func(
                current_date=current_date,
                prices=current_prices,
                factors=current_factors,
                portfolio=self.portfolio
            )
            
            # 处理信号
            if signals:
                for signal in signals:
                    order = self.portfolio.add_order(
                        symbol=signal['symbol'],
                        order_type=OrderType(signal['action']),
                        quantity=signal['quantity'],
                        price=current_prices.get(signal['symbol'], 0),
                        timestamp=current_date
                    )
                    self.portfolio.execute_order(order, current_prices)
            
            # 记录每日价值
            self.portfolio.record_daily_value(current_date, current_prices)
        
        # 生成回测报告
        return self.generate_report()
    
    def generate_report(self) -> Dict:
        """生成回测报告"""
        if not self.portfolio.daily_values:
            return {}
        
        df = pd.DataFrame(self.portfolio.daily_values)
        df.set_index('date', inplace=True)
        
        # 基础统计
        total_return = df['cumulative_return'].iloc[-1]
        daily_returns = df['daily_return']
        
        # 风险指标
        volatility = daily_returns.std() * np.sqrt(252)  # 年化波动率
        sharpe_ratio = (daily_returns.mean() * 252) / (daily_returns.std() * np.sqrt(252)) if daily_returns.std() > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + daily_returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 交易统计
        trades_df = pd.DataFrame([trade.__dict__ for trade in self.portfolio.trades])
        
        trade_stats = {}
        if not trades_df.empty:
            trade_stats = {
                'total_trades': len(trades_df),
                'winning_trades': len(trades_df[trades_df['pnl'] > 0]),
                'losing_trades': len(trades_df[trades_df['pnl'] < 0]),
                'win_rate': len(trades_df[trades_df['pnl'] > 0]) / len(trades_df) if len(trades_df) > 0 else 0,
                'avg_win': trades_df[trades_df['pnl'] > 0]['pnl'].mean() if len(trades_df[trades_df['pnl'] > 0]) > 0 else 0,
                'avg_loss': trades_df[trades_df['pnl'] < 0]['pnl'].mean() if len(trades_df[trades_df['pnl'] < 0]) > 0 else 0,
                'profit_factor': abs(trades_df[trades_df['pnl'] > 0]['pnl'].sum() / trades_df[trades_df['pnl'] < 0]['pnl'].sum()) if trades_df[trades_df['pnl'] < 0]['pnl'].sum() != 0 else float('inf')
            }
        
        report = {
            'summary': {
                'initial_capital': self.portfolio.initial_capital,
                'final_value': df['portfolio_value'].iloc[-1],
                'total_return': total_return,
                'annualized_return': (1 + total_return) ** (252 / len(df)) - 1,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'calmar_ratio': (total_return / abs(max_drawdown)) if max_drawdown != 0 else float('inf')
            },
            'trade_stats': trade_stats,
            'daily_values': df.to_dict('records'),
            'trades': trades_df.to_dict('records') if not trades_df.empty else [],
            'positions': {symbol: pos.__dict__ for symbol, pos in self.portfolio.positions.items()}
        }
        
        return report

# 示例策略函数
def simple_momentum_strategy(current_date, prices, factors, portfolio):
    """简单动量策略示例"""
    signals = []
    
    for symbol, price in prices.items():
        if symbol in factors:
            factor_data = factors[symbol]
            
            # 简单的动量信号：RSI < 30 买入，RSI > 70 卖出
            rsi = factor_data.get('rsi', 50)
            
            if rsi < 30 and symbol not in portfolio.positions:
                # 买入信号
                position_size = int(portfolio.cash * 0.1 / price)  # 10%仓位
                if position_size > 0:
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'quantity': position_size
                    })
            
            elif rsi > 70 and symbol in portfolio.positions:
                # 卖出信号
                position = portfolio.positions[symbol]
                signals.append({
                    'symbol': symbol,
                    'action': 'sell',
                    'quantity': position.quantity
                })
    
    return signals

def factor_score_strategy(current_date, prices, factors, portfolio):
    """基于因子评分的策略"""
    signals = []
    
    # 计算所有股票的综合评分
    stock_scores = {}
    for symbol, factor_data in factors.items():
        if symbol in prices:
            # 简化的评分计算
            score = 0
            score += (factor_data.get('rsi', 50) - 50) / 50 * 0.2  # RSI权重20%
            score += factor_data.get('macd', 0) * 0.3  # MACD权重30%
            score += (factor_data.get('roe', 0) / 20) * 0.3  # ROE权重30%
            score += (1 / factor_data.get('pe_ratio', 20)) * 0.2  # PE权重20%
            
            stock_scores[symbol] = score
    
    # 选择评分最高的前3只股票
    if stock_scores:
        sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
        top_stocks = [symbol for symbol, score in sorted_stocks[:3] if score > 0]
        
        # 卖出不在前3的持仓
        for symbol in list(portfolio.positions.keys()):
            if symbol not in top_stocks:
                position = portfolio.positions[symbol]
                signals.append({
                    'symbol': symbol,
                    'action': 'sell',
                    'quantity': position.quantity
                })
        
        # 买入前3只股票
        target_weight = 1.0 / len(top_stocks) if top_stocks else 0
        for symbol in top_stocks:
            if symbol in prices:
                target_value = portfolio.get_portfolio_value(prices) * target_weight
                current_value = portfolio.positions.get(symbol, Position(symbol, 0, 0, 0)).market_value
                
                if target_value > current_value:
                    quantity_to_buy = int((target_value - current_value) / prices[symbol])
                    if quantity_to_buy > 0:
                        signals.append({
                            'symbol': symbol,
                            'action': 'buy',
                            'quantity': quantity_to_buy
                        })
    
    return signals

# 全局回测引擎实例
backtest_engine = BacktestEngine()

def get_backtest_engine() -> BacktestEngine:
    """获取回测引擎实例"""
    return backtest_engine 