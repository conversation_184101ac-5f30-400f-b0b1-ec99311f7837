#!/usr/bin/env python3
"""
数据源管理模块
集成多个金融数据API，提供统一的数据接口
"""

import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime, timedelta
import requests
import time
from functools import lru_cache

logger = logging.getLogger(__name__)

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.cache_duration = 300  # 5分钟缓存
        self.last_cache_time = {}
        self.cache_data = {}
    
    @lru_cache(maxsize=100)
    def get_stock_info(self, symbol: str) -> Dict:
        """获取股票基本信息"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', None),
                'pb_ratio': info.get('priceToBook', None),
                'dividend_yield': info.get('dividendYield', 0),
                'beta': info.get('beta', None),
                'currency': info.get('currency', 'USD')
            }
        except Exception as e:
            logger.error(f"获取股票信息失败 {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def get_price_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """获取价格数据"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            if data.empty:
                logger.warning(f"未获取到 {symbol} 的价格数据")
                return pd.DataFrame()
            
            # 标准化列名
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            data.index.name = 'date'
            
            return data
            
        except Exception as e:
            logger.error(f"获取价格数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def get_financial_data(self, symbol: str) -> Dict:
        """获取财务数据"""
        try:
            ticker = yf.Ticker(symbol)
            
            # 获取财务报表
            financials = ticker.financials
            balance_sheet = ticker.balance_sheet
            cash_flow = ticker.cashflow
            
            # 提取关键财务指标
            financial_metrics = {}
            
            if not financials.empty:
                # 收入和利润指标
                if 'Total Revenue' in financials.index:
                    revenue = financials.loc['Total Revenue'].iloc[0] if len(financials.columns) > 0 else None
                    financial_metrics['revenue'] = float(revenue) if revenue and not pd.isna(revenue) else None
                
                if 'Net Income' in financials.index:
                    net_income = financials.loc['Net Income'].iloc[0] if len(financials.columns) > 0 else None
                    financial_metrics['net_income'] = float(net_income) if net_income and not pd.isna(net_income) else None
            
            if not balance_sheet.empty:
                # 资产负债指标
                if 'Total Assets' in balance_sheet.index:
                    total_assets = balance_sheet.loc['Total Assets'].iloc[0] if len(balance_sheet.columns) > 0 else None
                    financial_metrics['total_assets'] = float(total_assets) if total_assets and not pd.isna(total_assets) else None
                
                if 'Total Debt' in balance_sheet.index:
                    total_debt = balance_sheet.loc['Total Debt'].iloc[0] if len(balance_sheet.columns) > 0 else None
                    financial_metrics['total_debt'] = float(total_debt) if total_debt and not pd.isna(total_debt) else None
            
            return financial_metrics
            
        except Exception as e:
            logger.error(f"获取财务数据失败 {symbol}: {e}")
            return {}
    
    def get_market_data(self, symbol: str) -> Dict:
        """获取市场数据"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            market_data = {
                'current_price': info.get('currentPrice', None),
                'previous_close': info.get('previousClose', None),
                'open_price': info.get('open', None),
                'day_high': info.get('dayHigh', None),
                'day_low': info.get('dayLow', None),
                'volume': info.get('volume', None),
                'avg_volume': info.get('averageVolume', None),
                'market_cap': info.get('marketCap', None),
                'shares_outstanding': info.get('sharesOutstanding', None),
                'float_shares': info.get('floatShares', None),
                '52_week_high': info.get('fiftyTwoWeekHigh', None),
                '52_week_low': info.get('fiftyTwoWeekLow', None),
            }
            
            return market_data
            
        except Exception as e:
            logger.error(f"获取市场数据失败 {symbol}: {e}")
            return {}
    
    def get_analyst_data(self, symbol: str) -> Dict:
        """获取分析师数据"""
        try:
            ticker = yf.Ticker(symbol)
            
            # 获取分析师推荐
            recommendations = ticker.recommendations
            analyst_data = {}
            
            if recommendations is not None and not recommendations.empty:
                latest_rec = recommendations.iloc[-1]
                analyst_data = {
                    'recommendation': latest_rec.get('To Grade', 'N/A'),
                    'target_price': None,  # yfinance可能不提供目标价
                    'analyst_count': len(recommendations),
                    'last_update': latest_rec.name.strftime('%Y-%m-%d') if hasattr(latest_rec.name, 'strftime') else None
                }
            
            return analyst_data
            
        except Exception as e:
            logger.error(f"获取分析师数据失败 {symbol}: {e}")
            return {}
    
    def get_comprehensive_data(self, symbol: str) -> Dict:
        """获取综合数据"""
        try:
            comprehensive_data = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'basic_info': self.get_stock_info(symbol),
                'market_data': self.get_market_data(symbol),
                'financial_data': self.get_financial_data(symbol),
                'analyst_data': self.get_analyst_data(symbol)
            }
            
            # 获取价格数据
            price_data = self.get_price_data(symbol, period="6mo")
            if not price_data.empty:
                comprehensive_data['price_summary'] = {
                    'latest_close': float(price_data['close'].iloc[-1]),
                    'avg_volume_3m': float(price_data['volume'].tail(60).mean()),
                    'volatility_3m': float(price_data['close'].pct_change().tail(60).std() * np.sqrt(252)),
                    'return_3m': float((price_data['close'].iloc[-1] / price_data['close'].iloc[-60] - 1) * 100) if len(price_data) >= 60 else None,
                    'return_1m': float((price_data['close'].iloc[-1] / price_data['close'].iloc[-20] - 1) * 100) if len(price_data) >= 20 else None,
                }
            
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"获取综合数据失败 {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}

class AlternativeDataSource:
    """替代数据源"""
    
    def __init__(self):
        self.news_cache = {}
        self.sentiment_cache = {}
    
    def get_news_sentiment(self, symbol: str) -> Dict:
        """获取新闻情感分析（模拟）"""
        try:
            # 这里可以集成真实的新闻API，如NewsAPI、Alpha Vantage等
            # 目前返回模拟数据
            sentiment_score = np.random.normal(0, 0.3)  # 模拟情感分数
            
            return {
                'symbol': symbol,
                'sentiment_score': float(sentiment_score),
                'sentiment_label': 'positive' if sentiment_score > 0.1 else 'negative' if sentiment_score < -0.1 else 'neutral',
                'news_count': np.random.randint(5, 20),
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取新闻情感失败 {symbol}: {e}")
            return {}
    
    def get_social_sentiment(self, symbol: str) -> Dict:
        """获取社交媒体情感（模拟）"""
        try:
            # 这里可以集成Twitter API、Reddit API等
            # 目前返回模拟数据
            social_score = np.random.normal(0, 0.4)
            
            return {
                'symbol': symbol,
                'social_sentiment': float(social_score),
                'mention_count': np.random.randint(100, 1000),
                'trending_score': np.random.uniform(0, 1),
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取社交情感失败 {symbol}: {e}")
            return {}

# 全局实例
data_source_manager = DataSourceManager()
alternative_data_source = AlternativeDataSource()

def get_data_source_manager() -> DataSourceManager:
    """获取数据源管理器实例"""
    return data_source_manager

def get_alternative_data_source() -> AlternativeDataSource:
    """获取替代数据源实例"""
    return alternative_data_source 