#!/usr/bin/env python3
"""
因子计算API接口模块
为增强型因子计算提供RESTful API接口
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, validator
import logging
from datetime import datetime, timedelta

from .enhanced_factors import FactorManager, get_enhanced_factor_calculator, list_all_available_factors, get_factor_descriptions
from .data_manager import init_data_manager

logger = logging.getLogger(__name__)

# 创建路由器
factor_router = APIRouter(prefix="/factors", tags=["因子计算"])

# 请求模型
class FactorCalculationRequest(BaseModel):
    """因子计算请求模型"""
    symbol: str
    factor_names: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    
    @validator('symbol')
    def validate_symbol(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('股票代码不能为空')
        return v.strip().upper()
    
    @validator('start_date', 'end_date', pre=True, always=True)
    def validate_dates(cls, v):
        if v:
            try:
                datetime.strptime(v, '%Y-%m-%d')
                return v
            except ValueError:
                raise ValueError('日期格式错误，应为YYYY-MM-DD')
        return None

class BatchFactorCalculationRequest(BaseModel):
    """批量因子计算请求模型"""
    symbols: List[str]
    factor_names: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    
    @validator('symbols')
    def validate_symbols(cls, v):
        if not v or len(v) == 0:
            raise ValueError('股票代码列表不能为空')
        if len(v) > 50:  # 限制批量数量
            raise ValueError('批量计算股票数量不能超过50只')
        return [symbol.strip().upper() for symbol in v]

class FactorResponse(BaseModel):
    """因子计算响应模型"""
    success: bool
    symbol: str
    factors: Dict[str, float]
    calculation_time: str
    message: Optional[str] = None

class BatchFactorResponse(BaseModel):
    """批量因子计算响应模型"""
    success: bool
    results: Dict[str, Dict[str, float]]
    total_symbols: int
    successful_symbols: int
    calculation_time: str
    errors: Optional[List[str]] = None

class FactorListResponse(BaseModel):
    """因子列表响应模型"""
    categories: Dict[str, List[str]]
    total_factors: int
    descriptions: Dict[str, str]

# 依赖注入
def get_factor_manager():
    """获取因子管理器实例"""
    try:
        # 这里需要从配置或环境变量获取token
        data_manager = init_data_manager("your_tushare_token_here")
        return FactorManager(data_manager)
    except Exception as e:
        logger.error(f"初始化因子管理器失败: {e}")
        raise HTTPException(status_code=500, detail="因子管理器初始化失败")

@factor_router.get("/list", response_model=FactorListResponse, summary="获取所有可用因子列表")
async def get_factor_list():
    """
    获取所有可用的因子列表，按分类组织
    
    返回:
    - 因子分类和列表
    - 因子总数
    - 每个因子的描述
    """
    try:
        categories = list_all_available_factors()
        descriptions = get_factor_descriptions()
        total_factors = sum(len(factors) for factors in categories.values())
        
        return FactorListResponse(
            categories=categories,
            total_factors=total_factors,
            descriptions=descriptions
        )
    except Exception as e:
        logger.error(f"获取因子列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子列表失败: {str(e)}")

@factor_router.get("/categories", summary="获取因子分类")
async def get_factor_categories():
    """
    获取因子分类信息
    """
    try:
        calculator = get_enhanced_factor_calculator()
        summary = calculator.get_factor_categories()
        
        return {
            "categories": summary,
            "category_counts": {category: len(factors) for category, factors in summary.items()}
        }
    except Exception as e:
        logger.error(f"获取因子分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子分类失败: {str(e)}")

@factor_router.post("/calculate", response_model=FactorResponse, summary="计算单只股票的因子")
async def calculate_factors(
    request: FactorCalculationRequest,
    factor_manager: FactorManager = Depends(get_factor_manager)
):
    """
    计算单只股票的因子
    
    参数:
    - symbol: 股票代码
    - factor_names: 要计算的因子名称列表（可选，默认计算所有因子）
    - start_date: 开始日期（可选）
    - end_date: 结束日期（可选）
    """
    try:
        start_time = datetime.now()
        
        # 计算因子
        factors = factor_manager.calculate_factors_for_stock(
            symbol=request.symbol,
            factor_names=request.factor_names,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        calculation_time = datetime.now().isoformat()
        
        if not factors:
            return FactorResponse(
                success=False,
                symbol=request.symbol,
                factors={},
                calculation_time=calculation_time,
                message="未能计算出任何因子，请检查股票代码和数据可用性"
            )
        
        return FactorResponse(
            success=True,
            symbol=request.symbol,
            factors=factors,
            calculation_time=calculation_time,
            message=f"成功计算{len(factors)}个因子"
        )
        
    except Exception as e:
        logger.error(f"计算因子失败: {e}")
        return FactorResponse(
            success=False,
            symbol=request.symbol,
            factors={},
            calculation_time=datetime.now().isoformat(),
            message=f"计算失败: {str(e)}"
        )

@factor_router.post("/calculate/batch", response_model=BatchFactorResponse, summary="批量计算多只股票的因子")
async def calculate_factors_batch(
    request: BatchFactorCalculationRequest,
    factor_manager: FactorManager = Depends(get_factor_manager)
):
    """
    批量计算多只股票的因子
    
    参数:
    - symbols: 股票代码列表
    - factor_names: 要计算的因子名称列表（可选，默认计算所有因子）
    - start_date: 开始日期（可选）
    - end_date: 结束日期（可选）
    """
    try:
        start_time = datetime.now()
        
        # 批量计算因子
        results = factor_manager.batch_calculate_factors(
            symbols=request.symbols,
            factor_names=request.factor_names,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        calculation_time = datetime.now().isoformat()
        successful_symbols = len(results)
        total_symbols = len(request.symbols)
        
        # 收集错误信息
        errors = []
        for symbol in request.symbols:
            if symbol not in results:
                errors.append(f"股票 {symbol} 计算失败")
        
        return BatchFactorResponse(
            success=successful_symbols > 0,
            results=results,
            total_symbols=total_symbols,
            successful_symbols=successful_symbols,
            calculation_time=calculation_time,
            errors=errors if errors else None
        )
        
    except Exception as e:
        logger.error(f"批量计算因子失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量计算因子失败: {str(e)}")

@factor_router.get("/calculate/{symbol}", response_model=FactorResponse, summary="通过URL参数计算因子")
async def calculate_factors_by_url(
    symbol: str,
    factor_names: Optional[str] = Query(None, description="因子名称，多个用逗号分隔"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    factor_manager: FactorManager = Depends(get_factor_manager)
):
    """
    通过URL参数计算单只股票的因子
    """
    try:
        # 处理因子名称
        factor_list = None
        if factor_names:
            factor_list = [name.strip() for name in factor_names.split(',')]
        
        # 创建请求对象
        request = FactorCalculationRequest(
            symbol=symbol,
            factor_names=factor_list,
            start_date=start_date,
            end_date=end_date
        )
        
        # 调用计算函数
        return await calculate_factors(request, factor_manager)
        
    except Exception as e:
        logger.error(f"URL参数计算因子失败: {e}")
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")

@factor_router.get("/info/{factor_name}", summary="获取单个因子的详细信息")
async def get_factor_info(factor_name: str):
    """
    获取单个因子的详细信息
    """
    try:
        calculator = get_enhanced_factor_calculator()
        factor_info = calculator.get_factor_info(factor_name)
        
        if not factor_info:
            raise HTTPException(status_code=404, detail=f"因子 {factor_name} 不存在")
        
        return {
            "name": factor_info.name,
            "category": factor_info.category.value,
            "description": factor_info.description,
            "required_periods": factor_info.required_periods,
            "parameters": factor_info.parameters,
            "enabled": factor_info.enabled
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取因子信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子信息失败: {str(e)}")

@factor_router.get("/health", summary="因子服务健康检查")
async def factor_health_check():
    """
    因子服务健康检查
    """
    try:
        calculator = get_enhanced_factor_calculator()
        categories = calculator.get_factor_categories()
        
        return {
            "status": "healthy",
            "service": "Enhanced Factor Calculator",
            "total_factors": sum(len(factors) for factors in categories.values()),
            "categories_count": len(categories),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"因子服务健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务不可用: {str(e)}")

# 添加到主应用的函数
def include_factor_routes(app):
    """将因子路由添加到主应用"""
    app.include_router(factor_router) 