#!/usr/bin/env python3
"""
风险管理模块
包含VaR计算、风险指标监控、风险预警等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from scipy import stats
from sklearn.covariance import LedoitWolf
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class RiskMetrics:
    """风险指标计算器"""
    
    def __init__(self):
        self.confidence_levels = [0.95, 0.99, 0.999]
    
    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.95, 
                     method: str = 'historical') -> float:
        """计算风险价值(VaR)"""
        try:
            if len(returns) < 30:
                logger.warning("数据点不足，VaR计算可能不准确")
            
            if method == 'historical':
                # 历史模拟法
                return np.percentile(returns, (1 - confidence_level) * 100)
            
            elif method == 'parametric':
                # 参数法（正态分布假设）
                mean = returns.mean()
                std = returns.std()
                z_score = stats.norm.ppf(1 - confidence_level)
                return mean + z_score * std
            
            elif method == 'monte_carlo':
                # 蒙特卡洛模拟
                mean = returns.mean()
                std = returns.std()
                simulated_returns = np.random.normal(mean, std, 10000)
                return np.percentile(simulated_returns, (1 - confidence_level) * 100)
            
            else:
                raise ValueError(f"不支持的VaR计算方法: {method}")
                
        except Exception as e:
            logger.error(f"VaR计算失败: {e}")
            return 0.0
    
    def calculate_cvar(self, returns: pd.Series, confidence_level: float = 0.95) -> float:
        """计算条件风险价值(CVaR/Expected Shortfall)"""
        try:
            var = self.calculate_var(returns, confidence_level, 'historical')
            # CVaR是超过VaR的损失的期望值
            tail_losses = returns[returns <= var]
            return tail_losses.mean() if len(tail_losses) > 0 else var
        except Exception as e:
            logger.error(f"CVaR计算失败: {e}")
            return 0.0
    
    def calculate_volatility(self, returns: pd.Series, window: int = 252) -> Dict[str, float]:
        """计算波动率指标"""
        try:
            # 历史波动率
            historical_vol = returns.std() * np.sqrt(window)
            
            # EWMA波动率
            ewma_vol = returns.ewm(span=30).std().iloc[-1] * np.sqrt(window) if len(returns) > 30 else historical_vol
            
            # GARCH波动率（简化版）
            garch_vol = self._simple_garch_volatility(returns) * np.sqrt(window)
            
            return {
                'historical_volatility': historical_vol,
                'ewma_volatility': ewma_vol,
                'garch_volatility': garch_vol,
                'annualized_factor': window
            }
        except Exception as e:
            logger.error(f"波动率计算失败: {e}")
            return {'historical_volatility': 0.0}
    
    def _simple_garch_volatility(self, returns: pd.Series, alpha: float = 0.1, beta: float = 0.85) -> float:
        """简化的GARCH(1,1)波动率估计"""
        try:
            if len(returns) < 50:
                return returns.std()
            
            # 初始化
            omega = returns.var() * (1 - alpha - beta)
            variance = returns.var()
            
            # GARCH(1,1)递推
            for ret in returns[-30:]:  # 使用最近30个观测值
                variance = omega + alpha * (ret ** 2) + beta * variance
            
            return np.sqrt(variance)
        except Exception as e:
            logger.error(f"GARCH波动率计算失败: {e}")
            return returns.std()
    
    def calculate_beta(self, stock_returns: pd.Series, market_returns: pd.Series) -> Dict[str, float]:
        """计算Beta系数"""
        try:
            # 确保数据对齐
            aligned_data = pd.DataFrame({
                'stock': stock_returns,
                'market': market_returns
            }).dropna()
            
            if len(aligned_data) < 30:
                logger.warning("数据点不足，Beta计算可能不准确")
                return {'beta': 1.0, 'alpha': 0.0, 'r_squared': 0.0}
            
            # 线性回归计算Beta
            covariance = aligned_data['stock'].cov(aligned_data['market'])
            market_variance = aligned_data['market'].var()
            
            beta = covariance / market_variance if market_variance != 0 else 1.0
            
            # Alpha计算
            stock_mean = aligned_data['stock'].mean()
            market_mean = aligned_data['market'].mean()
            alpha = stock_mean - beta * market_mean
            
            # R平方
            correlation = aligned_data['stock'].corr(aligned_data['market'])
            r_squared = correlation ** 2 if not pd.isna(correlation) else 0.0
            
            return {
                'beta': beta,
                'alpha': alpha,
                'r_squared': r_squared,
                'correlation': correlation
            }
        except Exception as e:
            logger.error(f"Beta计算失败: {e}")
            return {'beta': 1.0, 'alpha': 0.0, 'r_squared': 0.0}
    
    def calculate_drawdown(self, cumulative_returns: pd.Series) -> Dict[str, float]:
        """计算回撤指标"""
        try:
            # 计算累计收益曲线
            if not isinstance(cumulative_returns.index, pd.DatetimeIndex):
                cumulative_returns.index = pd.to_datetime(cumulative_returns.index)
            
            # 计算历史最高点
            rolling_max = cumulative_returns.expanding().max()
            
            # 计算回撤
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            
            # 最大回撤
            max_drawdown = drawdown.min()
            
            # 最大回撤持续时间
            drawdown_duration = self._calculate_drawdown_duration(drawdown)
            
            # 当前回撤
            current_drawdown = drawdown.iloc[-1]
            
            # 回撤恢复时间
            recovery_time = self._calculate_recovery_time(drawdown)
            
            return {
                'max_drawdown': max_drawdown,
                'current_drawdown': current_drawdown,
                'max_drawdown_duration': drawdown_duration,
                'avg_recovery_time': recovery_time,
                'drawdown_series': drawdown.to_dict()
            }
        except Exception as e:
            logger.error(f"回撤计算失败: {e}")
            return {'max_drawdown': 0.0, 'current_drawdown': 0.0}
    
    def _calculate_drawdown_duration(self, drawdown: pd.Series) -> int:
        """计算最大回撤持续时间"""
        try:
            max_duration = 0
            current_duration = 0
            
            for dd in drawdown:
                if dd < 0:
                    current_duration += 1
                    max_duration = max(max_duration, current_duration)
                else:
                    current_duration = 0
            
            return max_duration
        except Exception as e:
            logger.error(f"回撤持续时间计算失败: {e}")
            return 0
    
    def _calculate_recovery_time(self, drawdown: pd.Series) -> float:
        """计算平均恢复时间"""
        try:
            recovery_times = []
            start_dd = None
            
            for i, dd in enumerate(drawdown):
                if dd < 0 and start_dd is None:
                    start_dd = i
                elif dd >= 0 and start_dd is not None:
                    recovery_times.append(i - start_dd)
                    start_dd = None
            
            return np.mean(recovery_times) if recovery_times else 0
        except Exception as e:
            logger.error(f"恢复时间计算失败: {e}")
            return 0

class PortfolioRiskAnalyzer:
    """投资组合风险分析器"""
    
    def __init__(self):
        self.risk_metrics = RiskMetrics()
    
    def analyze_portfolio_risk(self, portfolio_returns: pd.Series, 
                             positions: Dict[str, float],
                             individual_returns: Dict[str, pd.Series],
                             market_returns: pd.Series = None) -> Dict:
        """分析投资组合风险"""
        try:
            analysis = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_metrics': {},
                'individual_metrics': {},
                'correlation_analysis': {},
                'risk_decomposition': {}
            }
            
            # 投资组合整体风险指标
            analysis['portfolio_metrics'] = self._analyze_portfolio_metrics(
                portfolio_returns, market_returns
            )
            
            # 个股风险指标
            analysis['individual_metrics'] = self._analyze_individual_metrics(
                individual_returns, market_returns
            )
            
            # 相关性分析
            analysis['correlation_analysis'] = self._analyze_correlations(
                individual_returns
            )
            
            # 风险分解
            analysis['risk_decomposition'] = self._decompose_portfolio_risk(
                positions, individual_returns
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"投资组合风险分析失败: {e}")
            return {'error': str(e)}
    
    def _analyze_portfolio_metrics(self, portfolio_returns: pd.Series, 
                                 market_returns: pd.Series = None) -> Dict:
        """分析投资组合指标"""
        try:
            metrics = {}
            
            # VaR指标
            for confidence in [0.95, 0.99]:
                var_hist = self.risk_metrics.calculate_var(portfolio_returns, confidence, 'historical')
                var_param = self.risk_metrics.calculate_var(portfolio_returns, confidence, 'parametric')
                cvar = self.risk_metrics.calculate_cvar(portfolio_returns, confidence)
                
                metrics[f'var_{int(confidence*100)}'] = {
                    'historical': var_hist,
                    'parametric': var_param,
                    'cvar': cvar
                }
            
            # 波动率指标
            volatility_metrics = self.risk_metrics.calculate_volatility(portfolio_returns)
            metrics['volatility'] = volatility_metrics
            
            # 回撤指标
            cumulative_returns = (1 + portfolio_returns).cumprod()
            drawdown_metrics = self.risk_metrics.calculate_drawdown(cumulative_returns)
            metrics['drawdown'] = drawdown_metrics
            
            # Beta指标（如果有市场数据）
            if market_returns is not None:
                beta_metrics = self.risk_metrics.calculate_beta(portfolio_returns, market_returns)
                metrics['beta'] = beta_metrics
            
            # 其他风险指标
            metrics['skewness'] = portfolio_returns.skew()
            metrics['kurtosis'] = portfolio_returns.kurtosis()
            metrics['sharpe_ratio'] = portfolio_returns.mean() / portfolio_returns.std() * np.sqrt(252) if portfolio_returns.std() > 0 else 0
            
            return metrics
            
        except Exception as e:
            logger.error(f"投资组合指标分析失败: {e}")
            return {}
    
    def _analyze_individual_metrics(self, individual_returns: Dict[str, pd.Series],
                                  market_returns: pd.Series = None) -> Dict:
        """分析个股指标"""
        try:
            individual_metrics = {}
            
            for symbol, returns in individual_returns.items():
                if len(returns) < 30:
                    continue
                
                metrics = {}
                
                # VaR
                metrics['var_95'] = self.risk_metrics.calculate_var(returns, 0.95)
                metrics['cvar_95'] = self.risk_metrics.calculate_cvar(returns, 0.95)
                
                # 波动率
                volatility = self.risk_metrics.calculate_volatility(returns)
                metrics['volatility'] = volatility['historical_volatility']
                
                # Beta（如果有市场数据）
                if market_returns is not None:
                    beta_metrics = self.risk_metrics.calculate_beta(returns, market_returns)
                    metrics['beta'] = beta_metrics['beta']
                    metrics['alpha'] = beta_metrics['alpha']
                
                # 其他指标
                metrics['skewness'] = returns.skew()
                metrics['kurtosis'] = returns.kurtosis()
                
                individual_metrics[symbol] = metrics
            
            return individual_metrics
            
        except Exception as e:
            logger.error(f"个股指标分析失败: {e}")
            return {}
    
    def _analyze_correlations(self, individual_returns: Dict[str, pd.Series]) -> Dict:
        """分析相关性"""
        try:
            # 构建收益率矩阵
            returns_df = pd.DataFrame(individual_returns).dropna()
            
            if returns_df.empty:
                return {}
            
            # 相关系数矩阵
            correlation_matrix = returns_df.corr()
            
            # 平均相关性
            avg_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean()
            
            # 最高和最低相关性
            corr_values = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)]
            max_correlation = corr_values.max()
            min_correlation = corr_values.min()
            
            # 相关性分布
            correlation_stats = {
                'avg_correlation': avg_correlation,
                'max_correlation': max_correlation,
                'min_correlation': min_correlation,
                'correlation_matrix': correlation_matrix.to_dict()
            }
            
            return correlation_stats
            
        except Exception as e:
            logger.error(f"相关性分析失败: {e}")
            return {}
    
    def _decompose_portfolio_risk(self, positions: Dict[str, float],
                                individual_returns: Dict[str, pd.Series]) -> Dict:
        """风险分解"""
        try:
            # 构建收益率矩阵和权重向量
            returns_df = pd.DataFrame(individual_returns).dropna()
            
            if returns_df.empty:
                return {}
            
            # 权重向量
            weights = np.array([positions.get(symbol, 0) for symbol in returns_df.columns])
            weights = weights / weights.sum() if weights.sum() != 0 else weights
            
            # 协方差矩阵
            cov_matrix = returns_df.cov().values
            
            # 投资组合方差
            portfolio_variance = np.dot(weights, np.dot(cov_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # 边际风险贡献
            marginal_contrib = np.dot(cov_matrix, weights) / portfolio_volatility if portfolio_volatility > 0 else np.zeros_like(weights)
            
            # 风险贡献
            risk_contrib = weights * marginal_contrib
            risk_contrib_pct = risk_contrib / risk_contrib.sum() if risk_contrib.sum() != 0 else risk_contrib
            
            # 构建结果
            risk_decomposition = {}
            for i, symbol in enumerate(returns_df.columns):
                risk_decomposition[symbol] = {
                    'weight': weights[i],
                    'marginal_contrib': marginal_contrib[i],
                    'risk_contrib': risk_contrib[i],
                    'risk_contrib_pct': risk_contrib_pct[i]
                }
            
            risk_decomposition['portfolio_volatility'] = portfolio_volatility
            
            return risk_decomposition
            
        except Exception as e:
            logger.error(f"风险分解失败: {e}")
            return {}

class RiskMonitor:
    """风险监控器"""
    
    def __init__(self):
        self.risk_limits = {
            'max_portfolio_var_95': -0.05,  # 最大5%的日VaR
            'max_individual_weight': 0.20,   # 单只股票最大20%权重
            'max_sector_weight': 0.30,       # 单个行业最大30%权重
            'min_diversification': 5,        # 最少5只股票
            'max_correlation': 0.80,         # 最大相关性80%
            'max_drawdown': -0.20,           # 最大回撤20%
            'max_leverage': 1.0              # 最大杠杆1倍
        }
        
        self.alert_history = []
    
    def check_risk_limits(self, portfolio_analysis: Dict, positions: Dict[str, float]) -> List[Dict]:
        """检查风险限制"""
        alerts = []
        
        try:
            # 检查VaR限制
            portfolio_var = portfolio_analysis.get('portfolio_metrics', {}).get('var_95', {}).get('historical', 0)
            if portfolio_var < self.risk_limits['max_portfolio_var_95']:
                alerts.append({
                    'type': 'var_breach',
                    'severity': 'high',
                    'message': f"投资组合VaR超限: {portfolio_var:.2%} < {self.risk_limits['max_portfolio_var_95']:.2%}",
                    'current_value': portfolio_var,
                    'limit': self.risk_limits['max_portfolio_var_95']
                })
            
            # 检查个股权重限制
            total_weight = sum(positions.values())
            for symbol, weight in positions.items():
                weight_pct = weight / total_weight if total_weight > 0 else 0
                if weight_pct > self.risk_limits['max_individual_weight']:
                    alerts.append({
                        'type': 'concentration_risk',
                        'severity': 'medium',
                        'message': f"个股权重超限: {symbol} {weight_pct:.2%} > {self.risk_limits['max_individual_weight']:.2%}",
                        'symbol': symbol,
                        'current_value': weight_pct,
                        'limit': self.risk_limits['max_individual_weight']
                    })
            
            # 检查分散化程度
            if len(positions) < self.risk_limits['min_diversification']:
                alerts.append({
                    'type': 'diversification_risk',
                    'severity': 'medium',
                    'message': f"持仓分散度不足: {len(positions)} < {self.risk_limits['min_diversification']}",
                    'current_value': len(positions),
                    'limit': self.risk_limits['min_diversification']
                })
            
            # 检查相关性
            max_corr = portfolio_analysis.get('correlation_analysis', {}).get('max_correlation', 0)
            if max_corr > self.risk_limits['max_correlation']:
                alerts.append({
                    'type': 'correlation_risk',
                    'severity': 'medium',
                    'message': f"最大相关性超限: {max_corr:.2%} > {self.risk_limits['max_correlation']:.2%}",
                    'current_value': max_corr,
                    'limit': self.risk_limits['max_correlation']
                })
            
            # 检查回撤
            current_dd = portfolio_analysis.get('portfolio_metrics', {}).get('drawdown', {}).get('current_drawdown', 0)
            if current_dd < self.risk_limits['max_drawdown']:
                alerts.append({
                    'type': 'drawdown_risk',
                    'severity': 'high',
                    'message': f"当前回撤超限: {current_dd:.2%} < {self.risk_limits['max_drawdown']:.2%}",
                    'current_value': current_dd,
                    'limit': self.risk_limits['max_drawdown']
                })
            
            # 记录告警历史
            for alert in alerts:
                alert['timestamp'] = datetime.now().isoformat()
                self.alert_history.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"风险限制检查失败: {e}")
            return []
    
    def get_risk_dashboard(self, portfolio_analysis: Dict, positions: Dict[str, float]) -> Dict:
        """生成风险仪表板"""
        try:
            alerts = self.check_risk_limits(portfolio_analysis, positions)
            
            dashboard = {
                'timestamp': datetime.now().isoformat(),
                'risk_score': self._calculate_risk_score(portfolio_analysis),
                'alerts': alerts,
                'risk_metrics_summary': self._summarize_risk_metrics(portfolio_analysis),
                'position_summary': self._summarize_positions(positions),
                'recommendations': self._generate_recommendations(alerts, portfolio_analysis)
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"风险仪表板生成失败: {e}")
            return {'error': str(e)}
    
    def _calculate_risk_score(self, portfolio_analysis: Dict) -> int:
        """计算风险评分(0-100)"""
        try:
            score = 50  # 基础分数
            
            # VaR评分
            var_95 = portfolio_analysis.get('portfolio_metrics', {}).get('var_95', {}).get('historical', 0)
            if var_95 < -0.10:
                score -= 30
            elif var_95 < -0.05:
                score -= 15
            elif var_95 > -0.02:
                score += 10
            
            # 波动率评分
            volatility = portfolio_analysis.get('portfolio_metrics', {}).get('volatility', {}).get('historical_volatility', 0)
            if volatility > 0.30:
                score -= 20
            elif volatility > 0.20:
                score -= 10
            elif volatility < 0.10:
                score += 10
            
            # 回撤评分
            max_dd = portfolio_analysis.get('portfolio_metrics', {}).get('drawdown', {}).get('max_drawdown', 0)
            if max_dd < -0.30:
                score -= 25
            elif max_dd < -0.15:
                score -= 10
            elif max_dd > -0.05:
                score += 15
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"风险评分计算失败: {e}")
            return 50
    
    def _summarize_risk_metrics(self, portfolio_analysis: Dict) -> Dict:
        """总结风险指标"""
        try:
            portfolio_metrics = portfolio_analysis.get('portfolio_metrics', {})
            
            summary = {
                'var_95': portfolio_metrics.get('var_95', {}).get('historical', 0),
                'volatility': portfolio_metrics.get('volatility', {}).get('historical_volatility', 0),
                'max_drawdown': portfolio_metrics.get('drawdown', {}).get('max_drawdown', 0),
                'current_drawdown': portfolio_metrics.get('drawdown', {}).get('current_drawdown', 0),
                'sharpe_ratio': portfolio_metrics.get('sharpe_ratio', 0),
                'beta': portfolio_metrics.get('beta', {}).get('beta', 1.0) if 'beta' in portfolio_metrics else None
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"风险指标总结失败: {e}")
            return {}
    
    def _summarize_positions(self, positions: Dict[str, float]) -> Dict:
        """总结持仓信息"""
        try:
            total_value = sum(positions.values())
            
            summary = {
                'total_positions': len(positions),
                'total_value': total_value,
                'largest_position': max(positions.values()) / total_value if total_value > 0 else 0,
                'top_3_concentration': sum(sorted(positions.values(), reverse=True)[:3]) / total_value if total_value > 0 else 0
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"持仓总结失败: {e}")
            return {}
    
    def _generate_recommendations(self, alerts: List[Dict], portfolio_analysis: Dict) -> List[str]:
        """生成风险管理建议"""
        recommendations = []
        
        try:
            # 基于告警生成建议
            for alert in alerts:
                if alert['type'] == 'var_breach':
                    recommendations.append("建议降低投资组合风险敞口，考虑减仓或增加对冲")
                elif alert['type'] == 'concentration_risk':
                    recommendations.append(f"建议减少{alert.get('symbol', '')}的持仓权重，提高分散化程度")
                elif alert['type'] == 'diversification_risk':
                    recommendations.append("建议增加持仓股票数量，提高投资组合分散化程度")
                elif alert['type'] == 'correlation_risk':
                    recommendations.append("建议降低高相关性股票的权重，选择相关性较低的资产")
                elif alert['type'] == 'drawdown_risk':
                    recommendations.append("当前回撤较大，建议考虑止损或调整投资策略")
            
            # 基于风险指标生成建议
            portfolio_metrics = portfolio_analysis.get('portfolio_metrics', {})
            volatility = portfolio_metrics.get('volatility', {}).get('historical_volatility', 0)
            
            if volatility > 0.25:
                recommendations.append("投资组合波动率较高，建议增加低波动率资产配置")
            
            sharpe_ratio = portfolio_metrics.get('sharpe_ratio', 0)
            if sharpe_ratio < 0.5:
                recommendations.append("夏普比率较低，建议优化资产配置以提高风险调整后收益")
            
            return list(set(recommendations))  # 去重
            
        except Exception as e:
            logger.error(f"建议生成失败: {e}")
            return []

# 全局实例
risk_analyzer = PortfolioRiskAnalyzer()
risk_monitor = RiskMonitor()

def get_risk_analyzer() -> PortfolioRiskAnalyzer:
    """获取风险分析器实例"""
    return risk_analyzer

def get_risk_monitor() -> RiskMonitor:
    """获取风险监控器实例"""
    return risk_monitor 