#!/usr/bin/env python3
"""
MACD背离检测功能测试脚本
"""

import asyncio
import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.divergence_detector import (
    get_divergence_detector, 
    get_divergence_database, 
    get_chart_generator
)
from backend.market_scanner import get_market_scanner

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_divergence_detection():
    """测试背离检测功能"""
    
    # 测试参数
    TUSHARE_TOKEN = "d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c"
    TEST_MARKET = "US"
    
    print("=" * 60)
    print("🔍 MACD背离检测功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试数据库初始化
        print("\n📊 测试数据库初始化...")
        db = get_divergence_database()
        print("✅ 背离数据库初始化成功")
        
        # 2. 测试背离检测器
        print("\n🔍 测试背离检测器...")
        detector = get_divergence_detector()
        print("✅ 背离检测器初始化成功")
        
        # 3. 测试图表生成器
        print("\n📈 测试图表生成器...")
        chart_gen = get_chart_generator()
        print("✅ 图表生成器初始化成功")
        
        # 4. 测试市场扫描器
        print("\n🌍 测试市场扫描器...")
        scanner = get_market_scanner(TUSHARE_TOKEN)
        print("✅ 市场扫描器初始化成功")
        
        # 5. 测试小规模扫描（只扫描几只股票）
        print(f"\n🔍 开始测试扫描{TEST_MARKET}市场...")
        
        # 临时修改股票池为少量股票进行测试
        original_stocks = scanner.market_stocks[TEST_MARKET].copy()
        scanner.market_stocks[TEST_MARKET] = ['AAPL', 'MSFT', 'GOOGL']  # 只测试3只股票
        
        results = await scanner.scan_market(
            market=TEST_MARKET,
            divergence_types=['bullish', 'bearish']
        )
        
        # 恢复原始股票池
        scanner.market_stocks[TEST_MARKET] = original_stocks
        
        print("✅ 市场扫描完成")
        
        # 6. 显示结果
        print(f"\n📊 扫描结果:")
        print(f"  市场: {results['market']}")
        print(f"  扫描股票数: {results['total_stocks']}")
        print(f"  成功扫描: {results['scanned_stocks']}")
        print(f"  发现背离: {len(results['divergences'])}")
        print(f"  扫描耗时: {results['scan_duration']:.2f}秒")
        print(f"  错误数量: {len(results['errors'])}")
        
        if results['divergences']:
            print(f"\n🎯 发现的背离信号:")
            for i, div in enumerate(results['divergences'][:3], 1):  # 只显示前3个
                print(f"  {i}. {div['symbol']} - {div['type']} (置信度: {div['confidence']:.2f})")
        
        if results['errors']:
            print(f"\n❌ 扫描错误:")
            for error in results['errors'][:3]:  # 只显示前3个错误
                print(f"  - {error}")
        
        # 7. 测试历史查询
        print(f"\n📋 测试历史查询...")
        recent = scanner.get_recent_divergences(TEST_MARKET, 24)
        history = scanner.get_scan_history(TEST_MARKET, 5)
        
        print(f"  最近24小时背离信号: {len(recent)}")
        print(f"  扫描历史记录: {len(history)}")
        
        print("\n" + "=" * 60)
        print("🎉 MACD背离检测功能测试完成！")
        print("=" * 60)
        
        # 使用提示
        print("\n💡 使用提示:")
        print("1. 后端API已启动，可通过 http://127.0.0.1:8000/divergence/scan 进行扫描")
        print("2. 前端界面已添加MACD背离扫描模块")
        print("3. 支持美股(US)、港股(HK)、A股(CN)三个市场")
        print("4. 可选择扫描顶背离、底背离或两者")
        print("5. 扫描结果包含K线图和详细信息")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        print(f"\n❌ 测试失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 检查 Tushare Token 是否正确")
        print("2. 确保网络连接正常")
        print("3. 检查依赖包是否安装完整")

if __name__ == "__main__":
    asyncio.run(test_divergence_detection()) 