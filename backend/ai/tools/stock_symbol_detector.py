#!/usr/bin/env python3
"""
股票代码检测器
从用户查询中提取股票代码和公司名称
"""

import re
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class StockSymbol:
    """股票符号数据类"""
    symbol: str          # 股票代码，如 AAPL
    company_name: str    # 公司名称，如 Apple Inc.
    confidence: float    # 匹配置信度 0-1
    matched_text: str    # 原始匹配文本

class StockSymbolDetector:
    """股票代码检测器"""
    
    def __init__(self):
        # 常见股票代码映射
        self.symbol_mappings = {
            # 美股常见公司
            "苹果": "AAPL", "苹果公司": "AAPL", "apple": "AAPL",
            "特斯拉": "TSLA", "tesla": "TSLA",
            "微软": "MSFT", "微软公司": "MSFT", "microsoft": "MSFT",
            "谷歌": "GOOGL", "google": "GOOGL", "alphabet": "GOOGL",
            "亚马逊": "AMZN", "amazon": "AMZN",
            "脸书": "META", "meta": "META", "facebook": "META",
            "英伟达": "NVDA", "nvidia": "NVDA",
            "奈飞": "NFLX", "netflix": "NFLX",
            "推特": "TWTR", "twitter": "TWTR",
            "阿里巴巴": "BABA", "alibaba": "BABA",
            "百度": "BIDU", "baidu": "BIDU",
            "腾讯": "TCEHY", "tencent": "TCEHY",
            "比亚迪": "BYDDY", "byd": "BYDDY",
            "蔚来": "NIO", "nio": "NIO",
            "小鹏": "XPEV", "xpeng": "XPEV",
            "理想": "LI", "li auto": "LI",
            "拼多多": "PDD", "pinduoduo": "PDD",
            "京东": "JD", "jd.com": "JD",
            "网易": "NTES", "netease": "NTES",
            "新浪": "SINA", "sina": "SINA",
            "搜狐": "SOHU", "sohu": "SOHU",
            "爱奇艺": "IQ", "iqiyi": "IQ",
            "哔哩哔哩": "BILI", "bilibili": "BILI",
            "滴滴": "DIDI", "didi": "DIDI",
            
            # 其他知名公司
            "迪士尼": "DIS", "disney": "DIS",
            "可口可乐": "KO", "coca cola": "KO", "cocacola": "KO",
            "麦当劳": "MCD", "mcdonald": "MCD",
            "沃尔玛": "WMT", "walmart": "WMT",
            "强生": "JNJ", "johnson": "JNJ",
            "宝洁": "PG", "procter": "PG",
            "英特尔": "INTC", "intel": "INTC",
            "AMD": "AMD", "amd": "AMD",
            "高通": "QCOM", "qualcomm": "QCOM",
            "思科": "CSCO", "cisco": "CSCO",
            "甲骨文": "ORCL", "oracle": "ORCL",
            "IBM": "IBM", "ibm": "IBM",
            "惠普": "HPQ", "hp": "HPQ",
            "戴尔": "DELL", "dell": "DELL",
            "维萨": "V", "visa": "V",
            "万事达": "MA", "mastercard": "MA",
            "摩根大通": "JPM", "jpmorgan": "JPM",
            "美国银行": "BAC", "bank of america": "BAC",
            "富国银行": "WFC", "wells fargo": "WFC",
            "高盛": "GS", "goldman sachs": "GS",
            "摩根士丹利": "MS", "morgan stanley": "MS",
            "花旗": "C", "citigroup": "C",
            "埃克森美孚": "XOM", "exxon": "XOM",
            "雪佛龙": "CVX", "chevron": "CVX",
            "波音": "BA", "boeing": "BA",
            "洛克希德马丁": "LMT", "lockheed": "LMT",
            "通用电气": "GE", "general electric": "GE",
            "3M": "MMM", "3m": "MMM",
            "杜邦": "DD", "dupont": "DD",
            "辉瑞": "PFE", "pfizer": "PFE",
            "默克": "MRK", "merck": "MRK",
            "礼来": "LLY", "eli lilly": "LLY",
            "艾伯维": "ABBV", "abbvie": "ABBV",
            "百时美施贵宝": "BMY", "bristol myers": "BMY",
            "联合健康": "UNH", "unitedhealth": "UNH",
            "卡特彼勒": "CAT", "caterpillar": "CAT",
            "约翰迪尔": "DE", "john deere": "DE",
            "福特": "F", "ford": "F",
            "通用汽车": "GM", "general motors": "GM",
            "星巴克": "SBUX", "starbucks": "SBUX",
            "家得宝": "HD", "home depot": "HD",
            "沃尔格林": "WBA", "walgreens": "WBA",
            "CVS": "CVS", "cvs": "CVS",
            "塔吉特": "TGT", "target": "TGT",
            "开市客": "COST", "costco": "COST",
            "耐克": "NKE", "nike": "NKE",
            "阿迪达斯": "ADDYY", "adidas": "ADDYY",
            "优步": "UBER", "uber": "UBER",
            "Lyft": "LYFT", "lyft": "LYFT",
            "Airbnb": "ABNB", "airbnb": "ABNB",
            "Zoom": "ZM", "zoom": "ZM",
            "Slack": "WORK", "slack": "WORK",
            "Shopify": "SHOP", "shopify": "SHOP",
            "PayPal": "PYPL", "paypal": "PYPL",
            "Square": "SQ", "square": "SQ",
            "Stripe": "STRP", "stripe": "STRP",
            "Palantir": "PLTR", "palantir": "PLTR",
            "Snowflake": "SNOW", "snowflake": "SNOW",
        }
        
        # 公司全名到代码的映射
        self.company_full_names = {
            "Apple Inc.": "AAPL",
            "Tesla Inc.": "TSLA", 
            "Microsoft Corporation": "MSFT",
            "Alphabet Inc.": "GOOGL",
            "Amazon.com Inc.": "AMZN",
            "Meta Platforms Inc.": "META",
            "NVIDIA Corporation": "NVDA",
            "Netflix Inc.": "NFLX",
            "Alibaba Group Holding Limited": "BABA",
            "Baidu Inc.": "BIDU",
        }
        
        # 股票代码正则表达式模式
        self.stock_patterns = [
            r'\b[A-Z]{1,5}\b',  # 1-5个大写字母的股票代码
            r'\$[A-Z]{1,5}\b',  # 以$开头的股票代码
        ]
    
    def detect_symbols(self, query: str) -> List[StockSymbol]:
        """
        从查询中检测股票代码
        
        Args:
            query: 用户查询字符串
            
        Returns:
            List[StockSymbol]: 检测到的股票符号列表
        """
        symbols = []
        query_lower = query.lower()
        
        # 1. 检查预定义的公司名称映射
        for company_name, symbol in self.symbol_mappings.items():
            if company_name.lower() in query_lower:
                confidence = 0.9  # 预定义映射的置信度较高
                symbols.append(StockSymbol(
                    symbol=symbol,
                    company_name=self._get_company_name(symbol),
                    confidence=confidence,
                    matched_text=company_name
                ))
                logger.info(f"通过公司名称映射检测到股票: {company_name} -> {symbol}")
        
        # 2. 使用正则表达式检测股票代码
        for pattern in self.stock_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                symbol = match.replace('$', '').upper()
                
                # 排除常见的非股票代码词汇
                if self._is_valid_stock_symbol(symbol):
                    confidence = 0.7  # 正则匹配的置信度中等
                    
                    # 如果已经通过公司名称检测到了，提高置信度
                    existing_symbol = next((s for s in symbols if s.symbol == symbol), None)
                    if existing_symbol:
                        existing_symbol.confidence = max(existing_symbol.confidence, 0.95)
                        continue
                    
                    symbols.append(StockSymbol(
                        symbol=symbol,
                        company_name=self._get_company_name(symbol),
                        confidence=confidence,
                        matched_text=match
                    ))
                    logger.info(f"通过正则表达式检测到股票代码: {match} -> {symbol}")
        
        # 3. 去重并按置信度排序
        unique_symbols = {}
        for symbol in symbols:
            if symbol.symbol not in unique_symbols or symbol.confidence > unique_symbols[symbol.symbol].confidence:
                unique_symbols[symbol.symbol] = symbol
        
        result = list(unique_symbols.values())
        result.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"最终检测到 {len(result)} 个股票代码: {[s.symbol for s in result]}")
        return result
    
    def _is_valid_stock_symbol(self, symbol: str) -> bool:
        """检查是否为有效的股票代码"""
        # 排除常见的非股票代码词汇
        excluded_words = {
            'THE', 'AND', 'OR', 'BUT', 'FOR', 'NOR', 'SO', 'YET',
            'A', 'AN', 'AS', 'AT', 'BE', 'BY', 'DO', 'HE', 'IF', 'IN',
            'IS', 'IT', 'MY', 'NO', 'OF', 'ON', 'TO', 'UP', 'WE', 'AM',
            'AI', 'API', 'APP', 'BOT', 'CPU', 'GPU', 'RAM', 'SSD', 'USB',
            'HTML', 'CSS', 'JS', 'SQL', 'XML', 'JSON', 'HTTP', 'HTTPS',
            'GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'PATCH',
            'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD',
            'NEW', 'OLD', 'BIG', 'BAD', 'GOOD', 'BEST', 'LAST', 'NEXT',
            'ALL', 'ANY', 'EACH', 'BOTH', 'MANY', 'MUCH', 'MOST', 'SOME',
            'NOW', 'THEN', 'HERE', 'THERE', 'WHERE', 'WHEN', 'WHY', 'HOW',
            'WHO', 'WHAT', 'WHICH', 'WHOSE', 'WHOM',
        }
        
        if symbol in excluded_words:
            return False
        
        # 长度检查
        if len(symbol) < 1 or len(symbol) > 5:
            return False
        
        # 必须包含字母
        if not any(c.isalpha() for c in symbol):
            return False
        
        return True
    
    def _get_company_name(self, symbol: str) -> str:
        """根据股票代码获取公司名称"""
        # 反向查找公司名称
        for company_name, mapped_symbol in self.symbol_mappings.items():
            if mapped_symbol == symbol:
                return company_name.title()
        
        # 如果没找到，返回一个基于代码的默认名称
        return f"{symbol} Corporation"
    
    def get_primary_symbol(self, query: str) -> Optional[str]:
        """
        获取查询中的主要股票代码
        
        Args:
            query: 用户查询字符串
            
        Returns:
            Optional[str]: 主要股票代码，如果没有检测到则返回None
        """
        symbols = self.detect_symbols(query)
        if symbols:
            return symbols[0].symbol  # 返回置信度最高的
        return None
    
    def is_stock_query(self, query: str) -> bool:
        """
        判断查询是否与股票相关
        
        Args:
            query: 用户查询字符串
            
        Returns:
            bool: 是否为股票相关查询
        """
        stock_keywords = [
            'stock', 'stocks', '股票', '股价', '股市',
            'chart', 'candlestick', 'k线', 'k-line', 'kline',
            'technical', 'analysis', '技术分析', '分析',
            'trend', 'price', '趋势', '价格',
            'trading', 'trade', '交易',
            'market', '市场',
            'ticker', 'symbol', '代码',
            'finance', 'financial', '金融', '财务',
            'investment', 'invest', '投资',
            'portfolio', '投资组合',
            'earnings', '财报', '业绩',
            'dividend', '股息', '分红',
        ]
        
        query_lower = query.lower()
        
        # 检查是否包含股票相关关键词
        has_stock_keywords = any(keyword in query_lower for keyword in stock_keywords)
        
        # 检查是否包含股票代码
        has_stock_symbols = len(self.detect_symbols(query)) > 0
        
        return has_stock_keywords or has_stock_symbols


# 全局实例
stock_detector = StockSymbolDetector() 