import functools
import logging
from typing import Any, Callable, Optional, Union
from .cache_manager import cache_manager
from .cache_keys import create_tool_key

logger = logging.getLogger(__name__)

def cached(
    cache_type: str = 'general',
    ttl: Optional[int] = None,
    key_func: Optional[Callable] = None,
    bypass_on_error: bool = True
):
    """
    缓存装饰器
    
    Args:
        cache_type: 缓存类型 ('stock_data', 'news', 'general')
        ttl: 缓存生存时间（秒），None使用默认值
        key_func: 自定义键生成函数
        bypass_on_error: 出错时是否绕过缓存直接调用原函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键 - 将位置参数也包含在内
            if key_func:
                try:
                    cache_key = key_func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"Custom key function failed: {e}")
                    # 创建包含位置参数的完整参数字典
                    all_params = dict(kwargs)
                    all_params['_args'] = args
                    cache_key = create_tool_key(func.__name__, all_params)
            else:
                # 创建包含位置参数的完整参数字典
                all_params = dict(kwargs)
                all_params['_args'] = args
                cache_key = create_tool_key(func.__name__, all_params)
            
            try:
                # 尝试从缓存获取
                cached_result = cache_manager.get(cache_key, cache_type)
                if cached_result is not None:
                    logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                    return cached_result
                
                # 缓存未命中，调用原函数
                logger.debug(f"Cache miss for {func.__name__}: {cache_key}")
                result = func(*args, **kwargs)
                
                # 缓存结果
                if result is not None:
                    cache_manager.set(cache_key, result, cache_type, ttl)
                    logger.debug(f"Cached result for {func.__name__}: {cache_key}")
                
                return result
                
            except Exception as e:
                logger.error(f"Cache operation failed for {func.__name__}: {e}")
                if bypass_on_error:
                    # 绕过缓存，直接调用原函数
                    return func(*args, **kwargs)
                else:
                    raise
        
        # 添加缓存控制方法
        wrapper.clear_cache = lambda: cache_manager.clear_all()
        wrapper.cache_stats = lambda: cache_manager.get_global_stats()
        
        return wrapper
    return decorator

def stock_data_cached(ttl: int = 300):
    """股票数据专用缓存装饰器"""
    return cached(cache_type='stock_data', ttl=ttl)

def news_cached(ttl: int = 900):
    """新闻数据专用缓存装饰器"""  
    return cached(cache_type='news', ttl=ttl)

def general_cached(ttl: int = 600):
    """通用缓存装饰器"""
    return cached(cache_type='general', ttl=ttl)

def force_refresh_cached(func: Callable, *args, **kwargs):
    """
    强制刷新缓存并执行函数
    
    Args:
        func: 要执行的函数
        *args, **kwargs: 函数参数
    
    Returns:
        函数执行结果
    """
    # 生成缓存键 - 包含位置参数
    all_params = dict(kwargs)
    all_params['_args'] = args
    cache_key = create_tool_key(func.__name__, all_params)
    
    # 删除现有缓存
    cache_manager.delete(cache_key, 'general')
    cache_manager.delete(cache_key, 'stock_data') 
    cache_manager.delete(cache_key, 'news')
    
    # 执行函数
    result = func(*args, **kwargs)
    
    logger.info(f"Force refreshed cache for {func.__name__}")
    return result 