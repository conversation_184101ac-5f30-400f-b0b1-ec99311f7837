#!/usr/bin/env python3
"""
图表渲染器
生成ECharts K线图配置
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from .chart_data_provider import ChartData

logger = logging.getLogger(__name__)

class ChartRenderer:
    """图表渲染器"""
    
    def __init__(self):
        pass
    
    def generate_echarts_config(self, chart_data: ChartData) -> Dict[str, Any]:
        """
        生成ECharts配置
        
        Args:
            chart_data: 图表数据
            
        Returns:
            Dict[str, Any]: ECharts配置对象
        """
        try:
            # 基础配置
            config = {
                "title": {
                    "text": f"{chart_data.symbol} - {chart_data.company_name}",
                    "left": "left",
                    "textStyle": {
                        "fontSize": 16,
                        "fontWeight": "bold"
                    },
                    "subtext": self._generate_subtitle(chart_data),
                    "subtextStyle": {
                        "fontSize": 12,
                        "color": self._get_price_color(chart_data.price_change)
                    }
                },
                "tooltip": {
                    "trigger": "axis",
                    "axisPointer": {
                        "type": "cross"
                    },
                    "backgroundColor": "rgba(245, 245, 245, 0.8)",
                    "borderWidth": 1,
                    "borderColor": "#ccc",
                    "padding": 10,
                    "textStyle": {
                        "color": "#000"
                    }
                },
                "legend": {
                    "data": ["K线", "成交量"],
                    "top": 30
                },
                "grid": [
                    {
                        "left": "10%",
                        "right": "8%",
                        "top": "15%",
                        "height": "60%"
                    },
                    {
                        "left": "10%",
                        "right": "8%",
                        "top": "80%",
                        "height": "15%"
                    }
                ],
                "xAxis": [
                    {
                        "type": "category",
                        "data": chart_data.dates,
                        "scale": True,
                        "boundaryGap": False,
                        "axisLine": {"onZero": False},
                        "splitLine": {"show": False},
                        "min": "dataMin",
                        "max": "dataMax",
                        "axisPointer": {
                            "z": 100
                        }
                    },
                    {
                        "type": "category",
                        "gridIndex": 1,
                        "data": chart_data.dates,
                        "scale": True,
                        "boundaryGap": False,
                        "axisLine": {"onZero": False},
                        "axisTick": {"show": False},
                        "splitLine": {"show": False},
                        "axisLabel": {"show": False},
                        "min": "dataMin",
                        "max": "dataMax"
                    }
                ],
                "yAxis": [
                    {
                        "scale": True,
                        "splitArea": {"show": True}
                    },
                    {
                        "scale": True,
                        "gridIndex": 1,
                        "splitNumber": 2,
                        "axisLabel": {"show": False},
                        "axisLine": {"show": False},
                        "axisTick": {"show": False},
                        "splitLine": {"show": False}
                    }
                ],
                "dataZoom": [
                    {
                        "type": "inside",
                        "xAxisIndex": [0, 1],
                        "start": 80,
                        "end": 100
                    },
                    {
                        "show": True,
                        "xAxisIndex": [0, 1],
                        "type": "slider",
                        "top": "90%",
                        "start": 80,
                        "end": 100
                    }
                ],
                "series": [
                    {
                        "name": "K线",
                        "type": "candlestick",
                        "data": chart_data.kline_data,
                        "itemStyle": {
                            "color": "#ec0000",     # 阳线颜色
                            "color0": "#00da3c",   # 阴线颜色
                            "borderColor": "#8A0000",
                            "borderColor0": "#008F28"
                        },
                        "markPoint": {
                            "data": [
                                {
                                    "name": "最高值",
                                    "type": "max",
                                    "valueDim": "highest"
                                },
                                {
                                    "name": "最低值",
                                    "type": "min",
                                    "valueDim": "lowest"
                                }
                            ]
                        },
                        "markLine": {
                            "symbol": ["none", "none"],
                            "data": [
                                [
                                    {
                                        "name": "from lowest to highest",
                                        "type": "min",
                                        "valueDim": "lowest",
                                        "symbol": "circle",
                                        "symbolSize": 10,
                                        "label": {
                                            "show": False
                                        },
                                        "emphasis": {
                                            "label": {
                                                "show": False
                                            }
                                        }
                                    },
                                    {
                                        "type": "max",
                                        "valueDim": "highest",
                                        "symbol": "circle",
                                        "symbolSize": 10,
                                        "label": {
                                            "show": False
                                        },
                                        "emphasis": {
                                            "label": {
                                                "show": False
                                            }
                                        }
                                    }
                                ]
                            ]
                        }
                    },
                    {
                        "name": "成交量",
                        "type": "bar",
                        "xAxisIndex": 1,
                        "yAxisIndex": 1,
                        "data": self._prepare_volume_data_with_colors(chart_data),
                        "itemStyle": {
                            "color": "#1f77b4"
                        }
                    }
                ],
                "animation": True,
                "animationDuration": 1000,
                "animationEasing": "cubicInOut"
            }
            
            return config
            
        except Exception as e:
            logger.error(f"生成ECharts配置时发生错误: {e}")
            return self._get_default_config()
    
    def _generate_subtitle(self, chart_data: ChartData) -> str:
        """生成副标题"""
        change_text = f"{chart_data.price_change:+.2f}"
        percent_text = f"({chart_data.price_change_percent:+.2f}%)"
        
        return f"${chart_data.current_price:.2f} {change_text} {percent_text}"
    
    def _get_price_color(self, price_change: float) -> str:
        """根据价格变化获取颜色"""
        if price_change > 0:
            return "#ec0000"  # 红色表示上涨
        elif price_change < 0:
            return "#00da3c"  # 绿色表示下跌
        else:
            return "#666666"  # 灰色表示无变化
    
    def _get_volume_color(self, kline_point: List[float]) -> str:
        """根据K线数据获取成交量颜色"""
        open_price, close_price = kline_point[0], kline_point[1]
        if close_price >= open_price:
            return "#ec000080"  # 红色（半透明）
        else:
            return "#00da3c80"  # 绿色（半透明）
    
    def _prepare_volume_data_with_colors(self, chart_data: ChartData) -> List[Dict[str, Any]]:
        """准备带颜色的成交量数据"""
        volume_data = []
        for i, volume in enumerate(chart_data.volume_data):
            if i < len(chart_data.kline_data):
                kline = chart_data.kline_data[i]
                color = self._get_volume_color(kline)
                volume_data.append({
                    "value": volume,
                    "itemStyle": {"color": color}
                })
            else:
                volume_data.append({
                    "value": volume,
                    "itemStyle": {"color": "#1f77b4"}
                })
        return volume_data
    
    def _get_tooltip_formatter(self) -> str:
        """获取工具提示格式化函数"""
        return """
        function (param) {
            var colorSpan = color => '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + color + '"></span>';
            var res = param[0].name + '<br/>';
            if (param[0].seriesName === 'K线') {
                res += colorSpan(param[0].color) + ' 开盘: ' + param[0].data[1] + '<br/>';
                res += colorSpan(param[0].color) + ' 最高: ' + param[0].data[4] + '<br/>';
                res += colorSpan(param[0].color) + ' 最低: ' + param[0].data[3] + '<br/>';
                res += colorSpan(param[0].color) + ' 收盘: ' + param[0].data[2] + '<br/>';
                if (param[1]) {
                    res += colorSpan(param[1].color) + ' 成交量: ' + param[1].data.toLocaleString();
                }
            }
            return res;
        }
        """
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置（错误时使用）"""
        return {
            "title": {
                "text": "图表加载失败",
                "left": "center",
                "top": "center",
                "textStyle": {
                    "color": "#999"
                }
            },
            "graphic": {
                "type": "text",
                "left": "center",
                "top": "center",
                "style": {
                    "text": "无法加载股票数据",
                    "fontSize": 16,
                    "fill": "#999"
                }
            }
        }
    
    def generate_chart_html(self, chart_data: ChartData, container_id: str = "chart-container") -> str:
        """
        生成完整的HTML图表页面
        
        Args:
            chart_data: 图表数据
            container_id: 容器ID
            
        Returns:
            str: HTML内容
        """
        config = self.generate_echarts_config(chart_data)
        config_json = json.dumps(config, ensure_ascii=False, indent=2)
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{chart_data.symbol} - 股票K线图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }}
        #{container_id} {{
            width: 100%;
            height: 600px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            box-sizing: border-box;
        }}
        .chart-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }}
        .chart-title {{
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }}
        .chart-info {{
            font-size: 14px;
            color: #666;
        }}
        .price-info {{
            text-align: right;
        }}
        .current-price {{
            font-size: 18px;
            font-weight: bold;
        }}
        .price-change {{
            font-size: 14px;
        }}
        .positive {{ color: #ec0000; }}
        .negative {{ color: #00da3c; }}
        .neutral {{ color: #666; }}
    </style>
</head>
<body>
    <div class="chart-header">
        <div>
            <div class="chart-title">{chart_data.symbol} - {chart_data.company_name}</div>
            <div class="chart-info">最后更新: {chart_data.last_update[:19]}</div>
        </div>
        <div class="price-info">
            <div class="current-price">${chart_data.current_price:.2f}</div>
            <div class="price-change {self._get_price_class(chart_data.price_change)}">
                {chart_data.price_change:+.2f} ({chart_data.price_change_percent:+.2f}%)
            </div>
        </div>
    </div>
    
    <div id="{container_id}"></div>
    
    <script>
        // 初始化ECharts实例
        var chart = echarts.init(document.getElementById('{container_id}'));
        
        // 图表配置
        var option = {config_json};
        
        // 设置图表选项
        chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', function() {{
            chart.resize();
        }});
        
        // 图表事件处理
        chart.on('click', function(params) {{
            if (params.componentType === 'series') {{
                console.log('点击了数据点:', params);
            }}
        }});
        
        // 数据缩放事件
        chart.on('datazoom', function(params) {{
            console.log('数据缩放:', params);
        }});
    </script>
</body>
</html>
        """
        
        return html
    
    def _get_price_class(self, price_change: float) -> str:
        """获取价格变化的CSS类"""
        if price_change > 0:
            return "positive"
        elif price_change < 0:
            return "negative"
        else:
            return "neutral"


# 全局实例
chart_renderer = ChartRenderer() 