#!/usr/bin/env python3
"""
图表数据提供器
为ECharts K线图准备数据
"""

import logging
import json
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from .stock_symbol_detector import stock_detector
from .akshare.us_stock_data import us_stock_daily_sina_tool, get_famous_stock_data_tool

logger = logging.getLogger(__name__)

@dataclass
class ChartDataPoint:
    """K线数据点"""
    date: str           # 日期
    open: float        # 开盘价
    close: float       # 收盘价
    low: float         # 最低价
    high: float        # 最高价
    volume: int        # 成交量

@dataclass
class ChartData:
    """图表数据"""
    symbol: str                    # 股票代码
    company_name: str             # 公司名称
    dates: List[str]              # 日期列表
    kline_data: List[List[float]] # K线数据 [[开盘, 收盘, 最低, 最高], ...]
    volume_data: List[int]        # 成交量数据
    current_price: float          # 当前价格
    price_change: float           # 价格变化
    price_change_percent: float   # 价格变化百分比
    last_update: str              # 最后更新时间

class ChartDataProvider:
    """图表数据提供器"""
    
    def __init__(self):
        self.cache = {}  # 简单的数据缓存
        self.cache_timeout = 300  # 5分钟缓存
    
    async def get_chart_data(self, query: str, max_days: int = 120) -> Optional[ChartData]:
        """
        根据用户查询获取图表数据
        
        Args:
            query: 用户查询
            max_days: 获取最近多少天的数据
            
        Returns:
            Optional[ChartData]: 图表数据，如果无法获取则返回None
        """
        try:
            # 检测股票代码
            symbols = stock_detector.detect_symbols(query)
            if not symbols:
                logger.warning(f"未能从查询中检测到股票代码: {query}")
                return None
            
            primary_symbol = symbols[0].symbol
            company_name = symbols[0].company_name
            
            logger.info(f"为股票 {primary_symbol} ({company_name}) 获取图表数据")
            
            # 检查缓存
            cache_key = f"{primary_symbol}_{max_days}"
            if self._is_cache_valid(cache_key):
                logger.info(f"使用缓存的数据: {cache_key}")
                return self.cache[cache_key]['data']
            
            # 获取股票数据
            stock_data = await self._fetch_stock_data(primary_symbol, max_days)
            if stock_data is None:
                logger.error(f"无法获取股票数据: {primary_symbol}")
                return None
            
            # 转换为图表数据
            chart_data = self._convert_to_chart_data(
                stock_data, primary_symbol, company_name
            )
            
            # 缓存数据
            self._cache_data(cache_key, chart_data)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"获取图表数据时发生错误: {e}")
            return None
    
    async def _fetch_stock_data(self, symbol: str, max_days: int) -> Optional[pd.DataFrame]:
        """
        获取股票数据
        
        Args:
            symbol: 股票代码
            max_days: 最大天数
            
        Returns:
            Optional[pd.DataFrame]: 股票数据
        """
        try:
            # 首先尝试使用 get_famous_stock_data_tool
            logger.info(f"尝试使用 get_famous_stock_data_tool 获取 {symbol} 数据")
            
            # 将股票代码映射为公司名称
            company_name = self._symbol_to_company_name(symbol)
            
            result = get_famous_stock_data_tool.invoke({
                "company_name": company_name,
                "max_rows": max_days
            })
            
            if result and "error" not in result.lower():
                # 解析JSON数据
                stock_data = self._parse_stock_data_json(result)
                if stock_data is not None and not stock_data.empty:
                    logger.info(f"成功通过 get_famous_stock_data_tool 获取数据，{len(stock_data)} 条记录")
                    return stock_data
            
            # 如果失败，尝试使用 us_stock_daily_sina_tool
            logger.info(f"尝试使用 us_stock_daily_sina_tool 获取 {symbol} 数据")
            
            result = us_stock_daily_sina_tool.invoke({
                "symbol": symbol,
                "adjust": "qfq",  # 前复权
                "max_rows": max_days
            })
            
            if result and "error" not in result.lower():
                stock_data = self._parse_stock_data_json(result)
                if stock_data is not None and not stock_data.empty:
                    logger.info(f"成功通过 us_stock_daily_sina_tool 获取数据，{len(stock_data)} 条记录")
                    return stock_data
            
            logger.error(f"所有数据源都无法获取 {symbol} 的数据")
            return None
            
        except Exception as e:
            logger.error(f"获取股票数据时发生错误: {e}")
            return None
    
    def _symbol_to_company_name(self, symbol: str) -> str:
        """将股票代码转换为公司名称"""
        company_mappings = {
            "AAPL": "苹果公司",
            "TSLA": "特斯拉",
            "MSFT": "微软公司",
            "GOOGL": "谷歌",
            "AMZN": "亚马逊",
            "META": "脸书",
            "NVDA": "英伟达",
            "NFLX": "奈飞",
            "BABA": "阿里巴巴",
            "BIDU": "百度",
        }
        return company_mappings.get(symbol, symbol)
    
    def _parse_stock_data_json(self, json_str: str) -> Optional[pd.DataFrame]:
        """
        解析股票数据JSON字符串
        
        Args:
            json_str: JSON字符串
            
        Returns:
            Optional[pd.DataFrame]: 解析后的数据框
        """
        try:
            data = json.loads(json_str)
            
            # 处理不同的数据格式
            if isinstance(data, dict):
                if "data" in data:
                    df = pd.DataFrame(data["data"])
                elif "records" in data:
                    df = pd.DataFrame(data["records"])
                else:
                    # 假设字典本身就是数据
                    df = pd.DataFrame([data])
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                logger.error(f"无法解析的数据格式: {type(data)}")
                return None
            
            # 检查必需的列
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"数据缺少必需的列，当前列: {df.columns.tolist()}")
                return None
            
            # 数据类型转换
            df['date'] = pd.to_datetime(df['date'])
            for col in ['open', 'high', 'low', 'close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            
            # 排序（确保按日期升序）
            df = df.sort_values('date')
            
            # 去除无效数据
            df = df.dropna(subset=['open', 'high', 'low', 'close'])
            
            logger.info(f"成功解析股票数据，{len(df)} 条有效记录")
            return df
            
        except Exception as e:
            logger.error(f"解析股票数据JSON时发生错误: {e}")
            return None
    
    def _convert_to_chart_data(self, df: pd.DataFrame, symbol: str, company_name: str) -> ChartData:
        """
        将DataFrame转换为图表数据
        
        Args:
            df: 股票数据
            symbol: 股票代码
            company_name: 公司名称
            
        Returns:
            ChartData: 图表数据
        """
        # 准备日期列表
        dates = [d.strftime('%Y-%m-%d') for d in df['date']]
        
        # 准备K线数据 [开盘, 收盘, 最低, 最高]
        kline_data = []
        for _, row in df.iterrows():
            kline_data.append([
                float(row['open']),
                float(row['close']),
                float(row['low']),
                float(row['high'])
            ])
        
        # 准备成交量数据
        volume_data = [int(vol) if pd.notna(vol) else 0 for vol in df['volume']]
        
        # 计算价格变化
        current_price = float(df['close'].iloc[-1])
        previous_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
        price_change = current_price - previous_price
        price_change_percent = (price_change / previous_price * 100) if previous_price != 0 else 0
        
        return ChartData(
            symbol=symbol,
            company_name=company_name,
            dates=dates,
            kline_data=kline_data,
            volume_data=volume_data,
            current_price=current_price,
            price_change=price_change,
            price_change_percent=price_change_percent,
            last_update=datetime.now().isoformat()
        )
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).seconds < self.cache_timeout
    
    def _cache_data(self, cache_key: str, data: ChartData):
        """缓存数据"""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
        
        # 简单的缓存清理：如果缓存过多，删除最旧的
        if len(self.cache) > 50:
            oldest_key = min(self.cache.keys(), 
                           key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]


# 全局实例
chart_data_provider = ChartDataProvider() 