# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
AkShare股票数据工具包
提供中国股票市场和美股数据接口
"""

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    ak = None

from .us_stock_data import (
    us_stock_spot_tool,
    us_stock_hist_tool,
    us_stock_search_tool,
    us_stock_famous_tool,
    us_stock_pink_tool,
    us_stock_minute_tool,
    us_stock_daily_sina_tool,
    get_famous_stock_data_tool,
)

from .stock_news import (
    stock_news_em_tool,
    stock_news_main_cx_tool,
    news_report_time_baidu_tool,
    comprehensive_stock_news_tool,
)

__all__ = [
    # 美股数据工具
    "us_stock_spot_tool",
    "us_stock_hist_tool", 
    "us_stock_search_tool",
    "us_stock_famous_tool",
    "us_stock_pink_tool",
    "us_stock_minute_tool",
    "us_stock_daily_sina_tool",
    "get_famous_stock_data_tool",
    
    # 新闻工具
    "stock_news_em_tool",
    "stock_news_main_cx_tool",
    "news_report_time_baidu_tool",
    "comprehensive_stock_news_tool",
    
    # 可用性标志
    "AKSHARE_AVAILABLE",
] 