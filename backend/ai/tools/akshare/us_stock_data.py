# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
from typing import Annotated, Optional
import json

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import stock_data_cached
from ..cache_keys import akshare_stock_key
from .utils import (
    format_dataframe_to_json,
    get_date_range,
    find_stock_symbol,
    validate_period,
    validate_adjust,
    validate_famous_category,
    handle_akshare_error,
)

logger = logging.getLogger(__name__)

try:
    import akshare as ak
except ImportError:
    logger.error("akshare 库未安装，请运行: pip install akshare")
    ak = None


@tool
@log_io
def us_stock_spot_tool(
    max_rows: Annotated[int, "返回的最大行数，默认100"] = 100,
) -> str:
    """【AkShare工具】获取美股实时行情数据（东方财富网）。
    
    这是一个专门的美股实时数据查询工具，可以直接获取美股市场的实时股价信息。
    当用户询问美股价格、实时行情、股票报价时，请优先使用此工具。
    
    功能：返回所有美股上市公司的实时行情数据，包括股价、涨跌幅、成交量、市值等信息。
    数据来源：东方财富网，数据更新可能有延迟。
    
    Args:
        max_rows: 返回的最大行数，默认100行
        
    Returns:
        JSON格式的美股实时行情数据
    """
    if ak is None:
        return handle_akshare_error("us_stock_spot_tool", Exception("akshare库未安装"))
    
    try:
        logger.info("正在获取美股实时行情数据...")
        df = ak.stock_us_spot_em()
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_us_spot_em", e)


@tool
@log_io
@stock_data_cached(ttl=300)  # 5分钟缓存
def us_stock_hist_tool(
    symbol: Annotated[str, "美股代码，如 'AAPL'（注意：这里不需要前缀，直接使用股票代码）"],
    period: Annotated[str, "时间周期：'1d'(1天)/'5d'(5天)/'1mo'(1个月)/'3mo'(3个月)/'6mo'(6个月)/'1y'(1年)/'2y'(2年)/'5y'(5年)/'10y'(10年)/'ytd'(年初至今)/'max'(最大时间范围)"] = "1y",
    interval: Annotated[str, "数据间隔：'1d'(日线)/'1wk'(周线)/'1mo'(月线)"] = "1d",
    adjust: Annotated[str, "复权类型：'back'(后复权)/'forward'(前复权)/''(不复权)"] = "back",
    max_rows: Annotated[int, "返回的最大行数，默认60"] = 60,
) -> str:
    """【AkShare工具】获取美股历史数据（Yahoo Finance）。
    
    这是一个专门的美股历史数据查询工具，可以直接获取美股的历史价格数据。
    当用户询问股价历史、价格走势时，请优先使用此工具。
    
    使用Yahoo Finance接口获取美股历史行情数据，支持多种时间周期和复权方式。
    数据来源：Yahoo Finance，历史数据按日/周/月频率更新。
    注意：返回的数据会自动按日期降序排列，最新数据在前。
    
    Args:
        symbol: 美股代码，如 'AAPL'、'MSFT'、'TSLA' 等（不需要前缀）
        period: 时间周期，支持多种选项
        interval: 数据间隔，支持日线、周线、月线
        adjust: 复权类型，推荐使用后复权
        max_rows: 返回的最大行数，默认60行（约3个月的交易日数据）
        
    Returns:
        JSON格式的历史行情数据，包含日期、开盘、最高、最低、收盘、成交量等字段
        数据按日期降序排列，最新数据在前
    """
    if ak is None:
        return handle_akshare_error("us_stock_hist_tool", Exception("akshare库未安装"))
    
    # 验证参数
    if not validate_period(period):
        return handle_akshare_error("us_stock_hist_tool",
                                  ValueError(f"无效的period参数: {period}"))
    
    if not validate_adjust(adjust):
        return handle_akshare_error("us_stock_hist_tool",
                                  ValueError(f"无效的adjust参数: {adjust}"))
    
    try:
        logger.info(f"正在获取 {symbol} 的历史数据，周期: {period}, 间隔: {interval}")
        df = ak.stock_us_hist(symbol=symbol, period=period, interval=interval, adjust=adjust)
        
        if df.empty:
            return json.dumps({
                "message": f"未找到 {symbol} 的历史数据",
                "symbol": symbol,
                "period": period
            }, ensure_ascii=False)
        
        return format_dataframe_to_json(df, max_rows)
        
    except Exception as e:
        return handle_akshare_error("stock_us_hist", e)


@tool
@log_io
def us_stock_search_tool(
    company_name: Annotated[str, "公司名称，如 'Apple'、'Tesla'、'Microsoft'"],
    max_rows: Annotated[int, "返回的最大行数，默认10"] = 10,
) -> str:
    """【AkShare工具】搜索美股公司信息。
    
    这是一个专门的美股公司搜索工具，通过公司名称查找对应的股票代码和基本信息。
    当用户询问特定公司但不确定股票代码时，请使用此工具。
    
    功能：通过公司名称搜索美股市场的公司信息，返回匹配的公司列表。
    数据来源：东方财富网，结果按相关性排序。
    
    Args:
        company_name: 公司名称（英文或中文都可以）
        max_rows: 返回的最大搜索结果数，默认10行
        
    Returns:
        JSON格式的公司搜索结果，包含公司名称、股票代码、当前价格等信息
    """
    if ak is None:
        return handle_akshare_error("us_stock_search_tool", Exception("akshare库未安装"))
    
    try:
        logger.info(f"正在搜索公司: {company_name}")
        
        # 首先尝试通过名称映射快速查找
        symbol = find_stock_symbol(company_name)
        if symbol:
            logger.info(f"找到映射的股票代码: {symbol}")
            # 如果找到映射的代码，直接获取该股票的详细信息
            df = ak.stock_us_spot_em()
            if not df.empty:
                # 筛选出匹配的股票
                matched_df = df[df['代码'].str.contains(symbol, case=False, na=False) |
                               df['名称'].str.contains(company_name, case=False, na=False)]
                if not matched_df.empty:
                    return format_dataframe_to_json(matched_df, max_rows)
        
        # 如果没有找到映射或映射查找失败，使用全局搜索
        logger.info(f"使用全局搜索查找: {company_name}")
        df = ak.stock_us_spot_em()
        if df.empty:
            return json.dumps({
                "message": "未获取到美股数据",
                "search_term": company_name
            }, ensure_ascii=False)
        
        # 搜索包含关键词的公司
        search_terms = [company_name.lower()]
        
        # 添加一些常见的公司名称变体
        name_lower = company_name.lower()
        if 'apple' in name_lower or '苹果' in name_lower:
            search_terms.extend(['apple', 'aapl'])
        elif 'tesla' in name_lower or '特斯拉' in name_lower:
            search_terms.extend(['tesla', 'tsla'])
        elif 'microsoft' in name_lower or '微软' in name_lower:
            search_terms.extend(['microsoft', 'msft'])
        elif 'google' in name_lower or '谷歌' in name_lower:
            search_terms.extend(['google', 'googl', 'alphabet'])
        elif 'amazon' in name_lower or '亚马逊' in name_lower:
            search_terms.extend(['amazon', 'amzn'])
        elif 'meta' in name_lower or 'facebook' in name_lower or '脸书' in name_lower:
            search_terms.extend(['meta', 'facebook'])
        elif 'nvidia' in name_lower or '英伟达' in name_lower:
            search_terms.extend(['nvidia', 'nvda'])
        
        # 创建搜索条件
        search_condition = None
        for term in search_terms:
            condition = (df['名称'].str.contains(term, case=False, na=False) |
                        df['代码'].str.contains(term, case=False, na=False))
            if search_condition is None:
                search_condition = condition
            else:
                search_condition = search_condition | condition
        
        if search_condition is not None:
            matched_df = df[search_condition]
            if not matched_df.empty:
                return format_dataframe_to_json(matched_df, max_rows)
        
        # 如果没有找到匹配结果
        return json.dumps({
            "message": f"未找到与 '{company_name}' 相关的美股公司",
            "search_term": company_name,
            "suggestion": "请尝试使用更准确的公司名称或股票代码"
        }, ensure_ascii=False)
        
    except Exception as e:
        return handle_akshare_error("us_stock_search", e)


@tool
@log_io
def us_stock_famous_tool(
    category: Annotated[str, "知名美股分类：科技类/金融类/医药食品类/媒体类/汽车能源类/制造零售类"],
    max_rows: Annotated[int, "返回的最大行数，默认50"] = 50,
) -> str:
    """获取知名美股分类数据（东方财富网）。
    
    获取指定分类的知名美股实时行情数据。
    
    Args:
        category: 股票分类
        max_rows: 返回的最大行数
        
    Returns:
        JSON格式的知名美股数据
    """
    if ak is None:
        return handle_akshare_error("us_stock_famous_tool", Exception("akshare库未安装"))
    
    if not validate_famous_category(category):
        return handle_akshare_error("us_stock_famous_tool",
                                  ValueError(f"无效的category参数: {category}"))
    
    try:
        logger.info(f"正在获取知名美股数据，分类: {category}")
        df = ak.stock_us_famous_spot_em(symbol=category)
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_us_famous_spot_em", e)


@tool
@log_io
def us_stock_pink_tool(
    max_rows: Annotated[int, "返回的最大行数，默认50"] = 50,
) -> str:
    """获取美股粉单市场实时行情数据（东方财富网）。
    
    粉单市场是美国场外交易市场，交易的股票通常风险较高。
    
    Args:
        max_rows: 返回的最大行数
        
    Returns:
        JSON格式的粉单市场数据
    """
    if ak is None:
        return handle_akshare_error("us_stock_pink_tool", Exception("akshare库未安装"))
    
    try:
        logger.info("正在获取美股粉单市场数据...")
        df = ak.stock_us_pink_spot_em()
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_us_pink_spot_em", e)


@tool
@log_io
def us_stock_minute_tool(
    symbol: Annotated[str, "美股代码，如 '105.AAPL'"],
    start_date: Annotated[Optional[str], "开始日期时间，格式 'YYYY-MM-DD HH:MM:SS'"] = None,
    end_date: Annotated[Optional[str], "结束日期时间，格式 'YYYY-MM-DD HH:MM:SS'"] = None,
    max_rows: Annotated[int, "返回的最大行数，默认500"] = 500,
) -> str:
    """获取美股分时数据（东方财富网）。
    
    获取指定美股最近5个交易日的分钟级行情数据。注意：美股数据更新有延迟。
    
    Args:
        symbol: 美股代码
        start_date: 开始日期时间（可选）
        end_date: 结束日期时间（可选）
        max_rows: 返回的最大行数
        
    Returns:
        JSON格式的分时数据
    """
    if ak is None:
        return handle_akshare_error("us_stock_minute_tool", Exception("akshare库未安装"))
    
    try:
        logger.info(f"正在获取 {symbol} 的分时数据...")
        
        # 构建参数
        kwargs = {"symbol": symbol}
        if start_date:
            kwargs["start_date"] = start_date
        if end_date:
            kwargs["end_date"] = end_date
            
        df = ak.stock_us_hist_min_em(**kwargs)
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_us_hist_min_em", e)


@tool
@log_io
def us_stock_daily_sina_tool(
    symbol: Annotated[str, "美股代码，如 'AAPL'（注意：这里不需要前缀，直接使用股票代码）"],
    adjust: Annotated[str, "复权类型：''(不复权)/'qfq'(前复权)"] = "",
    max_rows: Annotated[int, "返回的最大行数，默认60"] = 60,
) -> str:
    """【AkShare工具】获取美股历史数据（新浪财经）。
    
    这是一个专门的美股历史数据查询工具，可以直接获取美股的历史价格数据。
    当用户询问股价历史、价格走势时，请优先使用此工具。
    
    使用新浪财经接口获取美股历史行情数据，支持前复权。
    数据来源：新浪财经，历史数据按日频率更新。
    注意：返回的数据会自动按日期降序排列，最新数据在前。
    
    Args:
        symbol: 美股代码，如 'AAPL'、'MSFT'、'TSLA' 等（不需要前缀）
        adjust: 复权类型，''不复权，'qfq'前复权
        max_rows: 返回的最大行数，默认60行（约3个月的交易日数据）
        
    Returns:
        JSON格式的历史行情数据，包含日期、开盘、最高、最低、收盘、成交量等字段
        数据按日期降序排列，最新数据在前
    """
    if ak is None:
        return handle_akshare_error("us_stock_daily_sina_tool", Exception("akshare库未安装"))
    
    # 验证复权参数（新浪接口只支持 '' 和 'qfq'）
    if adjust not in ['', 'qfq']:
        return handle_akshare_error("us_stock_daily_sina_tool",
                                  ValueError(f"无效的adjust参数: {adjust}，新浪接口只支持 ''(不复权) 或 'qfq'(前复权)"))
    
    try:
        logger.info(f"正在获取 {symbol} 的历史数据（新浪财经）...")
        df = ak.stock_us_daily_sina(symbol=symbol, adjust=adjust)
        
        if df.empty:
            return json.dumps({
                "message": f"未找到 {symbol} 的数据",
                "symbol": symbol
            }, ensure_ascii=False)
        
        return format_dataframe_to_json(df, max_rows)
        
    except Exception as e:
        return handle_akshare_error("stock_us_daily_sina", e)


@tool
@log_io
@stock_data_cached(ttl=300)  # 5分钟缓存
def get_famous_stock_data_tool(
    company_name: Annotated[str, "公司名称，如 '苹果公司'、'特斯拉'、'微软' 等"],
    max_rows: Annotated[int, "返回的最大行数，默认60"] = 60,
) -> str:
    """【AkShare工具】直接获取知名公司的股票数据。
    
    这是一个专门的知名股票数据查询工具，可以直接通过中文公司名称获取股价数据。
    当用户询问知名公司（如苹果、特斯拉、微软等）的股价时，请优先使用此工具。
    
    支持的知名公司包括：苹果、特斯拉、微软、谷歌、亚马逊、Meta等。
    直接使用预定义的股票代码，无需搜索，更快更准确。
    默认返回60个交易日的历史数据，控制数据量避免token超限问题。
    
    Args:
        company_name: 公司名称（中文）
        max_rows: 返回的最大行数，默认60（约3个月的交易日数据，避免token超限）
        
    Returns:
        JSON格式的股票历史数据，按日期降序排列
    """
    if ak is None:
        return handle_akshare_error("get_famous_stock_data_tool", Exception("akshare库未安装"))
    
    # 知名公司股票代码映射
    stock_symbols = {
        "苹果公司": "AAPL",
        "苹果": "AAPL",
        "特斯拉": "TSLA", 
        "特斯拉公司": "TSLA",
        "微软": "MSFT",
        "微软公司": "MSFT",
        "谷歌": "GOOGL",
        "谷歌公司": "GOOGL",
        "亚马逊": "AMZN",
        "亚马逊公司": "AMZN",
        "Meta": "META",
        "脸书": "META",
        "英伟达": "NVDA",
        "英伟达公司": "NVDA",
        "奈飞": "NFLX",
        "网飞": "NFLX",
        "推特": "TWTR",
        "摩根大通": "JPM",
        "高盛": "GS",
        "可口可乐": "KO",
        "麦当劳": "MCD",
        "迪士尼": "DIS",
        "波音": "BA",
        "强生": "JNJ",
        # 中概股
        "京东": "JD",
        "京东公司": "JD",
        "阿里巴巴": "BABA",
        "阿里": "BABA",
        "百度": "BIDU",
        "百度公司": "BIDU",
        "腾讯": "TCEHY",
        "腾讯控股": "TCEHY",
        "网易": "NTES",
        "网易公司": "NTES",
        "拼多多": "PDD",
        "哔哩哔哩": "BILI",
        "B站": "BILI",
        "小鹏汽车": "XPEV",
        "理想汽车": "LI",
        "蔚来": "NIO",
        "滴滴": "DIDI",
    }
    
    # 获取股票代码
    symbol = stock_symbols.get(company_name)
    if not symbol:
        return json.dumps({
            "error": f"不支持的公司名称: {company_name}",
            "supported_companies": list(stock_symbols.keys()),
            "suggestion": "请使用支持的公司名称，或使用 us_stock_search_tool 搜索其他公司"
        }, ensure_ascii=False)
    
    try:
        logger.info(f"正在获取 {company_name}({symbol}) 的股票数据...")
        df = ak.stock_us_daily(symbol=symbol, adjust="qfq")
        
        if df.empty:
            return json.dumps({
                "message": f"未找到 {company_name}({symbol}) 的数据",
                "company": company_name,
                "symbol": symbol
            }, ensure_ascii=False)
        
        # 添加公司信息到结果中
        result_data = format_dataframe_to_json(df, max_rows)
        result_dict = json.loads(result_data)
        result_dict["company_info"] = {
            "company_name": company_name,
            "stock_symbol": symbol,
            "data_source": "新浪财经"
        }
        
        return json.dumps(result_dict, ensure_ascii=False, indent=2)
        
    except Exception as e:
        return handle_akshare_error("get_famous_stock_data", e)


if __name__ == "__main__":
    # 测试示例
    print("=== 测试美股实时行情 ===")
    result = us_stock_spot_tool.invoke({"max_rows": 5})
    print(result)
    
    print("\n=== 测试搜索苹果公司 ===")
    result = us_stock_search_tool.invoke({"company_name": "苹果公司"})
    print(result)
    
    print("\n=== 测试新浪财经苹果数据 ===")
    result = us_stock_daily_sina_tool.invoke({"symbol": "AAPL", "adjust": "qfq", "max_rows": 120})
    print(result) 