# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
from typing import Annotated, Optional

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import news_cached
from ..cache_keys import news_key, comprehensive_news_key
from .utils import (
    format_dataframe_to_json,
    handle_akshare_error,
)

logger = logging.getLogger(__name__)

try:
    import akshare as ak
except ImportError:
    logger.error("akshare 库未安装，请运行: pip install akshare")
    ak = None


@tool
@log_io
@news_cached(ttl=900)  # 15分钟缓存
def stock_news_em_tool(
    symbol: Annotated[str, "股票代码或关键词，如 '300059'、'苹果'、'AAPL' 等"],
    max_rows: Annotated[int, "返回的最大行数，默认50"] = 50,
) -> str:
    """【AkShare工具】获取指定个股的新闻资讯数据（东方财富网）。
    
    这是一个专门的股票新闻查询工具，可以直接获取股票相关的最新新闻资讯。
    当用户询问股票新闻、公司动态、市场资讯时，请优先使用此工具。
    
    功能：获取东方财富网指定个股的最新新闻资讯，包括新闻标题、内容、发布时间、来源等信息。
    数据来源：东方财富网，限量返回当日最近100条新闻。
    
    Args:
        symbol: 股票代码或关键词，支持A股代码（如300059）、美股代码（如AAPL）或公司名称
        max_rows: 返回的最大行数，默认50行
        
    Returns:
        JSON格式的新闻数据，包含关键词、新闻标题、新闻内容、发布时间、文章来源、新闻链接等字段
    """
    if ak is None:
        return handle_akshare_error("stock_news_em_tool", Exception("akshare库未安装"))
    
    try:
        logger.info(f"正在获取 {symbol} 的新闻资讯...")
        df = ak.stock_news_em(symbol=symbol)
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_news_em", e)


@tool
@log_io
def stock_news_main_cx_tool(
    max_rows: Annotated[int, "返回的最大行数，默认100"] = 100,
) -> str:
    """获取财新网财经内容精选新闻。
    
    获取财新网财新数据通的内容精选，包含财经新闻、市场分析等高质量内容。
    数据来源：财新网，返回所有历史新闻数据。
    
    Args:
        max_rows: 返回的最大行数，默认100行
        
    Returns:
        JSON格式的财经新闻数据，包含标签、摘要、时间间隔、发布时间、链接等字段
    """
    if ak is None:
        return handle_akshare_error("stock_news_main_cx_tool", Exception("akshare库未安装"))
    
    try:
        logger.info("正在获取财新网财经内容精选...")
        df = ak.stock_news_main_cx()
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("stock_news_main_cx", e)


@tool
@log_io
def news_report_time_baidu_tool(
    date: Annotated[str, "查询日期，格式YYYYMMDD，如 '20241107'"],
    max_rows: Annotated[int, "返回的最大行数，默认100"] = 100,
) -> str:
    """获取指定日期的财报发行时间表（百度股市通）。
    
    获取百度股市通指定日期的财报发行安排，包含A股、港股、美股的财报发布信息。
    数据来源：百度股市通，提供港股的财报发行数据。
    
    Args:
        date: 查询日期，格式为YYYYMMDD，如 '20241107'
        max_rows: 返回的最大行数，默认100行
        
    Returns:
        JSON格式的财报发行数据，包含股票代码、交易所、股票简称、财报期等字段
    """
    if ak is None:
        return handle_akshare_error("news_report_time_baidu_tool", Exception("akshare库未安装"))
    
    # 验证日期格式
    if not date or len(date) != 8 or not date.isdigit():
        return handle_akshare_error("news_report_time_baidu_tool",
                                  ValueError(f"无效的日期格式: {date}，应为YYYYMMDD格式，如20241107"))
    
    try:
        logger.info(f"正在获取 {date} 的财报发行时间表...")
        df = ak.news_report_time_baidu(date=date)
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("news_report_time_baidu", e)


@tool
@log_io
@news_cached(ttl=900)  # 15分钟缓存
def search_news_tool(
    keyword: Annotated[str, "搜索关键词，如 '苹果'、'新能源'、'人工智能' 等"],
    max_rows: Annotated[int, "返回的最大行数，默认50"] = 50,
) -> str:
    """【AkShare工具】通用新闻搜索工具（东方财富网）。

    这是一个通用的新闻搜索工具，可以根据关键词搜索相关新闻资讯。
    支持股票名称、行业关键词、热点话题等多种搜索方式。

    功能：根据关键词搜索东方财富网的新闻资讯，包括新闻标题、内容、发布时间、来源等信息。
    数据来源：东方财富网，限量返回当日最近100条相关新闻。

    Args:
        keyword: 搜索关键词，支持中文和英文，如公司名称、行业词汇、热点话题等
        max_rows: 返回的最大行数，默认50行

    Returns:
        JSON格式的新闻数据，包含关键词、新闻标题、新闻内容、发布时间、文章来源、新闻链接等字段
    """
    if ak is None:
        return handle_akshare_error("search_news_tool", Exception("akshare库未安装"))

    try:
        logger.info(f"正在搜索关键词 '{keyword}' 的新闻资讯...")
        df = ak.stock_news_em(symbol=keyword)
        return format_dataframe_to_json(df, max_rows)
    except Exception as e:
        return handle_akshare_error("search_news_tool", e)


@tool
@log_io
@news_cached(ttl=900)  # 15分钟缓存
def comprehensive_stock_news_tool(
    symbol: Annotated[str, "股票代码或关键词"],
    include_general_news: Annotated[bool, "是否包含财经大盘新闻，默认True"] = True,
    date_for_reports: Annotated[Optional[str], "查询财报发行的日期，格式YYYYMMDD，可选"] = None,
    max_rows_per_source: Annotated[int, "每个数据源返回的最大行数，默认30"] = 30,
) -> str:
    """综合股票新闻获取工具。
    
    一次性获取指定股票的多种新闻信息，包括：
    1. 个股专门新闻（东方财富）
    2. 财经内容精选（财新网）- 可选
    3. 财报发行时间表（百度股市通）- 可选
    
    Args:
        symbol: 股票代码或关键词
        include_general_news: 是否包含财经大盘新闻
        date_for_reports: 查询财报发行的日期，格式YYYYMMDD
        max_rows_per_source: 每个数据源返回的最大行数
        
    Returns:
        JSON格式的综合新闻数据
    """
    if ak is None:
        return handle_akshare_error("comprehensive_stock_news_tool", Exception("akshare库未安装"))
    
    result = {
        "symbol": symbol,
        "timestamp": pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
        "data_sources": {}
    }
    
    try:
        # 1. 获取个股新闻
        logger.info(f"正在获取 {symbol} 的个股新闻...")
        try:
            stock_news_df = ak.stock_news_em(symbol=symbol)
            if not stock_news_df.empty:
                limited_df = stock_news_df.head(max_rows_per_source)
                result["data_sources"]["individual_stock_news"] = {
                    "source": "东方财富网",
                    "total_rows": len(stock_news_df),
                    "displayed_rows": len(limited_df),
                    "data": limited_df.to_dict('records')
                }
            else:
                result["data_sources"]["individual_stock_news"] = {
                    "source": "东方财富网",
                    "message": "未找到相关新闻"
                }
        except Exception as e:
            result["data_sources"]["individual_stock_news"] = {
                "source": "东方财富网",
                "error": str(e)
            }
        
        # 2. 获取财经内容精选（如果需要）
        if include_general_news:
            logger.info("正在获取财经内容精选...")
            try:
                general_news_df = ak.stock_news_main_cx()
                if not general_news_df.empty:
                    limited_df = general_news_df.head(max_rows_per_source)
                    result["data_sources"]["general_financial_news"] = {
                        "source": "财新网",
                        "total_rows": len(general_news_df),
                        "displayed_rows": len(limited_df),
                        "data": limited_df.to_dict('records')
                    }
                else:
                    result["data_sources"]["general_financial_news"] = {
                        "source": "财新网",
                        "message": "未找到财经新闻"
                    }
            except Exception as e:
                result["data_sources"]["general_financial_news"] = {
                    "source": "财新网",
                    "error": str(e)
                }
        
        # 3. 获取财报发行时间（如果指定了日期）
        if date_for_reports:
            logger.info(f"正在获取 {date_for_reports} 的财报发行时间...")
            try:
                report_time_df = ak.news_report_time_baidu(date=date_for_reports)
                if not report_time_df.empty:
                    # 尝试筛选与目标股票相关的财报
                    related_reports = report_time_df[
                        report_time_df['股票代码'].str.contains(symbol, na=False) |
                        report_time_df['股票简称'].str.contains(symbol, na=False)
                    ]
                    
                    if not related_reports.empty:
                        result["data_sources"]["earnings_reports"] = {
                            "source": "百度股市通",
                            "date": date_for_reports,
                            "total_rows": len(related_reports),
                            "displayed_rows": len(related_reports),
                            "data": related_reports.to_dict('records')
                        }
                    else:
                        # 如果没有找到相关的，返回当日所有财报（限制行数）
                        limited_df = report_time_df.head(max_rows_per_source)
                        result["data_sources"]["earnings_reports"] = {
                            "source": "百度股市通",
                            "date": date_for_reports,
                            "total_rows": len(report_time_df),
                            "displayed_rows": len(limited_df),
                            "data": limited_df.to_dict('records'),
                            "note": f"未找到 {symbol} 相关财报，显示当日所有财报"
                        }
                else:
                    result["data_sources"]["earnings_reports"] = {
                        "source": "百度股市通",
                        "date": date_for_reports,
                        "message": "当日无财报发行"
                    }
            except Exception as e:
                result["data_sources"]["earnings_reports"] = {
                    "source": "百度股市通",
                    "date": date_for_reports,
                    "error": str(e)
                }
        
        return json.dumps(result, ensure_ascii=False, indent=2, default=str)
        
    except Exception as e:
        return handle_akshare_error("comprehensive_stock_news_tool", e)


if __name__ == "__main__":
    # 测试示例
    import json
    import pandas as pd
    
    print("=== 测试个股新闻 ===")
    result = stock_news_em_tool.invoke({"symbol": "300059", "max_rows": 5})
    print(result)
    
    print("\n=== 测试财经内容精选 ===")
    result = stock_news_main_cx_tool.invoke({"max_rows": 5})
    print(result)
    
    print("\n=== 测试财报发行时间 ===")
    result = news_report_time_baidu_tool.invoke({"date": "20241107", "max_rows": 5})
    print(result)
    
    print("\n=== 测试综合新闻工具 ===")
    result = comprehensive_stock_news_tool.invoke({
        "symbol": "AAPL",
        "include_general_news": True,
        "date_for_reports": "20241107",
        "max_rows_per_source": 3
    })
    print(result)

    print("\n=== 测试新闻搜索工具 ===")
    result = search_news_tool.invoke({
        "keyword": "苹果",
        "max_rows": 5
    })
    print(result)