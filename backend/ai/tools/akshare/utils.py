# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


def format_dataframe_to_json(df: pd.DataFrame, max_rows: int = 100) -> str:
    """
    将DataFrame格式化为JSON字符串，限制行数以避免输出过长
    
    Args:
        df: pandas DataFrame
        max_rows: 最大行数限制
        
    Returns:
        格式化的JSON字符串
    """
    if df.empty:
        return json.dumps({"message": "没有找到数据"}, ensure_ascii=False)
    
    # 创建DataFrame副本以避免修改原数据
    df_copy = df.copy()
    
    # 尝试找到日期列并按日期降序排列（最新数据在前）
    date_columns = []
    for col in df_copy.columns:
        if any(keyword in col.lower() for keyword in ['date', '日期', 'time', '时间']):
            date_columns.append(col)
        elif pd.api.types.is_datetime64_any_dtype(df_copy[col]):
            date_columns.append(col)
    
    # 如果找到日期列，按第一个日期列降序排列
    if date_columns:
        try:
            # 确保日期列是datetime类型
            if not pd.api.types.is_datetime64_any_dtype(df_copy[date_columns[0]]):
                df_copy[date_columns[0]] = pd.to_datetime(df_copy[date_columns[0]], errors='coerce')
            
            # 按日期降序排列（最新的在前面）
            df_copy = df_copy.sort_values(by=date_columns[0], ascending=False)
            logger.info(f"数据已按 {date_columns[0]} 列降序排列，最新数据在前")
        except Exception as e:
            logger.warning(f"无法按日期排序: {e}")
    
    # 处理日期时间列，转换为字符串
    for col in df_copy.columns:
        if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
            df_copy[col] = df_copy[col].dt.strftime('%Y-%m-%d')
        elif df_copy[col].dtype == 'object':
            # 检查是否包含Timestamp对象
            try:
                if hasattr(df_copy[col].iloc[0], 'strftime'):
                    df_copy[col] = df_copy[col].apply(lambda x: x.strftime('%Y-%m-%d') if pd.notnull(x) else None)
            except (IndexError, AttributeError):
                pass
    
    # 限制行数
    if len(df_copy) > max_rows:
        df_limited = df_copy.head(max_rows)
        result = {
            "data": df_limited.to_dict('records'),
            "total_rows": len(df),
            "displayed_rows": max_rows,
            "message": f"显示前{max_rows}行，总共{len(df)}行数据（已按日期降序排列）"
        }
    else:
        result = {
            "data": df_copy.to_dict('records'),
            "total_rows": len(df),
            "displayed_rows": len(df),
            "message": f"显示全部{len(df)}行数据（已按日期降序排列）"
        }
    
    return json.dumps(result, ensure_ascii=False, indent=2, default=str)


def get_date_range(days_back: int = 365) -> tuple[str, str]:
    """
    获取日期范围
    
    Args:
        days_back: 往前推的天数，默认365天（一年）
        
    Returns:
        (start_date, end_date) 格式为 YYYYMMDD
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    return start_date.strftime('%Y%m%d'), end_date.strftime('%Y%m%d')


def find_stock_symbol(stock_list_df: pd.DataFrame, company_name: str) -> Optional[str]:
    """
    在股票列表中查找公司的代码
    
    Args:
        stock_list_df: 股票列表DataFrame
        company_name: 公司名称
        
    Returns:
        股票代码或None
    """
    if stock_list_df.empty:
        return None
        
    # 尝试精确匹配
    exact_match = stock_list_df[stock_list_df['名称'] == company_name]
    if not exact_match.empty:
        return exact_match.iloc[0]['代码']
    
    # 尝试模糊匹配
    fuzzy_match = stock_list_df[stock_list_df['名称'].str.contains(company_name, na=False)]
    if not fuzzy_match.empty:
        return fuzzy_match.iloc[0]['代码']
    
    return None


def validate_period(period: str) -> bool:
    """
    验证时间周期参数
    
    Args:
        period: 时间周期
        
    Returns:
        是否有效
    """
    valid_periods = ['daily', 'weekly', 'monthly']
    return period in valid_periods


def validate_adjust(adjust: str) -> bool:
    """
    验证复权参数
    
    Args:
        adjust: 复权类型
        
    Returns:
        是否有效
    """
    valid_adjusts = ['', 'qfq', 'hfq']
    return adjust in valid_adjusts


def validate_famous_category(category: str) -> bool:
    """
    验证知名美股分类参数
    
    Args:
        category: 分类名称
        
    Returns:
        是否有效
    """
    valid_categories = ['科技类', '金融类', '医药食品类', '媒体类', '汽车能源类', '制造零售类']
    return category in valid_categories


def handle_akshare_error(func_name: str, error: Exception) -> str:
    """
    处理akshare函数调用错误
    
    Args:
        func_name: 函数名称
        error: 异常对象
        
    Returns:
        错误信息JSON字符串
    """
    error_msg = f"调用 {func_name} 时发生错误: {str(error)}"
    logger.error(error_msg)
    
    return json.dumps({
        "error": error_msg,
        "function": func_name,
        "error_type": type(error).__name__
    }, ensure_ascii=False) 