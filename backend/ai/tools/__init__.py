# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os

from .crawl import crawl_tool
from .python_repl import python_repl_tool
from .search import get_web_search_tool

# 尝试导入可选工具
try:
    from .akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        stock_news_em_tool,
        stock_news_main_cx_tool,
        news_report_time_baidu_tool,
        comprehensive_stock_news_tool,
        get_famous_stock_data_tool,
    )
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

try:
    from .divergence_analysis import detect_price_macd_divergence
    from .technical_indicators import calculate_technical_indicators, fibonacci_retracement_levels
    TECHNICAL_ANALYSIS_AVAILABLE = True
except ImportError:
    TECHNICAL_ANALYSIS_AVAILABLE = False

try:
    from .yahoo_finance_news import yahoo_finance_news_tool
    YAHOO_FINANCE_AVAILABLE = True
except ImportError:
    YAHOO_FINANCE_AVAILABLE = False

__all__ = [
    "crawl_tool",
    "python_repl_tool", 
    "get_web_search_tool",
]

# 条件性添加可选工具
if AKSHARE_AVAILABLE:
    __all__.extend([
        "us_stock_spot_tool",
        "us_stock_hist_tool", 
        "us_stock_search_tool",
        "us_stock_famous_tool",
        "us_stock_pink_tool",
        "us_stock_minute_tool",
        "us_stock_daily_sina_tool",
        "stock_news_em_tool",
        "stock_news_main_cx_tool",
        "news_report_time_baidu_tool",
        "comprehensive_stock_news_tool",
        "get_famous_stock_data_tool",
    ])

if TECHNICAL_ANALYSIS_AVAILABLE:
    __all__.extend([
        "detect_price_macd_divergence",
        "calculate_technical_indicators",
        "fibonacci_retracement_levels",
    ])

if YAHOO_FINANCE_AVAILABLE:
    __all__.extend(["yahoo_finance_news_tool"]) 