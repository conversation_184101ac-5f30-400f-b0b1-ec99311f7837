import hashlib
import json
from typing import Any, Dict, List, Optional, Union

def generate_cache_key(tool_name: str, **kwargs) -> str:
    """
    生成标准化的缓存键
    
    Args:
        tool_name: 工具名称
        **kwargs: 工具参数
    
    Returns:
        str: 标准化的缓存键
    """
    # 创建参数字典
    params = {}
    for key, value in kwargs.items():
        if value is not None:
            params[key] = value
    
    # 排序确保一致性
    sorted_params = json.dumps(params, sort_keys=True, ensure_ascii=False)
    
    # 创建短哈希
    param_hash = hashlib.md5(sorted_params.encode('utf-8')).hexdigest()[:8]
    
    # 生成键
    cache_key = f"{tool_name}:{param_hash}"
    
    return cache_key

def stock_data_key(symbol: str, period: str = "1d", **kwargs) -> str:
    """股票数据缓存键"""
    return generate_cache_key("stock_data", symbol=symbol, period=period, **kwargs)

def news_key(symbol: Optional[str] = None, query: Optional[str] = None, **kwargs) -> str:
    """新闻数据缓存键"""
    return generate_cache_key("news", symbol=symbol, query=query, **kwargs)

def yahoo_finance_key(symbol: str, **kwargs) -> str:
    """Yahoo Finance缓存键"""
    return generate_cache_key("yahoo_finance", symbol=symbol, **kwargs)

def comprehensive_news_key(symbol: str, **kwargs) -> str:
    """综合新闻缓存键"""
    return generate_cache_key("comprehensive_news", symbol=symbol, **kwargs)

def akshare_stock_key(symbol: str, **kwargs) -> str:
    """AKShare股票数据缓存键"""
    return generate_cache_key("akshare_stock", symbol=symbol, **kwargs)

def create_tool_key(tool_name: str, params: Dict[str, Any]) -> str:
    """
    为任意工具创建缓存键
    
    Args:
        tool_name: 工具名称
        params: 工具参数字典
    
    Returns:
        str: 缓存键
    """
    return generate_cache_key(tool_name, **params) 