# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

# -*- coding: utf-8 -*-
"""
网络搜索工具
提供各种搜索引擎接口
"""

import os
import enum
import logging
from typing import Dict, Any, List

from .decorators import create_logged_tool, log_io
from ..config.tools import SearchEngine, SELECTED_SEARCH_ENGINE

logger = logging.getLogger(__name__)

def get_web_search_tool(max_search_results: int):
    """Get configured web search tool based on SELECTED_SEARCH_ENGINE"""
    
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY.value:
        return LoggedTavilySearch(max_results=max_search_results)
    elif SELECTED_SEARCH_ENGINE == SearchEngine.DUCKDUCKGO.value:
        return _get_duckduckgo_search(max_search_results)
    elif SELECTED_SEARCH_ENGINE == SearchEngine.BRAVE_SEARCH.value:
        return _get_brave_search(max_search_results)
    elif SELECTED_SEARCH_ENGINE == SearchEngine.ARXIV.value:
        return _get_arxiv_search(max_search_results)
    else:
        logger.warning(f"未知的搜索引擎: {SELECTED_SEARCH_ENGINE}, 使用 Tavily 作为默认")
        return LoggedTavilySearch(max_results=max_search_results)


def _get_duckduckgo_search(max_results: int):
    """获取 DuckDuckGo 搜索工具"""
    try:
        from langchain_community.tools import DuckDuckGoSearchRun
        return DuckDuckGoSearchRun(max_results=max_results)
    except ImportError:
        logger.error("DuckDuckGo 搜索不可用，请安装 duckduckgo-search")
        return LoggedTavilySearch(max_results=max_results)


def _get_brave_search(max_results: int):
    """获取 Brave 搜索工具"""
    try:
        from langchain_community.tools import BraveSearch
        return BraveSearch.from_api_key(
            api_key=os.getenv("BRAVE_SEARCH_API_KEY"),
            search_kwargs={"count": max_results}
        )
    except ImportError:
        logger.error("Brave 搜索不可用，请安装 langchain-community")
        return LoggedTavilySearch(max_results=max_results)


def _get_arxiv_search(max_results: int):
    """获取 ArXiv 搜索工具"""
    try:
        from langchain_community.tools import ArxivQueryRun
        return ArxivQueryRun(top_k_results=max_results)
    except ImportError:
        logger.error("ArXiv 搜索不可用，请安装 arxiv")
        return LoggedTavilySearch(max_results=max_results)


class LoggedTavilySearch:
    """带日志功能的 Tavily 搜索工具"""
    
    def __init__(self, max_results: int = 10):
        self.max_results = max_results
        self.api_key = os.getenv("TAVILY_API_KEY")
        
        if not self.api_key:
            logger.warning("TAVILY_API_KEY 未设置，搜索功能可能受限")
        
        try:
            from tavily import TavilyClient
            self.client = TavilyClient(api_key=self.api_key)
        except ImportError:
            logger.error("Tavily 客户端不可用，请安装 tavily-python")
            self.client = None

    @log_io
    def invoke(self, query_dict: dict) -> list:
        """执行搜索查询"""
        if not self.client:
            return [{"error": "搜索客户端不可用"}]
        
        query = query_dict.get("query", "") if isinstance(query_dict, dict) else str(query_dict)
        
        if not query:
            return [{"error": "搜索查询为空"}]
        
        try:
            logger.info(f"执行 Tavily 搜索: {query}")
            
            # 执行搜索
            response = self.client.search(
                query=query,
                max_results=self.max_results,
                search_depth="advanced"
            )
            
            # 提取结果
            results = []
            if "results" in response:
                for item in response["results"]:
                    results.append({
                        "title": item.get("title", ""),
                        "content": item.get("content", ""),
                        "url": item.get("url", ""),
                        "score": item.get("score", 0.0),
                    })
            
            logger.info(f"搜索返回 {len(results)} 条结果")
            return results
            
        except Exception as e:
            logger.error(f"Tavily 搜索失败: {e}")
            return [{"error": f"搜索失败: {str(e)}"}] 