# -*- coding: utf-8 -*-
"""
网页爬虫工具
提供网页内容抓取功能
"""

import logging
from typing import Annotated
from langchain_core.tools import tool

from .decorators import log_io

logger = logging.getLogger(__name__)

@tool
@log_io
def crawl_tool(
    url: Annotated[str, "要爬取的网页URL地址"],
) -> str:
    """
    爬取网页内容并转换为Markdown格式
    
    这个工具可以：
    - 抓取网页内容
    - 清理HTML标签
    - 转换为可读的Markdown格式
    - 提取主要内容
    
    Args:
        url: 要爬取的网页URL
        
    Returns:
        网页内容的Markdown格式文本
    """
    try:
        # 尝试使用Jina Reader API
        return _crawl_with_jina(url)
    except Exception as e:
        logger.warning(f"Jina爬虫失败: {e}，尝试备用方案")
        try:
            # 备用方案：使用requests + beautifulsoup
            return _crawl_with_requests(url)
        except Exception as e2:
            logger.error(f"所有爬虫方案都失败: {e2}")
            return f"网页爬取失败: {str(e2)}"


def _crawl_with_jina(url: str) -> str:
    """使用Jina Reader API爬取网页"""
    import os
    import requests
    
    headers = {
        "Content-Type": "application/json",
        "X-Return-Format": "markdown",
    }
    
    # 如果有Jina API Key，添加认证
    jina_api_key = os.getenv("JINA_API_KEY")
    if jina_api_key:
        headers["Authorization"] = f"Bearer {jina_api_key}"
    else:
        logger.info("未设置JINA_API_KEY，使用免费额度")
    
    data = {"url": url}
    response = requests.post("https://r.jina.ai/", headers=headers, json=data)
    
    if response.status_code == 200:
        return response.text
    else:
        raise Exception(f"Jina API返回错误: {response.status_code}")


def _crawl_with_requests(url: str) -> str:
    """使用requests + beautifulsoup爬取网页"""
    try:
        import requests
        from bs4 import BeautifulSoup
        from markdownify import markdownify as md
    except ImportError:
        raise Exception("缺少必要的依赖包: requests, beautifulsoup4, markdownify")
    
    # 设置请求头，模拟浏览器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    response = requests.get(url, headers=headers, timeout=10)
    response.raise_for_status()
    
    # 解析HTML
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # 移除脚本和样式标签
    for script in soup(["script", "style"]):
        script.decompose()
    
    # 尝试提取主要内容
    main_content = None
    
    # 常见的主要内容选择器
    content_selectors = [
        'main',
        'article', 
        '.content',
        '.main-content',
        '.post-content',
        '.entry-content',
        '#content',
        '#main'
    ]
    
    for selector in content_selectors:
        main_content = soup.select_one(selector)
        if main_content:
            break
    
    # 如果没找到主要内容，使用body
    if not main_content:
        main_content = soup.find('body')
    
    if not main_content:
        main_content = soup
    
    # 转换为Markdown
    markdown_content = md(str(main_content))
    
    # 清理多余的空行
    lines = markdown_content.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if line:
            cleaned_lines.append(line)
            prev_empty = False
        elif not prev_empty:
            cleaned_lines.append('')
            prev_empty = True
    
    return '\n'.join(cleaned_lines) 