import time
import threading
from typing import Any, Dict, Optional, Tuple
from collections import OrderedDict
import json
import hashlib
import logging

logger = logging.getLogger(__name__)

class LRUCache:
    """线程安全的LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: OrderedDict = OrderedDict()
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0
        }
    
    def get(self, key: str) -> Optional[Tuple[Any, float]]:
        """获取缓存数据，返回(data, timestamp)或None"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                self.cache.move_to_end(key)
                self.access_times[key] = time.time()
                self.stats['hits'] += 1
                return self.cache[key]
            else:
                self.stats['misses'] += 1
                return None
    
    def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """设置缓存数据，ttl为生存时间（秒）"""
        with self.lock:
            current_time = time.time()
            expires_at = current_time + ttl
            
            if key in self.cache:
                # 更新现有键
                self.cache[key] = (value, expires_at)
                self.cache.move_to_end(key)
            else:
                # 添加新键
                self.cache[key] = (value, expires_at)
                
                # 检查是否需要清理
                if len(self.cache) > self.max_size:
                    self._evict_oldest()
            
            self.access_times[key] = current_time
            self.stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """删除指定键"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            logger.info("Cache cleared")
    
    def cleanup_expired(self) -> int:
        """清理过期的缓存项，返回清理的数量"""
        with self.lock:
            current_time = time.time()
            expired_keys = []
            
            for key, (_, expires_at) in self.cache.items():
                if current_time > expires_at:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
            
            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
            
            return len(expired_keys)
    
    def _evict_oldest(self) -> None:
        """移除最老的缓存项"""
        if self.cache:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            if oldest_key in self.access_times:
                del self.access_times[oldest_key]
            self.stats['evictions'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': round(hit_rate, 2),
                'stats': self.stats.copy()
            }

class CacheManager:
    """全局缓存管理器"""
    
    def __init__(self):
        # 不同类型数据的缓存实例
        self.stock_data_cache = LRUCache(max_size=500)  # 股票数据缓存
        self.news_cache = LRUCache(max_size=300)        # 新闻数据缓存
        self.general_cache = LRUCache(max_size=200)     # 通用缓存
        
        # 默认TTL设置（秒）
        self.default_ttls = {
            'stock_data': 300,     # 5分钟
            'news': 900,           # 15分钟  
            'general': 600         # 10分钟
        }
        
        # 启动清理任务
        self._start_cleanup_task()
    
    def get_cache(self, cache_type: str) -> LRUCache:
        """获取指定类型的缓存实例"""
        cache_map = {
            'stock_data': self.stock_data_cache,
            'news': self.news_cache,
            'general': self.general_cache
        }
        return cache_map.get(cache_type, self.general_cache)
    
    def get(self, key: str, cache_type: str = 'general') -> Optional[Any]:
        """获取缓存数据，自动检查过期"""
        cache = self.get_cache(cache_type)
        result = cache.get(key)
        
        if result is None:
            return None
        
        data, expires_at = result
        current_time = time.time()
        
        if current_time > expires_at:
            # 数据已过期，删除并返回None
            cache.delete(key)
            return None
        
        return data
    
    def set(self, key: str, value: Any, cache_type: str = 'general', ttl: Optional[int] = None) -> None:
        """设置缓存数据"""
        cache = self.get_cache(cache_type)
        if ttl is None:
            ttl = self.default_ttls.get(cache_type, 600)
        
        cache.set(key, value, ttl)
        logger.debug(f"Cached data with key: {key[:50]}... in {cache_type} cache")
    
    def delete(self, key: str, cache_type: str = 'general') -> bool:
        """删除缓存数据"""
        cache = self.get_cache(cache_type)
        return cache.delete(key)
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        self.stock_data_cache.clear()
        self.news_cache.clear()
        self.general_cache.clear()
        logger.info("All caches cleared")
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取所有缓存的统计信息"""
        return {
            'stock_data': self.stock_data_cache.get_stats(),
            'news': self.news_cache.get_stats(),
            'general': self.general_cache.get_stats()
        }
    
    def _start_cleanup_task(self) -> None:
        """启动定期清理任务"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(60)  # 每分钟执行一次清理
                    total_cleaned = 0
                    total_cleaned += self.stock_data_cache.cleanup_expired()
                    total_cleaned += self.news_cache.cleanup_expired()
                    total_cleaned += self.general_cache.cleanup_expired()
                    
                    if total_cleaned > 0:
                        logger.info(f"Cache cleanup completed, removed {total_cleaned} expired entries")
                except Exception as e:
                    logger.error(f"Cache cleanup error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

# 全局缓存管理器实例
cache_manager = CacheManager() 