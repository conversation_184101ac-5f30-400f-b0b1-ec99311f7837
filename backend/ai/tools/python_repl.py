# -*- coding: utf-8 -*-
"""
Python REPL 工具
提供Python代码执行功能
"""

from typing import Annotated
from langchain_experimental.tools import PythonREPLTool
from langchain_core.tools import tool

from .decorators import log_io

# 创建Python REPL工具实例
_python_repl = PythonREPLTool()

@tool
@log_io
def python_repl_tool(
    code: Annotated[
        str, "要执行的Python代码，用于进一步分析或计算。"
    ],
):
    """
    执行Python代码进行数据分析和计算
    
    这个工具可以：
    - 执行数学计算
    - 数据分析和处理
    - 技术指标计算
    - 图表绘制
    - 统计分析
    
    Args:
        code: 要执行的Python代码
        
    Returns:
        代码执行结果
    """
    try:
        return _python_repl.run(code)
    except Exception as e:
        return f"Python代码执行错误: {str(e)}" 