# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

# -*- coding: utf-8 -*-
"""
工具装饰器
提供日志记录等功能
"""

import functools
import logging
from typing import Any, Callable, Type, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar('T')

def log_io(func: Callable) -> Callable:
    """
    装饰器：记录函数的输入和输出
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Log input parameters
        func_name = func.__name__
        
        # 避免记录过长的参数
        safe_args = []
        for arg in args:
            if isinstance(arg, str) and len(arg) > 200:
                safe_args.append(f"{arg[:200]}...(truncated)")
            else:
                safe_args.append(str(arg)[:100])
        
        safe_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, str) and len(value) > 200:
                safe_kwargs[key] = f"{value[:200]}...(truncated)"
            else:
                safe_kwargs[key] = str(value)[:100]
        
        logger.info(f"[{func_name}] 输入参数: args={safe_args}, kwargs={safe_kwargs}")
        
        try:
            # Execute function
            result = func(*args, **kwargs)
            
            # Log output (safely)
            if isinstance(result, str) and len(result) > 500:
                logger.info(f"[{func_name}] 输出: {result[:500]}...(truncated)")
            else:
                logger.info(f"[{func_name}] 输出: {str(result)[:500]}")
            
            return result
            
        except Exception as e:
            logger.error(f"[{func_name}] 执行错误: {e}")
            raise
    
    return wrapper


class LoggedToolMixin:
    """为工具类添加日志功能的Mixin"""
    
    def _log_operation(self, method_name: str, *args: Any, **kwargs: Any) -> None:
        """记录工具操作"""
        logger.info(f"工具操作: {self.__class__.__name__}.{method_name}")
        
    def _run(self, *args: Any, **kwargs: Any) -> Any:
        """重写_run方法以添加日志"""
        self._log_operation("_run", *args, **kwargs)
        try:
            return super()._run(*args, **kwargs)  # type: ignore
        except Exception as e:
            logger.error(f"工具执行错误: {e}")
            raise


def create_logged_tool(base_tool_class: Type[T]) -> Type[T]:
    """
    创建带日志功能的工具类
    
    Args:
        base_tool_class: 基础工具类
        
    Returns:
        带日志功能的工具类
    """
    class LoggedTool(LoggedToolMixin, base_tool_class):
        pass
    
    LoggedTool.__name__ = f"Logged{base_tool_class.__name__}"
    return LoggedTool 