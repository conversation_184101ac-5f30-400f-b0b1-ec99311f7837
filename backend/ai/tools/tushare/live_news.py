# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
import requests
import os
from typing import Annotated, Optional, Dict, Any, List
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import news_cached
from ..cache_keys import news_key

logger = logging.getLogger(__name__)

# Tushare Pro配置
TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', '')
TUSHARE_API_URL = 'http://api.tushare.pro'

# 支持的新闻源配置
SUPPORTED_NEWS_SOURCES = {
    'sina': '新浪财经',
    'eastmoney': '东方财富', 
    'wallstreetcn': '华尔街见闻',
    '10jqka': '同花顺',
    'fenghuang': '凤凰网',
    'jinrongjie': '金融界',
    'yuncaijing': '云财经'
}


def get_tushare_token() -> str:
    """获取Tushare Token"""
    token = TUSHARE_TOKEN or os.getenv('TUSHARE_PRO_TOKEN', '')
    if not token:
        logger.warning("Tushare Token未配置，可能影响数据获取")
    return token


def call_tushare_live_news_api(src: str, start_date: str, end_date: str, token: str) -> pd.DataFrame:
    """调用Tushare Pro新闻API获取实时新闻"""
    try:
        # 构建API请求
        params = {
            'api_name': 'news',
            'token': token,
            'params': {
                'src': src,
                'start_date': start_date,
                'end_date': end_date
            },
            'fields': 'datetime,content,title,channels'
        }
        
        response = requests.post(TUSHARE_API_URL, json=params, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('code') != 0:
            error_msg = result.get('msg', 'Unknown error')
            logger.error(f"Tushare API错误 [{src}]: {error_msg}")
            return pd.DataFrame()
        
        # 转换为DataFrame
        if result.get('data') and result['data'].get('items'):
            df = pd.DataFrame(result['data']['items'], columns=result['data']['fields'])
            # 添加新闻源标识
            df['source'] = src
            df['source_name'] = SUPPORTED_NEWS_SOURCES.get(src, src)
            return df
        else:
            logger.info(f"新闻源 {src} 无数据")
            return pd.DataFrame()
            
    except Exception as e:
        logger.error(f"调用Tushare API失败 [{src}]: {e}")
        return pd.DataFrame()


def get_single_source_news(source: str, hours_back: int, max_rows: int, token: str) -> pd.DataFrame:
    """获取单个新闻源的最新新闻"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=hours_back)
        
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"获取 {source} 新闻，时间范围: {start_date_str} 到 {end_date_str}")
        
        df = call_tushare_live_news_api(source, start_date_str, end_date_str, token)
        
        if not df.empty:
            # 按时间排序，获取最新的新闻
            df = df.sort_values('datetime', ascending=False)
            if len(df) > max_rows:
                df = df.head(max_rows)
                
        return df
        
    except Exception as e:
        logger.error(f"获取新闻源 {source} 失败: {e}")
        return pd.DataFrame()


@tool
@log_io
@news_cached(ttl=300)  # 5分钟缓存，保证实时性
def tushare_live_news_tool(
    sources: Annotated[str, "新闻源列表，用逗号分隔，如'sina,eastmoney'。留空获取所有源"] = "",
    max_rows_per_source: Annotated[int, "每个新闻源返回的最大行数，默认10"] = 10,
    hours_back: Annotated[int, "获取最近几小时的新闻，默认6小时"] = 6,
    max_total_rows: Annotated[int, "总共返回的最大新闻数，默认50"] = 50,
) -> str:
    """【Tushare Pro工具】实时新闻获取工具。

    获取指定新闻源的最新新闻，无需关键词过滤。支持多个新闻源同时获取。
    
    功能：获取Tushare Pro各新闻源的最新新闻数据，按时间排序。
    数据来源：Tushare Pro，需要有效的Token。

    Args:
        sources: 新闻源列表，支持sina,eastmoney,wallstreetcn,10jqka,fenghuang,jinrongjie,yuncaijing
        max_rows_per_source: 每个新闻源返回的最大行数
        hours_back: 获取最近几小时的新闻
        max_total_rows: 总共返回的最大新闻数

    Returns:
        JSON格式的实时新闻数据，包含多个新闻源的最新新闻
    """
    
    try:
        # 检查Token
        token = get_tushare_token()
        if not token:
            return json.dumps({
                "error": "Tushare Token未配置",
                "message": "请配置TUSHARE_TOKEN环境变量",
                "data": []
            }, ensure_ascii=False)

        # 确定要获取的新闻源
        if sources.strip():
            source_list = [s.strip() for s in sources.split(',') if s.strip()]
            # 验证新闻源是否支持
            source_list = [s for s in source_list if s in SUPPORTED_NEWS_SOURCES]
        else:
            source_list = list(SUPPORTED_NEWS_SOURCES.keys())
        
        if not source_list:
            return json.dumps({
                "error": "无效的新闻源",
                "message": f"支持的新闻源: {', '.join(SUPPORTED_NEWS_SOURCES.keys())}",
                "data": []
            }, ensure_ascii=False)

        logger.info(f"获取新闻源: {source_list}")

        # 使用线程池并发获取多个新闻源
        all_news_data = []
        
        with ThreadPoolExecutor(max_workers=min(len(source_list), 5)) as executor:
            # 提交所有任务
            future_to_source = {
                executor.submit(get_single_source_news, source, hours_back, max_rows_per_source, token): source
                for source in source_list
            }
            
            # 收集结果
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    df = future.result()
                    
                    if not df.empty:
                        # 转换为标准格式
                        for _, row in df.iterrows():
                            news_item = {
                                "新闻标题": str(row.get('title', '')),
                                "新闻内容": str(row.get('content', ''))[:300] + '...' if len(str(row.get('content', ''))) > 300 else str(row.get('content', '')),
                                "发布时间": str(row.get('datetime', '')),
                                "文章来源": f"Tushare Pro - {row.get('source_name', source)}",
                                "新闻源": source,
                                "新闻源名称": row.get('source_name', SUPPORTED_NEWS_SOURCES.get(source, source)),
                                "新闻链接": f"https://tushare.pro/news/{source}",
                                "分类": str(row.get('channels', ''))
                            }
                            all_news_data.append(news_item)
                            
                except Exception as e:
                    logger.error(f"处理新闻源 {source} 结果失败: {e}")

        # 按发布时间排序
        try:
            all_news_data.sort(key=lambda x: x['发布时间'], reverse=True)
        except:
            logger.warning("新闻时间排序失败，使用原始顺序")

        # 限制总数
        if len(all_news_data) > max_total_rows:
            all_news_data = all_news_data[:max_total_rows]

        # 按新闻源分组统计
        source_stats = {}
        for item in all_news_data:
            source = item['新闻源']
            if source not in source_stats:
                source_stats[source] = {
                    'name': item['新闻源名称'],
                    'count': 0
                }
            source_stats[source]['count'] += 1

        result = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "sources_requested": source_list,
            "sources_stats": source_stats,
            "total_news": len(all_news_data),
            "time_range_hours": hours_back,
            "data": all_news_data
        }

        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        logger.error(f"获取实时新闻错误: {e}")
        return json.dumps({
            "error": "获取实时新闻失败",
            "message": str(e),
            "data": []
        }, ensure_ascii=False)


@tool
@log_io  
def tushare_news_summary_tool(hours_back: Annotated[int, "统计最近几小时的新闻，默认24小时"] = 24) -> str:
    """获取新闻源统计摘要信息。
    
    Args:
        hours_back: 统计最近几小时的新闻
        
    Returns:
        JSON格式的新闻源统计信息
    """
    try:
        token = get_tushare_token()
        if not token:
            return json.dumps({
                "error": "Tushare Token未配置",
                "sources": []
            }, ensure_ascii=False)
            
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=hours_back)
        
        source_summary = []
        
        for source_id, source_name in SUPPORTED_NEWS_SOURCES.items():
            try:
                df = call_tushare_live_news_api(
                    source_id, 
                    start_date.strftime('%Y-%m-%d %H:%M:%S'),
                    end_date.strftime('%Y-%m-%d %H:%M:%S'),
                    token
                )
                
                source_info = {
                    "id": source_id,
                    "name": source_name,
                    "news_count": len(df),
                    "status": "active" if not df.empty else "inactive",
                    "latest_news_time": df['datetime'].max() if not df.empty else None
                }
                
            except Exception as e:
                source_info = {
                    "id": source_id,
                    "name": source_name,
                    "news_count": 0,
                    "status": "error",
                    "error": str(e)
                }
                
            source_summary.append(source_info)
        
        return json.dumps({
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "time_range_hours": hours_back,
            "sources": source_summary,
            "total_sources": len(SUPPORTED_NEWS_SOURCES)
        }, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"获取新闻源摘要错误: {e}")
        return json.dumps({
            "error": "获取新闻源摘要失败",
            "message": str(e),
            "sources": []
        }, ensure_ascii=False) 