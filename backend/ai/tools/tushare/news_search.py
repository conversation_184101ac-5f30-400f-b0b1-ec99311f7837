# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import pandas as pd
import requests
import os
from typing import Annotated, Optional, Dict, Any
from datetime import datetime, timedelta

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import news_cached
from ..cache_keys import news_key
from backend.utils.url_utils import (
    parse_tushare_news_url, 
    extract_keyword_from_tushare_url,
    is_tushare_news_url
)

logger = logging.getLogger(__name__)

# Tushare Pro配置
TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', '')
TUSHARE_API_URL = 'http://api.tushare.pro'


def get_tushare_token() -> str:
    """获取Tushare Token"""
    token = TUSHARE_TOKEN or os.getenv('TUSHARE_PRO_TOKEN', '')
    if not token:
        logger.warning("Tushare Token未配置，可能影响数据获取")
    return token


def call_tushare_news_api(src: str, start_date: str, end_date: str, token: str) -> pd.DataFrame:
    """调用Tushare Pro新闻API"""
    try:
        # 构建API请求
        params = {
            'api_name': 'news',
            'token': token,
            'params': {
                'src': src,
                'start_date': start_date,
                'end_date': end_date
            },
            'fields': 'datetime,content,title,channels'
        }
        
        response = requests.post(TUSHARE_API_URL, json=params, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('code') != 0:
            error_msg = result.get('msg', 'Unknown error')
            logger.error(f"Tushare API错误: {error_msg}")
            return pd.DataFrame()
        
        # 转换为DataFrame
        if result.get('data') and result['data'].get('items'):
            df = pd.DataFrame(result['data']['items'], columns=result['data']['fields'])
            return df
        else:
            return pd.DataFrame()
            
    except Exception as e:
        logger.error(f"调用Tushare API失败: {e}")
        return pd.DataFrame()


def filter_news_by_keyword(df: pd.DataFrame, keyword: str) -> pd.DataFrame:
    """根据关键词过滤新闻"""
    if df.empty or not keyword:
        return df
    
    try:
        # 在标题和内容中搜索关键词
        keyword_lower = keyword.lower()
        mask = (
            df['title'].str.lower().str.contains(keyword_lower, na=False) |
            df['content'].str.lower().str.contains(keyword_lower, na=False)
        )
        
        return df[mask]
    except Exception as e:
        logger.error(f"过滤新闻时出错: {e}")
        return df


@tool
@log_io
@news_cached(ttl=900)  # 15分钟缓存
def tushare_news_search_tool(
    keyword: Annotated[str, "搜索关键词或Tushare URL格式，如 '苹果' 或 '@https://tushare.pro/news/sina?s=苹果'"],
    source: Annotated[str, "新闻源，支持：sina,wallstreetcn,10jqka,eastmoney,yuncaijing,fenghuang,jinrongjie"] = 'sina',
    max_rows: Annotated[int, "返回的最大行数，默认50"] = 50,
    days_back: Annotated[int, "搜索几天内的新闻，默认3天"] = 3,
) -> str:
    """【Tushare Pro工具】Tushare新闻搜索工具。

    支持通过关键词搜索Tushare Pro的新闻数据，也支持直接使用Tushare URL格式。
    当用户输入如 '@https://tushare.pro/news/sina?s=苹果' 的格式时，自动解析并搜索。

    功能：获取Tushare Pro指定来源的新闻数据，并根据关键词进行过滤。
    数据来源：Tushare Pro，需要有效的Token。

    Args:
        keyword: 搜索关键词或完整的Tushare URL（支持@前缀）
        source: 新闻源标识
        max_rows: 返回的最大行数
        days_back: 搜索最近几天的新闻

    Returns:
        JSON格式的新闻数据，包含时间、标题、内容、分类等字段
    """
    
    try:
        # 检查Token
        token = get_tushare_token()
        if not token:
            return json.dumps({
                "error": "Tushare Token未配置",
                "message": "请配置TUSHARE_TOKEN环境变量",
                "data": []
            }, ensure_ascii=False)

        # 处理输入参数
        search_keyword = keyword
        news_source = source
        
        # 如果是Tushare URL格式，解析参数
        if is_tushare_news_url(keyword):
            logger.info(f"检测到Tushare URL格式: {keyword}")
            
            # 提取关键词
            extracted_keyword = extract_keyword_from_tushare_url(keyword)
            if extracted_keyword:
                search_keyword = extracted_keyword
                logger.info(f"提取的关键词: {search_keyword}")
            
            # 提取新闻源
            clean_url = keyword.strip()
            if clean_url.startswith('@'):
                clean_url = clean_url[1:]
            if not clean_url.startswith('http'):
                clean_url = 'https://' + clean_url
                
            parsed_data = parse_tushare_news_url(clean_url)
            if parsed_data.get('source'):
                news_source = parsed_data['source']
                logger.info(f"提取的新闻源: {news_source}")

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"搜索参数 - 关键词: {search_keyword}, 来源: {news_source}, 时间范围: {start_date_str} 到 {end_date_str}")

        # 调用Tushare API获取新闻
        df = call_tushare_news_api(news_source, start_date_str, end_date_str, token)
        
        if df.empty:
            return json.dumps({
                "keyword": search_keyword,
                "source": news_source,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "message": f"未找到{news_source}来源的新闻数据",
                "total_rows": 0,
                "data": []
            }, ensure_ascii=False)

        # 根据关键词过滤新闻
        if search_keyword:
            filtered_df = filter_news_by_keyword(df, search_keyword)
        else:
            filtered_df = df

        # 限制返回行数
        if len(filtered_df) > max_rows:
            filtered_df = filtered_df.head(max_rows)

        # 转换为标准格式
        news_data = []
        for _, row in filtered_df.iterrows():
            news_item = {
                "关键词": search_keyword,
                "新闻标题": str(row.get('title', '')),
                "新闻内容": str(row.get('content', ''))[:500] + '...' if len(str(row.get('content', ''))) > 500 else str(row.get('content', '')),
                "发布时间": str(row.get('datetime', '')),
                "文章来源": f"Tushare Pro - {news_source}",
                "新闻链接": f"https://tushare.pro/news/{news_source}",
                "分类": str(row.get('channels', ''))
            }
            news_data.append(news_item)

        result = {
            "keyword": search_keyword,
            "source": f"Tushare Pro - {news_source}",
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_rows": len(df),
            "filtered_rows": len(filtered_df),
            "displayed_rows": len(news_data),
            "data": news_data
        }

        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        logger.error(f"Tushare新闻搜索错误: {e}")
        return json.dumps({
            "error": "Tushare新闻搜索失败",
            "message": str(e),
            "keyword": keyword,
            "data": []
        }, ensure_ascii=False)


@tool
@log_io
def tushare_news_sources_tool() -> str:
    """获取Tushare Pro支持的新闻源列表。
    
    Returns:
        JSON格式的新闻源信息
    """
    sources = {
        "supported_sources": [
            {"id": "sina", "name": "新浪财经", "description": "新浪财经实时资讯"},
            {"id": "wallstreetcn", "name": "华尔街见闻", "description": "华尔街见闻快讯"},
            {"id": "10jqka", "name": "同花顺", "description": "同花顺财经新闻"},
            {"id": "eastmoney", "name": "东方财富", "description": "东方财富财经新闻"},
            {"id": "yuncaijing", "name": "云财经", "description": "云财经新闻"},
            {"id": "fenghuang", "name": "凤凰新闻", "description": "凤凰新闻"},
            {"id": "jinrongjie", "name": "金融界", "description": "金融界新闻"}
        ],
        "usage_example": "@https://tushare.pro/news/sina?s=苹果",
        "note": "需要配置有效的Tushare Pro Token"
    }
    
    return json.dumps(sources, ensure_ascii=False) 