# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Data coordination tools for agent-to-agent data sharing
Allows agents to store analysis results internally for final report generation
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from langchain_core.tools import Tool

logger = logging.getLogger(__name__)


def create_data_storage_tool():
    """Create tool for agents to store their analysis data"""
    
    def store_agent_data(agent_name: str, data_type: str, data: str, summary: str = "") -> str:
        """
        Store analysis results for coordination with other agents
        
        Args:
            agent_name: Name of the agent storing the data
            data_type: Type of data (technical_analysis, news_analysis, fundamental_analysis, research, coding)
            data: The actual analysis data/content to store
            summary: Optional summary of the analysis
            
        Returns:
            Confirmation message
        """
        try:
            # This function will be called by agents during execution
            # The actual storage happens in the agent execution context
            logger.info(f"Agent {agent_name} storing {data_type} data")
            return f"Successfully stored {data_type} data from {agent_name}. Summary: {summary[:100]}..."
        except Exception as e:
            logger.error(f"Error storing data from {agent_name}: {e}")
            return f"Error storing data: {str(e)}"
    
    return Tool(
        name="store_agent_data",
        description="Store analysis results for coordination with other agents. Use this to save your analysis so the final coordinator can access it.",
        func=store_agent_data
    )


def store_agent_data_in_state(state: Any, agent_name: str, data_type: str, data: Dict[str, Any]) -> None:
    """
    Store agent data in the workflow state
    
    Args:
        state: The workflow state object
        agent_name: Name of the agent
        data_type: Type of analysis data
        data: Dictionary containing the analysis results
    """
    try:
        # Prepare data package
        data_package = {
            "agent_name": agent_name,
            "data_type": data_type,
            "timestamp": datetime.now().isoformat(),
            "content": data
        }
        
        # Store in appropriate state field based on data type
        if data_type == "technical_analysis":
            state["technical_analysis_data"] = data_package
        elif data_type == "news_analysis":
            state["news_analysis_data"] = data_package
        elif data_type == "fundamental_analysis":
            state["fundamental_analysis_data"] = data_package
        elif data_type == "research":
            state["research_data"] = data_package
        elif data_type == "coding":
            state["coding_analysis_data"] = data_package
        
        # Track agent completion
        if agent_name not in state.get("agents_completed", []):
            agents_completed = state.get("agents_completed", [])
            agents_completed.append(agent_name)
            state["agents_completed"] = agents_completed
        
        # Update workflow stage
        state["workflow_stage"] = "data_collection"
        state["current_active_agent"] = agent_name
        
        logger.info(f"Stored {data_type} data from {agent_name} in state")
        
    except Exception as e:
        logger.error(f"Error storing {data_type} data from {agent_name}: {e}")


def retrieve_agent_data(state: Any, agent_name: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve stored data from a specific agent
    
    Args:
        state: The workflow state object
        agent_name: Name of the agent whose data to retrieve
        
    Returns:
        Dictionary containing the agent's data or None if not found
    """
    try:
        # Check all data fields for the agent
        data_fields = [
            "technical_analysis_data",
            "news_analysis_data", 
            "fundamental_analysis_data",
            "research_data",
            "coding_analysis_data"
        ]
        
        for field in data_fields:
            data = state.get(field)
            if data and data.get("agent_name") == agent_name:
                return data
        
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving data for {agent_name}: {e}")
        return None


def get_all_agent_data(state: Any) -> Dict[str, Any]:
    """
    Get all stored agent data for final report generation
    
    Args:
        state: The workflow state object
        
    Returns:
        Dictionary containing all agent data organized by type
    """
    try:
        all_data = {
            "technical_analysis": state.get("technical_analysis_data"),
            "news_analysis": state.get("news_analysis_data"),
            "fundamental_analysis": state.get("fundamental_analysis_data"),
            "research": state.get("research_data"),
            "coding_analysis": state.get("coding_analysis_data"),
            "agents_completed": state.get("agents_completed", []),
            "workflow_stage": state.get("workflow_stage", "unknown")
        }
        
        # Filter out None values
        filtered_data = {k: v for k, v in all_data.items() if v is not None}
        
        logger.info(f"Retrieved data from {len(filtered_data)} agent data sources")
        return filtered_data
        
    except Exception as e:
        logger.error(f"Error retrieving all agent data: {e}")
        return {}


def check_data_collection_complete(state: Any, required_agents: Optional[list] = None) -> bool:
    """
    Check if all required agents have completed their data collection
    
    Args:
        state: The workflow state object
        required_agents: List of required agent names (optional)
        
    Returns:
        True if data collection is complete, False otherwise
    """
    try:
        completed_agents = state.get("agents_completed", [])
        
        # If specific required agents are provided, use them
        if required_agents is not None:
            all_complete = all(agent in completed_agents for agent in required_agents)
        else:
            # More flexible approach: check if any specialist agents have completed
            specialist_agents = [
                "technical_analysis_specialist", 
                "news_analysis_specialist", 
                "fundamental_analysis_specialist"
            ]
            
            # Check if at least one specialist agent has completed
            has_specialist_data = any(agent in completed_agents for agent in specialist_agents)
            
            # Also check if we have any stored agent data
            has_stored_data = any([
                state.get("technical_analysis_data"),
                state.get("news_analysis_data"),
                state.get("fundamental_analysis_data"),
                state.get("research_data")
            ])
            
            # Check if traditional research steps are completed (fallback)
            current_plan = state.get("current_plan")
            has_research_results = False
            if current_plan and current_plan.steps:
                completed_research_steps = sum(1 for step in current_plan.steps if step.execution_res)
                total_steps = len(current_plan.steps)
                has_research_results = completed_research_steps >= max(1, total_steps // 2)  # At least half completed
            
            # Consider complete if any of these conditions are met:
            # 1. At least one specialist agent completed
            # 2. We have stored data from agents
            # 3. Substantial research work is done
            all_complete = has_specialist_data or has_stored_data or has_research_results
            
            logger.info(f"Data collection assessment: specialist_data={has_specialist_data}, stored_data={has_stored_data}, research_results={has_research_results}, final={all_complete}")
        
        if all_complete and not state.get("data_collection_complete", False):
            state["data_collection_complete"] = True
            state["workflow_stage"] = "report_generation"  # Updated stage name
            logger.info("Data collection phase completed - transitioning to report generation")
        
        return all_complete
        
    except Exception as e:
        logger.error(f"Error checking data collection status: {e}")
        return False


def format_agent_data_for_report(all_data: Dict[str, Any]) -> str:
    """
    Format all agent data into a structured string for report generation
    
    Args:
        all_data: Dictionary containing all agent data
        
    Returns:
        Formatted string containing all analysis data
    """
    try:
        formatted_content = "# 综合分析数据汇总\n\n"
        
        # Technical Analysis
        if all_data.get("technical_analysis"):
            tech_data = all_data["technical_analysis"]
            formatted_content += "## 技术分析\n"
            formatted_content += f"**分析时间**: {tech_data.get('timestamp', 'N/A')}\n"
            formatted_content += f"**内容**: {tech_data.get('content', {}).get('analysis', 'N/A')}\n\n"
        
        # News Analysis  
        if all_data.get("news_analysis"):
            news_data = all_data["news_analysis"]
            formatted_content += "## 新闻分析\n"
            formatted_content += f"**分析时间**: {news_data.get('timestamp', 'N/A')}\n"
            formatted_content += f"**内容**: {news_data.get('content', {}).get('analysis', 'N/A')}\n\n"
        
        # Fundamental Analysis
        if all_data.get("fundamental_analysis"):
            fund_data = all_data["fundamental_analysis"]
            formatted_content += "## 基本面分析\n"
            formatted_content += f"**分析时间**: {fund_data.get('timestamp', 'N/A')}\n"
            formatted_content += f"**内容**: {fund_data.get('content', {}).get('analysis', 'N/A')}\n\n"
        
        # Research Data
        if all_data.get("research"):
            research_data = all_data["research"]
            formatted_content += "## 研究发现\n"
            formatted_content += f"**研究时间**: {research_data.get('timestamp', 'N/A')}\n"
            formatted_content += f"**内容**: {research_data.get('content', {}).get('analysis', 'N/A')}\n\n"
        
        # Coding Analysis
        if all_data.get("coding_analysis"):
            coding_data = all_data["coding_analysis"]
            formatted_content += "## 数据分析\n"
            formatted_content += f"**分析时间**: {coding_data.get('timestamp', 'N/A')}\n"
            formatted_content += f"**内容**: {coding_data.get('content', {}).get('analysis', 'N/A')}\n\n"
        
        formatted_content += f"**完成的智能体**: {', '.join(all_data.get('agents_completed', []))}\n"
        formatted_content += f"**工作流阶段**: {all_data.get('workflow_stage', 'unknown')}\n"
        
        return formatted_content
        
    except Exception as e:
        logger.error(f"Error formatting agent data: {e}")
        return "Error formatting analysis data for report generation." 