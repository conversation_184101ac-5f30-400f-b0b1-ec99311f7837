# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from .types import State
from .nodes import (
    coordinator_node,
    planner_node,
    reporter_node,
    research_team_node,
    researcher_node,
    coder_node,
    technical_analyst_node,
    technical_analysis_specialist_node,
    news_analysis_specialist_node,
    fundamental_analysis_specialist_node,
    background_investigation_node,
    financial_report_coordinator_node,
    bullish_reporter_node,
    bearish_reporter_node,
    trading_advice_reporter_node,
    final_comprehensive_reporter_node,
)
from .chart_node import chart_generation_node

"""
金融领域多Agent智能分析系统图结构说明:

1. 基础节点:
   - coordinator: 协调器节点,负责整体流程控制
   - background_investigator: 背景调查节点,收集相关信息
   - planner: 规划节点,制定执行计划
   - research_team: 研究团队节点,协调研究工作
   - researcher: 研究员节点,执行具体研究
   - coder: 编码节点,负责代码实现和数据处理
   - technical_analyst: 技术分析节点,进行专业技术评估

2. 金融报告节点系统:
   - financial_report_coordinator: 金融报告协调器,管理报告生成流程
   - bullish_reporter: 看多报告节点,生成多头投资观点
   - bearish_reporter: 看空报告节点,生成空头投资观点
   - trading_advice_reporter: 交易建议节点,生成具体交易策略
   - final_comprehensive_reporter: 综合报告节点,整合所有分析生成最终报告

3. 执行流程:
   - 从 START 开始,首先进入 coordinator 节点
   - 经过背景调查和规划后,进入研究团队执行具体分析
   - 完成技术研究后,进入金融报告协调器
   - 依次生成看多、看空、交易建议三个专业报告
   - 最终整合所有报告生成综合投资分析报告
   - 通过 final_comprehensive_reporter 节点到达 END

4. 特色功能:
   - 多维度金融分析：技术面、基本面、情绪面综合评估
   - 平衡投资观点：同时提供看多和看空的客观分析
   - 实用交易指导：具体的入场、出场和风险管理建议
   - 专业报告整合：生成包含所有分析维度的综合投资报告
   - 自动化流程：无需人工干预的完全自动化分析流程

5. 内存管理:
   - 支持持久化内存存储对话历史和报告数据
   - 计划支持 SQLite/PostgreSQL 存储
   - 分别存储各类型报告以便后续分析和对比
"""

def _build_base_graph():
    """Build and return the base state graph with all nodes and edges."""
    builder = StateGraph(State)
    builder.add_edge(START, "coordinator")
    
    # 基础节点
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("chart_generator", chart_generation_node)
    builder.add_node("background_investigator", background_investigation_node)
    builder.add_node("planner", planner_node)
    builder.add_node("reporter", reporter_node)
    builder.add_node("research_team", research_team_node)
    builder.add_node("researcher", researcher_node)
    builder.add_node("coder", coder_node)
    builder.add_node("technical_analyst", technical_analyst_node)
    
    # 专业化分析节点 (Phase 7 优化)
    builder.add_node("technical_analysis_specialist", technical_analysis_specialist_node)
    builder.add_node("news_analysis_specialist", news_analysis_specialist_node)
    builder.add_node("fundamental_analysis_specialist", fundamental_analysis_specialist_node)
    
    # 金融报告节点
    builder.add_node("financial_report_coordinator", financial_report_coordinator_node)
    builder.add_node("bullish_reporter", bullish_reporter_node)
    builder.add_node("bearish_reporter", bearish_reporter_node)
    builder.add_node("trading_advice_reporter", trading_advice_reporter_node)
    builder.add_node("final_comprehensive_reporter", final_comprehensive_reporter_node)
    
    # 结束边：最终综合报告节点连接到END
    builder.add_edge("final_comprehensive_reporter", END)
    
    return builder


def build_graph_with_memory():
    """Build and return the agent workflow graph with memory."""
    # use persistent memory to save conversation history
    # TODO: be compatible with SQLite / PostgreSQL
    memory = MemorySaver()

    # build state graph
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)


def build_graph():
    """Build and return the agent workflow graph without memory."""
    # build state graph
    builder = _build_base_graph()
    return builder.compile()


graph = build_graph() 