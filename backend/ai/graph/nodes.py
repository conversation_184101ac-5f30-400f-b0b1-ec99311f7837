# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import Annotated, Literal
from datetime import datetime

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command, interrupt

from ..agents import create_agent
from ..tools.search import LoggedTavilySearch
from ..tools import (
    crawl_tool,
    get_web_search_tool,
    python_repl_tool,
)

from ..config.agents import AGENT_LLM_MAP
from ..config.configuration import Configuration
from ..llms.llm import get_llm_by_type
from ..prompts.planner_model import Plan, StepType
from ..prompts.template import apply_prompt_template
from ..utils.json_utils import repair_json_output

from .types import State
from ..config import SELECTED_SEARCH_ENGINE, SearchEngine

logger = logging.getLogger(__name__)


@tool
def handoff_to_planner(
    task_title: Annotated[str, "The title of the task to be handed off."],
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."],
):
    """Handoff to planner agent to do plan."""
    return


@tool
def show_capabilities(
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."] = "zh-CN",
):
    """Show DeerFlow's capabilities and what it can help with."""
    if locale.startswith("zh"):
        return """
# 🦌 DeerFlow AI 智能分析助手

我是 DeerFlow，您的专业 AI 金融分析助手。我可以为您提供以下服务：

## 🎯 核心能力

### 📊 金融市场分析
- **股票分析**：深度分析个股基本面、技术面、财务状况
- **市场研究**：行业分析、板块轮动、市场趋势研判
- **技术指标**：MACD、RSI、布林带等技术指标计算与分析
- **量化因子**：多因子模型构建、因子有效性验证

### 📈 投资决策支持
- **多维度报告**：看多观点、看空观点、交易建议三重视角
- **风险评估**：投资风险识别与量化分析
- **投资组合**：资产配置建议与优化方案
- **市场情绪**：基于新闻、社交媒体的情绪分析

### 🔍 实时数据获取
- **财务数据**：实时股价、财报数据、估值指标
- **新闻资讯**：相关新闻搜索与分析
- **行业动态**：行业政策、竞争格局分析
- **宏观经济**：经济指标对市场影响分析

## 💡 使用示例

您可以这样向我提问：

- "分析苹果公司(AAPL)的投资价值"
- "比较特斯拉和比亚迪的竞争优势"
- "分析新能源汽车行业的投资机会"
- "计算贵州茅台的技术指标"
- "帮我分析当前A股市场趋势"

## 🚀 工作流程

1. **需求理解**：我会仔细分析您的问题
2. **数据收集**：自动搜索相关信息和数据
3. **深度分析**：运用专业模型进行分析
4. **报告生成**：提供结构化的分析报告

## 📝 注意事项

- 所有分析仅供参考，不构成投资建议
- 投资有风险，决策需谨慎
- 建议结合多方信息做出投资决定

有什么想了解的，尽管问我吧！ 🎉
"""
    else:
        return """
# 🦌 DeerFlow AI Financial Assistant

I'm DeerFlow, your professional AI financial analysis assistant. Here's what I can help you with:

## 🎯 Core Capabilities

### 📊 Financial Market Analysis
- **Stock Analysis**: In-depth analysis of fundamentals, technicals, and financials
- **Market Research**: Industry analysis, sector rotation, market trend assessment
- **Technical Indicators**: MACD, RSI, Bollinger Bands calculation and analysis
- **Quantitative Factors**: Multi-factor model construction and validation

### 📈 Investment Decision Support
- **Multi-perspective Reports**: Bullish, bearish, and trading advice perspectives
- **Risk Assessment**: Investment risk identification and quantification
- **Portfolio Management**: Asset allocation recommendations and optimization
- **Market Sentiment**: News and social media sentiment analysis

### 🔍 Real-time Data Access
- **Financial Data**: Real-time prices, earnings data, valuation metrics
- **News & Information**: Relevant news search and analysis
- **Industry Dynamics**: Policy analysis and competitive landscape
- **Macroeconomic**: Economic indicators' market impact analysis

## 💡 Usage Examples

You can ask me questions like:

- "Analyze Apple Inc. (AAPL) investment value"
- "Compare Tesla vs BYD competitive advantages"
- "Analyze investment opportunities in EV industry"
- "Calculate technical indicators for Kweichow Moutai"
- "Help me analyze current A-share market trends"

## 🚀 Workflow

1. **Understanding**: I carefully analyze your questions
2. **Data Collection**: Automatically search relevant information
3. **Deep Analysis**: Apply professional models for analysis
4. **Report Generation**: Provide structured analysis reports

## 📝 Disclaimer

- All analysis is for reference only, not investment advice
- Investment involves risks, please make decisions carefully
- Recommend combining multiple sources for investment decisions

Feel free to ask me anything! 🎉
"""


def background_investigation_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner"]]:
    logger.info("background investigation node is running.")
    configurable = Configuration.from_runnable_config(config)
    query = state["messages"][-1].content
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY:
        searched_content = LoggedTavilySearch(
            max_results=configurable.max_search_results
        ).invoke({"query": query})
        background_investigation_results = None
        if isinstance(searched_content, list):
            background_investigation_results = [
                {"title": elem["title"], "content": elem["content"]}
                for elem in searched_content
            ]
        else:
            logger.error(
                f"Tavily search returned malformed response: {searched_content}"
            )
    else:
        background_investigation_results = get_web_search_tool(
            configurable.max_search_results
        ).invoke(query)
    return Command(
        update={
            "background_investigation_results": json.dumps(
                background_investigation_results, ensure_ascii=False
            )
        },
        goto="planner",
    )


def planner_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team", "financial_report_coordinator"]]:
    """Planner node that generates a plan for the research task."""
    logger.info("Planner generating plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state.get("plan_iterations", 0)
    messages = apply_prompt_template("planner", state)

    # 添加明确的 JSON 格式要求，强调必需字段
    json_instruction = HumanMessage(
        content="""Please strictly adhere to the following JSON format for your response, without including any other text:

{
  "locale": "zh-CN",
  "has_enough_context": false,
  "thought": "Analysis thought",
  "title": "Research Plan Title",
  "steps": [
    {
      "need_web_search": true,
      "title": "Step Title",
      "description": "Step Description",
      "step_type": "research"
    }
  ]
}

CRITICAL: Each step MUST include ALL four fields:
- need_web_search: boolean (true/false)
- title: string
- description: string  
- step_type: string (must be one of: "research", "processing", "technical_analysis", "news_analysis", "fundamental_analysis")

Ensure the response is in a valid JSON format and do not add any explanatory text.""",
        name="system"
    )
    messages.append(json_instruction)

    # 使用基础 LLM 而不是结构化输出
    llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the financial report coordinator
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="financial_report_coordinator")

    logger.info("Planner generating full plan")
    response = llm.invoke(messages)
    full_response = response.content

    # Validate response content
    if not full_response or (isinstance(full_response, str) and full_response.strip() == ""):
        logger.error("Planner returned empty response")
        if plan_iterations > 0:
            return Command(goto="financial_report_coordinator")
        else:
            return Command(goto="__end__")

    logger.debug(f"Current state messages: {state['messages']}")
    logger.info(f"Planner response: {full_response}")

    try:
        # 尝试修复和解析 JSON
        repaired_json = repair_json_output(full_response)
        curr_plan = json.loads(repaired_json)
    except json.JSONDecodeError as e:
        logger.warning(f"Planner response is not a valid JSON: {e}")
        logger.warning(f"Raw response: {full_response}")
        
        # 如果 JSON 解析失败，尝试提取 JSON 部分
        import re
        json_match = re.search(r'\{.*\}', full_response, re.DOTALL)
        if json_match:
            try:
                repaired_json = repair_json_output(json_match.group())
                curr_plan = json.loads(repaired_json)
                logger.info("Successfully extracted JSON from response")
            except json.JSONDecodeError:
                logger.error("Failed to extract valid JSON from response")
                if plan_iterations > 0:
                    return Command(goto="financial_report_coordinator")
                else:
                    return Command(goto="__end__")
        else:
            logger.error("No JSON found in response")
            if plan_iterations > 0:
                return Command(goto="financial_report_coordinator")
            else:
                return Command(goto="__end__")
    
    # 验证和修复步骤中缺失的 step_type 字段
    if "steps" in curr_plan and isinstance(curr_plan["steps"], list):
        for i, step in enumerate(curr_plan["steps"]):
            if isinstance(step, dict) and "step_type" not in step:
                # 根据步骤内容推断 step_type
                title = step.get("title", "").lower()
                description = step.get("description", "").lower()
                need_web_search = step.get("need_web_search", True)
                
                # 智能推断 step_type
                if any(keyword in title + description for keyword in ["技术分析", "technical", "macd", "rsi", "指标", "chart", "price"]):
                    step_type = "technical_analysis"
                elif any(keyword in title + description for keyword in ["新闻", "news", "sentiment", "媒体", "舆情"]):
                    step_type = "news_analysis"
                elif any(keyword in title + description for keyword in ["财务", "fundamental", "财报", "估值", "financial", "balance"]):
                    step_type = "fundamental_analysis"
                elif any(keyword in title + description for keyword in ["计算", "processing", "数据处理", "分析", "代码"]):
                    step_type = "processing"
                else:
                    step_type = "research"  # 默认为研究类型
                
                step["step_type"] = step_type
                logger.info(f"Added missing step_type '{step_type}' to step {i}: {step.get('title', 'Unknown')}")
    
    # 自动接受计划，无需人工反馈
    logger.info("Plan generated and automatically accepted")
    
    try:
        new_plan = Plan.model_validate(curr_plan)
    except Exception as validation_error:
        logger.error(f"Plan validation failed: {validation_error}")
        logger.error(f"Plan content: {curr_plan}")
        if plan_iterations > 0:
            return Command(goto="financial_report_coordinator")
        else:
            return Command(goto="__end__")
    
    plan_iterations += 1
    
    # 决定下一个节点
    if new_plan.has_enough_context:
        logger.info("Plan has enough context, going to financial report coordinator")
        goto = "financial_report_coordinator"
    else:
        logger.info("Plan needs research, going to research team")
        goto = "research_team"
    
    return Command(
        update={
            "messages": [AIMessage(content=repaired_json, name="planner")],
            "current_plan": new_plan,
            "plan_iterations": plan_iterations,
            "locale": curr_plan.get("locale", state.get("locale", "zh-CN")),
            "auto_accepted_plan": True,  # 标记计划已自动接受
        },
        goto=goto,
    )


def coordinator_node(
    state: State,
) -> Command[Literal["chart_generator", "planner", "background_investigator", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    
    try:
        # 添加详细的状态日志
        messages = state.get("messages", [])
        logger.debug(f"Coordinator received {len(messages)} messages")
        
        # 应用提示模板并添加超时保护
        logger.debug("Applying coordinator prompt template...")
        messages = apply_prompt_template("coordinator", state)
        logger.debug(f"Template applied, message count: {len(messages)}")
        
        # 获取LLM并绑定工具
        logger.debug("Getting LLM and binding tools...")
        llm = get_llm_by_type(AGENT_LLM_MAP["coordinator"])
        bound_llm = llm.bind_tools([handoff_to_planner, show_capabilities])
        logger.debug("LLM tools bound successfully")
        
        # 调用LLM并添加超时保护
        logger.debug("Invoking LLM...")
        
        # 添加消息内容验证
        for i, msg in enumerate(messages):
            if hasattr(msg, 'content') and msg.content:
                logger.debug(f"Message {i} content preview: {str(msg.content)[:100]}...")
        
        response = bound_llm.invoke(messages)
        logger.debug(f"LLM response received: {type(response)}")
        logger.debug(f"Response content preview: {getattr(response, 'content', '')[:200]}...")
        logger.debug(f"Tool calls count: {len(getattr(response, 'tool_calls', []))}")
        
    except Exception as e:
        logger.error(f"Error in coordinator LLM call: {e}", exc_info=True)
        # 提供错误恢复响应
        error_message = f"抱歉，系统遇到了技术问题：{str(e)}。请稍后重试或联系技术支持。"
        logger.info(f"Coordinator error response: {error_message}")
        return Command(
            update={
                "locale": state.get("locale", "zh-CN"),
                "messages": [AIMessage(content=error_message, name="coordinator")],
                "final_report": error_message
            },
            goto="__end__",
        )

    goto = "__end__"
    locale = state.get("locale", "en-US")  # Default locale if not specified

    if len(response.tool_calls) > 0:
        try:
            logger.debug(f"Processing {len(response.tool_calls)} tool calls...")
            for tool_call in response.tool_calls:
                tool_name = tool_call.get("name", "")
                logger.debug(f"Processing tool call: {tool_name}")
                
                if tool_name == "show_capabilities":
                    # 处理能力展示工具调用
                    tool_locale = tool_call.get("args", {}).get("locale", locale)
                    logger.debug(f"Showing capabilities for locale: {tool_locale}")
                    capabilities_info = show_capabilities.invoke({"locale": tool_locale})
                    logger.info(f"Coordinator capabilities response: {capabilities_info[:200]}...")
                    
                    return Command(
                        update={
                            "locale": tool_locale,
                            "messages": [AIMessage(content=capabilities_info, name="coordinator")],
                            "final_report": capabilities_info
                        },
                        goto="__end__",
                    )
                
                elif tool_name == "handoff_to_planner":
                    # 处理转交给规划者的工具调用 - 先检查是否需要生成图表
                    logger.debug("Handing off to planner via chart_generator")
                    goto = "chart_generator"  # 先尝试生成图表，图表节点会决定下一步
                    if tool_locale := tool_call.get("args", {}).get("locale"):
                        locale = tool_locale
                    
                    # 添加用户消息到messages中以便后续节点处理
                    user_message_content = state.get("messages", [])[-1].content if state.get("messages") else ""
                    logger.info(f"Coordinator handoff to planner for: {user_message_content[:100]}...")
                    return Command(
                        update={
                            "locale": locale,
                            "messages": [AIMessage(content=f"正在为您分析：{user_message_content}", name="coordinator")]
                        },
                        goto=goto,
                    )
                    
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}", exc_info=True)
            # 如果工具调用出错，先尝试图表生成
            goto = "chart_generator"
            return Command(
                update={
                    "locale": locale,
                    "messages": [AIMessage(content="正在为您分析中，请稍候...", name="coordinator")]
                },
                goto=goto,
            )
    else:
        # 如果没有工具调用，说明协调器直接回答了用户问题（如问候语）
        logger.info("Coordinator provided direct response without tool calls.")
        logger.debug(f"Coordinator response: {response}")
        
        # 获取协调器的回答
        coordinator_response = getattr(response, 'content', '') or "我是 DeerFlow，您的 AI 智能分析助手。"
        logger.info(f"Coordinator direct response: {coordinator_response[:200]}...")
        
        return Command(
            update={
                "locale": locale,
                "messages": [AIMessage(content=coordinator_response, name="coordinator")],
                "final_report": coordinator_response
            },
            goto=goto,
        )

    logger.debug(f"Coordinator finished, going to: {goto}")
    return Command(
        update={
            "locale": locale,
            "messages": [AIMessage(content="正在为您准备分析...", name="coordinator")]
        },
        goto=goto,
    )


def reporter_node(state: State):
    """Reporter node that write a final report."""
    logger.info("Reporter write final report")
    current_plan = state.get("current_plan")
    
    # Validate current_plan
    if not current_plan:
        logger.error("No current plan found for reporter")
        return {"final_report": "无法生成报告：未找到研究计划。"}
    
    # Validate plan content
    plan_title = getattr(current_plan, 'title', None) or "未知任务"
    plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    input_ = {
        "messages": [
            HumanMessage(
                f"# Research Requirements\n\n## Task\n\n{plan_title}\n\n## Description\n\n{plan_thought}"
            )
        ],
        "locale": state.get("locale", "en-US"),
    }
    invoke_messages = apply_prompt_template("reporter", input_)
    observations = state.get("observations", [])

    # Add a reminder about the new report format, citation style, and table usage
    invoke_messages.append(
        HumanMessage(
            content="IMPORTANT: Structure your report according to the format in the prompt. Remember to include:\n\n1. Key Points - A bulleted list of the most important findings\n2. Overview - A brief introduction to the topic\n3. Detailed Analysis - Organized into logical sections\n4. Survey Note (optional) - For more comprehensive reports\n5. Key Citations - List all references at the end\n\nFor citations, DO NOT include inline citations in the text. Instead, place all citations in the 'Key Citations' section at the end using the format: `- [Source Title](URL)`. Include an empty line between each citation for better readability.\n\nPRIORITIZE USING MARKDOWN TABLES for data presentation and comparison. Use tables whenever presenting comparative data, statistics, features, or options. Structure tables with clear headers and aligned columns. Example table format:\n\n| Feature | Description | Pros | Cons |\n|---------|-------------|------|------|\n| Feature 1 | Description 1 | Pros 1 | Cons 1 |\n| Feature 2 | Description 2 | Pros 2 | Cons 2 |",
            name="system",
        )
    )

    for observation in observations:
        if observation and observation.strip():  # Only add non-empty observations
            invoke_messages.append(
                HumanMessage(
                    content=f"Below are some observations for the research task:\n\n{observation}",
                    name="observation",
                )
            )
    logger.debug(f"Current invoke messages: {invoke_messages}")
    
    try:
        response = get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            response_content = "报告生成失败：未获得有效响应内容。"
            logger.warning("Reporter returned empty response content")
        
        logger.info(f"reporter response: {response_content}")
        return {"final_report": response_content}
    
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return {"final_report": f"报告生成失败：{str(e)}"}


def bullish_reporter_node(state: State) -> Command[Literal["financial_report_coordinator"]]:
    """Bullish reporter node that writes a bullish investment perspective report."""
    logger.info("Bullish reporter generating bullish analysis report")
    
    from ..prompts.template import apply_financial_report_template
    
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])
    
    # Validate current_plan
    if not current_plan:
        logger.error("No current plan found for bullish reporter")
        return Command(
            update={"bullish_report": "无法生成看多报告：未找到研究计划。"},
            goto="financial_report_coordinator"
        )
    
    # Validate plan content
    plan_title = getattr(current_plan, 'title', None) or "未知投资标的"
    plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    # Prepare context for bullish analysis
    additional_context = {
        "analysis_focus": "bullish_perspective",
        "task_title": plan_title,
        "task_description": plan_thought,
        "observations": observations
    }
    
    input_ = {
        "messages": [
            HumanMessage(
                f"请基于以下研究数据生成看多投资观点报告：\n\n"
                f"# 投资标的\n{plan_title}\n\n"
                f"# 分析思路\n{plan_thought}\n\n"
                f"# 研究发现\n"
                + "\n\n".join([f"- {obs}" for obs in observations if obs and obs.strip()])
            )
        ],
        "locale": state.get("locale", "zh-CN"),
    }
    
    try:
        invoke_messages = apply_financial_report_template("bullish_reporter", input_, additional_context)
        response = get_llm_by_type(AGENT_LLM_MAP.get("reporter", "gpt-4")).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            response_content = "看多报告生成失败：未获得有效响应内容。"
            logger.warning("Bullish reporter returned empty response content")
        
        logger.info(f"Bullish reporter response generated successfully")
        return Command(
            update={"bullish_report": response_content},
            goto="financial_report_coordinator"
        )
    
    except Exception as e:
        logger.error(f"Error generating bullish report: {e}")
        return Command(
            update={"bullish_report": f"看多报告生成失败：{str(e)}"},
            goto="financial_report_coordinator"
        )


def bearish_reporter_node(state: State) -> Command[Literal["financial_report_coordinator"]]:
    """Bearish reporter node that writes a bearish investment perspective report."""
    logger.info("Bearish reporter generating bearish analysis report")
    
    from ..prompts.template import apply_financial_report_template
    
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])
    
    # Validate current_plan
    if not current_plan:
        logger.error("No current plan found for bearish reporter")
        return Command(
            update={"bearish_report": "无法生成看空报告：未找到研究计划。"},
            goto="financial_report_coordinator"
        )
    
    # Validate plan content
    plan_title = getattr(current_plan, 'title', None) or "未知投资标的"
    plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    # Prepare context for bearish analysis
    additional_context = {
        "analysis_focus": "bearish_perspective",
        "task_title": plan_title,
        "task_description": plan_thought,
        "observations": observations
    }
    
    input_ = {
        "messages": [
            HumanMessage(
                f"请基于以下研究数据生成看空投资观点报告：\n\n"
                f"# 投资标的\n{plan_title}\n\n"
                f"# 分析思路\n{plan_thought}\n\n"
                f"# 研究发现\n"
                + "\n\n".join([f"- {obs}" for obs in observations if obs and obs.strip()])
            )
        ],
        "locale": state.get("locale", "zh-CN"),
    }
    
    try:
        invoke_messages = apply_financial_report_template("bearish_reporter", input_, additional_context)
        response = get_llm_by_type(AGENT_LLM_MAP.get("reporter", "gpt-4")).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            response_content = "看空报告生成失败：未获得有效响应内容。"
            logger.warning("Bearish reporter returned empty response content")
        
        logger.info(f"Bearish reporter response generated successfully")
        return Command(
            update={"bearish_report": response_content},
            goto="financial_report_coordinator"
        )
    
    except Exception as e:
        logger.error(f"Error generating bearish report: {e}")
        return Command(
            update={"bearish_report": f"看空报告生成失败：{str(e)}"},
            goto="financial_report_coordinator"
        )


def trading_advice_reporter_node(state: State) -> Command[Literal["financial_report_coordinator"]]:
    """Trading advice reporter node that writes actionable trading recommendations."""
    logger.info("Trading advice reporter generating trading recommendations")
    
    from ..prompts.template import apply_financial_report_template
    
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])
    
    # Validate current_plan
    if not current_plan:
        logger.error("No current plan found for trading advice reporter")
        return Command(
            update={"trading_advice_report": "无法生成交易建议报告：未找到研究计划。"},
            goto="financial_report_coordinator"
        )
    
    # Validate plan content
    plan_title = getattr(current_plan, 'title', None) or "未知投资标的"
    plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    # Prepare context for trading advice
    additional_context = {
        "analysis_focus": "trading_advice",
        "task_title": plan_title,
        "task_description": plan_thought,
        "observations": observations
    }
    
    input_ = {
        "messages": [
            HumanMessage(
                f"请基于以下研究数据生成实用交易建议报告：\n\n"
                f"# 投资标的\n{plan_title}\n\n"
                f"# 分析思路\n{plan_thought}\n\n"
                f"# 研究发现\n"
                + "\n\n".join([f"- {obs}" for obs in observations if obs and obs.strip()])
            )
        ],
        "locale": state.get("locale", "zh-CN"),
    }
    
    try:
        invoke_messages = apply_financial_report_template("trading_advice_reporter", input_, additional_context)
        response = get_llm_by_type(AGENT_LLM_MAP.get("reporter", "gpt-4")).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            response_content = "交易建议报告生成失败：未获得有效响应内容。"
            logger.warning("Trading advice reporter returned empty response content")
        
        logger.info(f"Trading advice reporter response generated successfully")
        return Command(
            update={"trading_advice_report": response_content},
            goto="financial_report_coordinator"
        )
    
    except Exception as e:
        logger.error(f"Error generating trading advice report: {e}")
        return Command(
            update={"trading_advice_report": f"交易建议报告生成失败：{str(e)}"},
            goto="financial_report_coordinator"
        )


def final_comprehensive_reporter_node(state: State):
    """Final comprehensive reporter node that integrates all stored agent data and financial analysis reports."""
    logger.info("Final comprehensive reporter generating integrated analysis report with data coordination")
    
    from ..tools.data_coordination import get_all_agent_data, check_data_collection_complete
    from ..prompts.template import apply_financial_report_template
    
    # Check if data collection is complete and get all agent data
    data_complete = check_data_collection_complete(state)
    all_agent_data = get_all_agent_data(state)
    logger.info(f"Data collection complete: {data_complete}, Available agent data: {list(all_agent_data.keys())}")
    
    current_plan = state.get("current_plan")
    bullish_report = state.get("bullish_report", "")
    bearish_report = state.get("bearish_report", "")
    trading_advice_report = state.get("trading_advice_report", "")
    observations = state.get("observations", [])
    
    # Extract analysis content from stored agent data
    technical_analysis = ""
    news_analysis = ""
    fundamental_analysis = ""
    research_findings = ""
    
    if all_agent_data.get("technical_analysis"):
        tech_content = all_agent_data["technical_analysis"].get("content", {})
        technical_analysis = tech_content.get("analysis", "")
    
    if all_agent_data.get("news_analysis"):
        news_content = all_agent_data["news_analysis"].get("content", {})
        news_analysis = news_content.get("analysis", "")
    
    if all_agent_data.get("fundamental_analysis"):
        fund_content = all_agent_data["fundamental_analysis"].get("content", {})
        fundamental_analysis = fund_content.get("analysis", "")
    
    if all_agent_data.get("research"):
        research_content = all_agent_data["research"].get("content", {})
        research_findings = research_content.get("analysis", "")
    
    # Validate current_plan and provide fallbacks
    if not current_plan:
        logger.warning("No current plan found for final comprehensive reporter, generating basic report")
        plan_title = "投资分析"
        plan_thought = "基于多智能体分析系统的综合评估"
    else:
        plan_title = getattr(current_plan, 'title', None) or "未知投资标的"
        plan_thought = getattr(current_plan, 'thought', None) or "无具体分析思路"
    
    # Check if we have any content to work with (including agent data)
    has_bullish = bullish_report and bullish_report.strip()
    has_bearish = bearish_report and bearish_report.strip()
    has_trading = trading_advice_report and trading_advice_report.strip()
    has_observations = observations and len([obs for obs in observations if obs and obs.strip()]) > 0
    has_technical = technical_analysis and technical_analysis.strip()
    has_news = news_analysis and news_analysis.strip()
    has_fundamental = fundamental_analysis and fundamental_analysis.strip()
    has_research = research_findings and research_findings.strip()
    
    # If we have no content at all, generate a basic fallback report
    if not any([has_bullish, has_bearish, has_trading, has_observations, has_technical, has_news, has_fundamental, has_research]):
        logger.warning("No report content available, generating minimal fallback report")
        fallback_report = f"""# {plan_title} - 分析报告

## 分析概况
基于多智能体分析系统，我们尝试为您分析了 {plan_title}，但在数据收集过程中遇到了一些技术问题。

## 分析思路
{plan_thought}

## 系统状态
- 研究团队: 已部署多个分析智能体
- 数据收集: 遇到技术问题，部分数据未能获取
- 报告生成: 采用备用方案生成基础报告

## 建议
1. 请确认分析标的信息是否准确
2. 稍后重试可能获得更完整的分析
3. 如需要特定信息，请提供更详细的查询

## 风险提示
本报告因技术问题采用简化方式生成，仅供参考。投资有风险，决策需谨慎。

---
*生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*状态: 系统备用报告*"""
        
        # Create fallback state update
        fallback_state_update = {
            "comprehensive_final_report": fallback_report,
            "final_report": fallback_report,
            "workflow_stage": "completed", 
            "workflow_complete": True,
            "workflow_fallback": True
        }
        
        # Debug logging for fallback
        logger.info(f"Final comprehensive reporter fallback updating state with fields: {list(fallback_state_update.keys())}")
        logger.info(f"Fallback report content length: {len(fallback_report)} characters")
        
        return fallback_state_update
    
    # Generate content sections based on what's available (prioritize agent data)
    content_sections = []
    
    # Add specialist agent analyses first
    if has_technical:
        content_sections.append(f"# 🔧 技术分析师报告\n\n{technical_analysis}")
    
    if has_news:
        content_sections.append(f"# 📰 新闻分析师报告\n\n{news_analysis}")
    
    if has_fundamental:
        content_sections.append(f"# 💰 基本面分析师报告\n\n{fundamental_analysis}")
    
    if has_research:
        content_sections.append(f"# 🔍 研究团队发现\n\n{research_findings}")
    
    # Add legacy research findings if available and no specific research data
    if has_observations and not has_research:
        research_content = "\n\n".join([f"- {obs}" for obs in observations if obs and obs.strip()])
        content_sections.append(f"# 📊 研究发现\n\n{research_content}")
    
    # Add investment viewpoint reports
    if has_bullish:
        content_sections.append(f"# 🟢 看多观点分析\n\n{bullish_report}")
    
    if has_bearish:
        content_sections.append(f"# 🔴 看空观点分析\n\n{bearish_report}")
    
    if has_trading:
        content_sections.append(f"# 📋 交易建议\n\n{trading_advice_report}")
    
    # Prepare context for comprehensive report
    additional_context = {
        "analysis_focus": "comprehensive_integration",
        "task_title": plan_title,
        "task_description": plan_thought,
        "bullish_analysis": bullish_report,
        "bearish_analysis": bearish_report,
        "trading_recommendations": trading_advice_report,
        "technical_observations": observations,
        "available_sections": len(content_sections),
        "has_bullish": has_bullish,
        "has_bearish": has_bearish,
        "has_trading": has_trading,
        "has_observations": has_observations
    }
    
    # Create comprehensive input message
    comprehensive_content = f"""请整合以下所有分析报告，生成最终综合投资分析报告：

# 投资标的
{plan_title}

# 分析思路
{plan_thought}

# 可用分析内容
{chr(10).join(content_sections)}

请生成一份结构完整、逻辑清晰的综合分析报告。如果某些部分缺失，请在报告中说明。"""
    
    input_ = {
        "messages": [HumanMessage(comprehensive_content)],
        "locale": state.get("locale", "zh-CN"),
    }
    
    try:
        invoke_messages = apply_financial_report_template("final_comprehensive_reporter", input_, additional_context)
        response = get_llm_by_type(AGENT_LLM_MAP.get("reporter", "gpt-4")).invoke(invoke_messages)
        response_content = getattr(response, 'content', None)
        
        if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
            logger.warning("Final comprehensive reporter returned empty response, generating structured fallback")
            
            # Generate a structured fallback based on available content
            structured_fallback = f"""# {plan_title} - 综合投资分析报告

## 执行摘要
基于多智能体分析系统的研究，以下是针对 {plan_title} 的综合分析：

{chr(10).join(content_sections) if content_sections else "## 分析状态\n系统已部署多个分析智能体，但部分分析内容生成遇到技术问题。"}

## 综合评估
- 分析完成度: {len(content_sections)}/4 个主要分析模块
- 数据可用性: {"正常" if has_observations else "受限"}
- 报告可信度: {"高" if len(content_sections) >= 3 else "中等" if len(content_sections) >= 1 else "基础"}

## 投资建议
{"基于以上分析，请结合个人风险承受能力做出投资决策。" if content_sections else "建议重新查询或联系技术支持获取完整分析。"}

## 风险提示
本报告基于公开信息和模型分析生成，仅供参考，不构成投资建议。投资有风险，决策需谨慎。

---
*生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*分析师: AI多智能体系统*
*完成度: {len(content_sections)}/4 模块*"""
            
            response_content = structured_fallback
        
        logger.info(f"Final comprehensive reporter response generated successfully")
        
        # Create state update with explicit final report fields
        state_update = {
            "comprehensive_final_report": response_content,
            "final_report": response_content,  # 保持与原有系统的兼容性
            "workflow_stage": "completed",
            "workflow_complete": True
        }
        
        # Debug logging for state update
        logger.info(f"Final comprehensive reporter updating state with fields: {list(state_update.keys())}")
        logger.info(f"Final report content length: {len(response_content)} characters")
        logger.info(f"Final report preview: {response_content[:200]}...")
        
        return state_update
    
    except Exception as e:
        logger.error(f"Error generating comprehensive final report: {e}")
        
        # Generate error recovery report with available content
        error_recovery_report = f"""# {plan_title} - 分析报告 (错误恢复)

## 系统状态
在生成综合报告时遇到技术问题: {str(e)}

## 已收集的分析内容
{chr(10).join(content_sections) if content_sections else "暂无可用分析内容"}

## 分析概况
- 目标: {plan_title}
- 分析思路: {plan_thought}
- 状态: 系统错误恢复模式
- 可用数据: {len(content_sections)} 个分析模块

## 风险提示
本报告因系统错误采用恢复模式生成，内容可能不完整。投资有风险，决策需谨慎。

## 建议操作
1. 稍后重新发起分析请求
2. 检查查询参数是否正确
3. 如问题持续，请联系技术支持

---
*生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*模式: 错误恢复*"""
        
        # Create error recovery state update
        error_state_update = {
            "comprehensive_final_report": error_recovery_report,
            "final_report": error_recovery_report,
            "workflow_stage": "completed",
            "workflow_complete": True,
            "workflow_error": True
        }
        
        # Debug logging for error recovery
        logger.info(f"Final comprehensive reporter error recovery updating state with fields: {list(error_state_update.keys())}")
        logger.info(f"Error recovery report content length: {len(error_recovery_report)} characters")
        
        return error_state_update


def research_team_node(
    state: State,
) -> Command[Literal["financial_report_coordinator", "researcher", "coder", "technical_analyst", "technical_analysis_specialist", "news_analysis_specialist", "fundamental_analysis_specialist"]]:
    """Research team node that collaborates on tasks with enhanced step management."""
    logger.info("Research team is collaborating on tasks.")
    current_plan = state.get("current_plan")
    if not current_plan or not current_plan.steps:
        logger.info("No current plan or steps found, going to financial report coordinator")
        return Command(goto="financial_report_coordinator")
    
    # Enhanced completion check - consider steps completed if they have results OR reached retry limit
    step_retry_counts = state.get("step_retry_counts", {})
    MAX_RETRIES_PER_STEP = 3
    
    completed_steps = 0
    total_steps = len(current_plan.steps)
    
    for i, step in enumerate(current_plan.steps):
        step_key = f"step_{i}_{step.title}"
        retry_count = step_retry_counts.get(step_key, 0)
        
        # Consider step completed if:
        # 1. It has execution result, OR
        # 2. It reached max retry limit (to prevent infinite loops)
        if step.execution_res or retry_count >= MAX_RETRIES_PER_STEP:
            completed_steps += 1
            if retry_count >= MAX_RETRIES_PER_STEP and not step.execution_res:
                logger.warning(f"Step '{step.title}' reached max retries ({MAX_RETRIES_PER_STEP}), marking as completed")
    
    logger.info(f"Progress: {completed_steps}/{total_steps} steps completed (including max-retry steps)")
    
    if completed_steps >= total_steps:
        logger.info("All steps completed or reached retry limits, going to financial report coordinator")
        return Command(goto="financial_report_coordinator")
    
    # Find the first uncompleted step that hasn't reached retry limit
    current_step = None
    current_step_index = None
    for i, step in enumerate(current_plan.steps):
        step_key = f"step_{i}_{step.title}"
        retry_count = step_retry_counts.get(step_key, 0)
        
        if not step.execution_res and retry_count < MAX_RETRIES_PER_STEP:
            current_step = step
            current_step_index = i
            break
    
    if not current_step:
        logger.info("No uncompleted steps found (all completed or at retry limit), going to financial report coordinator")
        return Command(goto="financial_report_coordinator")
    
    # Route to appropriate node based on step type
    logger.info(f"Routing step '{current_step.title}' of type '{current_step.step_type}' to appropriate node")
    
    # Find the step index for reliable state passing
    current_step_index = None
    for i, step in enumerate(current_plan.steps):
        if step.title == current_step.title and not step.execution_res:
            current_step_index = i
            break
    
    if current_step.step_type and current_step.step_type == StepType.RESEARCH:
        logger.info(f"Assigning research step to researcher: {current_step.title} (index: {current_step_index})")
        update_data = {
            "current_step_index": current_step_index,
            "current_step_title": current_step.title
        }
        logger.info(f"研究团队更新状态: {update_data}")
        return Command(
            update=update_data,
            goto="researcher"
        )
    if current_step.step_type and current_step.step_type == StepType.PROCESSING:
        logger.info(f"Assigning processing step to coder: {current_step.title} (index: {current_step_index})")
        return Command(
            update={
                "current_step_index": current_step_index,
                "current_step_title": current_step.title
            },
            goto="coder"
        )
    if current_step.step_type and current_step.step_type == StepType.TECHNICAL_ANALYSIS:
        # Enhanced routing: Use specialized technical analysis agent for better results
        logger.info(f"Assigning technical analysis step to technical_analysis_specialist: {current_step.title} (index: {current_step_index})")
        return Command(
            update={
                "current_step_index": current_step_index,
                "current_step_title": current_step.title
            },
            goto="technical_analysis_specialist"
        )
    if current_step.step_type and current_step.step_type == StepType.NEWS_ANALYSIS:
        logger.info(f"Assigning news analysis step to news_analysis_specialist: {current_step.title} (index: {current_step_index})")
        return Command(
            update={
                "current_step_index": current_step_index,
                "current_step_title": current_step.title
            },
            goto="news_analysis_specialist"
        )
    if current_step.step_type and current_step.step_type == StepType.FUNDAMENTAL_ANALYSIS:
        logger.info(f"Assigning fundamental analysis step to fundamental_analysis_specialist: {current_step.title} (index: {current_step_index})")
        return Command(
            update={
                "current_step_index": current_step_index,
                "current_step_title": current_step.title
            },
            goto="fundamental_analysis_specialist"
        )
    
    logger.warning(f"Unknown step type: {current_step.step_type}, going to financial report coordinator")
    return Command(goto="financial_report_coordinator")


def financial_report_coordinator_node(
    state: State,
) -> Command[Literal["bullish_reporter", "bearish_reporter", "trading_advice_reporter", "final_comprehensive_reporter"]]:
    """Financial report coordinator node that manages report generation with data coordination."""
    from ..tools.data_coordination import check_data_collection_complete, get_all_agent_data
    
    logger.info("Financial report coordinator managing report generation with data coordination")
    
    # Check if agent data collection is complete
    data_complete = check_data_collection_complete(state)
    all_agent_data = get_all_agent_data(state)
    completed_agents = state.get("agents_completed", [])
    
    logger.info(f"Data collection status: {data_complete}, Completed agents: {completed_agents}")
    logger.info(f"Available agent data types: {list(all_agent_data.keys())}")
    
    # Enhanced check for substantial agent data
    has_specialist_data = any([
        all_agent_data.get("technical_analysis"),
        all_agent_data.get("news_analysis"), 
        all_agent_data.get("fundamental_analysis")
    ])
    
    # Check for traditional research data as fallback
    has_research_data = all_agent_data.get("research") is not None
    
    # Check for completed research steps
    current_plan = state.get("current_plan")
    has_completed_steps = False
    if current_plan and current_plan.steps:
        completed_steps = sum(1 for step in current_plan.steps if step.execution_res)
        has_completed_steps = completed_steps > 0
    
    has_substantial_data = has_specialist_data or has_research_data or has_completed_steps
    
    logger.info(f"Data assessment: specialist={has_specialist_data}, research={has_research_data}, completed_steps={has_completed_steps}, substantial={has_substantial_data}")
    
    # If we have substantial data (specialist or research), prioritize going directly to final report
    if has_substantial_data:
        logger.info("Substantial analysis data available, routing directly to final comprehensive reporter")
        return Command(
            update={"workflow_stage": "final_report"},
            goto="final_comprehensive_reporter"
        )
    
    # Check which traditional reports have been generated
    bullish_report = state.get("bullish_report", "")
    bearish_report = state.get("bearish_report", "")
    trading_advice_report = state.get("trading_advice_report", "")
    
    # Traditional sequential report generation logic (fallback)
    if not bullish_report:
        logger.info("No substantial data available, generating bullish analysis report")
        return Command(goto="bullish_reporter")
    elif not bearish_report:
        logger.info("Generating bearish analysis report")
        return Command(goto="bearish_reporter")
    elif not trading_advice_report:
        logger.info("Generating trading advice report")
        return Command(goto="trading_advice_reporter")
    else:
        logger.info("All individual reports completed, generating final comprehensive report")
        return Command(
            update={"workflow_stage": "final_report"},
            goto="final_comprehensive_reporter"
        )


async def _execute_agent_step(
    state: State, agent, agent_name: str
) -> Command[Literal["research_team"]]:
    """Helper function to execute a step using the specified agent."""
    current_plan = state.get("current_plan")
    current_step_index = state.get("current_step_index")
    current_step_title = state.get("current_step_title", "Unknown")
    observations = state.get("observations", [])

    # Get the current step by index
    current_step = None
    if current_plan and current_plan.steps and current_step_index is not None:
        if 0 <= current_step_index < len(current_plan.steps):
            current_step = current_plan.steps[current_step_index]
            logger.info(f"Retrieved step by index {current_step_index}: {current_step.title}")
        else:
            logger.error(f"Step index {current_step_index} out of range (max: {len(current_plan.steps)-1})")

    if not current_step:
        logger.error(f"No current step found in {agent_name} node. State keys: {list(state.keys())}")
        logger.error(f"Expected step title: {current_step_title}, index: {current_step_index}")
        
        # Create a fallback execution result instead of returning empty
        fallback_result = f"""# {agent_name.title()} 执行报告

## 执行状态
由于技术问题，无法找到当前执行步骤，但 {agent_name} 智能体已尝试基于可用信息进行分析。

## 可用信息
- 计划状态: {'有效' if current_plan else '缺失'}
- 步骤索引: {current_step_index}
- 期望步骤: {current_step_title}
- 已有观察: {len(observations)} 项

## 系统建议
建议重新发起查询或检查输入参数的准确性。

---
*执行时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*执行者: {agent_name} (错误恢复模式)*"""
        
        return Command(
            update={
                "messages": [
                    HumanMessage(
                        content=fallback_result,
                        name=agent_name,
                    )
                ],
                "observations": observations + [fallback_result],
                "current_plan": current_plan,
            },
            goto="research_team",
        )

    logger.info(f"Executing step: {current_step.title}")

    # Validate step content to avoid null values
    step_title = current_step.title or "未命名任务"
    step_description = current_step.description or "无具体描述"
    
    # Format completed steps information
    completed_steps = []
    for step in current_plan.steps:
        if step.execution_res:
            completed_steps.append(step)
    
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            completed_steps_info += f"## Existing Finding {i+1}: {step.title or '未命名步骤'}\n\n"
            completed_steps_info += f"<finding>\n{step.execution_res or '无执行结果'}\n</finding>\n\n"

    # Prepare the input for the agent with completed steps info
    content = f"{completed_steps_info}# Current Task\n\n## Title\n\n{step_title}\n\n## Description\n\n{step_description}\n\n## Locale\n\n{state.get('locale', 'zh-CN')}"
    
    # Ensure content is not empty
    if not content or content.strip() == "":
        content = "默认任务：请分析当前状态并提供相关信息。"
    
    agent_input = {
        "messages": [
            HumanMessage(content=content)
        ]
    }

    # Add specific system messages for different agent types
    if agent_name == "researcher":
        system_message = (
            "IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. "
            "Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL)\n\n- [Another Source](URL)\n\n"
            "**TOOLS AVAILABLE:** 您拥有专门的AkShare股票数据工具，包括：\n"
            "- get_famous_stock_data_tool: 【推荐】直接获取知名公司股票数据（支持中文公司名称，如\"苹果公司\"、\"特斯拉\"）\n"
            "- stock_news_em_tool: 获取股票新闻\n"
            "- us_stock_spot_tool: 获取美股实时行情\n"
            "- us_stock_daily_sina_tool: 获取历史股价数据（自动按日期降序排列，最新数据在前）\n"
            "- us_stock_search_tool: 搜索美股公司（支持中英文名称映射）\n"
            "- comprehensive_stock_news_tool: 综合股票资讯\n"
            "- yahoo_finance_news_tool: Yahoo Finance新闻\n"
            "- get_web_search_tool: 网络搜索工具\n\n"
            "**使用建议：** 当用户询问知名公司（苹果、特斯拉、微软等）股价时，请优先使用 get_famous_stock_data_tool，它支持中文公司名称并直接返回股票数据。数据已经过优化，会自动按日期降序排列，确保最新数据在前。同时使用新闻工具获取最新市场动态。"
        )
        if system_message and system_message.strip():
            agent_input["messages"].append(
                HumanMessage(content=system_message, name="system")
            )
    elif agent_name == "technical_analyst":
        system_message = (
            "**专业技术分析师指南：**\n\n"
            "您是一位专业的股票技术分析师，请按照以下结构进行全面的技术面分析：\n\n"
            "**核心分析框架：**\n"
            "1. 📈 趋势判断（移动平均线、趋势线分析）\n"
            "2. 🔁 支撑位与阻力位分析（关键价位、突破确认）\n"
            "3. 🔍 成交量分析（量价关系、背离信号）\n"
            "4. ⚙️ 技术指标信号（MACD、RSI、KDJ、布林带等）\n"
            "5. 🔺 图形形态与K线信号（经典形态识别）\n"
            "6. ✅ 综合结论与操作建议\n\n"
            "**数据获取和分析策略（重要！）：**\n"
            "- **获取120根K线数据**：为MACD的EMA26等技术指标提供足够的计算基础\n"
            "- **重点分析90根K线**：所有技术分析结论基于最近90个交易日\n"
            "- **技术指标计算准确性**：确保长周期指标（如EMA26、MA60）有足够的历史数据支撑\n"
            "- 优先使用 `get_famous_stock_data_tool(max_rows=120)` 获取知名公司数据\n"
            "- 使用 `us_stock_daily_sina_tool(max_rows=120)` 获取完整历史K线数据\n\n"
            "**重要：必须使用工具进行实际的技术指标计算和数据分析**\n\n"
            "**输出要求：**\n"
            "- 使用结构化格式，包含所有6个分析部分\n"
            "- 基于实际数据进行客观分析\n"
            "- 提供具体的操作建议和风险提示\n"
            "- 必须包含投资风险警示\n"
            "- **每个分析部分都要有实际的计算结果支撑**\n"
            "- **明确说明分析基于最近90根K线，技术指标基于120根K线计算**"
        )
        if system_message and system_message.strip():
            agent_input["messages"].append(
                HumanMessage(content=system_message, name="system")
            )

    # 验证所有消息内容确保不为null
    validated_messages = []
    for msg in agent_input["messages"]:
        if hasattr(msg, 'content'):
            if msg.content is None or (isinstance(msg.content, str) and msg.content.strip() == ""):
                logger.warning(f"Found null/empty message content in {agent_name}, replacing with default")
                msg.content = f"默认消息内容（{agent_name}）"
        validated_messages.append(msg)
    
    agent_input["messages"] = validated_messages

    # Invoke the agent with comprehensive error handling
    default_recursion_limit = 25
    response_content = None
    
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default_recursion_limit))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            recursion_limit = parsed_limit
            logger.info(f"Recursion limit set to: {recursion_limit}")
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default_recursion_limit}."
            )
            recursion_limit = default_recursion_limit
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default_recursion_limit}."
        )
        recursion_limit = default_recursion_limit

    try:
        result = await agent.ainvoke(
            input=agent_input, config={"recursion_limit": recursion_limit}
        )

        # Process the result with enhanced error handling
        if not result or "messages" not in result or not result["messages"]:
            response_content = f"""# {agent_name.title()} 分析报告

## 执行状态
{agent_name} 智能体已尝试执行分析任务 "{step_title}"，但未返回详细消息内容。

## 任务信息
- 步骤: {step_title}
- 描述: {step_description}
- 执行时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

## 系统状态
Agent 执行完成，但响应内容为空。这可能是由于：
1. 网络连接问题
2. API 响应延迟
3. 数据源暂时不可用

## 建议
建议重试查询或稍后再试。

---
*执行者: {agent_name}*
*状态: 执行完成但内容为空*"""
            logger.warning(f"{agent_name} returned no messages")
        else:
            last_message = result["messages"][-1]
            response_content = getattr(last_message, 'content', None)
            if not response_content or (isinstance(response_content, str) and response_content.strip() == ""):
                response_content = f"""# {agent_name.title()} 分析报告

## 执行状态
{agent_name} 智能体已执行完成任务 "{step_title}"，但返回的内容为空。

## 任务详情
- 步骤标题: {step_title}
- 步骤描述: {step_description}
- 处理时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

## 可能原因
1. 查询的数据源暂时不可用
2. 输入参数可能需要调整
3. 网络或API服务临时异常

## 下步建议
1. 检查查询参数（如股票代码、公司名称）
2. 稍后重试分析
3. 尝试更具体的查询内容

---
*执行者: {agent_name}*
*模式: 空响应恢复*"""
                logger.warning(f"{agent_name} returned empty response content")
                
    except Exception as agent_error:
        logger.error(f"Agent {agent_name} execution failed: {agent_error}")
        response_content = f"""# {agent_name.title()} 执行错误报告

## 错误状态
在执行任务 "{step_title}" 时，{agent_name} 智能体遇到了技术错误。

## 错误信息
```
{str(agent_error)}
```

## 任务信息
- 步骤: {step_title}
- 描述: {step_description}
- 错误时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

## 系统诊断
这个错误可能是由以下原因引起：
1. 网络连接问题
2. 外部数据源API异常
3. 处理超时
4. 系统资源限制

## 恢复建议
1. 稍后重试分析请求
2. 检查网络连接状态
3. 简化查询内容重新尝试
4. 如问题持续存在，请联系技术支持

---
*执行者: {agent_name}*
*状态: 错误恢复模式*
*错误类型: {type(agent_error).__name__}*"""
    
    # Final validation of response content
    if not response_content:
        response_content = f"""# {agent_name.title()} 默认报告

## 系统状态
{agent_name} 智能体处理了任务 "{step_title}"，但未能生成具体分析内容。

## 执行概要
- 任务: {step_title}
- 智能体: {agent_name}
- 时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- 状态: 使用默认恢复机制

## 建议
请尝试重新发起查询，或提供更具体的分析要求。

---
*模式: 系统默认响应*"""
    
    logger.debug(f"{agent_name.capitalize()} full response: {response_content[:200]}...")

    # Update retry counter for this step
    step_retry_counts = state.get("step_retry_counts", {})
    step_key = f"step_{current_step_index}_{current_step.title}"
    step_retry_counts[step_key] = step_retry_counts.get(step_key, 0) + 1
    
    # Update the step with the execution result
    current_step.execution_res = response_content
    logger.info(f"Step '{current_step.title}' execution completed by {agent_name} (attempt #{step_retry_counts[step_key]})")

    return Command(
        update={
            "messages": [
                HumanMessage(
                    content=response_content,
                    name=agent_name,
                )
            ],
            "observations": observations + [response_content],
            "current_plan": current_plan,
            "step_retry_counts": step_retry_counts,
        },
        goto="research_team",
    )


async def _execute_agent_step_silent(
    state: State, agent, agent_name: str, data_type: str
) -> Command[Literal["research_team"]]:
    """
    Execute agent step but store results internally instead of sending messages to frontend.
    This is used for specialist agents that accumulate data for final report coordination.
    
    Args:
        state: Current workflow state
        agent: The agent to execute
        agent_name: Name of the agent for logging and state tracking
        data_type: Type of data being generated (technical_analysis, news_analysis, etc.)
        
    Returns:
        Command to update state and continue to research_team
    """
    from ..tools.data_coordination import store_agent_data_in_state
    from datetime import datetime
    
    logger.info(f"Executing {agent_name} agent step (silent mode)")
    
    # Get current step information
    current_plan = state.get("current_plan")
    current_step_index = state.get("current_step_index")
    current_step_title = state.get("current_step_title", "Unknown")
    observations = state.get("observations", [])

    # Get the current step by index
    current_step = None
    if current_plan and current_plan.steps and current_step_index is not None:
        if 0 <= current_step_index < len(current_plan.steps):
            current_step = current_plan.steps[current_step_index]
            logger.info(f"Retrieved step by index {current_step_index}: {current_step.title}")
        else:
            logger.error(f"Step index {current_step_index} out of range (max: {len(current_plan.steps)-1})")

    if not current_step:
        logger.error(f"No current step found in {agent_name} node (silent mode). State keys: {list(state.keys())}")
        
        # Store error information instead of creating fallback message
        error_data = {
            "error": f"No current step found for {agent_name}",
            "step_title": current_step_title,
            "step_index": current_step_index,
            "execution_timestamp": datetime.now().isoformat()
        }
        
        # Initialize state_updates dict
        state_updates = {
            "current_active_agent": agent_name,
            "workflow_stage": "data_collection"
        }
        
        store_agent_data_in_state(state_updates, agent_name, data_type, error_data)
        
        return Command(
            update=state_updates,
            goto="research_team",
        )

    logger.info(f"{agent_name} executing step (silent): {current_step.title}")
    
    # Prepare state updates
    state_updates = {
        "current_active_agent": agent_name,
        "workflow_stage": "data_collection"
    }
    
    # Validate step content to avoid null values
    step_title = current_step.title or "未命名任务"
    step_description = current_step.description or "无具体描述"
    
    # Format completed steps information
    completed_steps = []
    for step in current_plan.steps:
        if step.execution_res:
            completed_steps.append(step)
    
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            completed_steps_info += f"## Existing Finding {i+1}: {step.title or '未命名步骤'}\n\n"
            completed_steps_info += f"<finding>\n{step.execution_res or '无执行结果'}\n</finding>\n\n"

    # Prepare the input for the agent with completed steps info
    content = f"{completed_steps_info}# Current Task\n\n## Title\n\n{step_title}\n\n## Description\n\n{step_description}\n\n## Locale\n\n{state.get('locale', 'zh-CN')}"
    
    # Ensure content is not empty
    if not content or content.strip() == "":
        content = "默认任务：请分析当前状态并提供相关信息。"
    
    agent_input = {
        "messages": [
            HumanMessage(content=content)
        ]
    }

    # Add specific system messages for different agent types
    if agent_name in ["technical_analysis_specialist", "technical_analyst"]:
        system_message = (
            "**重要：数据存储模式**\n"
            "您正在协作模式下工作。请完成您的专业技术分析，分析结果将被存储供最终报告使用。\n\n"
            "**专业技术分析师指南：**\n\n"
            "您是一位专业的股票技术分析师，请按照以下结构进行全面的技术面分析：\n\n"
            "**核心分析框架：**\n"
            "1. 📈 趋势判断（移动平均线、趋势线分析）\n"
            "2. 🔁 支撑位与阻力位分析（关键价位、突破确认）\n"
            "3. 🔍 成交量分析（量价关系、背离信号）\n"
            "4. ⚙️ 技术指标信号（MACD、RSI、KDJ、布林带等）\n"
            "5. 🔺 图形形态与K线信号（经典形态识别）\n"
            "6. ✅ 综合结论与操作建议\n\n"
            "**数据获取和分析策略（重要！）：**\n"
            "- **获取120根K线数据**：为MACD的EMA26等技术指标提供足够的计算基础\n"
            "- **重点分析90根K线**：所有技术分析结论基于最近90个交易日\n"
            "- **技术指标计算准确性**：确保长周期指标（如EMA26、MA60）有足够的历史数据支撑\n"
            "- 优先使用 `get_famous_stock_data_tool(max_rows=120)` 获取知名公司数据\n"
            "- 使用 `us_stock_daily_sina_tool(max_rows=120)` 获取完整历史K线数据\n\n"
            "**重要：必须使用工具进行实际的技术指标计算和数据分析**\n\n"
            "**输出要求：**\n"
            "- 使用结构化格式，包含所有6个分析部分\n"
            "- 基于实际数据进行客观分析\n"
            "- 提供具体的操作建议和风险提示\n"
            "- 必须包含投资风险警示\n"
            "- **每个分析部分都要有实际的计算结果支撑**\n"
            "- **明确说明分析基于最近90根K线，技术指标基于120根K线计算**"
        )
        agent_input["messages"].append(
            HumanMessage(content=system_message, name="system")
        )
    elif agent_name == "news_analysis_specialist":
        system_message = (
            "**重要：数据存储模式**\n"
            "您正在协作模式下工作。请完成您的专业新闻分析，分析结果将被存储供最终报告使用。\n\n"
            "**专业新闻分析师指南：**\n\n"
            "您是一位专业的金融新闻分析师，请按照以下结构进行全面的新闻面分析：\n\n"
            "**核心分析框架：**\n"
            "1. 📰 最新新闻汇总与分类\n"
            "2. 📈 市场情绪分析（正面/负面/中性）\n"
            "3. 🔍 重要事件影响评估\n"
            "4. 🌍 行业和宏观环境分析\n"
            "5. ⏰ 短期和中期市场预期\n"
            "6. ✅ 综合结论与投资建议\n\n"
            "**数据获取策略：**\n"
            "- 使用 `comprehensive_stock_news_tool` 获取综合资讯\n"
            "- 使用 `yahoo_finance_news_tool` 获取专业金融新闻\n"
            "- 使用 `stock_news_em_tool` 获取东方财富新闻\n"
            "- 使用 `get_web_search_tool` 获取最新市场动态\n\n"
            "**输出要求：**\n"
            "- 基于实际新闻数据进行客观分析\n"
            "- 量化情绪指标（如正面新闻占比）\n"
            "- 识别关键市场驱动因素\n"
            "- 提供时间敏感的投资建议"
        )
        agent_input["messages"].append(
            HumanMessage(content=system_message, name="system")
        )
    elif agent_name == "fundamental_analysis_specialist":
        system_message = (
            "**重要：数据存储模式**\n"
            "您正在协作模式下工作。请完成您的专业基本面分析，分析结果将被存储供最终报告使用。\n\n"
            "**专业基本面分析师指南：**\n\n"
            "您是一位专业的基本面分析师，请按照以下结构进行全面的基本面分析：\n\n"
            "**核心分析框架：**\n"
            "1. 💰 财务健康状况分析\n"
            "2. 📊 估值水平评估（PE、PB、PS等）\n"
            "3. 🏢 业务模式与竞争优势\n"
            "4. 📈 成长性与盈利能力\n"
            "5. 🏭 行业地位与发展前景\n"
            "6. ✅ 综合投资价值评估\n\n"
            "**数据获取策略：**\n"
            "- 使用可用的财务数据工具获取公司基本信息\n"
            "- 通过 `get_famous_stock_data_tool` 获取股价表现\n"
            "- 使用 `python_repl_tool` 进行财务计算和建模\n"
            "- 结合新闻和市场信息进行综合分析\n\n"
            "**输出要求：**\n"
            "- 基于可获得的财务数据进行分析\n"
            "- 提供量化的估值指标\n"
            "- 评估长期投资价值\n"
            "- 识别关键风险因素"
        )
        agent_input["messages"].append(
            HumanMessage(content=system_message, name="system")
        )

    try:
        # Execute the agent
        logger.info(f"Invoking {agent_name} agent for step: {current_step.title} (silent mode)")
        
        # Set recursion limit
        default_recursion_limit = 25
        recursion_limit = default_recursion_limit
        
        result = await agent.ainvoke(
            input=agent_input, config={"recursion_limit": recursion_limit}
        )
        
        # Extract analysis content from response
        response_content = ""
        if result and "messages" in result and result["messages"]:
            last_message = result["messages"][-1]
            if hasattr(last_message, 'content'):
                response_content = last_message.content or ""
            else:
                response_content = str(last_message)
        else:
            response_content = f"{agent_name} completed analysis (no detailed content returned)"
        
        # Store analysis data in state instead of sending as message
        analysis_data = {
            "analysis": response_content,
            "step_title": current_step.title,
            "step_index": current_step_index,
            "execution_timestamp": datetime.now().isoformat(),
            "agent_type": data_type
        }
        
        store_agent_data_in_state(state_updates, agent_name, data_type, analysis_data)
        
        # Mark step as completed
        current_step.execution_res = response_content
        
        # Update observations for coordination
        new_observations = observations.copy()
        new_observations.append(f"{agent_name} completed {data_type} analysis")
        state_updates["observations"] = new_observations
        state_updates["current_plan"] = current_plan
        
        logger.info(f"{agent_name} completed step silently and stored {data_type} data")
        
        return Command(
            update=state_updates,
            goto="research_team",
        )
        
    except Exception as e:
        logger.error(f"Error executing {agent_name} agent: {e}")
        
        # Store error information
        error_data = {
            "error": str(e),
            "step_title": current_step.title,
            "step_index": current_step_index,
            "execution_timestamp": datetime.now().isoformat(),
            "agent_type": data_type
        }
        
        store_agent_data_in_state(state_updates, agent_name, data_type, error_data)
        
        return Command(
            update=state_updates,
            goto="research_team",
        )


async def researcher_node(
    state: State, config: RunnableConfig = None
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认查询消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    configurable = Configuration.from_runnable_config(config)
    
    # Import akshare tools
    from ..tools.akshare import (
        stock_news_em_tool,
        stock_news_main_cx_tool,
        news_report_time_baidu_tool,
        comprehensive_stock_news_tool,
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    )
    
    # Import Yahoo Finance news tool
    try:
        from ..tools.yahoo_finance_news import yahoo_finance_news_tool
    except ImportError:
        yahoo_finance_news_tool = None
        logger.warning("Yahoo Finance news tool not available")
    
    # Create agent with tools
    from ..agents import create_agent
    
    tools = [
        # AkShare 股票和新闻工具
        stock_news_em_tool,
        stock_news_main_cx_tool,
        news_report_time_baidu_tool,
        comprehensive_stock_news_tool,
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    ]
    
    if yahoo_finance_news_tool:
        tools.append(yahoo_finance_news_tool)
    
    agent = create_agent("researcher", "researcher", tools, "researcher")
    
    return await _execute_agent_step(state, agent, "researcher")


async def coder_node(
    state: State,
    config: RunnableConfig = None,
) -> Command[Literal["research_team"]]:
    """Coder node that do code analysis."""
    logger.info("Coder node is coding.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认代码分析消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    
    # Import akshare tools for data analysis
    from ..tools.akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_daily_sina_tool,
    )
    
    # Create agent with tools
    from ..agents import create_agent
    from ..tools import python_repl_tool
    
    tools = [
        python_repl_tool, 
        # AkShare 数据分析工具
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_daily_sina_tool,
    ]
    
    agent = create_agent("coder", "coder", tools, "coder")
    
    return await _execute_agent_step(state, agent, "coder")


async def technical_analyst_node(
    state: State,
    config: RunnableConfig = None,
) -> Command[Literal["research_team"]]:
    """Technical analyst node that performs financial technical analysis."""
    logger.info("Technical analyst node is performing technical analysis.")
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认技术分析消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    
    # Import akshare tools for technical analysis
    from ..tools.akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    )
    
    # Import dedicated technical indicators tools
    try:
        from ..tools.technical_indicators import (
            calculate_technical_indicators,
            fibonacci_retracement_levels,
        )
    except ImportError:
        calculate_technical_indicators = None
        fibonacci_retracement_levels = None
        logger.warning("Technical indicators tools not available")
    
    # Import divergence analysis tool
    try:
        from ..tools.divergence_analysis import detect_price_macd_divergence
    except ImportError:
        detect_price_macd_divergence = None
        logger.warning("Divergence analysis tool not available")
    
    # Create agent with tools
    from ..agents import create_agent
    from ..tools import python_repl_tool
    
    tools = [
        # 股票数据获取工具 - 技术分析的数据源
        get_famous_stock_data_tool,        # 知名公司股票数据（推荐）
        us_stock_daily_sina_tool,          # 新浪美股历史数据
        us_stock_hist_tool,                # 美股历史数据（多周期）
        us_stock_spot_tool,                # 美股实时行情
        us_stock_search_tool,              # 美股搜索
        us_stock_famous_tool,              # 知名美股分类数据
        us_stock_pink_tool,                # 粉单市场数据
        us_stock_minute_tool,              # 美股分时数据
        python_repl_tool,                  # Python代码执行（补充工具）
    ]
    
    # Add technical analysis tools if available
    if calculate_technical_indicators:
        tools.append(calculate_technical_indicators)
    if fibonacci_retracement_levels:
        tools.append(fibonacci_retracement_levels)
    if detect_price_macd_divergence:
        tools.append(detect_price_macd_divergence)
    
    agent = create_agent("technical_analyst", "technical_analyst", tools, "technical_analyst")
    
    return await _execute_agent_step(state, agent, "technical_analyst")


async def technical_analysis_specialist_node(
    state: State,
    config: RunnableConfig = None,
) -> Command[Literal["research_team"]]:
    """Technical Analysis Specialist node - Advanced technical analysis with enhanced capabilities."""
    logger.info("Technical Analysis Specialist node performing enhanced technical analysis.")
    
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认技术分析专家消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    
    # 检查消息历史总长度，防止token超限
    total_content_length = sum(len(str(msg.content)) for msg in state['messages'] if hasattr(msg, 'content'))
    if total_content_length > 50000:  # 如果内容过长，只保留最近的几条消息
        logger.warning(f"消息历史过长({total_content_length}字符)，截断以防止token超限")
        # 保留最近的3条消息和原始查询
        recent_messages = state['messages'][-3:]
        if len(state['messages']) > 3:
            first_message = state['messages'][0]  # 保留原始查询
            state['messages'] = [first_message] + recent_messages
        logger.info(f"截断后消息数量: {len(state['messages'])}")
    
    # Import akshare tools for technical analysis
    from ..tools.akshare import (
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        us_stock_famous_tool,
        us_stock_pink_tool,
        us_stock_minute_tool,
        us_stock_daily_sina_tool,
        get_famous_stock_data_tool,
    )
    
    # Import dedicated technical indicators tools
    try:
        from ..tools.technical_indicators import (
            calculate_technical_indicators,
            fibonacci_retracement_levels,
        )
    except ImportError:
        calculate_technical_indicators = None
        fibonacci_retracement_levels = None
        logger.warning("Technical indicators tools not available")
    
    # Import divergence analysis tool
    try:
        from ..tools.divergence_analysis import detect_price_macd_divergence
    except ImportError:
        detect_price_macd_divergence = None
        logger.warning("Divergence analysis tool not available")
    
    # Create specialized technical analysis agent
    from ..agents import create_agent
    from ..tools import python_repl_tool
    
    tools = [
        # Core stock data tools for technical analysis
        get_famous_stock_data_tool,        # 【推荐】知名公司股票数据，支持中文公司名称
        us_stock_daily_sina_tool,          # 获取详细历史K线数据（自动按日期降序排列）
        us_stock_hist_tool,                # 获取多时间框架数据（周线、月线）
        us_stock_minute_tool,              # 获取分钟级数据用于短期分析
        us_stock_search_tool,              # 搜索股票代码和公司信息
        us_stock_spot_tool,                # 获取实时行情数据
        python_repl_tool,                  # 【必须使用】执行Python代码进行深度技术分析
    ]
    
    # Add specialized technical analysis tools
    if calculate_technical_indicators:
        tools.append(calculate_technical_indicators)  # 【核心工具】计算所有主要技术指标
    if fibonacci_retracement_levels:
        tools.append(fibonacci_retracement_levels)    # 计算斐波那契回调位
    if detect_price_macd_divergence:
        tools.append(detect_price_macd_divergence)    # 【重要】MACD背离分析工具
    
    agent = create_agent("technical_analysis_specialist", "technical_analysis_specialist", tools, "technical_analysis_specialist")
    
    return await _execute_agent_step_silent(state, agent, "technical_analysis_specialist", "technical_analysis")


async def news_analysis_specialist_node(
    state: State,
    config: RunnableConfig = None,
) -> Command[Literal["research_team"]]:
    """News Analysis Specialist node - Advanced news sentiment analysis and market impact assessment."""
    logger.info("News Analysis Specialist node performing enhanced news analysis.")
    
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认新闻分析专家消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    
    # Import news and financial tools
    from ..tools.akshare import (
        stock_news_em_tool,                # 获取东方财富股票新闻，适合中文市场分析
        comprehensive_stock_news_tool,     # 【推荐】综合股票资讯工具，多源信息聚合
        us_stock_search_tool,              # 搜索股票基本信息，确保新闻标的准确性
        get_famous_stock_data_tool,        # 获取股票基础数据作为新闻分析背景
    )
    
    # Import Yahoo Finance news tool
    try:
        from ..tools.yahoo_finance_news import yahoo_finance_news_tool  # 【重要】获取Yahoo Finance专业金融新闻，实时更新
    except ImportError:
        yahoo_finance_news_tool = None
        logger.warning("Yahoo Finance news tool not available")
    
    # Import web search tools
    try:
        from ..tools.web_search import get_web_search_tool  # 【补充工具】获取更广泛的网络新闻和信息
    except ImportError:
        get_web_search_tool = None
        logger.warning("Web search tool not available")
    
    # Create specialized news analysis agent
    from ..agents import create_agent
    
    tools = [
        # Core news analysis tools
        comprehensive_stock_news_tool,     # 【推荐】综合股票资讯工具，多源信息聚合
        stock_news_em_tool,                # 获取东方财富股票新闻，适合中文市场分析
        us_stock_search_tool,              # 搜索股票基本信息，确保新闻标的准确性
        get_famous_stock_data_tool,        # 获取股票基础数据作为新闻分析背景
    ]
    
    # Add specialized news tools if available
    if yahoo_finance_news_tool:
        tools.append(yahoo_finance_news_tool)  # 【重要】获取Yahoo Finance专业金融新闻，实时更新
    
    if get_web_search_tool:
        tools.append(get_web_search_tool)      # 【补充工具】获取更广泛的网络新闻和信息
    
    agent = create_agent("news_analysis_specialist", "news_analysis_specialist", tools, "news_analysis_specialist")
    
    return await _execute_agent_step_silent(state, agent, "news_analysis_specialist", "news_analysis")


async def fundamental_analysis_specialist_node(
    state: State,
    config: RunnableConfig = None,
) -> Command[Literal["research_team"]]:
    """Fundamental Analysis Specialist node - Company financials, valuation, and business analysis."""
    logger.info("Fundamental Analysis Specialist node performing enhanced fundamental analysis.")
    
    # 添加检查逻辑
    if not state.get('messages') or not state['messages']:
        state['messages'] = [HumanMessage(content="默认基本面分析专家消息，以避免API错误")]
    
    if config is None:
        config = {"configurable": {}}
    
    # Import financial data tools
    try:
        from ..tools.financial_data import (
            us_stock_balance_sheet_tool,       # 【重要】获取资产负债表数据
            us_stock_income_statement_tool,    # 【重要】获取利润表数据
            us_stock_cash_flow_tool,           # 【重要】获取现金流量表数据
            us_stock_financial_ratios_tool,    # 【核心】获取各类财务比率
            us_stock_profile_tool,             # 获取公司基本信息和业务描述
            us_stock_key_stats_tool,           # 获取关键统计数据和估值指标
            us_stock_analyst_estimates_tool,   # 获取分析师预期和评级
            us_stock_industry_peers_tool,      # 获取同行业公司对比数据
            us_stock_sector_performance_tool,  # 获取行业表现数据
        )
    except ImportError:
        # Fallback to basic stock tools if specialized financial tools not available
        from ..tools.akshare import (
            us_stock_search_tool,
            get_famous_stock_data_tool,
        )
        us_stock_balance_sheet_tool = None
        us_stock_income_statement_tool = None
        us_stock_cash_flow_tool = None
        us_stock_financial_ratios_tool = None
        us_stock_profile_tool = None
        us_stock_key_stats_tool = None
        us_stock_analyst_estimates_tool = None
        us_stock_industry_peers_tool = None
        us_stock_sector_performance_tool = None
        logger.warning("Specialized financial data tools not available, using basic tools")
    
    # Import basic tools that are always available
    from ..tools.akshare import (
        us_stock_search_tool,
        get_famous_stock_data_tool,
    )
    
    # Create specialized fundamental analysis agent
    from ..agents import create_agent
    from ..tools import python_repl_tool
    
    tools = [
        # Always available basic tools
        us_stock_search_tool,              # 搜索股票基本信息
        get_famous_stock_data_tool,        # 获取股票基础数据
        python_repl_tool,                  # 【必须使用】执行财务模型计算和深度分析
    ]
    
    # Add specialized financial tools if available
    if us_stock_balance_sheet_tool:
        tools.append(us_stock_balance_sheet_tool)       # 【重要】获取资产负债表数据
    if us_stock_income_statement_tool:
        tools.append(us_stock_income_statement_tool)    # 【重要】获取利润表数据
    if us_stock_cash_flow_tool:
        tools.append(us_stock_cash_flow_tool)           # 【重要】获取现金流量表数据
    if us_stock_financial_ratios_tool:
        tools.append(us_stock_financial_ratios_tool)    # 【核心】获取各类财务比率
    if us_stock_profile_tool:
        tools.append(us_stock_profile_tool)             # 获取公司基本信息和业务描述
    if us_stock_key_stats_tool:
        tools.append(us_stock_key_stats_tool)           # 获取关键统计数据和估值指标
    if us_stock_analyst_estimates_tool:
        tools.append(us_stock_analyst_estimates_tool)   # 获取分析师预期和评级
    if us_stock_industry_peers_tool:
        tools.append(us_stock_industry_peers_tool)      # 获取同行业公司对比数据
    if us_stock_sector_performance_tool:
        tools.append(us_stock_sector_performance_tool)  # 获取行业表现数据
    
    agent = create_agent("fundamental_analysis_specialist", "fundamental_analysis_specialist", tools, "fundamental_analysis_specialist")
    
    return await _execute_agent_step_silent(state, agent, "fundamental_analysis_specialist", "fundamental_analysis") 