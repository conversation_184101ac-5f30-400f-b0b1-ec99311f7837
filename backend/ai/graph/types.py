# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import operator
from typing import Annotated

from langgraph.graph import MessagesState

from ..prompts.planner_model import Plan


class State(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "en-US"
    observations: list[str] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    
    # Step execution tracking
    current_step_index: int = None
    current_step_title: str = None
    
    # Financial analysis reports
    bullish_report: str = ""
    bearish_report: str = ""
    trading_advice_report: str = ""
    comprehensive_final_report: str = "" 
    
    # Chart data
    chart_data: dict = None
    chart_config: dict = None
    has_chart_data: bool = False
    
    # Agent data storage for coordination
    technical_analysis_data: dict = None
    news_analysis_data: dict = None  
    fundamental_analysis_data: dict = None
    research_data: dict = None
    coding_analysis_data: dict = None
    
    # Data coordination flags
    agents_completed: list[str] = []
    data_collection_complete: bool = False
    current_active_agent: str = None
    
    # Internal workflow tracking
    internal_messages: list[dict] = []
    workflow_stage: str = "initialization"  # initialization, data_collection, chart_generation, final_report 