#!/usr/bin/env python3
"""
图表生成节点
为股票相关查询生成K线图
"""

import logging
import json
from typing import Literal
from dataclasses import asdict

from langchain_core.messages import HumanMessage
from langgraph.types import Command

from ..tools.stock_symbol_detector import stock_detector
from ..tools.chart_data_provider import chart_data_provider
from ..tools.chart_renderer import chart_renderer
from .types import State

logger = logging.getLogger(__name__)

async def chart_generation_node(
    state: State,
) -> Command[Literal["planner", "background_investigator"]]:
    """
    图表生成节点 - 检测股票查询并生成K线图
    
    Args:
        state: 当前状态
        
    Returns:
        Command: 下一步命令
    """
    logger.info("图表生成节点开始执行")
    
    # 检查是否已经生成过图表数据
    if state.get("has_chart_data"):
        logger.info("图表数据已存在，跳过重复生成")
        next_node = "background_investigator" if state.get("enable_background_investigation") else "planner"
        return Command(goto=next_node)
    
    try:
        # 获取用户查询
        if not state.get("messages") or len(state["messages"]) == 0:
            logger.warning("没有找到用户消息")
            return Command(goto="planner")
        
        user_query = state["messages"][-1].content
        logger.info(f"分析用户查询: {user_query}")
        
        # 检查是否为股票相关查询
        if not stock_detector.is_stock_query(user_query):
            logger.info("非股票相关查询，跳过图表生成")
            return Command(goto="planner")
        
        # 检测股票代码
        symbols = stock_detector.detect_symbols(user_query)
        if not symbols:
            logger.info("未检测到股票代码，跳过图表生成")
            return Command(goto="planner")
        
        primary_symbol = symbols[0]
        logger.info(f"检测到主要股票: {primary_symbol.symbol} ({primary_symbol.company_name})")
        
        # 获取图表数据
        chart_data = await chart_data_provider.get_chart_data(user_query)
        
        if chart_data is None:
            logger.warning(f"无法获取 {primary_symbol.symbol} 的图表数据")
            return Command(
                update={
                    "has_chart_data": False,
                    "chart_data": None,
                    "chart_config": None
                },
                goto="planner"
            )
        
        # 生成图表配置
        chart_config = chart_renderer.generate_echarts_config(chart_data)
        
        # 转换为可序列化的字典格式
        chart_data_dict = {
            "symbol": chart_data.symbol,
            "company_name": chart_data.company_name,
            "dates": chart_data.dates,
            "kline_data": chart_data.kline_data,
            "volume_data": chart_data.volume_data,
            "current_price": chart_data.current_price,
            "price_change": chart_data.price_change,
            "price_change_percent": chart_data.price_change_percent,
            "last_update": chart_data.last_update
        }
        
        logger.info(f"成功生成 {chart_data.symbol} 的图表数据和配置")
        logger.info(f"数据点数量: {len(chart_data.dates)}")
        logger.info(f"当前价格: ${chart_data.current_price:.2f}")
        logger.info(f"价格变化: {chart_data.price_change:+.2f} ({chart_data.price_change_percent:+.2f}%)")
        
        # 决定下一步：如果启用背景调查，先进行调查，否则直接进入规划器
        next_node = "background_investigator" if state.get("enable_background_investigation") else "planner"
        
        return Command(
            update={
                "has_chart_data": True,
                "chart_data": chart_data_dict,
                "chart_config": chart_config
            },
            goto=next_node
        )
        
    except Exception as e:
        logger.error(f"图表生成节点执行失败: {e}")
        
        # 即使失败也要继续流程
        return Command(
            update={
                "has_chart_data": False,
                "chart_data": None,
                "chart_config": None
            },
            goto="planner"
        )


def get_chart_summary_message(chart_data_dict: dict) -> str:
    """
    根据图表数据生成摘要消息
    
    Args:
        chart_data_dict: 图表数据字典
        
    Returns:
        str: 图表摘要消息
    """
    if not chart_data_dict:
        return ""
    
    symbol = chart_data_dict.get("symbol", "未知")
    company_name = chart_data_dict.get("company_name", "")
    current_price = chart_data_dict.get("current_price", 0)
    price_change = chart_data_dict.get("price_change", 0)
    price_change_percent = chart_data_dict.get("price_change_percent", 0)
    data_points = len(chart_data_dict.get("dates", []))
    
    change_direction = "上涨" if price_change > 0 else "下跌" if price_change < 0 else "平盘"
    
    summary = f"""
📈 **股票K线图已生成**

**股票信息:**
- 代码: {symbol}
- 公司: {company_name}
- 当前价格: ${current_price:.2f}
- 价格变化: {price_change:+.2f} ({price_change_percent:+.2f}%) - {change_direction}
- 数据周期: 最近{data_points}个交易日

图表包含K线数据和成交量信息，支持缩放和交互操作。
"""
    
    return summary.strip() 