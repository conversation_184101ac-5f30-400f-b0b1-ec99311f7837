#!/usr/bin/env python3
"""
模拟LLM管理器
在网络连接不可用时提供备用服务
"""

import os
import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator

logger = logging.getLogger(__name__)

class MockLLMManager:
    """模拟LLM管理器 - 用于演示和测试"""
    
    def __init__(self):
        self.api_key = "mock-api-key"
        self.model = "mock-model"
        self.base_url = "http://localhost"
        logger.info("模拟LLM管理器初始化成功")
    
    async def get_completion(self, prompt: str, system_message: Optional[str] = None,
                           temperature: float = 0.7, max_tokens: int = 2000) -> str:
        """
        获取模拟LLM完成响应
        """
        
        logger.info(f"模拟LLM调用: {prompt[:50]}...")
        
        # 模拟处理延迟
        await asyncio.sleep(0.5)
        
        # 根据提示内容返回不同的响应
        if "技术分析" in prompt or "technical" in prompt.lower():
            return """
            基于您的请求，我进行了技术分析：
            
            ## 技术指标分析
            - RSI: 65 (中性偏强)
            - MACD: 看涨信号
            - 移动平均线: 多头排列
            - 成交量: 温和放量
            
            ## 结论
            技术面显示偏向看涨的信号，但需注意风险控制。
            
            *注意：这是模拟响应，仅用于演示系统功能*
            """
            
        elif "基本面" in prompt or "fundamental" in prompt.lower():
            return """
            基于您的请求，我进行了基本面分析：
            
            ## 财务指标
            - P/E 比率: 25.6
            - ROE: 18.5%
            - 债务比率: 适中
            - 现金流: 健康
            
            ## 结论
            公司基本面良好，财务状况稳健。
            
            *注意：这是模拟响应，仅用于演示系统功能*
            """
            
        elif "意图分析" in system_message if system_message else False:
            return json.dumps({
                "analysis_type": "技术分析",
                "confidence": 0.85,
                "extracted_symbols": ["AAPL"],
                "keywords": ["技术面", "股票", "分析"],
                "user_intent": "用户想要进行股票技术分析"
            }, ensure_ascii=False)
            
        elif "报告" in prompt or "report" in prompt.lower():
            return """
            # 投资分析报告
            
            ## 执行摘要
            本报告对目标标的进行了全面分析，综合技术面和基本面指标。
            
            ## 详细分析
            ### 技术分析
            - 趋势：上升趋势
            - 支撑位：$145
            - 阻力位：$165
            
            ### 基本面分析
            - 估值：合理
            - 成长性：良好
            - 风险：中等
            
            ## 投资建议
            建议适度配置，关注市场波动。
            
            ## 风险提示
            市场存在不确定性，请谨慎投资。
            
            ## 免责声明
            本报告仅供参考，不构成投资建议。
            
            *注意：这是模拟响应，仅用于演示系统功能*
            """
        
        else:
            return """
            感谢您使用AI金融分析系统！
            
            我是您的智能分析助手，可以为您提供：
            - 📈 技术分析
            - 📊 基本面分析  
            - 🔍 市场研究
            - 📝 投资报告
            
            由于网络连接限制，目前运行在模拟模式下。
            您可以尝试询问股票分析、技术指标等问题。
            
            *注意：这是模拟响应，仅用于演示系统功能*
            """
    
    async def get_streaming_completion(self, prompt: str, system_message: Optional[str] = None,
                                     temperature: float = 0.7, 
                                     max_tokens: int = 2000) -> AsyncGenerator[str, None]:
        """
        获取模拟流式LLM响应
        """
        
        # 获取完整响应
        full_response = await self.get_completion(prompt, system_message, temperature, max_tokens)
        
        # 模拟流式输出
        chunk_size = 20
        for i in range(0, len(full_response), chunk_size):
            chunk = full_response[i:i+chunk_size]
            yield chunk
            await asyncio.sleep(0.05)  # 模拟流式延迟
    
    async def analyze_user_intent(self, user_input: str) -> Dict[str, Any]:
        """
        模拟用户意图分析
        """
        
        await asyncio.sleep(0.3)  # 模拟处理时间
        
        # 简单的关键词匹配
        if any(word in user_input for word in ["技术", "MACD", "RSI", "K线"]):
            analysis_type = "技术分析"
            confidence = 0.9
        elif any(word in user_input for word in ["基本面", "财务", "PE", "ROE"]):
            analysis_type = "基本面分析"
            confidence = 0.9
        elif any(word in user_input for word in ["行业", "板块", "产业"]):
            analysis_type = "行业分析"
            confidence = 0.8
        elif any(word in user_input for word in ["市场", "大盘", "指数"]):
            analysis_type = "市场分析"
            confidence = 0.8
        else:
            analysis_type = "个股查询"
            confidence = 0.6
        
        # 提取股票代码
        symbols = []
        common_symbols = ["AAPL", "TSLA", "MSFT", "GOOGL", "AMZN", "苹果", "特斯拉", "微软"]
        for symbol in common_symbols:
            if symbol in user_input:
                symbols.append(symbol)
        
        return {
            "analysis_type": analysis_type,
            "confidence": confidence,
            "extracted_symbols": symbols,
            "keywords": user_input.split()[:5],
            "user_intent": f"用户想要进行{analysis_type}"
        }
    
    async def generate_report(self, analysis_data: Dict[str, Any], 
                            report_type: str = "comprehensive") -> str:
        """
        生成模拟分析报告
        """
        
        await asyncio.sleep(1.0)  # 模拟报告生成时间
        
        symbol = analysis_data.get('symbol', 'UNKNOWN')
        
        return f"""
# {symbol} - {report_type}分析报告

## 执行摘要
本报告对{symbol}进行了全面的{report_type}分析，基于模拟数据生成。

## 详细分析

### 价格表现
- 当前价格: ${analysis_data.get('price', 'N/A')}
- 日涨跌: {analysis_data.get('change', 'N/A')}%

### 技术指标
{json.dumps(analysis_data.get('technical_indicators', {}), ensure_ascii=False, indent=2)}

### 分析结论
基于当前数据，{symbol}显示出以下特征：
1. 技术面分析显示中性偏强信号
2. 成交量温和，市场情绪稳定
3. 短期内可能面临调整压力

## 投资建议
- 建议类型: 谨慎持有
- 风险等级: 中等
- 建议仓位: 5-10%

## 风险提示
1. 市场波动风险
2. 行业周期风险
3. 宏观经济风险

## 免责声明
本报告基于模拟数据生成，仅用于系统功能演示，不构成实际投资建议。

*模拟报告生成时间: {asyncio.get_event_loop().time()}*
"""
    
    def is_available(self) -> bool:
        """检查模拟LLM是否可用"""
        return True 