#!/usr/bin/env python3
"""
LLM管理器
处理与大语言模型的交互，支持Google Gemini API (OpenAI兼容)
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
import asyncio

logger = logging.getLogger(__name__)

# 导入OpenAI客户端和模拟LLM管理器
try:
    from openai import OpenAI, AsyncOpenAI
except ImportError:
    logger.error("OpenAI库未安装，请运行: pip install openai")
    OpenAI = None
    AsyncOpenAI = None

try:
    from .mock_llm import MockLLMManager
except ImportError:
    from mock_llm import MockLLMManager

class LLMManager:
    """LLM管理器 - 使用Gemini的OpenAI兼容API，简化调用逻辑"""
    
    def __init__(self):
        self.client = None
        self.async_client = None
        self.model = None
        self.mock_mode = False
        self.mock_llm = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化Gemini OpenAI兼容客户端"""
        try:
            # 检查OpenAI库是否可用
            if OpenAI is None or AsyncOpenAI is None:
                logger.warning("OpenAI库不可用，启用模拟模式")
                self._enable_mock_mode()
                return
            
            # 从环境变量读取配置
            api_key = os.getenv('GEMINI_API_KEY')
            model = os.getenv('GEMINI_MODEL', 'gemini-2.0-flash')
            base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
            
            if not api_key:
                logger.warning("GEMINI_API_KEY环境变量未设置，启用模拟模式")
                self._enable_mock_mode()
                return
            
            # 创建OpenAI兼容客户端
            self.client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            self.async_client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            self.model = model
            
            logger.info(f"Gemini OpenAI兼容客户端初始化成功，使用模型: {model}")
            
        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            logger.warning("启用模拟模式作为备用方案")
            self._enable_mock_mode()
    
    def _enable_mock_mode(self):
        """启用模拟模式"""
        self.mock_mode = True
        self.mock_llm = MockLLMManager()
        logger.info("已启用模拟LLM模式")

    async def get_completion(self, prompt: str, system_message: Optional[str] = None,
                           temperature: float = 0.7, max_tokens: int = 2000) -> str:
        """
        获取LLM完成响应
        
        Args:
            prompt: 用户提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            str: LLM响应内容
        """
        
        # 如果是模拟模式，使用模拟LLM
        if self.mock_mode:
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
        
        if not self.async_client:
            logger.warning("Gemini客户端不可用，回退到模拟模式")
            self._enable_mock_mode()
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
        
        try:
            # 构建消息列表
            messages = []
            
            if system_message:
                messages.append({
                    "role": "system", 
                    "content": system_message
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            logger.info(f"正在调用Gemini API (OpenAI兼容): {self.model}")
            
            # 使用OpenAI兼容API调用
            response = await self.async_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            logger.info("Gemini API调用成功")
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Gemini API调用错误: {e}")
            logger.warning("Gemini API调用失败，回退到模拟模式")
            self._enable_mock_mode()
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
    
    async def get_streaming_completion(self, prompt: str, system_message: Optional[str] = None,
                                     temperature: float = 0.7, 
                                     max_tokens: int = 2000) -> AsyncGenerator[str, None]:
        """
        获取流式LLM响应
        
        Args:
            prompt: 用户提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大token数
            
        Yields:
            str: 流式响应内容片段
        """
        
        # 如果是模拟模式，使用模拟LLM
        if self.mock_mode:
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
            return
        
        if not self.async_client:
            logger.warning("Gemini客户端不可用，回退到模拟模式")
            self._enable_mock_mode()
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
            return
        
        try:
            # 构建消息列表
            messages = []
            
            if system_message:
                messages.append({
                    "role": "system",
                    "content": system_message
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            logger.info(f"正在调用Gemini流式API (OpenAI兼容): {self.model}")
            
            # 使用OpenAI兼容流式API调用
            stream = await self.async_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            logger.info("Gemini流式API调用成功")
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Gemini流式调用错误: {e}")
            logger.warning("Gemini流式调用失败，回退到模拟模式")
            self._enable_mock_mode()
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
    
    async def analyze_user_intent(self, user_input: str) -> Dict[str, Any]:
        """
        分析用户意图
        
        Args:
            user_input: 用户输入
            
        Returns:
            Dict: 分析结果
        """
        
        system_message = """
        你是一个专业的金融分析助手。请分析用户的输入，判断用户想要进行什么类型的分析。

        可能的分析类型包括：
        1. 技术分析 - 用户想了解股票的技术指标、图表形态等
        2. 基本面分析 - 用户想了解公司的财务状况、估值等
        3. 行业分析 - 用户想了解某个行业的情况
        4. 市场分析 - 用户想了解整体市场情况
        5. 个股查询 - 用户想了解某只股票的基本信息
        6. 投资建议 - 用户寻求投资建议
        7. 风险评估 - 用户想了解投资风险
        8. 其他

        请以JSON格式返回分析结果，包含：
        - analysis_type: 分析类型
        - confidence: 置信度(0-1)
        - extracted_symbols: 提取到的股票代码列表
        - keywords: 关键词列表
        - user_intent: 用户意图描述
        """
        
        prompt = f"请分析以下用户输入：\n\n{user_input}"
        
        try:
            # 如果是模拟模式，直接使用模拟分析
            if self.mock_mode:
                return await self.mock_llm.analyze_user_intent(user_input)
            
            response = await self.get_completion(prompt, system_message)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                # 如果无法解析JSON，返回默认结果
                return {
                    "analysis_type": "其他",
                    "confidence": 0.5,
                    "extracted_symbols": [],
                    "keywords": [],
                    "user_intent": response
                }
                
        except Exception as e:
            logger.error(f"用户意图分析错误: {e}")
            return {
                "analysis_type": "其他",
                "confidence": 0.0,
                "extracted_symbols": [],
                "keywords": [],
                "user_intent": "分析失败",
                "error": str(e)
            }
    
    async def generate_report(self, analysis_data: Dict[str, Any], 
                            report_type: str = "comprehensive") -> str:
        """
        生成分析报告
        
        Args:
            analysis_data: 分析数据
            report_type: 报告类型
            
        Returns:
            str: 生成的报告
        """
        
        system_message = f"""
        你是一个专业的金融分析报告撰写专家。请基于提供的分析数据生成一份{report_type}分析报告。

        报告要求：
        1. 结构清晰，逻辑严谨
        2. 数据准确，分析客观
        3. 语言专业，易于理解
        4. 包含具体的投资建议
        5. 提及相关风险

        报告格式：
        # 投资分析报告

        ## 执行摘要
        
        ## 详细分析
        
        ## 投资建议
        
        ## 风险提示
        
        ## 免责声明
        """
        
        prompt = f"""
        请基于以下分析数据生成投资分析报告：

        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}
        """
        
        try:
            # 如果是模拟模式，直接使用模拟报告生成
            if self.mock_mode:
                return await self.mock_llm.generate_report(analysis_data, report_type)
            
            report = await self.get_completion(prompt, system_message, temperature=0.3)
            return report
            
        except Exception as e:
            logger.error(f"报告生成错误: {e}")
            return f"报告生成失败: {str(e)}"
    
    def is_available(self) -> bool:
        """检查LLM是否可用"""
        return (self.client is not None and self.async_client is not None) or self.mock_mode 