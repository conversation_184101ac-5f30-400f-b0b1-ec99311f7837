# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import re
import logging

logger = logging.getLogger(__name__)


def repair_json_output(content: str) -> str:
    """
    修复和清理 JSON 输出
    
    Args:
        content: 可能包含 JSON 的字符串内容
        
    Returns:
        修复后的 JSON 字符串
    """
    if not content or not content.strip():
        return "{}"
    
    # 移除可能的 markdown 代码块标记
    content = re.sub(r'```json\s*', '', content)
    content = re.sub(r'```\s*$', '', content)
    
    # 尝试提取 JSON 部分
    json_match = re.search(r'\{.*\}', content, re.DOTALL)
    if json_match:
        json_str = json_match.group()
    else:
        # 如果没有找到完整的 JSON，返回原内容
        return content.strip()
    
    try:
        # 验证 JSON 是否有效
        json.loads(json_str)
        return json_str
    except json.JSONDecodeError as e:
        logger.warning(f"JSON 解析错误: {e}")
        
        # 尝试一些常见的修复
        json_str = json_str.strip()
        
        # 修复常见的尾部逗号问题
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        
        # 修复缺失的引号
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)
        
        try:
            # 再次验证
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            # 如果还是失败，返回一个基本的 JSON 结构
            logger.error(f"无法修复 JSON: {content[:200]}...")
            return '{"error": "无法解析的JSON内容"}'
