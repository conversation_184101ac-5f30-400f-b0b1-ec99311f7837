#!/usr/bin/env python3
"""
智能体管理器
管理各种专业分析智能体
"""

import logging
from typing import Dict, List, Any, Optional
import pandas as pd
from datetime import datetime

from .llm import LLMManager
from .tools import (
    get_web_search_tool,
    python_repl_tool,
    crawl_tool,
)

# 尝试导入新的智能体创建函数
try:
    from .agents.agents import create_agent
    NEW_AGENT_SYSTEM_AVAILABLE = True
except ImportError:
    NEW_AGENT_SYSTEM_AVAILABLE = False

# 尝试导入AkShare工具
try:
    from .tools.akshare import (
        stock_news_em_tool,
        comprehensive_stock_news_tool,
        us_stock_spot_tool,
        us_stock_hist_tool,
        us_stock_search_tool,
        get_famous_stock_data_tool,
        AKSHARE_AVAILABLE,
    )
except ImportError:
    AKSHARE_AVAILABLE = False

# 尝试导入技术分析工具
try:
    from .tools.technical_indicators import (
        calculate_technical_indicators,
        fibonacci_retracement_levels,
    )
    from .tools.divergence_analysis import detect_price_macd_divergence
    TECHNICAL_ANALYSIS_AVAILABLE = True
except ImportError:
    TECHNICAL_ANALYSIS_AVAILABLE = False

# 尝试导入Yahoo Finance工具
try:
    from .tools.yahoo_finance_news import yahoo_finance_news_tool
    YAHOO_FINANCE_AVAILABLE = True
except ImportError:
    YAHOO_FINANCE_AVAILABLE = False

logger = logging.getLogger(__name__)

class AgentManager:
    """智能体管理器 - 兼容旧系统的同时支持新的多智能体系统"""
    
    def __init__(self):
        self.llm_manager = LLMManager()
        self.available_tools = self._initialize_tools()
        logger.info(f"智能体管理器初始化完成，新系统可用: {NEW_AGENT_SYSTEM_AVAILABLE}")
        
    def _initialize_tools(self) -> Dict[str, Any]:
        """初始化可用工具"""
        tools = {
            "web_search": get_web_search_tool(max_search_results=10),
            "python_repl": python_repl_tool,
            "crawl": crawl_tool,
        }
        
        # 添加AkShare工具
        if AKSHARE_AVAILABLE:
            tools.update({
                "stock_news": stock_news_em_tool,
                "comprehensive_stock_news": comprehensive_stock_news_tool,
                "us_stock_spot": us_stock_spot_tool,
                "us_stock_hist": us_stock_hist_tool,
                "us_stock_search": us_stock_search_tool,
                "famous_stock_data": get_famous_stock_data_tool,
            })
            logger.info("AkShare工具已加载")
        else:
            logger.warning("AkShare工具不可用")
        
        # 添加技术分析工具
        if TECHNICAL_ANALYSIS_AVAILABLE:
            tools.update({
                "technical_indicators": calculate_technical_indicators,
                "fibonacci_levels": fibonacci_retracement_levels,
                "divergence_analysis": detect_price_macd_divergence,
            })
            logger.info("技术分析工具已加载")
        else:
            logger.warning("技术分析工具不可用")
        
        # 添加Yahoo Finance工具
        if YAHOO_FINANCE_AVAILABLE:
            tools["yahoo_finance_news"] = yahoo_finance_news_tool
            logger.info("Yahoo Finance工具已加载")
        else:
            logger.warning("Yahoo Finance工具不可用")
        
        logger.info(f"总共加载了 {len(tools)} 个工具")
        return tools

    def create_specialized_agent(self, agent_name: str, agent_type: str, tools: list, prompt_template: str):
        """
        创建专业化的智能体
        
        Args:
            agent_name: 智能体名称
            agent_type: 智能体类型
            tools: 工具列表
            prompt_template: 提示词模板
            
        Returns:
            创建的智能体或None（如果新系统不可用）
        """
        if NEW_AGENT_SYSTEM_AVAILABLE:
            try:
                return create_agent(agent_name, agent_type, tools, prompt_template)
            except Exception as e:
                logger.error(f"创建新智能体失败: {e}")
        
        logger.warning("新智能体系统不可用，回退到传统方法")
        return None
        
    async def get_technical_analysis(self, symbol: str, stock_data: pd.DataFrame, 
                                   technical_factors: Dict[str, float]) -> Dict[str, Any]:
        """
        技术分析师智能体
        
        Args:
            symbol: 股票代码
            stock_data: 股票数据
            technical_factors: 技术指标因子
            
        Returns:
            Dict: 技术分析结果
        """
        
        # 构建技术分析提示词
        prompt = f"""
        作为专业的股票技术分析师，请对股票 {symbol} 进行全面的技术分析。

        ## 股票数据概况
        - 股票代码: {symbol}
        - 数据时间范围: {stock_data.index.min()} 到 {stock_data.index.max()}
        - 数据条数: {len(stock_data)}
        - 最新价格: {stock_data['close'].iloc[-1]:.2f}

        ## 技术指标数据
        {self._format_technical_factors(technical_factors)}

        ## 分析要求
        请按照以下结构进行专业技术分析：

        ### 1. 趋势分析
        - 主要趋势方向（上升/下降/震荡）
        - 移动平均线排列情况
        - 趋势强度评估

        ### 2. 支撑阻力位分析
        - 关键支撑位
        - 关键阻力位
        - 价格突破情况

        ### 3. 技术指标信号分析
        - RSI超买超卖情况
        - MACD金叉死叉信号
        - 布林带位置分析
        - 其他技术指标综合判断

        ### 4. 成交量分析
        - 量价配合情况
        - 成交量变化趋势

        ### 5. 综合判断和操作建议
        - 短期走势预判（1-2周）
        - 中期走势预判（1-3个月）
        - 具体操作建议（买入/卖出/持有）
        - 风险提示

        请提供专业、客观、详细的分析报告。
        """
        
        try:
            analysis_result = await self.llm_manager.get_completion(prompt)
            
            return {
                "agent_type": "技术分析师",
                "symbol": symbol,
                "analysis_content": analysis_result,
                "technical_factors": technical_factors,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"技术分析师智能体错误: {e}")
            return {
                "agent_type": "技术分析师",
                "symbol": symbol,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_fundamental_analysis(self, symbol: str, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        基本面分析师智能体
        
        Args:
            symbol: 股票代码
            fundamental_data: 基本面数据
            
        Returns:
            Dict: 基本面分析结果
        """
        
        prompt = f"""
        作为专业的基本面分析师，请对股票 {symbol} 进行基本面分析。

        ## 基本面数据
        {self._format_fundamental_data(fundamental_data)}

        请从以下角度进行分析：
        1. 估值水平分析（PE、PB、PS等）
        2. 盈利能力分析（ROE、ROA等）
        3. 财务健康状况
        4. 成长性分析
        5. 行业地位和竞争优势
        6. 投资价值评估

        请提供专业的基本面分析报告。
        """
        
        try:
            analysis_result = await self.llm_manager.get_completion(prompt)
            
            return {
                "agent_type": "基本面分析师",
                "symbol": symbol,
                "analysis_content": analysis_result,
                "fundamental_data": fundamental_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"基本面分析师智能体错误: {e}")
            return {
                "agent_type": "基本面分析师",
                "symbol": symbol,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_risk_analysis(self, symbol: str, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        风险分析师智能体
        
        Args:
            symbol: 股票代码
            risk_data: 风险数据
            
        Returns:
            Dict: 风险分析结果
        """
        
        prompt = f"""
        作为专业的风险分析师，请对股票 {symbol} 进行风险评估。

        ## 风险数据
        {self._format_risk_data(risk_data)}

        请从以下角度进行风险分析：
        1. 市场风险评估
        2. 行业风险分析
        3. 公司特定风险
        4. 流动性风险
        5. 波动率分析
        6. 风险控制建议

        请提供专业的风险分析报告。
        """
        
        try:
            analysis_result = await self.llm_manager.get_completion(prompt)
            
            return {
                "agent_type": "风险分析师",
                "symbol": symbol,
                "analysis_content": analysis_result,
                "risk_data": risk_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"风险分析师智能体错误: {e}")
            return {
                "agent_type": "风险分析师",
                "symbol": symbol,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_tool_enhanced_analysis(self, symbol: str, query: str) -> Dict[str, Any]:
        """
        使用工具增强的智能分析
        
        Args:
            symbol: 股票代码
            query: 用户查询
            
        Returns:
            Dict: 增强分析结果
        """
        
        results = {
            "agent_type": "工具增强分析师",
            "symbol": symbol,
            "query": query,
            "tool_results": {},
            "analysis_content": "",
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 1. 获取股票新闻（如果AkShare可用）
            if AKSHARE_AVAILABLE and "stock_news" in self.available_tools:
                try:
                    news_result = self.available_tools["stock_news"].invoke({"symbol": symbol})
                    results["tool_results"]["stock_news"] = news_result
                    logger.info(f"成功获取 {symbol} 的新闻数据")
                except Exception as e:
                    logger.error(f"获取股票新闻失败: {e}")
                    results["tool_results"]["stock_news"] = f"获取失败: {str(e)}"
            
            # 2. 获取美股实时数据（如果是美股）
            if AKSHARE_AVAILABLE and "us_stock_spot" in self.available_tools:
                try:
                    us_spot_result = self.available_tools["us_stock_spot"].invoke({"max_rows": 50})
                    results["tool_results"]["us_stock_spot"] = us_spot_result
                    logger.info("成功获取美股实时数据")
                except Exception as e:
                    logger.error(f"获取美股数据失败: {e}")
                    results["tool_results"]["us_stock_spot"] = f"获取失败: {str(e)}"
            
            # 3. 进行网络搜索获取最新信息
            if "web_search" in self.available_tools:
                try:
                    search_query = f"{symbol} 股票 最新消息 分析"
                    search_result = self.available_tools["web_search"].invoke({"query": search_query})
                    results["tool_results"]["web_search"] = search_result
                    logger.info(f"成功搜索 {symbol} 相关信息")
                except Exception as e:
                    logger.error(f"网络搜索失败: {e}")
                    results["tool_results"]["web_search"] = f"搜索失败: {str(e)}"
            
            # 4. 使用LLM综合分析所有信息
            analysis_prompt = f"""
            作为专业的股票分析师，请基于以下工具获取的信息，对股票 {symbol} 进行综合分析。

            用户查询: {query}

            ## 工具获取的数据
            {self._format_tool_results(results["tool_results"])}

            请提供：
            1. 基于最新信息的股票分析
            2. 新闻和市场情绪分析
            3. 技术面和基本面综合判断
            4. 投资建议和风险提示
            5. 回答用户的具体问题

            请确保分析客观、专业、有依据。
            """
            
            analysis_result = await self.llm_manager.get_completion(analysis_prompt)
            results["analysis_content"] = analysis_result
            
        except Exception as e:
            logger.error(f"工具增强分析错误: {e}")
            results["error"] = str(e)
        
        return results
    
    async def get_comprehensive_analysis(self, symbol: str, all_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合分析师智能体
        整合所有分析结果，提供综合投资建议
        
        Args:
            symbol: 股票代码
            all_data: 所有分析数据
            
        Returns:
            Dict: 综合分析结果
        """
        
        prompt = f"""
        作为综合投资分析师，请对股票 {symbol} 进行综合投资分析。

        ## 综合数据
        {self._format_comprehensive_data(all_data)}

        请提供以下内容：
        1. 投资亮点总结
        2. 主要风险因素
        3. 综合评分（1-10分）
        4. 投资建议（强烈买入/买入/持有/卖出/强烈卖出）
        5. 目标价位区间
        6. 投资时间框架建议
        7. 仓位管理建议

        请提供专业的综合投资分析报告。
        """
        
        try:
            analysis_result = await self.llm_manager.get_completion(prompt)
            
            return {
                "agent_type": "综合分析师",
                "symbol": symbol,
                "analysis_content": analysis_result,
                "all_data": all_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"综合分析师智能体错误: {e}")
            return {
                "agent_type": "综合分析师",
                "symbol": symbol,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _format_technical_factors(self, factors: Dict[str, float]) -> str:
        """格式化技术指标数据"""
        formatted = []
        for key, value in factors.items():
            if isinstance(value, float):
                formatted.append(f"- {key}: {value:.4f}")
            else:
                formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _format_fundamental_data(self, data: Dict[str, Any]) -> str:
        """格式化基本面数据"""
        formatted = []
        for key, value in data.items():
            formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _format_risk_data(self, data: Dict[str, Any]) -> str:
        """格式化风险数据"""
        formatted = []
        for key, value in data.items():
            formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _format_comprehensive_data(self, data: Dict[str, Any]) -> str:
        """格式化综合数据"""
        formatted = []
        for key, value in data.items():
            if isinstance(value, dict):
                formatted.append(f"## {key}")
                for sub_key, sub_value in value.items():
                    formatted.append(f"- {sub_key}: {sub_value}")
            else:
                formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _format_tool_results(self, tool_results: Dict[str, Any]) -> str:
        """格式化工具结果"""
        if not tool_results:
            return "暂无工具数据"
        
        formatted = []
        for tool_name, result in tool_results.items():
            formatted.append(f"### {tool_name} 结果:")
            if isinstance(result, str):
                # 限制字符串长度以避免过长
                if len(result) > 1000:
                    formatted.append(f"{result[:1000]}...(结果已截断)")
                else:
                    formatted.append(result)
            else:
                formatted.append(str(result))
            formatted.append("")  # 添加空行
        
        return "\n".join(formatted)