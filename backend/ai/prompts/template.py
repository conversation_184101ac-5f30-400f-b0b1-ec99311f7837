# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import dataclasses
from datetime import datetime
from jinja2 import Environment, FileSystemLoader, select_autoescape
from langgraph.prebuilt.chat_agent_executor import AgentState
from langchain_core.messages import SystemMessage
from ..config.configuration import Configuration

# Initialize Jinja2 environment
env = Environment(
    loader=FileSystemLoader(os.path.dirname(__file__)),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)


def get_prompt_template(prompt_name: str) -> str:
    """
    Load and return a prompt template using Jinja2.

    Args:
        prompt_name: Name of the prompt template file (without .md extension)

    Returns:
        The template string with proper variable substitution syntax
    """
    try:
        template = env.get_template(f"{prompt_name}.md")
        return template.render()
    except Exception as e:
        raise ValueError(f"Error loading template {prompt_name}: {e}")


def apply_prompt_template(
    prompt_name: str, state: AgentState, configurable: Configuration = None
) -> list:
    """
    Apply template variables to a prompt template and return formatted messages.

    Args:
        prompt_name: Name of the prompt template to use
        state: Current agent state containing variables to substitute

    Returns:
        List of LangChain message objects with the system prompt as the first message
    """
    # Convert state to dict for template rendering
    state_vars = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        **state,
    }

    # Add configurable variables
    if configurable:
        state_vars.update(dataclasses.asdict(configurable))

    try:
        template = env.get_template(f"{prompt_name}.md")
        system_prompt = template.render(**state_vars)
        
        # 验证system_prompt不为空
        if not system_prompt or system_prompt.strip() == "":
            system_prompt = f"系统提示模板 {prompt_name} 为空，使用默认提示"
        
        # 验证state中的messages
        messages = state.get("messages", [])
        validated_messages = []
        
        for msg in messages:
            if hasattr(msg, 'content'):
                if msg.content is None:
                    # 创建一个新的消息对象，避免修改原对象
                    from langchain_core.messages import HumanMessage
                    validated_messages.append(HumanMessage(content="默认消息内容"))
                elif isinstance(msg.content, str) and msg.content.strip() == "":
                    from langchain_core.messages import HumanMessage
                    validated_messages.append(HumanMessage(content="默认消息内容"))
                else:
                    validated_messages.append(msg)
            else:
                validated_messages.append(msg)
        
        return [SystemMessage(content=system_prompt)] + validated_messages
    except Exception as e:
        raise ValueError(f"Error applying template {prompt_name}: {e}")


def apply_financial_report_template(
    prompt_name: str, 
    state: AgentState, 
    additional_context: dict = None,
    configurable: Configuration = None
) -> list:
    """
    Apply template variables specifically for financial reporting nodes.

    Args:
        prompt_name: Name of the financial report template to use
        state: Current agent state containing variables to substitute
        additional_context: Additional context for financial reports (e.g., previous reports)
        configurable: Configuration object

    Returns:
        List of LangChain message objects with the system prompt as the first message
    """
    # Convert state to dict for template rendering
    state_vars = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        **state,
    }

    # Add additional context for financial reports
    if additional_context:
        state_vars.update(additional_context)

    # Add configurable variables
    if configurable:
        state_vars.update(dataclasses.asdict(configurable))

    try:
        template = env.get_template(f"{prompt_name}.md")
        system_prompt = template.render(**state_vars)
        
        # 验证system_prompt不为空
        if not system_prompt or system_prompt.strip() == "":
            system_prompt = f"金融报告模板 {prompt_name} 为空，使用默认提示"
        
        # 验证state中的messages
        messages = state.get("messages", [])
        validated_messages = []
        
        for msg in messages:
            if hasattr(msg, 'content'):
                if msg.content is None:
                    from langchain_core.messages import HumanMessage
                    validated_messages.append(HumanMessage(content="默认金融分析消息内容"))
                elif isinstance(msg.content, str) and msg.content.strip() == "":
                    from langchain_core.messages import HumanMessage
                    validated_messages.append(HumanMessage(content="默认金融分析消息内容"))
                else:
                    validated_messages.append(msg)
            else:
                validated_messages.append(msg)
        
        return [SystemMessage(content=system_prompt)] + validated_messages
    except Exception as e:
        raise ValueError(f"Error applying financial template {prompt_name}: {e}")
