#!/usr/bin/env python3
"""
AkShare数据迁移测试脚本
测试新的AkShare数据管理器功能
"""

import os
import sys
import asyncio
import logging
from typing import List

# 添加backend模块到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import init_data_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('akshare_migration_test.log')
    ]
)

logger = logging.getLogger(__name__)

class AkShareMigrationTester:
    """AkShare数据迁移测试器"""
    
    def __init__(self):
        self.data_manager = init_data_manager()  # 不传入tushare_token
        
    def test_stock_list_download(self):
        """测试股票列表下载功能"""
        logger.info("=== 测试股票列表下载功能 ===")
        
        if not self.data_manager.akshare_data_manager:
            logger.error("AkShare数据管理器未初始化")
            return False
        
        success_count = 0
        
        # 测试A股列表下载
        try:
            logger.info("测试A股列表下载...")
            cn_stocks = self.data_manager.akshare_data_manager.download_stock_list('CN')
            if not cn_stocks.empty:
                logger.info(f"成功下载{len(cn_stocks)}只A股信息")
                logger.info(f"A股样本: {cn_stocks.head()}")
                success_count += 1
            else:
                logger.warning("A股列表为空")
        except Exception as e:
            logger.error(f"A股列表下载失败: {e}")
        
        # 测试美股列表下载  
        try:
            logger.info("测试美股列表下载...")
            us_stocks = self.data_manager.akshare_data_manager.download_stock_list('US')
            if not us_stocks.empty:
                logger.info(f"成功下载{len(us_stocks)}只美股信息")
                logger.info(f"美股样本: {us_stocks.head()}")
                success_count += 1
            else:
                logger.warning("美股列表为空")
        except Exception as e:
            logger.error(f"美股列表下载失败: {e}")
        
        # 测试港股列表下载
        try:
            logger.info("测试港股列表下载...")
            hk_stocks = self.data_manager.akshare_data_manager.download_stock_list('HK')
            if not hk_stocks.empty:
                logger.info(f"成功下载{len(hk_stocks)}只港股信息")
                logger.info(f"港股样本: {hk_stocks.head()}")
                success_count += 1
            else:
                logger.warning("港股列表为空")
        except Exception as e:
            logger.error(f"港股列表下载失败: {e}")
        
        logger.info(f"股票列表下载测试完成，成功率: {success_count}/3")
        return success_count >= 2  # 至少2个市场成功
    
    def test_stock_data_download(self):
        """测试股票数据下载功能"""
        logger.info("=== 测试股票数据下载功能 ===")
        
        # 测试股票代码
        test_stocks = {
            'CN': ['000001', '000002', '600000'],  # A股
            'US': ['AAPL', 'MSFT', 'GOOGL'],       # 美股
            'HK': ['00700.HK', '09988.HK']         # 港股
        }
        
        success_count = 0
        total_tests = 0
        
        for market, stocks in test_stocks.items():
            for stock in stocks:
                total_tests += 1
                try:
                    logger.info(f"测试下载{market}股票 {stock} 的数据...")
                    
                    # 使用智能数据获取
                    df = self.data_manager.get_stock_data_intelligent(
                        stock, 
                        start_date='20230101', 
                        end_date='20231201',
                        force_download=True
                    )
                    
                    if not df.empty:
                        logger.info(f"成功下载{stock}的{len(df)}条数据")
                        logger.info(f"数据样本:\n{df.head()}")
                        success_count += 1
                    else:
                        logger.warning(f"{stock}数据为空")
                        
                except Exception as e:
                    logger.error(f"下载{stock}数据失败: {e}")
        
        logger.info(f"股票数据下载测试完成，成功率: {success_count}/{total_tests}")
        return success_count >= total_tests * 0.6  # 60%成功率算通过
    
    def test_stock_recognition(self):
        """测试股票代码识别功能"""
        logger.info("=== 测试股票代码识别功能 ===")
        
        test_cases = [
            ('000001', 'CN', 'A股'),
            ('600000', 'CN', 'A股'),
            ('AAPL', 'US', '美股'),
            ('MSFT', 'US', '美股'),
            ('00700.HK', 'HK', '港股'),
            ('09988.HK', 'HK', '港股'),
        ]
        
        success_count = 0
        
        for code, expected_market, description in test_cases:
            try:
                is_cn = self.data_manager._is_cn_stock(code)
                is_us = self.data_manager._is_us_stock(code)
                is_hk = self.data_manager._is_hk_stock(code)
                
                detected_market = None
                if is_cn:
                    detected_market = 'CN'
                elif is_us:
                    detected_market = 'US'
                elif is_hk:
                    detected_market = 'HK'
                
                if detected_market == expected_market:
                    logger.info(f"✓ {code} 正确识别为 {description}")
                    success_count += 1
                else:
                    logger.error(f"✗ {code} 识别错误，期望 {expected_market}，实际 {detected_market}")
                    
            except Exception as e:
                logger.error(f"识别{code}时出错: {e}")
        
        logger.info(f"股票代码识别测试完成，成功率: {success_count}/{len(test_cases)}")
        return success_count == len(test_cases)
    
    def test_database_operations(self):
        """测试数据库操作功能"""
        logger.info("=== 测试数据库操作功能 ===")
        
        try:
            # 测试获取可用股票列表
            available_stocks = self.data_manager.get_available_stocks()
            logger.info(f"数据库中有{len(available_stocks)}只股票")
            
            if available_stocks:
                # 测试获取股票数据
                test_stock = available_stocks[0]
                stock_data = self.data_manager.get_stock_data(test_stock)
                logger.info(f"从数据库获取{test_stock}的{len(stock_data)}条数据")
                
                return True
            else:
                logger.warning("数据库中没有股票数据")
                return False
                
        except Exception as e:
            logger.error(f"数据库操作测试失败: {e}")
            return False
    
    def test_initialize_data(self):
        """测试数据初始化功能"""
        logger.info("=== 测试数据初始化功能 ===")
        
        # 测试用的少量股票代码
        test_stocks = ['000001', '000002', 'AAPL', '00700.HK']
        
        try:
            logger.info(f"测试初始化股票: {test_stocks}")
            self.data_manager.initialize_data(test_stocks, start_date='20231101')
            
            # 检查初始化结果
            available_stocks = self.data_manager.get_available_stocks()
            initialized_count = len([s for s in test_stocks if s in available_stocks])
            
            logger.info(f"成功初始化{initialized_count}/{len(test_stocks)}只股票")
            return initialized_count >= len(test_stocks) * 0.5  # 50%成功率
            
        except Exception as e:
            logger.error(f"数据初始化测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行AkShare数据迁移测试...")
        
        tests = [
            ("股票代码识别", self.test_stock_recognition),
            ("股票列表下载", self.test_stock_list_download),
            ("数据库操作", self.test_database_operations),
            ("股票数据下载", self.test_stock_data_download),
            ("数据初始化", self.test_initialize_data),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"运行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                results[test_name] = result
                status = "通过" if result else "失败"
                logger.info(f"测试 '{test_name}' {status}")
            except Exception as e:
                logger.error(f"测试 '{test_name}' 异常: {e}")
                results[test_name] = False
        
        # 汇总结果
        logger.info(f"\n{'='*50}")
        logger.info("测试结果汇总")
        logger.info(f"{'='*50}")
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"\n总体结果: {passed}/{total} 通过")
        
        if passed >= total * 0.8:  # 80%通过率
            logger.info("🎉 AkShare数据迁移测试整体成功！")
            return True
        else:
            logger.warning("⚠️  AkShare数据迁移测试需要改进")
            return False

def main():
    """主函数"""
    try:
        tester = AkShareMigrationTester()
        success = tester.run_all_tests()
        
        if success:
            logger.info("\n✅ 所有测试完成，AkShare数据源已准备就绪！")
            logger.info("📝 建议:")
            logger.info("   1. 可以开始使用AkShare作为主要数据源")
            logger.info("   2. Tushare token现在是可选的")
            logger.info("   3. 股票推荐功能将使用AkShare的完整股票列表")
        else:
            logger.warning("\n❌ 部分测试失败，请检查配置和网络连接")
            
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 