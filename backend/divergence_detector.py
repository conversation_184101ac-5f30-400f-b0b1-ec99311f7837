#!/usr/bin/env python3
"""
MACD背离检测模块
检测股票的MACD顶背离和底背离，并生成K线图
"""

import pandas as pd
import numpy as np
import sqlite3
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import os
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum
import base64
import io
import matplotlib.font_manager as fm

logger = logging.getLogger(__name__)

class DivergenceType(Enum):
    """背离类型"""
    BULLISH = "bullish"  # 底背离（看涨）
    BEARISH = "bearish"  # 顶背离（看跌）

@dataclass
class DivergenceSignal:
    """背离信号"""
    symbol: str
    divergence_type: DivergenceType
    start_date: str
    end_date: str
    price_high: float
    price_low: float
    macd_high: float
    macd_low: float
    strength: float  # 背离强度 0-1
    confidence: float  # 置信度 0-1
    detected_at: datetime

class DivergenceDatabase:
    """背离检测结果数据库管理"""
    
    def __init__(self, db_path: str = "data/divergence_data.db"):
        self.db_path = db_path
        self.ensure_db_directory()
        self.init_database()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 创建背离信号表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS divergence_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    market TEXT NOT NULL,
                    divergence_type TEXT NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    price_high REAL NOT NULL,
                    price_low REAL NOT NULL,
                    macd_high REAL NOT NULL,
                    macd_low REAL NOT NULL,
                    strength REAL NOT NULL,
                    confidence REAL NOT NULL,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    chart_image TEXT,
                    UNIQUE(symbol, divergence_type, start_date, end_date)
                )
            """)
            
            # 创建扫描历史表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS scan_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    market TEXT NOT NULL,
                    scan_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_stocks INTEGER,
                    divergence_count INTEGER,
                    scan_duration REAL
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_divergence_symbol ON divergence_signals(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_divergence_market ON divergence_signals(market)",
                "CREATE INDEX IF NOT EXISTS idx_divergence_type ON divergence_signals(divergence_type)",
                "CREATE INDEX IF NOT EXISTS idx_divergence_date ON divergence_signals(detected_at)",
                "CREATE INDEX IF NOT EXISTS idx_scan_history_market ON scan_history(market)"
            ]
            
            for index_sql in indexes:
                conn.execute(index_sql)
            
            conn.commit()
            logger.info("背离检测数据库初始化完成")
    
    def save_divergence_signal(self, signal: DivergenceSignal, market: str, chart_image: str = None):
        """保存背离信号"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT OR REPLACE INTO divergence_signals (
                    symbol, market, divergence_type, start_date, end_date,
                    price_high, price_low, macd_high, macd_low,
                    strength, confidence, detected_at, chart_image
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal.symbol, market, signal.divergence_type.value,
                signal.start_date if isinstance(signal.start_date, str) else signal.start_date.strftime('%Y%m%d'),
                signal.end_date if isinstance(signal.end_date, str) else signal.end_date.strftime('%Y%m%d'),
                signal.price_high, signal.price_low,
                signal.macd_high, signal.macd_low,
                signal.strength, signal.confidence,
                signal.detected_at.strftime('%Y-%m-%d %H:%M:%S'), # 转换为字符串格式
                chart_image
            ))
            conn.commit()
    
    def get_recent_divergences(self, market: str = None, hours: int = 24) -> List[Dict]:
        """获取最近的背离信号"""
        query = """
            SELECT * FROM divergence_signals 
            WHERE detected_at > datetime('now', '-{} hours')
        """.format(hours)
        
        params = []
        if market:
            query += " AND market = ?"
            params.append(market)
        
        query += " ORDER BY symbol, detected_at DESC"
        
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def save_scan_history(self, market: str, total_stocks: int, divergence_count: int, scan_duration: float):
        """保存扫描历史"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO scan_history (market, total_stocks, divergence_count, scan_duration)
                VALUES (?, ?, ?, ?)
            """, (market, total_stocks, divergence_count, scan_duration))
            conn.commit()

class MACDDivergenceDetector:
    """MACD背离检测器"""
    
    def __init__(self):
        self.min_periods = 20  # 最小检测周期
        self.lookback_periods = 100  # 回看周期
        
    def calculate_macd(self, prices: pd.Series, fast_period: int = 12, 
                      slow_period: int = 26, signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        # 计算EMA
        ema_fast = prices.ewm(span=fast_period).mean()
        ema_slow = prices.ewm(span=slow_period).mean()
        
        # MACD线
        macd_line = ema_fast - ema_slow
        
        # 信号线
        signal_line = macd_line.ewm(span=signal_period).mean()
        
        # MACD柱状图
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def find_peaks_and_troughs(self, data: pd.Series, min_distance: int = 5) -> Tuple[List[int], List[int]]:
        """寻找峰值和谷值"""
        peaks = []
        troughs = []
        
        for i in range(min_distance, len(data) - min_distance):
            # 检查峰值
            is_peak = True
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and data.iloc[j] >= data.iloc[i]:
                    is_peak = False
                    break
            if is_peak:
                peaks.append(i)
            
            # 检查谷值
            is_trough = True
            for j in range(i - min_distance, i + min_distance + 1):
                if j != i and data.iloc[j] <= data.iloc[i]:
                    is_trough = False
                    break
            if is_trough:
                troughs.append(i)
        
        return peaks, troughs
    
    def detect_divergence(self, price_data: pd.DataFrame) -> List[DivergenceSignal]:
        """检测MACD背离"""
        if len(price_data) < self.lookback_periods:
            return []
        
        # 只取最近100根K线
        recent_data = price_data.tail(self.lookback_periods).copy()
        recent_data.reset_index(drop=True, inplace=True)
        
        # 计算MACD
        macd_line, signal_line, histogram = self.calculate_macd(recent_data['close'])
        
        # 寻找价格和MACD的峰值谷值
        price_peaks, price_troughs = self.find_peaks_and_troughs(recent_data['close'])
        macd_peaks, macd_troughs = self.find_peaks_and_troughs(macd_line)
        
        divergences = []
        
        # 检测顶背离（价格创新高，MACD不创新高）
        bearish_divergences = self._detect_bearish_divergence(
            recent_data, macd_line, price_peaks, macd_peaks
        )
        divergences.extend(bearish_divergences)
        
        # 检测底背离（价格创新低，MACD不创新低）
        bullish_divergences = self._detect_bullish_divergence(
            recent_data, macd_line, price_troughs, macd_troughs
        )
        divergences.extend(bullish_divergences)
        
        return divergences
    
    def _detect_bearish_divergence(self, data: pd.DataFrame, macd: pd.Series, 
                                  price_peaks: List[int], macd_peaks: List[int]) -> List[DivergenceSignal]:
        """检测顶背离"""
        divergences = []
        
        if len(price_peaks) < 2 or len(macd_peaks) < 2:
            return divergences
        
        # 检查最近的两个峰值
        for i in range(len(price_peaks) - 1):
            for j in range(i + 1, len(price_peaks)):
                peak1_idx = price_peaks[i]
                peak2_idx = price_peaks[j]
                
                # 确保时间顺序正确
                if peak2_idx <= peak1_idx:
                    continue
                
                price1 = data['close'].iloc[peak1_idx]
                price2 = data['close'].iloc[peak2_idx]
                
                # 价格创新高
                if price2 > price1:
                    # 寻找对应的MACD峰值
                    macd_peak1 = self._find_closest_peak(macd_peaks, peak1_idx, macd)
                    macd_peak2 = self._find_closest_peak(macd_peaks, peak2_idx, macd)
                    
                    if macd_peak1 is not None and macd_peak2 is not None:
                        macd1 = macd.iloc[macd_peak1]
                        macd2 = macd.iloc[macd_peak2]
                        
                        # MACD不创新高（背离）
                        if macd2 < macd1:
                            strength = abs(macd1 - macd2) / abs(macd1) if macd1 != 0 else 0
                            confidence = min(1.0, (price2 - price1) / price1 * 10)  # 简化的置信度计算
                            
                            divergence = DivergenceSignal(
                                symbol=data.get('symbol', 'UNKNOWN'),
                                divergence_type=DivergenceType.BEARISH,
                                start_date=data['trade_date'].iloc[peak1_idx] if 'trade_date' in data.columns else str(peak1_idx),
                                end_date=data['trade_date'].iloc[peak2_idx] if 'trade_date' in data.columns else str(peak2_idx),
                                price_high=max(price1, price2),
                                price_low=min(price1, price2),
                                macd_high=max(macd1, macd2),
                                macd_low=min(macd1, macd2),
                                strength=strength,
                                confidence=confidence,
                                detected_at=datetime.now()
                            )
                            divergences.append(divergence)
        
        return divergences
    
    def _detect_bullish_divergence(self, data: pd.DataFrame, macd: pd.Series,
                                  price_troughs: List[int], macd_troughs: List[int]) -> List[DivergenceSignal]:
        """检测底背离"""
        divergences = []
        
        if len(price_troughs) < 2 or len(macd_troughs) < 2:
            return divergences
        
        # 检查最近的两个谷值
        for i in range(len(price_troughs) - 1):
            for j in range(i + 1, len(price_troughs)):
                trough1_idx = price_troughs[i]
                trough2_idx = price_troughs[j]
                
                # 确保时间顺序正确
                if trough2_idx <= trough1_idx:
                    continue
                
                price1 = data['close'].iloc[trough1_idx]
                price2 = data['close'].iloc[trough2_idx]
                
                # 价格创新低
                if price2 < price1:
                    # 寻找对应的MACD谷值
                    macd_trough1 = self._find_closest_trough(macd_troughs, trough1_idx, macd)
                    macd_trough2 = self._find_closest_trough(macd_troughs, trough2_idx, macd)
                    
                    if macd_trough1 is not None and macd_trough2 is not None:
                        macd1 = macd.iloc[macd_trough1]
                        macd2 = macd.iloc[macd_trough2]
                        
                        # MACD不创新低（背离）
                        if macd2 > macd1:
                            strength = abs(macd2 - macd1) / abs(macd1) if macd1 != 0 else 0
                            confidence = min(1.0, (price1 - price2) / price1 * 10)  # 简化的置信度计算
                            
                            divergence = DivergenceSignal(
                                symbol=data.get('symbol', 'UNKNOWN'),
                                divergence_type=DivergenceType.BULLISH,
                                start_date=data['trade_date'].iloc[trough1_idx] if 'trade_date' in data.columns else str(trough1_idx),
                                end_date=data['trade_date'].iloc[trough2_idx] if 'trade_date' in data.columns else str(trough2_idx),
                                price_high=max(price1, price2),
                                price_low=min(price1, price2),
                                macd_high=max(macd1, macd2),
                                macd_low=min(macd1, macd2),
                                strength=strength,
                                confidence=confidence,
                                detected_at=datetime.now()
                            )
                            divergences.append(divergence)
        
        return divergences
    
    def _find_closest_peak(self, peaks: List[int], target_idx: int, data: pd.Series) -> Optional[int]:
        """寻找最接近目标索引的峰值"""
        if not peaks:
            return None
        
        closest_peak = None
        min_distance = float('inf')
        
        for peak in peaks:
            distance = abs(peak - target_idx)
            if distance < min_distance:
                min_distance = distance
                closest_peak = peak
        
        return closest_peak if min_distance <= 10 else None  # 最大允许距离
    
    def _find_closest_trough(self, troughs: List[int], target_idx: int, data: pd.Series) -> Optional[int]:
        """寻找最接近目标索引的谷值"""
        if not troughs:
            return None
        
        closest_trough = None
        min_distance = float('inf')
        
        for trough in troughs:
            distance = abs(trough - target_idx)
            if distance < min_distance:
                min_distance = distance
                closest_trough = trough
        
        return closest_trough if min_distance <= 10 else None  # 最大允许距离

class ChartGenerator:
    """图表生成器"""
    
    def __init__(self):
        # 尝试设置中文字体
        plt.rcParams['font.sans-serif'] = ['PingFang HK', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'sans-serif']
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False # 解决负号显示问题
        
        # 使用更通用的seaborn风格，避免特定版本问题
        sns.set_style("whitegrid")

        logger.info("ChartGenerator 初始化成功")
    
    def generate_kline_chart(self, data: pd.DataFrame, divergences: List[DivergenceSignal], 
                           symbol: str) -> str:
        """生成K线图并返回base64编码的图片"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[2, 1])
        
        # 确保数据按日期排序
        data = data.sort_values('trade_date').reset_index(drop=True)
        
        # 转换日期
        if 'trade_date' in data.columns:
            dates = pd.to_datetime(data['trade_date'], format='%Y%m%d')
        else:
            dates = pd.date_range(start='2023-01-01', periods=len(data), freq='D')
        
        # 绘制K线图
        self._plot_candlestick(ax1, dates, data)
        
        # 计算并绘制MACD
        macd_line, signal_line, histogram = MACDDivergenceDetector().calculate_macd(data['close'])
        self._plot_macd(ax2, dates, macd_line, signal_line, histogram)
        
        # 标记背离点
        self._mark_divergences(ax1, ax2, dates, data, divergences)
        
        # 设置标题和标签
        ax1.set_title(f'{symbol} - K-line Chart and MACD Divergence (Last 100 Days)', fontsize=16, fontweight='bold')
        ax1.set_ylabel('Price', fontsize=12)
        ax2.set_ylabel('MACD', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        
        # 格式化日期轴
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        # 添加网格
        ax1.grid(True, alpha=0.3)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 转换为base64
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return image_base64
    
    def _plot_candlestick(self, ax, dates, data):
        """绘制K线图"""
        for i in range(len(data)):
            date = dates.iloc[i]
            open_price = data['open'].iloc[i]
            high_price = data['high'].iloc[i]
            low_price = data['low'].iloc[i]
            close_price = data['close'].iloc[i]
            
            # 确定颜色
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制影线
            ax.plot([date, date], [low_price, high_price], color='black', linewidth=1)
            
            # 绘制实体
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            rect = Rectangle((date, body_bottom), timedelta(days=0.6), body_height,
                           facecolor=color, alpha=0.8, edgecolor='black')
            ax.add_patch(rect)
    
    def _plot_macd(self, ax, dates, macd_line, signal_line, histogram):
        """绘制MACD指标"""
        ax.plot(dates, macd_line, label='MACD', color='blue', linewidth=2)
        ax.plot(dates, signal_line, label='Signal', color='red', linewidth=2)
        
        # 绘制柱状图
        colors = ['red' if x >= 0 else 'green' for x in histogram]
        ax.bar(dates, histogram, color=colors, alpha=0.6, width=0.8)
        
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.legend()
    
    def _mark_divergences(self, ax1, ax2, dates, data, divergences):
        """标记背离点"""
        for div in divergences:
            if div.divergence_type == DivergenceType.BULLISH:
                # 底背离标记
                ax1.annotate('Bullish Divergence', xy=(dates.iloc[-1], data['close'].iloc[-1]), 
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            else:
                # 顶背离标记
                ax1.annotate('Bearish Divergence', xy=(dates.iloc[-1], data['close'].iloc[-1]), 
                           xytext=(10, -20), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

# 全局实例
divergence_db = DivergenceDatabase()
divergence_detector = MACDDivergenceDetector()
chart_generator = ChartGenerator()

def get_divergence_detector() -> MACDDivergenceDetector:
    """获取背离检测器实例"""
    return divergence_detector

def get_divergence_database() -> DivergenceDatabase:
    """获取背离数据库实例"""
    return divergence_db

def get_chart_generator() -> ChartGenerator:
    """获取图表生成器实例"""
    return chart_generator 