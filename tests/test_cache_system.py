import unittest
import time
import threading
from unittest.mock import patch
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from backend.ai.tools.cache_manager import <PERSON>acheManager, LRUCache
from backend.ai.tools.cache_keys import generate_cache_key, stock_data_key, news_key
from backend.ai.tools.cache_decorator import cached, stock_data_cached, news_cached


class TestLRUCache(unittest.TestCase):
    def setUp(self):
        self.cache = LRUCache(max_size=3)
    
    def test_basic_operations(self):
        """测试基本的缓存操作"""
        # 测试设置和获取
        self.cache.set("key1", "value1", ttl=60)
        result = self.cache.get("key1")
        self.assertIsNotNone(result)
        self.assertEqual(result[0], "value1")
        
        # 测试缓存未命中
        result = self.cache.get("nonexistent")
        self.assertIsNone(result)
    
    def test_ttl_expiration(self):
        """测试TTL过期机制"""
        # 设置短TTL
        self.cache.set("key1", "value1", ttl=1)
        
        # 立即获取应该成功
        result = self.cache.get("key1")
        self.assertIsNotNone(result)
        
        # 等待过期
        time.sleep(1.1)
        
        # 清理过期项
        cleaned = self.cache.cleanup_expired()
        self.assertEqual(cleaned, 1)
        
        # 获取应该失败
        result = self.cache.get("key1")
        self.assertIsNone(result)
    
    def test_lru_eviction(self):
        """测试LRU淘汰机制"""
        # 填充缓存至最大容量
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        
        # 添加第4个项，应该淘汰最老的
        self.cache.set("key4", "value4")
        
        # key1应该被淘汰
        result = self.cache.get("key1")
        self.assertIsNone(result)
        
        # 其他键应该存在
        self.assertIsNotNone(self.cache.get("key2"))
        self.assertIsNotNone(self.cache.get("key3"))
        self.assertIsNotNone(self.cache.get("key4"))
    
    def test_thread_safety(self):
        """测试线程安全性"""
        results = []
        
        def worker(thread_id):
            for i in range(10):
                key = f"thread_{thread_id}_key_{i}"
                value = f"thread_{thread_id}_value_{i}"
                self.cache.set(key, value)
                result = self.cache.get(key)
                if result:
                    results.append(result[0])
        
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 应该没有异常，且有一些结果
        self.assertGreater(len(results), 0)
    
    def test_stats(self):
        """测试统计功能"""
        # 初始统计
        stats = self.cache.get_stats()
        self.assertEqual(stats['stats']['hits'], 0)
        self.assertEqual(stats['stats']['misses'], 0)
        
        # 设置一些数据
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        # 命中测试
        self.cache.get("key1")
        self.cache.get("key1")  # 再次命中
        
        # 未命中测试
        self.cache.get("nonexistent")
        
        stats = self.cache.get_stats()
        self.assertEqual(stats['stats']['hits'], 2)
        self.assertEqual(stats['stats']['misses'], 1)
        self.assertGreater(stats['hit_rate'], 0)


class TestCacheManager(unittest.TestCase):
    def setUp(self):
        self.manager = CacheManager()
    
    def test_cache_types(self):
        """测试不同类型的缓存"""
        # 股票数据缓存
        self.manager.set("stock_key", {"price": 100}, "stock_data")
        result = self.manager.get("stock_key", "stock_data")
        self.assertEqual(result["price"], 100)
        
        # 新闻缓存
        self.manager.set("news_key", {"title": "News"}, "news")
        result = self.manager.get("news_key", "news")
        self.assertEqual(result["title"], "News")
        
        # 通用缓存
        self.manager.set("general_key", "general_value", "general")
        result = self.manager.get("general_key", "general")
        self.assertEqual(result, "general_value")
    
    def test_global_stats(self):
        """测试全局统计"""
        # 添加一些数据到不同缓存
        self.manager.set("key1", "value1", "stock_data")
        self.manager.set("key2", "value2", "news")
        self.manager.set("key3", "value3", "general")
        
        stats = self.manager.get_global_stats()
        
        # 检查统计结构
        self.assertIn("stock_data", stats)
        self.assertIn("news", stats)
        self.assertIn("general", stats)
        
        # 检查每个缓存的大小
        self.assertGreater(stats["stock_data"]["size"], 0)
        self.assertGreater(stats["news"]["size"], 0)
        self.assertGreater(stats["general"]["size"], 0)
    
    def test_clear_all(self):
        """测试清空所有缓存"""
        # 添加数据
        self.manager.set("key1", "value1", "stock_data")
        self.manager.set("key2", "value2", "news")
        
        # 清空
        self.manager.clear_all()
        
        # 验证清空
        self.assertIsNone(self.manager.get("key1", "stock_data"))
        self.assertIsNone(self.manager.get("key2", "news"))


class TestCacheKeys(unittest.TestCase):
    def test_key_generation(self):
        """测试缓存键生成"""
        # 基本键生成
        key1 = generate_cache_key("test_tool", param1="value1", param2="value2")
        key2 = generate_cache_key("test_tool", param1="value1", param2="value2")
        
        # 相同参数应该生成相同的键
        self.assertEqual(key1, key2)
        
        # 不同参数应该生成不同的键
        key3 = generate_cache_key("test_tool", param1="value1", param2="different")
        self.assertNotEqual(key1, key3)
    
    def test_specific_key_functions(self):
        """测试特定工具的键生成函数"""
        # 股票数据键
        stock_key1 = stock_data_key("AAPL", "1d")
        stock_key2 = stock_data_key("AAPL", "1d")
        self.assertEqual(stock_key1, stock_key2)
        
        # 新闻键
        news_key1 = news_key(symbol="AAPL")
        news_key2 = news_key(symbol="AAPL")
        self.assertEqual(news_key1, news_key2)
        
        # 不同符号应该生成不同键
        news_key3 = news_key(symbol="TSLA")
        self.assertNotEqual(news_key1, news_key3)


class TestCacheDecorators(unittest.TestCase):
    def setUp(self):
        # 创建测试用的函数
        self.call_count = 0
    
    def test_basic_caching_decorator(self):
        """测试基本缓存装饰器"""
        @cached(cache_type='general', ttl=60)
        def test_function(param1, param2):
            self.call_count += 1
            return f"result_{param1}_{param2}"
        
        # 第一次调用
        result1 = test_function("a", "b")
        self.assertEqual(result1, "result_a_b")
        self.assertEqual(self.call_count, 1)
        
        # 第二次调用应该使用缓存
        result2 = test_function("a", "b")
        self.assertEqual(result2, "result_a_b")
        self.assertEqual(self.call_count, 1)  # 函数不应该再次被调用
        
        # 不同参数应该调用函数
        result3 = test_function("c", "d")
        self.assertEqual(result3, "result_c_d")
        self.assertEqual(self.call_count, 2)
    
    def test_stock_data_cached_decorator(self):
        """测试股票数据缓存装饰器"""
        @stock_data_cached(ttl=300)
        def get_stock_data(symbol):
            self.call_count += 1
            return {"symbol": symbol, "price": 100.0}
        
        # 第一次调用
        result1 = get_stock_data("AAPL")
        self.assertEqual(result1["symbol"], "AAPL")
        self.assertEqual(self.call_count, 1)
        
        # 第二次调用应该使用缓存
        result2 = get_stock_data("AAPL")
        self.assertEqual(result2["symbol"], "AAPL")
        self.assertEqual(self.call_count, 1)
    
    def test_news_cached_decorator(self):
        """测试新闻缓存装饰器"""
        @news_cached(ttl=900)
        def get_news(symbol):
            self.call_count += 1
            return {"symbol": symbol, "news": ["Latest news"]}
        
        # 第一次调用
        result1 = get_news("AAPL")
        self.assertEqual(result1["symbol"], "AAPL")
        self.assertEqual(self.call_count, 1)
        
        # 第二次调用应该使用缓存
        result2 = get_news("AAPL")
        self.assertEqual(result2["symbol"], "AAPL")
        self.assertEqual(self.call_count, 1)
    
    def test_error_handling(self):
        """测试错误处理"""
        @cached(cache_type='general', ttl=60, bypass_on_error=True)
        def failing_function():
            if self.call_count == 0:
                self.call_count += 1
                raise Exception("First call fails")
            self.call_count += 1
            return "success"
        
        # 第一次调用失败，应该绕过缓存
        result = failing_function()
        self.assertEqual(result, "success")
        self.assertEqual(self.call_count, 2)  # 失败一次，成功一次


class TestCacheIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_cache_hit_rate_improvement(self):
        """测试缓存命中率提升"""
        manager = CacheManager()
        
        # 模拟重复查询
        queries = [
            ("AAPL", "stock_data"),
            ("TSLA", "stock_data"), 
            ("AAPL", "stock_data"),  # 重复
            ("GOOGL", "news"),
            ("AAPL", "stock_data"),  # 重复
            ("TSLA", "stock_data"),  # 重复
        ]
        
        for i, (key, cache_type) in enumerate(queries):
            # 模拟数据获取
            if manager.get(key, cache_type) is None:
                # 缓存未命中，设置数据
                manager.set(key, f"data_for_{key}", cache_type)
        
        # 检查统计
        stats = manager.get_global_stats()
        
        # 应该有一些命中
        total_hits = sum(stat['stats']['hits'] for stat in stats.values())
        total_requests = sum(stat['stats']['hits'] + stat['stats']['misses'] for stat in stats.values())
        
        if total_requests > 0:
            hit_rate = total_hits / total_requests * 100
            print(f"Cache hit rate: {hit_rate:.2f}%")
            # 由于有重复查询，命中率应该大于0
            self.assertGreater(hit_rate, 0)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 