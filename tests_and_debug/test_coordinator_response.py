#!/usr/bin/env python3
"""
测试coordinator对"你是谁"问题的响应
"""

import asyncio
import logging
from backend.ai.graph.nodes import coordinator_node
from backend.ai.graph.types import State
from langchain_core.messages import HumanMessage

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_coordinator_response():
    """测试coordinator对"你是谁"的响应"""
    
    # 创建测试状态
    test_state = State(
        messages=[HumanMessage(content="你是谁")],
        locale="zh-CN",
        enable_background_investigation=True
    )
    
    print("🧪 测试coordinator对'你是谁'的响应")
    print("=" * 50)
    
    try:
        # 调用coordinator_node
        result = coordinator_node(test_state)
        
        print(f"📊 Coordinator响应结果:")
        print(f"   - goto: {result.goto}")
        print(f"   - update keys: {list(result.update.keys()) if result.update else 'None'}")
        
        if result.update:
            if 'final_report' in result.update:
                print(f"   - final_report: {result.update['final_report']}")
            if 'locale' in result.update:
                print(f"   - locale: {result.update['locale']}")
        
        # 验证响应是否合理
        if result.goto == "__end__" and result.update and 'final_report' in result.update:
            final_report = result.update['final_report']
            if final_report and "DeerFlow" in final_report:
                print("✅ Coordinator正确处理了问候语")
            else:
                print(f"❌ Coordinator响应不正确: {final_report}")
        else:
            print("❌ Coordinator没有直接处理问候语，而是转发给了其他节点")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_coordinator_response()) 