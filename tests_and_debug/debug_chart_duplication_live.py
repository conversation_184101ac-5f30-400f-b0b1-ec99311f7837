#!/usr/bin/env python3
"""
实时调试图表重复生成问题
监控实际工作流执行过程中的图表生成情况
"""

import asyncio
import logging
import json
from datetime import datetime

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_live_workflow():
    """调试实际工作流中的图表生成"""
    
    print("🔍 开始实时调试图表重复生成问题")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from backend.ai.workflow import AIWorkflowManager
        from backend.data_manager import init_data_manager
        
        # 初始化组件
        data_manager = init_data_manager("d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c")
        workflow_manager = AIWorkflowManager(data_manager=data_manager)
        
        # 测试查询
        test_query = "分析苹果公司AAPL的股票"
        
        print(f"📝 测试查询: {test_query}")
        print("🚀 开始执行工作流...")
        print()
        
        chart_events = []
        message_count = 0
        
        # 监控工作流执行
        async for result in workflow_manager.process_user_query(test_query):
            message_count += 1
            result_type = result.get("type", "unknown")
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            print(f"[{timestamp}] 消息 #{message_count}: {result_type}")
            
            # 特别关注图表相关事件
            if result_type == "chart_data":
                chart_event = {
                    "timestamp": timestamp,
                    "message_num": message_count,
                    "content": result.get("content", {}),
                    "has_chart": result.get("has_chart", False),
                    "agent": result.get("agent", "unknown")
                }
                chart_events.append(chart_event)
                
                print(f"  🎯 检测到图表数据!")
                print(f"     代理: {chart_event['agent']}")
                print(f"     有图表: {chart_event['has_chart']}")
                print(f"     内容键: {list(chart_event['content'].keys())}")
            
            # 显示其他重要事件
            elif result_type in ["final_report", "agent_message", "workflow_complete"]:
                content_preview = str(result.get("content", ""))[:50]
                print(f"     内容: {content_preview}...")
            
            # 限制输出长度
            if message_count > 50:
                print("⚠️ 消息数量过多，停止监控")
                break
        
        # 分析结果
        print("\n" + "=" * 60)
        print("📊 图表生成分析结果:")
        print(f"总消息数: {message_count}")
        print(f"图表事件数: {len(chart_events)}")
        
        if len(chart_events) == 0:
            print("✅ 未检测到图表事件")
        elif len(chart_events) == 1:
            print("✅ 只检测到1个图表事件 (正常)")
            event = chart_events[0]
            print(f"   时间: {event['timestamp']}")
            print(f"   消息位置: #{event['message_num']}")
            print(f"   代理: {event['agent']}")
        else:
            print(f"❌ 检测到{len(chart_events)}个图表事件 (异常!)")
            print("\n详细事件列表:")
            for i, event in enumerate(chart_events, 1):
                print(f"  事件 {i}:")
                print(f"    时间: {event['timestamp']}")
                print(f"    消息位置: #{event['message_num']}")
                print(f"    代理: {event['agent']}")
                print(f"    内容相同: {i > 1 and event['content'] == chart_events[0]['content']}")
        
        return len(chart_events)
        
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return -1

async def debug_chart_node_state():
    """调试图表节点状态管理"""
    
    print("\n🔧 调试图表节点状态管理")
    print("=" * 40)
    
    try:
        from backend.ai.graph.chart_node import chart_generation_node
        from langchain_core.messages import HumanMessage
        
        # 模拟状态变化
        test_states = [
            {
                "messages": [HumanMessage(content="分析AAPL股票")],
                "has_chart_data": False,
                "enable_background_investigation": True
            },
            {
                "messages": [HumanMessage(content="分析AAPL股票")],
                "has_chart_data": True,  # 模拟已有图表数据
                "chart_config": {"test": "config"},
                "chart_data": {"symbol": "AAPL"},
                "enable_background_investigation": True
            }
        ]
        
        for i, state in enumerate(test_states, 1):
            print(f"\n测试状态 {i}: has_chart_data={state.get('has_chart_data')}")
            
            try:
                result = await chart_generation_node(state)
                print(f"  结果: {result}")
                print(f"  下一步: {result.update if hasattr(result, 'update') else 'N/A'}")
            except Exception as e:
                print(f"  错误: {e}")
        
    except Exception as e:
        logger.error(f"调试图表节点时发生错误: {e}")

async def debug_backend_streaming():
    """调试后端流式传输"""
    
    print("\n📡 调试后端流式传输")
    print("=" * 40)
    
    try:
        # 模拟服务器流式响应
        from backend.server import stream_ai_workflow_response
        from backend.data_manager import init_data_manager
        
        data_manager = init_data_manager("d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c")
        
        chart_data_count = 0
        
        print("开始模拟流式响应...")
        
        async for response_line in stream_ai_workflow_response(
            user_input="分析AAPL股票",
            data_manager=data_manager,
            max_plan_iterations=1,
            max_step_num=3,
            enable_background_investigation=False,
            debug=True
        ):
            if "chart_data" in response_line:
                chart_data_count += 1
                print(f"  检测到图表数据流 #{chart_data_count}")
                print(f"    内容: {response_line[:100]}...")
        
        print(f"\n图表数据流总数: {chart_data_count}")
        
        if chart_data_count <= 1:
            print("✅ 流式传输正常")
        else:
            print("❌ 流式传输异常 - 发送了多个图表数据")
            
    except Exception as e:
        logger.error(f"调试流式传输时发生错误: {e}")

async def main():
    """主调试函数"""
    
    print("🚀 开始全面调试图表重复生成问题")
    print("=" * 70)
    
    # 1. 调试实际工作流
    chart_count = await debug_live_workflow()
    
    # 2. 调试图表节点
    await debug_chart_node_state()
    
    # 3. 调试后端流式传输
    await debug_backend_streaming()
    
    print("\n" + "=" * 70)
    print("🏁 调试完成")
    
    if chart_count == 1:
        print("✅ 图表生成正常 - 只生成了1个图表")
    elif chart_count > 1:
        print(f"❌ 图表生成异常 - 生成了{chart_count}个图表")
        print("💡 建议检查:")
        print("   1. 前端是否正确处理去重逻辑")
        print("   2. 是否有多个工作流实例在运行")
        print("   3. 状态管理是否按预期工作")
    elif chart_count == 0:
        print("⚠️ 未检测到图表生成 - 可能是股票检测失败")
    else:
        print("❌ 调试过程中发生错误")

if __name__ == "__main__":
    asyncio.run(main()) 