#!/usr/bin/env python3
"""
Final test for chart deduplication fix
Tests both backend and frontend deduplication mechanisms
"""

import asyncio
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

async def test_backend_chart_deduplication():
    """Test backend chart deduplication mechanisms"""
    print("🧪 Testing Backend Chart Deduplication...")
    
    try:
        # Import backend components
        from backend.ai.workflow import AIWorkflowManager
        from backend.ai.graph.chart_node import chart_generation_node
        
        # Test chart generation node with same symbol multiple times
        test_state = {
            "messages": [{"content": "Analyze AAPL stock", "type": "user"}],
            "has_chart_data": False,
            "enable_background_investigation": True
        }
        
        print("1. Testing first chart generation...")
        result1 = await chart_generation_node(test_state)
        print(f"   Result: {result1.update if hasattr(result1, 'update') else result1}")
        
        # Simulate state after first chart generation
        updated_state = {
            **test_state,
            "has_chart_data": True,
            "chart_data": {"symbol": "AAPL"},
            "chart_config": {"some": "config"}
        }
        
        print("2. Testing second chart generation (should skip)...")
        result2 = await chart_generation_node(updated_state)
        print(f"   Result: {result2.update if hasattr(result2, 'update') else result2}")
        
        # Test workflow manager
        print("3. Testing workflow manager deduplication...")
        workflow_manager = AIWorkflowManager()
        
        chart_data_count = 0
        async for result in workflow_manager.process_user_query("Analyze AAPL stock"):
            if result.get("type") == "chart_data":
                chart_data_count += 1
                print(f"   Chart data event #{chart_data_count}: {result.get('agent', 'unknown')}")
            if chart_data_count > 1:
                print("   ❌ Multiple chart data events detected!")
                break
            if result.get("type") in ["workflow_complete", "done", "error"]:
                break
        
        if chart_data_count <= 1:
            print("   ✅ Backend chart deduplication working")
        else:
            print("   ❌ Backend chart deduplication failed")
            
        return chart_data_count <= 1
        
    except Exception as e:
        print(f"   ❌ Backend test failed: {e}")
        return False

def test_frontend_chart_logic():
    """Test frontend chart deduplication logic"""
    print("🧪 Testing Frontend Chart Deduplication Logic...")
    
    try:
        # Simulate frontend chart handling logic
        chart_message_ids = set()
        messages = []
        
        def simulate_chart_data_handling(data):
            """Simulate the frontend chart data handling"""
            if data.get('type') == 'chart_data':
                symbol = data.get('chart_data', {}).get('symbol', 'unknown')
                chart_id = symbol  # This should be the fixed logic
                
                # Check if we already have a chart for this symbol
                chart_exists = any(
                    msg.get('type') == 'chart' and 
                    msg.get('chartData', {}).get('symbol') == symbol
                    for msg in messages
                )
                
                if not chart_exists and chart_id not in chart_message_ids:
                    chart_message = {
                        'id': f"{len(messages)}_chart",
                        'type': 'chart',
                        'content': f"📊 {symbol} 股票图表分析",
                        'chartConfig': data.get('content', {}),
                        'chartData': data.get('chart_data', {}),
                        'agent': data.get('agent', 'chart_generator'),
                        'timestamp': datetime.now().timestamp()
                    }
                    messages.append(chart_message)
                    chart_message_ids.add(chart_id)
                    return True
                else:
                    print(f"   Chart for {symbol} already exists, skipping duplicate")
                    return False
        
        # Test 1: First AAPL chart
        print("1. Testing first AAPL chart...")
        result1 = simulate_chart_data_handling({
            'type': 'chart_data',
            'content': {'config': 'test'},
            'chart_data': {'symbol': 'AAPL'},
            'agent': 'chart_generator'
        })
        print(f"   Added chart: {result1}")
        
        # Test 2: Duplicate AAPL chart
        print("2. Testing duplicate AAPL chart...")
        result2 = simulate_chart_data_handling({
            'type': 'chart_data',
            'content': {'config': 'test2'},
            'chart_data': {'symbol': 'AAPL'},
            'agent': 'chart_generator'
        })
        print(f"   Added chart: {result2}")
        
        # Test 3: Different symbol
        print("3. Testing different symbol (MSFT)...")
        result3 = simulate_chart_data_handling({
            'type': 'chart_data',
            'content': {'config': 'test3'},
            'chart_data': {'symbol': 'MSFT'},
            'agent': 'chart_generator'
        })
        print(f"   Added chart: {result3}")
        
        chart_count = len([msg for msg in messages if msg.get('type') == 'chart'])
        print(f"   Total chart messages: {chart_count}")
        
        # Should have 2 charts (AAPL and MSFT), not 3
        if chart_count == 2 and result1 and not result2 and result3:
            print("   ✅ Frontend chart deduplication working")
            return True
        else:
            print("   ❌ Frontend chart deduplication failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Frontend test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🔍 Chart Deduplication Fix Verification")
    print("=" * 50)
    
    backend_test = await test_backend_chart_deduplication()
    frontend_test = test_frontend_chart_logic()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Backend deduplication: {'✅ PASS' if backend_test else '❌ FAIL'}")
    print(f"   Frontend deduplication: {'✅ PASS' if frontend_test else '❌ FAIL'}")
    
    if backend_test and frontend_test:
        print("\n🎉 All chart deduplication tests PASSED!")
        print("   ✅ Multiple chart issue should be resolved")
    else:
        print("\n⚠️ Some tests FAILED - manual verification needed")
    
    print("\n🔧 Changes Made:")
    print("   1. Fixed frontend chart ID generation (removed timestamp)")
    print("   2. Improved chart existence checking (entire message history)")
    print("   3. Added debug logging for duplicate detection")
    print("   4. Backend already had deduplication mechanisms")

if __name__ == "__main__":
    asyncio.run(main()) 