#!/usr/bin/env python3
"""
金融多报告系统测试脚本

测试新实现的金融领域多Agent报告系统，包括：
1. 看多报告节点
2. 看空报告节点  
3. 交易建议报告节点
4. 最终综合报告节点

运行方式:
python test_financial_reports_system.py
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.ai.graph.builder import build_graph
from backend.ai.graph.types import State
from langchain_core.messages import HumanMessage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinancialReportsSystemTester:
    """金融多报告系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.graph = None
        self.test_results = {}
        
    def setup(self):
        """设置测试环境"""
        try:
            logger.info("正在初始化金融多报告系统图...")
            self.graph = build_graph()
            logger.info("✅ 图初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 图初始化失败: {e}")
            return False
    
    def create_test_state(self, query: str) -> Dict[str, Any]:
        """创建测试状态"""
        return {
            "messages": [HumanMessage(content=query)],
            "locale": "zh-CN",
            "observations": [],
            "plan_iterations": 0,
            "auto_accepted_plan": True,  # 自动接受计划，避免人工干预
            "enable_background_investigation": False,  # 简化测试流程
            # 初始化报告字段
            "bullish_report": "",
            "bearish_report": "",
            "trading_advice_report": "",
            "comprehensive_final_report": "",
            "final_report": ""
        }
    
    async def test_basic_financial_analysis(self):
        """测试基础金融分析流程"""
        logger.info("\n🧪 测试1: 基础金融分析流程")
        
        query = "请分析苹果公司(AAPL)的投资价值，包括技术面和基本面分析"
        state = self.create_test_state(query)
        
        try:
            # 创建配置
            config = {"configurable": {"thread_id": "test-basic-1"}}
            
            logger.info("开始执行金融分析流程...")
            result = await self.graph.ainvoke(state, config=config)
            
            # 检查各个报告是否生成
            reports_check = {
                "看多报告": bool(result.get("bullish_report", "")),
                "看空报告": bool(result.get("bearish_report", "")),
                "交易建议报告": bool(result.get("trading_advice_report", "")),
                "综合最终报告": bool(result.get("comprehensive_final_report", "")),
                "兼容性最终报告": bool(result.get("final_report", ""))
            }
            
            # 记录测试结果
            all_reports_generated = all(reports_check.values())
            self.test_results["basic_analysis"] = {
                "success": all_reports_generated,
                "reports_generated": reports_check,
                "details": f"生成了 {sum(reports_check.values())}/5 个报告"
            }
            
            # 打印报告生成情况
            logger.info("📊 报告生成情况:")
            for report_name, generated in reports_check.items():
                status = "✅" if generated else "❌"
                logger.info(f"  {status} {report_name}: {'已生成' if generated else '未生成'}")
            
            if all_reports_generated:
                logger.info("✅ 基础金融分析流程测试成功")
                
                # 显示报告长度统计
                logger.info("\n📈 报告内容统计:")
                logger.info(f"  看多报告长度: {len(result.get('bullish_report', ''))} 字符")
                logger.info(f"  看空报告长度: {len(result.get('bearish_report', ''))} 字符")
                logger.info(f"  交易建议报告长度: {len(result.get('trading_advice_report', ''))} 字符")
                logger.info(f"  综合报告长度: {len(result.get('comprehensive_final_report', ''))} 字符")
                
                return True
            else:
                logger.error("❌ 基础金融分析流程测试失败 - 部分报告未生成")
                return False
                
        except Exception as e:
            logger.error(f"❌ 基础金融分析流程测试失败: {e}")
            self.test_results["basic_analysis"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_financial_report_coordinator(self):
        """测试金融报告协调器"""
        logger.info("\n🧪 测试2: 金融报告协调器节点")
        
        try:
            from backend.ai.graph.nodes import financial_report_coordinator_node
            
            # 测试不同状态下的协调器行为
            test_cases = [
                {
                    "name": "无报告状态",
                    "state": {
                        "bullish_report": "",
                        "bearish_report": "",
                        "trading_advice_report": ""
                    },
                    "expected_goto": "bullish_reporter"
                },
                {
                    "name": "已有看多报告",
                    "state": {
                        "bullish_report": "测试看多报告",
                        "bearish_report": "",
                        "trading_advice_report": ""
                    },
                    "expected_goto": "bearish_reporter"
                },
                {
                    "name": "已有看多和看空报告",
                    "state": {
                        "bullish_report": "测试看多报告",
                        "bearish_report": "测试看空报告",
                        "trading_advice_report": ""
                    },
                    "expected_goto": "trading_advice_reporter"
                },
                {
                    "name": "所有报告完成",
                    "state": {
                        "bullish_report": "测试看多报告",
                        "bearish_report": "测试看空报告",
                        "trading_advice_report": "测试交易建议"
                    },
                    "expected_goto": "final_comprehensive_reporter"
                }
            ]
            
            coordinator_test_success = True
            for test_case in test_cases:
                logger.info(f"  测试场景: {test_case['name']}")
                command = financial_report_coordinator_node(test_case["state"])
                
                if hasattr(command, 'goto') and command.goto == test_case["expected_goto"]:
                    logger.info(f"    ✅ 正确路由到: {command.goto}")
                else:
                    logger.error(f"    ❌ 期望路由到: {test_case['expected_goto']}, 实际: {getattr(command, 'goto', 'None')}")
                    coordinator_test_success = False
            
            self.test_results["coordinator"] = {"success": coordinator_test_success}
            
            if coordinator_test_success:
                logger.info("✅ 金融报告协调器测试成功")
                return True
            else:
                logger.error("❌ 金融报告协调器测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 金融报告协调器测试失败: {e}")
            self.test_results["coordinator"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_individual_reporters(self):
        """测试各个报告节点"""
        logger.info("\n🧪 测试3: 各个报告节点")
        
        try:
            from backend.ai.graph.nodes import (
                bullish_reporter_node,
                bearish_reporter_node,
                trading_advice_reporter_node,
                final_comprehensive_reporter_node
            )
            
            # 创建测试状态
            test_state = {
                "current_plan": type('Plan', (), {
                    'title': 'AAPL股票分析',
                    'thought': '分析苹果公司的投资价值'
                })(),
                "observations": [
                    "苹果公司Q4财报显示营收增长8%",
                    "iPhone销量同比增长5%",
                    "技术指标显示RSI为45，处于中性区间"
                ],
                "locale": "zh-CN",
                "bullish_report": "测试看多报告内容",
                "bearish_report": "测试看空报告内容",
                "trading_advice_report": "测试交易建议内容"
            }
            
            reporters_test = {
                "看多报告节点": bullish_reporter_node,
                "看空报告节点": bearish_reporter_node,
                "交易建议节点": trading_advice_reporter_node,
                "综合报告节点": final_comprehensive_reporter_node
            }
            
            individual_reporters_success = True
            for reporter_name, reporter_func in reporters_test.items():
                logger.info(f"  测试: {reporter_name}")
                try:
                    result = reporter_func(test_state)
                    
                    # 检查返回结果
                    if hasattr(result, 'update') and result.update:
                        logger.info(f"    ✅ {reporter_name} 成功返回更新数据")
                    elif isinstance(result, dict) and result:
                        logger.info(f"    ✅ {reporter_name} 成功返回字典数据")
                    else:
                        logger.error(f"    ❌ {reporter_name} 返回数据格式异常")
                        individual_reporters_success = False
                        
                except Exception as e:
                    logger.error(f"    ❌ {reporter_name} 执行失败: {e}")
                    individual_reporters_success = False
            
            self.test_results["individual_reporters"] = {"success": individual_reporters_success}
            
            if individual_reporters_success:
                logger.info("✅ 各个报告节点测试成功")
                return True
            else:
                logger.error("❌ 部分报告节点测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 各个报告节点测试失败: {e}")
            self.test_results["individual_reporters"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_graph_structure(self):
        """测试图结构完整性"""
        logger.info("\n🧪 测试4: 图结构完整性")
        
        try:
            if not self.graph:
                logger.error("❌ 图未初始化")
                return False
            
            # 检查所有必需的节点
            required_nodes = [
                "coordinator",
                "planner", 
                "research_team",
                "financial_report_coordinator",
                "bullish_reporter",
                "bearish_reporter", 
                "trading_advice_reporter",
                "final_comprehensive_reporter"
            ]
            
            graph_dict = self.graph.get_graph()
            actual_nodes = set(graph_dict.nodes)
            
            missing_nodes = []
            for node in required_nodes:
                if node not in actual_nodes:
                    missing_nodes.append(node)
            
            if missing_nodes:
                logger.error(f"❌ 缺少必需节点: {missing_nodes}")
                self.test_results["graph_structure"] = {
                    "success": False,
                    "missing_nodes": missing_nodes
                }
                return False
            else:
                logger.info("✅ 所有必需节点都存在")
                logger.info(f"📊 图中共有 {len(actual_nodes)} 个节点")
                
                self.test_results["graph_structure"] = {"success": True}
                return True
                
        except Exception as e:
            logger.error(f"❌ 图结构测试失败: {e}")
            self.test_results["graph_structure"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "="*60)
        logger.info("📋 金融多报告系统测试总结")
        logger.info("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        logger.info(f"总测试数量: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📊 详细测试结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get("success", False) else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
            if not result.get("success", False) and "error" in result:
                logger.info(f"    错误: {result['error']}")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 所有测试通过！新的金融多报告系统工作正常！")
        else:
            logger.info(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，需要进一步检查。")

async def main():
    """主测试函数"""
    logger.info("🚀 开始金融多报告系统测试")
    logger.info(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = FinancialReportsSystemTester()
    
    # 设置测试环境
    if not tester.setup():
        logger.error("❌ 测试环境设置失败，退出测试")
        return
    
    # 执行测试
    test_functions = [
        tester.test_graph_structure,
        tester.test_financial_report_coordinator,
        tester.test_individual_reporters,
        # tester.test_basic_financial_analysis,  # 需要实际的LLM API，暂时注释
    ]
    
    for test_func in test_functions:
        try:
            if asyncio.iscoroutinefunction(test_func):
                await test_func()
            else:
                test_func()
        except Exception as e:
            logger.error(f"测试执行异常: {e}")
    
    # 打印测试总结
    tester.print_test_summary()

if __name__ == "__main__":
    asyncio.run(main()) 