#!/usr/bin/env python3
"""
深度分析测试
检查不同时间范围的原始数据差异和因子计算过程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.data_manager import init_data_manager
from backend.enhanced_factors import EnhancedFactorCalculator
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

def deep_analysis_test():
    """深度分析不同时间范围的数据和因子计算"""
    
    print("=" * 80)
    print("🔬 深度分析测试：时间范围对因子计算的影响")
    print("=" * 80)
    
    # 初始化
    data_manager = init_data_manager("test_token")
    calculator = EnhancedFactorCalculator()
    symbol = "AAPL"
    
    # 定义时间范围
    end_date = datetime.now()
    timeframes = {
        "1个月": (end_date - timedelta(days=30), 30),
        "3个月": (end_date - timedelta(days=90), 90),
        "6个月": (end_date - timedelta(days=180), 180),
    }
    
    end_date_str = end_date.strftime("%Y%m%d")
    results = {}
    
    for tf_name, (start_date, days) in timeframes.items():
        start_date_str = start_date.strftime("%Y%m%d")
        
        print(f"\n📊 分析时间范围: {tf_name} ({start_date_str} 到 {end_date_str})")
        print("-" * 60)
        
        # 获取数据
        data = data_manager.get_stock_data(symbol, start_date_str, end_date_str)
        
        if data.empty:
            print(f"❌ 无法获取 {tf_name} 数据")
            continue
            
        print(f"✅ 获取到 {len(data)} 条记录")
        
        # 分析原始数据特征
        close_prices = data['close'].values
        print(f"📈 价格数据分析:")
        print(f"   最早价格: {close_prices[0]:.2f} ({data['trade_date'].iloc[0]})")
        print(f"   最新价格: {close_prices[-1]:.2f} ({data['trade_date'].iloc[-1]})")
        print(f"   价格变化: {((close_prices[-1] - close_prices[0]) / close_prices[0] * 100):+.2f}%")
        print(f"   均值: {np.mean(close_prices):.2f}")
        print(f"   标准差: {np.std(close_prices):.2f}")
        
        # 计算关键统计
        recent_20_mean = np.mean(close_prices[-20:]) if len(close_prices) >= 20 else np.mean(close_prices)
        recent_50_mean = np.mean(close_prices[-50:]) if len(close_prices) >= 50 else np.mean(close_prices)
        
        print(f"   最近20天均价: {recent_20_mean:.2f}")
        print(f"   最近50天均价: {recent_50_mean:.2f}")
        
        # 手动计算RSI验证
        if len(close_prices) >= 15:
            manual_rsi = calculate_rsi_manual(close_prices)
            print(f"🔢 手动RSI计算: {manual_rsi:.4f}")
        
        # 使用计算器计算因子
        factors = calculator.calculate_all_enhanced_factors(data)
        
        print(f"🧮 因子计算结果:")
        key_factors = ['rsi', 'macd', 'sma_20', 'sma_50']
        for factor_name in key_factors:
            if factor_name in factors:
                print(f"   {factor_name}: {factors[factor_name]:.4f}")
        
        results[tf_name] = {
            'data_count': len(data),
            'price_start': close_prices[0],
            'price_end': close_prices[-1],
            'price_change_pct': (close_prices[-1] - close_prices[0]) / close_prices[0] * 100,
            'mean_price': np.mean(close_prices),
            'std_price': np.std(close_prices),
            'factors': factors
        }
    
    # 对比分析
    print("\n" + "=" * 80)
    print("🔍 对比分析结果")
    print("=" * 80)
    
    timeframe_list = list(results.keys())
    if len(timeframe_list) >= 2:
        for i in range(len(timeframe_list) - 1):
            tf1, tf2 = timeframe_list[i], timeframe_list[i + 1]
            print(f"\n📊 {tf1} vs {tf2}:")
            
            # 比较数据特征
            print(f"   数据量差异: {results[tf2]['data_count'] - results[tf1]['data_count']} 条")
            print(f"   起始价格差异: {results[tf2]['price_start'] - results[tf1]['price_start']:+.2f}")
            print(f"   价格变化差异: {results[tf2]['price_change_pct'] - results[tf1]['price_change_pct']:+.2f}%")
            print(f"   价格波动差异: {results[tf2]['std_price'] - results[tf1]['std_price']:+.2f}")
            
            # 比较因子
            print(f"   因子差异:")
            for factor_name in ['rsi', 'macd', 'sma_20']:
                if factor_name in results[tf1]['factors'] and factor_name in results[tf2]['factors']:
                    diff = results[tf2]['factors'][factor_name] - results[tf1]['factors'][factor_name]
                    print(f"     {factor_name}: {diff:+.4f} ({results[tf1]['factors'][factor_name]:.4f} → {results[tf2]['factors'][factor_name]:.4f})")
    
    # 分析为什么差异小
    print("\n" + "=" * 80)
    print("🎯 分析结论")
    print("=" * 80)
    
    all_same = True
    for factor in ['rsi', 'macd']:
        values = []
        for tf in timeframe_list:
            if factor in results[tf]['factors']:
                values.append(results[tf]['factors'][factor])
        
        if len(values) >= 2:
            max_diff = max(values) - min(values)
            if max_diff > 0.001:
                all_same = False
                print(f"✅ {factor.upper()}: 有差异 (范围: {min(values):.4f} - {max(values):.4f}, 差异: {max_diff:.4f})")
            else:
                print(f"⚠️  {factor.upper()}: 差异很小 (范围: {min(values):.4f} - {max(values):.4f}, 差异: {max_diff:.4f})")
    
    if all_same:
        print("\n❌ 问题确认: 不同时间范围的因子值几乎相同")
        print("💡 可能原因:")
        print("   1. RSI/MACD等技术指标主要依赖最近的价格变化")
        print("   2. AAPL近期价格相对稳定，导致技术指标差异小")
        print("   3. 需要选择变化更大的股票或增加更敏感的因子")
    else:
        print("\n✅ 正常: 不同时间范围产生了不同的因子值")
        print("🎉 时间范围参数修复成功！")
        
    print("\n💡 建议解决方案:")
    print("   1. 在前端显示更多敏感性因子（如价格变化率、波动率）")
    print("   2. 增加基于整个时间序列的统计量（如夏普比率、最大回撤）")
    print("   3. 提供技术指标的时间序列图表，而不只是单点值")

def calculate_rsi_manual(prices, period=14):
    """手动计算RSI用于验证"""
    if len(prices) < period + 1:
        return 50.0
        
    deltas = np.diff(prices)
    ups = np.where(deltas > 0, deltas, 0)
    downs = np.where(deltas < 0, -deltas, 0)
    
    # 使用指数移动平均
    alpha = 1.0 / period
    avg_up = np.mean(ups[:period])
    avg_down = np.mean(downs[:period])
    
    for i in range(period, len(ups)):
        avg_up = alpha * ups[i] + (1 - alpha) * avg_up
        avg_down = alpha * downs[i] + (1 - alpha) * avg_down
    
    if avg_down == 0:
        return 100.0
    
    rs = avg_up / avg_down
    rsi = 100 - (100 / (1 + rs))
    return rsi

if __name__ == "__main__":
    deep_analysis_test() 