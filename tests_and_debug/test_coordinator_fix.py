#!/usr/bin/env python3
"""
测试协调器修复后的功能
"""

import requests
import json
import time

def test_simple_greeting():
    """测试简单问候语"""
    
    # 后端 API 地址
    base_url = "http://localhost:8000"
    
    # 测试数据
    test_queries = [
        "你是谁",
        "hello",
        "你好",
        "how are you",
        "what's your name"
    ]
    
    print("🧪 测试协调器修复后的功能")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n📤 测试问题: {query}")
        print("-" * 30)
        
        try:
            # 发送请求到后端
            response = requests.post(
                f"{base_url}/chat/stream",
                json={"message": query},
                headers={"Content-Type": "application/json"},
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ 后端响应成功")
                
                # 处理流式响应
                full_response = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            line_str = line.decode('utf-8')
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]  # 移除 'data: ' 前缀
                                if data_str.strip() == '[DONE]':
                                    break
                                data = json.loads(data_str)
                                if 'content' in data:
                                    content = data['content']
                                    full_response += content
                                    print(content, end='', flush=True)
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"处理响应行时出错: {e}")
                            continue
                
                print(f"\n📝 完整回答: {full_response}")
                
                if full_response.strip():
                    print("✅ 获得了有效回答")
                else:
                    print("❌ 回答为空")
                    
            else:
                print(f"❌ 后端响应失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 处理响应时出错: {e}")
        
        print("\n" + "=" * 50)
        time.sleep(1)  # 避免请求过快

if __name__ == "__main__":
    test_simple_greeting() 