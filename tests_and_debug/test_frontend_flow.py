#!/usr/bin/env python3
"""
前端兼容性测试脚本
测试新的金融多报告系统是否能够与现有前端正常工作
"""

import asyncio
import json
import aiohttp
import time
from typing import Dict, Any

class FrontendCompatibilityTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.test_results = []
    
    async def test_chat_stream_endpoint(self, test_query: str = "分析苹果公司的投资价值") -> Dict[str, Any]:
        """测试chat/stream端点，检查是否能正常返回多报告数据"""
        print(f"\n🧪 测试聊天流接口...")
        print(f"查询: {test_query}")
        
        url = f"{self.base_url}/chat/stream"
        headers = {"Content-Type": "application/json"}
        payload = {
            "message": test_query,
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "enable_background_investigation": True,
            "debug": False
        }
        
        received_data = {
            "messages": [],
            "reports": {
                "bullish_report": "",
                "bearish_report": "",
                "trading_advice_report": "",
                "comprehensive_final_report": "",
                "final_report": ""  # 兼容字段
            },
            "agents_seen": set(),
            "stream_events": []
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=120)  # 2分钟超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status != 200:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {await response.text()}"
                        }
                    
                    print("📡 开始接收流式数据...")
                    start_time = time.time()
                    
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                event_type = data.get('type', 'unknown')
                                content = data.get('content', '')
                                agent = data.get('agent', 'unknown')
                                
                                received_data['stream_events'].append({
                                    'type': event_type,
                                    'agent': agent,
                                    'content_length': len(content),
                                    'timestamp': time.time() - start_time
                                })
                                
                                if agent:
                                    received_data['agents_seen'].add(agent)
                                
                                if event_type in ['message', 'agent_message']:
                                    received_data['messages'].append({
                                        'type': event_type,
                                        'agent': agent,
                                        'content': content[:100] + "..." if len(content) > 100 else content
                                    })
                                    print(f"💬 [{agent}]: {content[:50]}...")
                                
                                elif event_type == 'final_report':
                                    received_data['reports']['final_report'] = content
                                    print(f"📊 收到最终报告 ({len(content)} 字符)")
                                
                                elif event_type in ['end', 'done', 'workflow_complete']:
                                    print(f"✅ 工作流完成: {event_type}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"⚠️ JSON解析错误: {e}")
                                continue
        
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "请求超时",
                "partial_data": received_data
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"请求错误: {str(e)}",
                "partial_data": received_data
            }
        
        # 转换agents_seen为列表以便JSON序列化
        received_data['agents_seen'] = list(received_data['agents_seen'])
        
        return {
            "success": True,
            "data": received_data,
            "execution_time": time.time() - start_time
        }
    
    async def check_backend_state_compatibility(self) -> Dict[str, Any]:
        """检查后端状态结构是否包含新的报告字段"""
        print(f"\n🔍 检查后端状态兼容性...")
        
        try:
            # 导入后端模块检查状态结构
            from backend.ai.graph.types import State
            
            # 检查State类是否包含新的字段
            state_fields = []
            if hasattr(State, '__annotations__'):
                state_fields = list(State.__annotations__.keys())
            
            required_fields = [
                'bullish_report',
                'bearish_report', 
                'trading_advice_report',
                'comprehensive_final_report',
                'final_report'  # 兼容字段
            ]
            
            missing_fields = [field for field in required_fields if field not in state_fields]
            
            return {
                "success": len(missing_fields) == 0,
                "state_fields": state_fields,
                "required_fields": required_fields,
                "missing_fields": missing_fields
            }
            
        except ImportError as e:
            return {
                "success": False,
                "error": f"无法导入后端模块: {str(e)}"
            }
    
    async def test_frontend_interfaces(self) -> Dict[str, Any]:
        """检查前端接口类型定义是否需要更新"""
        print(f"\n🎨 检查前端接口兼容性...")
        
        try:
            # 读取前端TypeScript文件
            with open("frontend/src/App.tsx", "r", encoding="utf-8") as f:
                frontend_code = f.read()
            
            # 检查StreamData接口是否包含所需的类型
            required_stream_types = [
                'final_report',
                'agent_message', 
                'workflow_complete'
            ]
            
            missing_types = []
            for stream_type in required_stream_types:
                if f"'{stream_type}'" not in frontend_code:
                    missing_types.append(stream_type)
            
            # 检查是否有处理final_report的逻辑
            has_final_report_handling = "final_report" in frontend_code and "data.type === 'final_report'" in frontend_code
            
            return {
                "success": len(missing_types) == 0 and has_final_report_handling,
                "required_stream_types": required_stream_types,
                "missing_types": missing_types,
                "has_final_report_handling": has_final_report_handling,
                "frontend_compatible": len(missing_types) == 0
            }
            
        except FileNotFoundError:
            return {
                "success": False,
                "error": "无法找到前端App.tsx文件"
            }
    
    async def simulate_frontend_data_flow(self) -> Dict[str, Any]:
        """模拟前端数据流处理"""
        print(f"\n🔄 模拟前端数据流处理...")
        
        # 模拟前端接收到的流式数据
        mock_stream_data = [
            {"type": "message", "content": "开始分析苹果公司...", "agent": "coordinator"},
            {"type": "agent_message", "content": "正在调查苹果公司背景信息...", "agent": "background_investigator"},
            {"type": "agent_message", "content": "制定分析计划...", "agent": "planner"},
            {"type": "agent_message", "content": "执行技术分析...", "agent": "technical_analyst"},
            {"type": "agent_message", "content": "生成看多报告...", "agent": "bullish_reporter"},
            {"type": "agent_message", "content": "生成看空报告...", "agent": "bearish_reporter"},
            {"type": "agent_message", "content": "制定交易建议...", "agent": "trading_advice_reporter"},
            {"type": "final_report", "content": "# 苹果公司综合投资分析报告\n\n## 看多观点\n...\n## 看空观点\n...\n## 交易建议\n...", "agent": "final_comprehensive_reporter"},
            {"type": "workflow_complete", "content": "分析完成"}
        ]
        
        # 模拟前端处理逻辑
        processed_messages = []
        final_report = ""
        agents_seen = set()
        
        for data in mock_stream_data:
            event_type = data.get('type')
            content = data.get('content', '')
            agent = data.get('agent', 'unknown')
            
            if agent:
                agents_seen.add(agent)
            
            if event_type in ['message', 'agent_message']:
                processed_messages.append({
                    'type': event_type,
                    'agent': agent,
                    'content': content
                })
            elif event_type == 'final_report':
                final_report = content
            elif event_type == 'workflow_complete':
                break
        
        return {
            "success": True,
            "processed_messages": len(processed_messages),
            "agents_involved": list(agents_seen),
            "has_final_report": bool(final_report),
            "final_report_length": len(final_report),
            "simulation_complete": True
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有兼容性测试"""
        print("🚀 开始前端兼容性测试...")
        
        results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": {}
        }
        
        # 1. 检查后端状态兼容性
        results["tests"]["backend_state"] = await self.check_backend_state_compatibility()
        
        # 2. 检查前端接口兼容性
        results["tests"]["frontend_interfaces"] = await self.test_frontend_interfaces()
        
        # 3. 模拟前端数据流
        results["tests"]["data_flow_simulation"] = await self.simulate_frontend_data_flow()
        
        # 4. 测试实际API调用（如果后端运行中）
        try:
            results["tests"]["api_endpoint"] = await self.test_chat_stream_endpoint()
        except Exception as e:
            results["tests"]["api_endpoint"] = {
                "success": False,
                "error": f"API测试失败: {str(e)}",
                "note": "请确保后端服务正在运行"
            }
        
        # 生成总体结果
        all_tests_passed = all(
            test_result.get("success", False) 
            for test_result in results["tests"].values()
        )
        
        results["overall_success"] = all_tests_passed
        results["compatibility_status"] = "完全兼容" if all_tests_passed else "需要调整"
        
        return results
    
    def print_test_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        print("\n" + "="*60)
        print("📋 前端兼容性测试报告")
        print("="*60)
        
        print(f"🕒 测试时间: {results['timestamp']}")
        print(f"✅ 总体状态: {results['compatibility_status']}")
        print(f"🎯 测试通过: {'是' if results['overall_success'] else '否'}")
        
        print("\n📊 详细测试结果:")
        
        for test_name, test_result in results["tests"].items():
            success = test_result.get("success", False)
            status_icon = "✅" if success else "❌"
            
            print(f"\n{status_icon} {test_name.replace('_', ' ').title()}:")
            
            if "error" in test_result:
                print(f"   错误: {test_result['error']}")
            
            # 显示关键信息
            if test_name == "backend_state":
                if "missing_fields" in test_result and test_result["missing_fields"]:
                    print(f"   缺少字段: {test_result['missing_fields']}")
                else:
                    print(f"   ✅ 所有必需字段都存在")
            
            elif test_name == "frontend_interfaces":
                if test_result.get("has_final_report_handling"):
                    print(f"   ✅ 前端已支持final_report处理")
                else:
                    print(f"   ⚠️ 前端可能需要更新final_report处理逻辑")
            
            elif test_name == "api_endpoint":
                if success and "data" in test_result:
                    data = test_result["data"]
                    print(f"   执行时间: {test_result.get('execution_time', 0):.2f}秒")
                    print(f"   接收消息: {len(data.get('messages', []))}")
                    print(f"   涉及智能体: {data.get('agents_seen', [])}")
                    print(f"   最终报告: {'是' if data.get('reports', {}).get('final_report') else '否'}")
        
        print("\n" + "="*60)
        
        if results['overall_success']:
            print("🎉 新的金融多报告系统与前端完全兼容！")
            print("💡 您可以直接使用现有前端访问新功能。")
        else:
            print("⚠️ 发现兼容性问题，建议进行以下调整：")
            
            for test_name, test_result in results["tests"].items():
                if not test_result.get("success", False):
                    if test_name == "backend_state":
                        print("   - 检查后端状态定义")
                    elif test_name == "frontend_interfaces":
                        print("   - 更新前端TypeScript接口定义")
                    elif test_name == "api_endpoint":
                        print("   - 确保后端服务正常运行")

async def main():
    """主函数"""
    tester = FrontendCompatibilityTester()
    results = await tester.run_all_tests()
    tester.print_test_results(results)
    
    # 保存详细结果到文件
    # 处理不可JSON序列化的对象
    def make_serializable(obj):
        if isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, dict):
            return {k: make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [make_serializable(item) for item in obj]
        else:
            return obj
    
    serializable_results = make_serializable(results)
    
    with open("frontend_compatibility_test_results.json", "w", encoding="utf-8") as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试结果已保存到: frontend_compatibility_test_results.json")

if __name__ == "__main__":
    asyncio.run(main()) 