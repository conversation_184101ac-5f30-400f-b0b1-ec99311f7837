#!/usr/bin/env python3
"""
测试图表重复生成修复方案
验证K线图只生成一次的逻辑
"""

import asyncio
import logging
from typing import Dict, Any
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_chart_generation_once():
    """测试图表只生成一次的逻辑"""
    
    print("🧪 测试图表重复生成修复方案")
    print("=" * 50)
    
    # 模拟状态变化
    workflow_states = [
        {"messages": ["Query about AAPL"], "chart_config": None, "has_chart_data": False},
        {"messages": ["Query about AAPL"], "chart_config": {"test": "config"}, "has_chart_data": True},  # Chart generated
        {"messages": ["Query about AAPL"], "chart_config": {"test": "config"}, "has_chart_data": True},  # Still there
        {"messages": ["Query about AAPL"], "chart_config": {"test": "config"}, "has_chart_data": True},  # Still there
    ]
    
    # 模拟工作流逻辑
    chart_data_sent = False
    chart_emissions = 0
    
    print("📊 模拟工作流状态变化:")
    
    for i, state in enumerate(workflow_states):
        print(f"\n步骤 {i+1}: {state}")
        
        # 应用修复后的逻辑
        if (state.get("chart_config") is not None and 
            state.get("has_chart_data") and 
            not chart_data_sent):
            
            print("  ✅ 发送图表数据 (首次)")
            chart_emissions += 1
            chart_data_sent = True
            
        elif (state.get("chart_config") is not None and 
              state.get("has_chart_data") and 
              chart_data_sent):
            
            print("  ⏭️ 跳过图表数据 (已发送)")
            
        else:
            print("  ⚫ 无图表数据")
    
    print(f"\n📈 结果统计:")
    print(f"图表发送次数: {chart_emissions}")
    print(f"预期发送次数: 1")
    
    if chart_emissions == 1:
        print("✅ 测试通过: 图表只发送了一次")
        return True
    else:
        print("❌ 测试失败: 图表发送次数不正确")
        return False

async def test_chart_node_skip_logic():
    """测试图表节点跳过逻辑"""
    
    print("\n🔄 测试图表节点跳过逻辑")
    print("=" * 50)
    
    # 模拟chart_generation_node的逻辑
    def simulate_chart_node(state: Dict[str, Any]) -> str:
        """模拟图表生成节点"""
        
        # 检查是否已经生成过图表数据
        if state.get("has_chart_data"):
            logger.info("图表数据已存在，跳过重复生成")
            return "skipped"
        
        # 模拟图表生成
        logger.info("生成新的图表数据")
        return "generated"
    
    # 测试场景
    test_cases = [
        {"scenario": "首次调用", "state": {"has_chart_data": False}, "expected": "generated"},
        {"scenario": "重复调用", "state": {"has_chart_data": True}, "expected": "skipped"},
        {"scenario": "再次重复调用", "state": {"has_chart_data": True}, "expected": "skipped"},
    ]
    
    results = []
    
    for test_case in test_cases:
        scenario = test_case["scenario"]
        state = test_case["state"]
        expected = test_case["expected"]
        
        result = simulate_chart_node(state)
        success = result == expected
        
        print(f"  {scenario}: {result} {'✅' if success else '❌'}")
        results.append(success)
    
    all_passed = all(results)
    print(f"\n节点逻辑测试: {'✅ 全部通过' if all_passed else '❌ 有失败'}")
    
    return all_passed

async def test_frontend_deduplication():
    """测试前端去重逻辑"""
    
    print("\n🎨 测试前端去重逻辑")
    print("=" * 50)
    
    # 模拟前端逻辑
    chart_message_ids = set()
    chart_messages = []
    
    # 模拟接收到的图表数据
    incoming_chart_data = [
        {"content": {"chart": "config1"}, "type": "chart_data"},
        {"content": {"chart": "config1"}, "type": "chart_data"},  # 重复
        {"content": {"chart": "config2"}, "type": "chart_data"},  # 不同的图表
        {"content": {"chart": "config1"}, "type": "chart_data"},  # 又是第一个
    ]
    
    print("📥 模拟接收图表数据:")
    
    for i, data in enumerate(incoming_chart_data):
        chart_id = json.dumps(data["content"])[:50]  # 使用图表配置作为ID
        
        print(f"\n数据 {i+1}: {chart_id[:30]}...")
        
        if chart_id not in chart_message_ids:
            chart_messages.append(f"Chart_{len(chart_messages)+1}")
            chart_message_ids.add(chart_id)
            print(f"  ✅ 添加图表消息: Chart_{len(chart_messages)}")
        else:
            print(f"  ⏭️ 跳过重复图表")
    
    print(f"\n📊 结果:")
    print(f"接收到的数据: {len(incoming_chart_data)} 条")
    print(f"创建的图表: {len(chart_messages)} 个")
    print(f"图表列表: {chart_messages}")
    
    expected_charts = 2  # 应该只有2个不同的图表
    success = len(chart_messages) == expected_charts
    
    print(f"前端去重测试: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

async def main():
    """主测试函数"""
    
    print("🚀 开始测试图表重复生成修复方案")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(await test_chart_generation_once())
    test_results.append(await test_chart_node_skip_logic())
    test_results.append(await test_frontend_deduplication())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！图表重复生成问题已修复。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main()) 