#!/usr/bin/env python3
"""
增强型因子计算功能测试脚本
验证因子计算的准确性和API接口的功能
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.enhanced_factors import (
    EnhancedFactorCalculator,
    FactorManager,
    get_enhanced_factor_calculator,
    list_all_available_factors
)

class EnhancedFactorsTest:
    """增强型因子计算测试类"""
    
    def __init__(self, api_base_url="http://localhost:8000"):
        self.api_base_url = api_base_url
        self.calculator = get_enhanced_factor_calculator()
        
    def test_factor_calculator(self):
        """测试因子计算器基础功能"""
        print("🧪 测试因子计算器基础功能")
        print("-" * 50)
        
        try:
            # 测试获取因子分类
            categories = self.calculator.get_factor_categories()
            print(f"✅ 因子分类数量: {len(categories)}")
            
            for category, factors in categories.items():
                print(f"  📊 {category}: {len(factors)} 个因子")
            
            # 测试获取启用的因子
            enabled_factors = self.calculator.get_enabled_factors()
            print(f"✅ 启用的因子数量: {len(enabled_factors)}")
            
            # 测试因子信息获取
            test_factor = "rsi"
            factor_info = self.calculator.get_factor_info(test_factor)
            if factor_info:
                print(f"✅ 因子信息获取成功: {test_factor}")
                print(f"  描述: {factor_info.description}")
                print(f"  分类: {factor_info.category.value}")
            else:
                print(f"❌ 无法获取因子信息: {test_factor}")
            
        except Exception as e:
            print(f"❌ 因子计算器测试失败: {e}")
    
    def test_api_endpoints(self):
        """测试API接口"""
        print("\n🌐 测试API接口")
        print("-" * 50)
        
        # 测试健康检查
        self._test_health_check()
        
        # 测试因子列表获取
        self._test_factor_list()
        
        # 测试因子分类
        self._test_factor_categories()
        
        # 测试因子信息
        self._test_factor_info()
        
        # 测试单股票因子计算 (使用模拟数据)
        self._test_single_calculation()
        
        # 测试批量计算
        self._test_batch_calculation()
    
    def _test_health_check(self):
        """测试健康检查接口"""
        try:
            response = requests.get(f"{self.api_base_url}/factors/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                print(f"  服务: {data['service']}")
                print(f"  因子数量: {data['total_factors']}")
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查请求失败: {e}")
    
    def _test_factor_list(self):
        """测试因子列表接口"""
        try:
            response = requests.get(f"{self.api_base_url}/factors/list", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 因子列表获取成功")
                print(f"  总因子数: {data['total_factors']}")
                print(f"  分类数: {len(data['categories'])}")
            else:
                print(f"❌ 因子列表获取失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 因子列表请求失败: {e}")
    
    def _test_factor_categories(self):
        """测试因子分类接口"""
        try:
            response = requests.get(f"{self.api_base_url}/factors/categories", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 因子分类获取成功")
                print(f"  分类数量: {len(data['categories'])}")
            else:
                print(f"❌ 因子分类获取失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 因子分类请求失败: {e}")
    
    def _test_factor_info(self):
        """测试因子信息接口"""
        try:
            test_factor = "rsi"
            response = requests.get(f"{self.api_base_url}/factors/info/{test_factor}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 因子信息获取成功: {test_factor}")
                print(f"  描述: {data['description']}")
                print(f"  分类: {data['category']}")
            else:
                print(f"❌ 因子信息获取失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 因子信息请求失败: {e}")
    
    def _test_single_calculation(self):
        """测试单股票因子计算"""
        try:
            test_data = {
                "symbol": "AAPL",
                "factor_names": ["rsi", "macd", "sma_20", "bollinger_position"],
                "start_date": "2024-01-01",
                "end_date": "2024-12-01"
            }
            
            response = requests.post(
                f"{self.api_base_url}/factors/calculate",
                json=test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"✅ 单股票因子计算成功: {data['symbol']}")
                    print(f"  计算的因子数: {len(data['factors'])}")
                    for factor_name, value in list(data['factors'].items())[:3]:
                        print(f"    {factor_name}: {value:.4f}")
                else:
                    print(f"⚠️  单股票因子计算返回失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ 单股票因子计算请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 单股票因子计算异常: {e}")
    
    def _test_batch_calculation(self):
        """测试批量因子计算"""
        try:
            test_data = {
                "symbols": ["AAPL", "MSFT", "GOOGL"],
                "factor_names": ["rsi", "macd", "sma_20"],
                "start_date": "2024-01-01",
                "end_date": "2024-12-01"
            }
            
            response = requests.post(
                f"{self.api_base_url}/factors/calculate/batch",
                json=test_data,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"✅ 批量因子计算成功")
                    print(f"  总股票数: {data['total_symbols']}")
                    print(f"  成功计算: {data['successful_symbols']}")
                    print(f"  结果数量: {len(data['results'])}")
                else:
                    print(f"⚠️  批量因子计算返回失败")
            else:
                print(f"❌ 批量因子计算请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 批量因子计算异常: {e}")
    
    def test_url_parameter_calculation(self):
        """测试URL参数方式的因子计算"""
        print("\n🔗 测试URL参数因子计算")
        print("-" * 50)
        
        try:
            test_symbol = "AAPL"
            test_factors = "rsi,macd,sma_20"
            
            url = f"{self.api_base_url}/factors/calculate/{test_symbol}?factor_names={test_factors}"
            
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"✅ URL参数因子计算成功: {data['symbol']}")
                    print(f"  计算的因子数: {len(data['factors'])}")
                    for factor_name, value in data['factors'].items():
                        print(f"    {factor_name}: {value:.4f}")
                else:
                    print(f"⚠️  URL参数因子计算返回失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ URL参数因子计算请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ URL参数因子计算异常: {e}")
    
    def test_performance(self):
        """测试性能"""
        print("\n⚡ 测试性能")
        print("-" * 50)
        
        try:
            # 测试单次计算性能
            start_time = time.time()
            
            test_data = {
                "symbol": "AAPL",
                "factor_names": None,  # 计算所有因子
                "start_date": "2024-01-01",
                "end_date": "2024-12-01"
            }
            
            response = requests.post(
                f"{self.api_base_url}/factors/calculate",
                json=test_data,
                timeout=30
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"✅ 性能测试完成")
                    print(f"  计算时间: {duration:.2f} 秒")
                    print(f"  计算因子数: {len(data['factors'])}")
                    print(f"  平均每因子: {duration/len(data['factors']):.4f} 秒")
                else:
                    print(f"⚠️  性能测试计算失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ 性能测试请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 性能测试异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始增强型因子计算功能测试")
        print("=" * 80)
        
        # 1. 测试因子计算器
        self.test_factor_calculator()
        
        # 2. 测试API接口
        self.test_api_endpoints()
        
        # 3. 测试URL参数方式
        self.test_url_parameter_calculation()
        
        # 4. 测试性能
        self.test_performance()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成！")
        print("💡 如果有测试失败，请检查:")
        print("   1. 后端服务是否正常运行 (python backend/server.py)")
        print("   2. 网络连接是否正常")
        print("   3. 数据管理器是否正确配置")
        print("=" * 80)

def check_server_status(api_base_url="http://localhost:8000"):
    """检查服务器状态"""
    try:
        response = requests.get(f"{api_base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        print("💡 请确保后端服务已启动: python backend/server.py")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强型因子计算功能测试')
    parser.add_argument('--api-url', '-u', type=str, default='http://localhost:8000',
                       help='API服务器地址')
    parser.add_argument('--test', '-t', type=str, 
                       choices=['all', 'calculator', 'api', 'url', 'performance'],
                       default='all',
                       help='要运行的测试类型')
    
    args = parser.parse_args()
    
    # 检查服务器状态
    if not check_server_status(args.api_url):
        return
    
    # 创建测试实例
    tester = EnhancedFactorsTest(args.api_url)
    
    try:
        if args.test == 'all':
            tester.run_all_tests()
        elif args.test == 'calculator':
            tester.test_factor_calculator()
        elif args.test == 'api':
            tester.test_api_endpoints()
        elif args.test == 'url':
            tester.test_url_parameter_calculation()
        elif args.test == 'performance':
            tester.test_performance()
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

if __name__ == "__main__":
    main() 