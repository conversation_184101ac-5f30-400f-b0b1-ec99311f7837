#!/usr/bin/env python3
"""
测试时间范围参数修复效果
验证因子分析是否根据不同时间范围计算出不同结果
"""

import requests
import json
from datetime import datetime, timedelta

def test_timeframe_analysis():
    """测试不同时间范围的因子分析结果"""
    
    # API端点
    url = "http://localhost:8000/factors/calculate/batch"
    
    # 测试参数
    symbols = ["AAPL"]
    factor_names = ["rsi", "macd", "sma20"]
    
    # 当前时间
    end_date = datetime.now()
    
    # 不同的时间范围
    timeframes = {
        "1个月": (end_date - timedelta(days=30)).strftime("%Y-%m-%d"),
        "3个月": (end_date - timedelta(days=90)).strftime("%Y-%m-%d"),
        "6个月": (end_date - timedelta(days=180)).strftime("%Y-%m-%d"),
    }
    
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    print("=" * 60)
    print("🧪 测试时间范围参数修复效果")
    print(f"📅 结束日期: {end_date_str}")
    print("=" * 60)
    
    results = {}
    
    for timeframe_name, start_date in timeframes.items():
        print(f"\n🔍 测试时间范围: {timeframe_name} ({start_date} 到 {end_date_str})")
        
        payload = {
            "symbols": symbols,
            "factor_names": factor_names,
            "start_date": start_date,
            "end_date": end_date_str
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("results"):
                    symbol_data = data["results"].get("AAPL", {})
                    results[timeframe_name] = symbol_data
                    
                    print(f"✅ 成功获取数据:")
                    for factor, value in symbol_data.items():
                        print(f"   {factor}: {value:.4f}")
                else:
                    print(f"❌ API返回失败: {data}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    # 比较结果
    print("\n" + "=" * 60)
    print("📊 结果比较分析:")
    print("=" * 60)
    
    if len(results) >= 2:
        timeframe_list = list(results.keys())
        print(f"\n比较 {timeframe_list[0]} vs {timeframe_list[1]}:")
        
        for factor in factor_names:
            values = []
            for timeframe in timeframe_list:
                if factor in results[timeframe]:
                    values.append(results[timeframe][factor])
            
            if len(values) >= 2:
                diff = abs(values[0] - values[1])
                if diff > 0.001:  # 如果差异大于0.001
                    print(f"✅ {factor}: 值有差异 ({values[0]:.4f} vs {values[1]:.4f}, 差异: {diff:.4f})")
                else:
                    print(f"⚠️  {factor}: 值相近 ({values[0]:.4f} vs {values[1]:.4f}, 差异: {diff:.4f})")
    else:
        print("❌ 无法比较，获取的数据不足")
    
    # 检查所有结果是否相同
    all_same = True
    if len(results) >= 2:
        first_result = list(results.values())[0]
        for timeframe, result in results.items():
            for factor in factor_names:
                if factor in first_result and factor in result:
                    if abs(first_result[factor] - result[factor]) > 0.001:
                        all_same = False
                        break
    
    print("\n" + "=" * 60)
    print("🎯 测试结论:")
    print("=" * 60)
    
    if all_same and len(results) >= 2:
        print("❌ 问题依然存在: 不同时间范围返回相同结果")
        print("💡 可能原因:")
        print("   1. 数据源没有根据时间范围筛选")
        print("   2. 后端缓存导致返回相同结果")
        print("   3. 模拟数据没有时间差异")
        return False
    elif len(results) >= 2:
        print("✅ 修复成功: 不同时间范围返回不同结果")
        print("🎉 时间范围参数现在正常工作!")
        return True
    else:
        print("⚠️  无法确定: 获取数据不足以进行比较")
        return None

if __name__ == "__main__":
    test_timeframe_analysis() 