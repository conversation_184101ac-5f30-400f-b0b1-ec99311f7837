#!/usr/bin/env python3
"""
LangGraph 工作流测试脚本
专门测试多智能体协作的工作流逻辑
"""

import asyncio
import sys
import json
from pathlib import Path
from typing import Dict, Any

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def log(msg):
    print(f"🤖 {msg}")

async def test_graph_workflow():
    """测试完整的 LangGraph 工作流"""
    log("开始测试 LangGraph 工作流...")
    
    try:
        from backend.ai.graph import build_graph
        from backend.ai.graph.types import State
        from langchain_core.messages import HumanMessage
        
        # 构建图
        graph = build_graph()
        log("✓ 图构建成功")
        
        # 测试不同类型的输入
        test_cases = [
            {
                "name": "简单问候",
                "input": "你好",
                "expected_path": ["coordinator", "END"]
            },
            {
                "name": "股票分析请求", 
                "input": "请分析苹果公司AAPL的股票",
                "expected_path": ["coordinator", "planner"]
            },
            {
                "name": "技术分析请求",
                "input": "TSLA的技术指标分析",
                "expected_path": ["coordinator", "planner"]
            }
        ]
        
        for test_case in test_cases:
            log(f"\n--- 测试用例: {test_case['name']} ---")
            log(f"输入: {test_case['input']}")
            
            # 准备初始状态
            initial_state = {
                "messages": [HumanMessage(content=test_case['input'])],
                "locale": "zh-CN",
                "auto_accepted_plan": True,  # 自动接受计划以避免人工干预
                "enable_background_investigation": False,  # 禁用背景调查以简化流程
            }
            
            try:
                # 运行工作流（限制步数）
                step_count = 0
                max_steps = 5
                current_state = initial_state
                path = ["START"]
                
                config = {
                    "configurable": {
                        "max_plan_iterations": 1,
                        "max_step_num": 2,
                        "max_search_results": 3
                    }
                }
                
                # 执行工作流
                result = await graph.ainvoke(current_state, config=config)
                
                log(f"✓ 工作流执行完成")
                log(f"✓ 最终状态包含 {len(result.get('messages', []))} 条消息")
                
                # 检查最终报告
                if 'final_report' in result and result['final_report']:
                    log("✓ 生成了最终报告")
                    log(f"报告长度: {len(result['final_report'])} 字符")
                else:
                    log("⚠ 未生成最终报告")
                    
            except Exception as e:
                log(f"✗ 测试用例失败: {e}")
                
        return True
        
    except Exception as e:
        log(f"✗ 工作流测试失败: {e}")
        return False

async def test_individual_nodes():
    """测试单个节点功能"""
    log("测试单个节点功能...")
    
    try:
        from backend.ai.graph.nodes import (
            coordinator_node, 
            planner_node,
            researcher_node,
            technical_analyst_node,
            reporter_node
        )
        from backend.ai.graph.types import State
        from langchain_core.messages import HumanMessage
        
        # 测试 coordinator 节点
        log("\n--- 测试 Coordinator 节点 ---")
        coordinator_state = {
            "messages": [HumanMessage(content="分析AAPL股票")],
            "locale": "zh-CN"
        }
        
        coordinator_result = coordinator_node(coordinator_state)
        log(f"✓ Coordinator 节点执行完成")
        log(f"下一步: {coordinator_result.goto if hasattr(coordinator_result, 'goto') else 'unknown'}")
        
        # 测试 planner 节点
        log("\n--- 测试 Planner 节点 ---")
        planner_state = {
            "messages": [HumanMessage(content="请制定AAPL股票分析计划")],
            "locale": "zh-CN",
            "plan_iterations": 0
        }
        
        config = {
            "configurable": {
                "max_plan_iterations": 1,
                "max_step_num": 2
            }
        }
        
        planner_result = planner_node(planner_state, config)
        log(f"✓ Planner 节点执行完成")
        log(f"下一步: {planner_result.goto if hasattr(planner_result, 'goto') else 'unknown'}")
        
        return True
        
    except Exception as e:
        log(f"✗ 节点测试失败: {e}")
        return False

async def test_agent_tools():
    """测试智能体工具调用"""
    log("测试智能体工具调用...")
    
    try:
        from backend.ai.agents import create_agent
        from backend.ai.tools import python_repl_tool, get_web_search_tool
        from langchain_core.messages import HumanMessage
        
        # 创建一个简单的研究员智能体
        tools = [python_repl_tool, get_web_search_tool(max_search_results=3)]
        
        researcher_agent = create_agent(
            "test_researcher",
            "researcher", 
            tools,
            "researcher"
        )
        
        # 测试简单的计算任务
        test_input = {
            "messages": [HumanMessage(content="计算 2+2 等于多少")]
        }
        
        result = await researcher_agent.ainvoke(test_input, config={"recursion_limit": 10})
        
        if result and "messages" in result:
            log("✓ 智能体工具调用成功")
            log(f"响应消息数: {len(result['messages'])}")
            return True
        else:
            log("✗ 智能体工具调用失败")
            return False
            
    except Exception as e:
        log(f"✗ 工具调用测试失败: {e}")
        return False

async def test_step_execution():
    """测试步骤执行逻辑"""
    log("测试步骤执行逻辑...")
    
    try:
        from backend.ai.prompts.planner_model import Plan, Step, StepType
        from backend.ai.graph.nodes import research_team_node
        from backend.ai.graph.types import State
        from langchain_core.messages import HumanMessage
        
        # 创建模拟计划
        plan = Plan(
            locale="zh-CN",
            has_enough_context=False,
            thought="测试思路",
            title="测试计划",
            steps=[
                Step(
                    need_web_search=True,
                    title="搜索股票信息",
                    description="搜索AAPL的基本信息",
                    step_type=StepType.RESEARCH,
                    execution_res=None
                ),
                Step(
                    need_web_search=False,
                    title="技术分析",
                    description="进行技术指标计算",
                    step_type=StepType.TECHNICAL_ANALYSIS,
                    execution_res=None
                )
            ]
        )
        
        # 测试研究团队节点
        team_state = {
            "messages": [HumanMessage(content="执行计划")],
            "current_plan": plan,
            "observations": []
        }
        
        team_result = research_team_node(team_state)
        
        if hasattr(team_result, 'goto'):
            log(f"✓ 研究团队路由成功，下一步: {team_result.goto}")
            return True
        else:
            log("✗ 研究团队路由失败")
            return False
            
    except Exception as e:
        log(f"✗ 步骤执行测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    log("="*60)
    log("LangGraph 工作流测试")
    log("="*60)
    
    tests = [
        ("图工作流", test_graph_workflow),
        ("单个节点", test_individual_nodes),
        ("智能体工具", test_agent_tools),
        ("步骤执行", test_step_execution),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        log(f"\n{'='*40}")
        log(f"测试: {test_name}")
        log("="*40)
        
        try:
            success = await test_func()
            if success:
                passed += 1
                log(f"✅ {test_name} - 通过")
            else:
                failed += 1
                log(f"❌ {test_name} - 失败")
        except Exception as e:
            failed += 1
            log(f"❌ {test_name} - 异常: {e}")
    
    # 总结
    log("\n" + "="*60)
    log("测试总结")
    log("="*60)
    log(f"总测试数: {passed + failed}")
    log(f"通过: {passed}")
    log(f"失败: {failed}")
    log(f"成功率: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        log("🎉 所有 LangGraph 测试通过！")
        return True
    else:
        log(f"❌ {failed} 个测试失败")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 