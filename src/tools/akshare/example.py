#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
AkShare 美股数据工具使用示例

这个脚本演示了如何使用 akshare 工具获取美股数据。
"""

import json
from us_stock_data import (
    us_stock_spot_tool,
    us_stock_hist_tool,
    us_stock_search_tool,
    us_stock_famous_tool,
    us_stock_pink_tool,
    us_stock_minute_tool,
)


def main():
    """主函数，演示各种工具的使用"""
    
    print("=" * 60)
    print("AkShare 美股数据工具使用示例")
    print("=" * 60)
    
    # 1. 搜索苹果公司
    print("\n1. 搜索苹果公司")
    print("-" * 30)
    try:
        result = us_stock_search_tool.invoke({"company_name": "苹果公司", "max_rows": 3})
        data = json.loads(result)
        if "data" in data and data["data"]:
            apple_symbol = data["data"][0]["代码"]
            print(f"找到苹果公司代码: {apple_symbol}")
            print(f"公司名称: {data['data'][0]['名称']}")
            print(f"最新价: ${data['data'][0]['最新价']}")
        else:
            print("未找到苹果公司")
            apple_symbol = "105.AAPL"  # 使用默认代码
    except Exception as e:
        print(f"搜索失败: {e}")
        apple_symbol = "105.AAPL"  # 使用默认代码
    
    # 2. 获取苹果历史数据（过去30天）
    print(f"\n2. 获取苹果历史数据（过去30天）")
    print("-" * 30)
    try:
        result = us_stock_hist_tool.invoke({
            "symbol": apple_symbol,
            "days_back": 30,
            "max_rows": 10
        })
        data = json.loads(result)
        if "data" in data:
            print(f"获取到 {data['displayed_rows']} 行数据")
            if data["data"]:
                latest = data["data"][0]  # 最新的数据
                print(f"最新交易日: {latest['日期']}")
                print(f"收盘价: ${latest['收盘']}")
                print(f"涨跌幅: {latest['涨跌幅']}%")
        else:
            print("获取历史数据失败")
    except Exception as e:
        print(f"获取历史数据失败: {e}")
    
    # 3. 获取科技类知名美股
    print("\n3. 获取科技类知名美股")
    print("-" * 30)
    try:
        result = us_stock_famous_tool.invoke({
            "category": "科技类",
            "max_rows": 5
        })
        data = json.loads(result)
        if "data" in data:
            print(f"获取到 {data['displayed_rows']} 只科技股")
            for i, stock in enumerate(data["data"][:3], 1):
                print(f"{i}. {stock['名称']} ({stock['代码']}) - ${stock['最新价']}")
        else:
            print("获取科技股数据失败")
    except Exception as e:
        print(f"获取科技股数据失败: {e}")
    
    # 4. 获取美股实时行情（前5只）
    print("\n4. 获取美股实时行情（前5只）")
    print("-" * 30)
    try:
        result = us_stock_spot_tool.invoke({"max_rows": 5})
        data = json.loads(result)
        if "data" in data:
            print(f"获取到 {data['displayed_rows']} 只股票的实时数据")
            for i, stock in enumerate(data["data"], 1):
                print(f"{i}. {stock['名称']} - ${stock['最新价']} ({stock['涨跌幅']:+.2f}%)")
        else:
            print("获取实时行情失败")
    except Exception as e:
        print(f"获取实时行情失败: {e}")
    
    # 5. 获取粉单市场数据（前3只）
    print("\n5. 获取粉单市场数据（前3只）")
    print("-" * 30)
    try:
        result = us_stock_pink_tool.invoke({"max_rows": 3})
        data = json.loads(result)
        if "data" in data:
            print(f"获取到 {data['displayed_rows']} 只粉单股票")
            for i, stock in enumerate(data["data"], 1):
                print(f"{i}. {stock['名称']} ({stock['代码']}) - ${stock['最新价']}")
        else:
            print("获取粉单市场数据失败")
    except Exception as e:
        print(f"获取粉单市场数据失败: {e}")
    
    print("\n" + "=" * 60)
    print("示例完成！")
    print("=" * 60)


if __name__ == "__main__":
    main() 