# AkShare Stock News Tools

This module provides stock news acquisition tools based on the AkShare library, supporting individual stock news, financial news, earnings report release times, and more.

## Features

- 📰 **Individual Stock News**: Get the latest news for a specified stock (Eastmoney.com)
- 📊 **Financial Highlights**: Get selected financial content from Caixin.com
- 📅 **Earnings Report Time**: Get the earnings report release schedule for a specified date (Baidu Stock)
- 🔄 **Comprehensive Query**: Get information from multiple news data sources at once

## Tool List

### 1. stock_news_em_tool - Individual Stock News Tool

Gets news data for a specified individual stock from Eastmoney.com.

**Parameters:**
- `symbol` (str): Stock code or keyword, e.g., "300059", "Apple", "AAPL"
- `max_rows` (int): Maximum number of rows to return, default 50

**Returned Fields:**
- Keyword
- News Title
- News Content
- Publish Time
- Source
- News Link

**Example:**
```python
from src.tools.akshare import stock_news_em_tool

# Get Apple Inc. news
result = stock_news_em_tool.invoke({
    "symbol": "Apple",
    "max_rows": 10
})

# Get A-share individual stock news
result = stock_news_em_tool.invoke({
    "symbol": "300059",
    "max_rows": 20
})
```

### 2. stock_news_main_cx_tool - Financial Content Highlights Tool

Gets selected financial content news from Caixin.com.

**Parameters:**
- `max_rows` (int): Maximum number of rows to return, default 100

**Returned Fields:**
- tag
- summary
- interval_time
- pub_time
- url

**Example:**
```python
from src.tools.akshare import stock_news_main_cx_tool

result = stock_news_main_cx_tool.invoke({
    "max_rows": 50
})
```

### 3. news_report_time_baidu_tool - Earnings Report Release Time Tool

Gets the earnings report release schedule for a specified date from Baidu Stock.

**Parameters:**
- `date` (str): Query date, format YYYYMMDD, e.g., "20241107"
- `max_rows` (int): Maximum number of rows to return, default 100

**Returned Fields:**
- Stock Code
- Exchange
- Stock Abbreviation
- Report Period

**Example:**
```python
from src.tools.akshare import news_report_time_baidu_tool

result = news_report_time_baidu_tool.invoke({
    "date": "20241107",
    "max_rows": 50
})
```

### 4. comprehensive_stock_news_tool - Comprehensive News Tool

Gets various news information for a specified stock at once.

**Parameters:**
- `symbol` (str): Stock code or keyword
- `include_general_news` (bool): Whether to include general financial news, default True
- `date_for_reports` (str, optional): Query date for earnings reports, format YYYYMMDD
- `max_rows_per_source` (int): Maximum number of rows to return per data source, default 30

**Functions:**
1. Individual stock specific news (Eastmoney)
2. Financial content highlights (Caixin) - Optional
3. Earnings report release schedule (Baidu Stock) - Optional

**Example:**
```python
from src.tools.akshare import comprehensive_stock_news_tool

# Comprehensive query for Apple Inc. information
result = comprehensive_stock_news_tool.invoke({
    "symbol": "Apple",
    "include_general_news": True,
    "date_for_reports": "20241107",
    "max_rows_per_source": 20
})

# Only query individual stock news
result = comprehensive_stock_news_tool.invoke({
    "symbol": "AAPL",
    "include_general_news": False,
    "max_rows_per_source": 10
})
```

## Data Format

### Single Tool Return Format
```json
{
  "data": [...],           // Actual data array
  "total_rows": 1000,      // Total rows
  "displayed_rows": 100,   // Displayed rows
  "message": "Displaying first 100 rows, total 1000 rows"
}
```

### Comprehensive Tool Return Format
```json
{
  "symbol": "Apple",
  "timestamp": "2024-01-15 10:30:00",
  "data_sources": {
    "individual_stock_news": {
      "source": "Eastmoney.com",
      "total_rows": 50,
      "displayed_rows": 20,
      "data": [...]
    },
    "general_financial_news": {
      "source": "Caixin.com",
      "total_rows": 100,
      "displayed_rows": 20,
      "data": [...]
    },
    "earnings_reports": {
      "source": "Baidu Stock",
      "date": "20241107",
      "total_rows": 5,
      "displayed_rows": 5,
      "data": [...]
    }
  }
}
```

## Usage Example

See `news_example.py` file for complete usage examples:

```bash
cd src/tools/akshare
python news_example.py
```

## Supported Stock Code Formats

- **A-shares**: 300059, 000001, 600036, etc.
- **US Stocks**: AAPL, MSFT, TSLA, etc.
- **Company Name**: Apple, Microsoft, Tesla, etc.
- **Keywords**: Any relevant search keywords

## Data Sources

1. **Eastmoney.com**: Individual stock news, latest 100 items of the day
2. **Caixin.com**: Selected financial content, historical data
3. **Baidu Stock**: Earnings report release times, includes A-shares, Hong Kong stocks, US stocks

## Important Notes

1. **Data Delay**: News data may have some delay
2. **API Limits**: AkShare may have rate limits
3. **Network Dependency**: Requires a stable network connection to access data sources
4. **Data Accuracy**: News data is for reference only, invest with caution

## Error Handling

All tools include comprehensive error handling mechanisms, and will return JSON with error information when an error occurs:

```json
{
  "error": "Error description",
  "function": "Function name",
  "error_type": "Error type"
}
```

## Dependencies

- `akshare`: Financial data interface library
- `pandas`: Data processing library
- `langchain_core`: LangChain core library

## License

MIT License 