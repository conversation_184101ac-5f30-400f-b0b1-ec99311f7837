# Yahoo Finance 新闻工具

这个工具模块包含了用于获取 Yahoo Finance 股票新闻的功能。

## 功能特性

### `yahoo_finance_news_tool`

获取指定股票代码的 Yahoo Finance 新闻资讯。

**功能特点：**
- 🔄 多数据源支持：RSS源、API接口
- 📰 实时新闻数据获取
- 🧹 自动清理和去重
- 🎯 支持主要美股代码（AAPL、TSLA、MSFT等）

**输入参数：**
- `symbol` (str): 股票代码，如 'AAPL', 'TSLA', 'MSFT' 等

**返回格式：**
```json
{
  "symbol": "AAPL",
  "source": "Yahoo Finance",
  "news_count": 10,
  "news_data": [
    {
      "title": "新闻标题",
      "url": "新闻链接",
      "summary": "新闻摘要",
      "source": "Yahoo Finance RSS",
      "published": "发布时间"
    }
  ],
  "status": "success",
  "methods_tried": ["RSS", "API"]
}
```

## 使用示例

### 在代码中使用

```python
from src.tools.yahoo_finance_news import yahoo_finance_news_tool

# 获取苹果公司新闻
result = yahoo_finance_news_tool.invoke({"symbol": "AAPL"})
print(result)
```

### 在 Agent 中使用

该工具已经被添加到 `researcher_node` 中，可以在研究任务中自动调用。

## 测试

运行测试脚本验证工具功能：

```bash
python src/tools/test_yahoo_finance_news.py
```

## 数据源

1. **Yahoo Finance RSS** - 主要数据源，实时性强
2. **Yahoo Finance API** - 备用数据源

## 注意事项

- 工具会自动尝试多个数据源，确保高可用性
- 新闻数据限制为前10条，避免信息过载
- 自动处理网络错误和解析错误
- 支持中英文公司名称映射

## 支持的股票代码

主要支持美股代码，包括但不限于：
- AAPL (苹果)
- TSLA (特斯拉)
- MSFT (微软)
- GOOGL (谷歌)
- AMZN (亚马逊)
- META (Meta)
- NVDA (英伟达)
- NFLX (Netflix)

## 错误处理

工具包含完善的错误处理机制：
- 网络请求错误
- 数据解析错误
- 速率限制处理 