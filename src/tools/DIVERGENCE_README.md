# Divergence Analysis Tool

This tool is specifically designed to detect divergence phenomena between stock prices and the MACD signal line, serving as an important signal identification tool in technical analysis.

## Features

- 🔍 **Bullish Divergence Detection**: Identifies bullish divergence signals where price makes a new low but the MACD signal line does not make a new low (or even makes a new high)
- 📈 **Bearish Divergence Detection**: Identifies bearish divergence signals where price makes a new high but the MACD signal line does not make a new high (or even makes a new low)
- 📊 **Strength Evaluation**: Calculates the strength of divergence signals to help assess signal reliability
- ⚡ **Real-time Analysis**: Supports real-time stock data analysis
- 🎯 **Adjustable Parameters**: Supports custom MACD parameters and detection parameters

## Technical Principles

### What is Divergence?

Divergence is a crucial concept in technical analysis, referring to situations where price movement and technical indicator movement go in opposite directions:

1. **Bullish Divergence**:
   - Price makes a new low
   - But the MACD signal line does not make a new low (or even makes a new high)
   - Usually indicates a potential price rebound

2. **Bearish Divergence**:
   - Price makes a new high
   - But the MACD signal line does not make a new high (or even makes a new low)
   - Usually indicates a potential price correction

### MACD Indicator

MACD (Moving Average Convergence Divergence) consists of three parts:
- **MACD Line**: Fast EMA - Slow EMA
- **Signal Line**: EMA smoothing of the MACD Line
- **Histogram**: MACD Line - Signal Line

This tool primarily uses the MACD signal line for divergence analysis.

## Tool Usage

### Basic Usage

```python
from src.tools.divergence_analysis import detect_price_macd_divergence

# Perform divergence analysis using stock data
result = detect_price_macd_divergence.invoke({
    "stock_data_json": stock_data_json,  # Stock data JSON string
    "lookback_period": 20,               # Lookback period (trading days)
    "min_distance": 5,                   # Minimum distance between extreme points
    "macd_fast": 12,                     # MACD fast EMA period
    "macd_slow": 26,                     # MACD slow EMA period
    "macd_signal": 9                     # MACD signal line EMA period
})
```

### Parameter Description

| Parameter | Type | Default Value | Description |
|---|---|---|---|
| `stock_data_json` | str | Required | JSON string containing historical stock data |
| `lookback_period` | int | 20 | Lookback period, used to define the time range for finding divergences |
| `min_distance` | int | 5 | Minimum distance between two extreme points, to avoid noise |
| `macd_fast` | int | 12 | MACD fast EMA period |
| `macd_slow` | int | 26 | MACD slow EMA period |
| `macd_signal` | int | 9 | MACD signal line EMA period |

### Data Format Requirements

The input stock data JSON should contain the following fields:

```json
{
  "data": [
    {
      "date": "2024-01-01",
      "close": 150.25,
      "open": 149.80,
      "high": 151.00,
      "low": 149.50,
      "volume": 1000000
    }
    // ... more data
  ]
}
```

**Required Fields**:
- `date`: Trading date
- `close`: Closing price

**Optional Fields**:
- `open`, `high`, `low`, `volume`: Other price and volume data

## Return Results

### Result Structure

```json
{
  "analysis_summary": {
    "symbol": "AAPL",
    "analysis_period": {
      "start_date": "2024-01-01",
      "end_date": "2024-12-31",
      "total_days": 252
    },
    "parameters": {
      "lookback_period": 20,
      "min_distance": 5,
      "macd_settings": {
        "fast": 12,
        "slow": 26,
        "signal": 9
      }
    },
    "divergences_found": {
      "bullish_count": 3,
      "bearish_count": 2,
      "total_count": 5
    }
  },
  "bullish_divergences": [...],
  "bearish_divergences": [...],
  "current_market_status": {...},
  "trading_suggestions": [...],
  "risk_disclaimer": [...]
}
```

### Divergence Signal Details

Each divergence signal includes the following information:

```json
{
  "type": "bullish_divergence",
  "current_date": "2024-06-15",
  "previous_date": "2024-05-20",
  "current_price": 145.30,
  "previous_price": 148.50,
  "price_change_pct": -2.15,
  "current_macd_signal": -0.0234,
  "previous_macd_signal": -0.0456,
  "macd_change_pct": 48.68,
  "strength": 0.5234,
  "days_apart": 18
}
```

## Usage Examples

### Example 1: Analyzing Apple Inc. Stock

```python
# 1. Get stock data
from src.tools.akshare import us_stock_daily_sina_tool

stock_data = us_stock_daily_sina_tool.invoke({
    "symbol": "AAPL",
    "adjust": "qfq",
    "max_rows": 100
})

# 2. Perform divergence analysis
result = detect_price_macd_divergence.invoke({
    "stock_data_json": stock_data,
    "lookback_period": 20,
    "min_distance": 5
})

# 3. Parse results
import json
analysis = json.loads(result)

print(f"Found {analysis['analysis_summary']['divergences_found']['total_count']} divergence signals")
```

### Example 2: Analyzing with Custom Parameters

```python
# Use more sensitive parameter settings
result = detect_price_macd_divergence.invoke({
    "stock_data_json": stock_data,
    "lookback_period": 15,    # Shorter lookback period
    "min_distance": 3,        # Smaller minimum distance
    "macd_fast": 8,           # Faster MACD settings
    "macd_slow": 21,
    "macd_signal": 5
})
```

## Running Examples

The project provides a complete example script:

```bash
cd src/tools
python divergence_example.py
```

This script will:
1. Fetch real stock data
2. Perform divergence analysis
3. Display detailed analysis results
4. Test with simulated data

## Trading Applications

### Bullish Divergence Trading Strategy

1. **Signal Confirmation**:
   - Price makes a new low
   - MACD signal line does not make a new low
   - High divergence strength

2. **Entry Timing**:
   - Wait for price to confirm a rebound
   - Combine with other technical indicators (e.g., RSI oversold)

3. **Risk Management**:
   - Set stop-loss below the previous low
   - Target price can be set near the previous high

### Bearish Divergence Trading Strategy

1. **Signal Confirmation**:
   - Price makes a new high
   - MACD signal line does not make a new high
   - High divergence strength

2. **Entry Timing**:
   - Wait for price to confirm a pullback
   - Combine with other technical indicators (e.g., RSI overbought)

3. **Risk Management**:
   - Set stop-loss above the previous high
   - Target price can be set near the previous low

## Important Notes

### Data Requirements

- **Minimum Data Volume**: At least 50-60 trading days of data required
- **Recommended Data Volume**: 100+ trading days for improved analysis accuracy
- **Data Quality**: Ensure data integrity and avoid missing values

### Parameter Tuning

- **lookback_period**:
  - Larger value: Captures long-term divergences, fewer but more reliable signals
  - Smaller value: Captures short-term divergences, more signals but possibly more noise

- **min_distance**:
  - Larger value: Filters short-term fluctuations, reduces false signals
  - Smaller value: Captures more signals, but may include noise

### Limitations

1. **False Signals**: Divergence signals may have false breakouts
2. **Time Lag**: Divergence signals may appear earlier than the actual trend reversal
3. **Market Environment**: In strong trending markets, divergence signals may fail
4. **Single Indicator**: Recommended to combine with other technical indicators for confirmation

## Risk Warning

⚠️ **Important Reminder**:

1. Divergence signals are for reference only and do not constitute investment advice
2. Divergence signals may produce false signals, it is recommended to confirm with other technical indicators
3. Market risks exist, invest with caution
4. Over-reliance on a single technical indicator may lead to investment losses

## Technical Support

For questions or suggestions, please refer to:
- Project documentation: `docs/`
- Example code: `src/tools/divergence_example.py`
- Test cases: Run example scripts for testing

## License

MIT License 