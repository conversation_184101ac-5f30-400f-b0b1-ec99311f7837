#!/bin/bash
# 完整系统启动脚本（前端 + AI集成后端）- 自动监控日志版本

set -e  # 遇到错误时退出

echo "🚀 启动AI金融分析完整系统 (自动监控日志)"
echo "================================"

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "backend" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查端口是否被占用的函数
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -ti:$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，正在停止占用该端口的进程..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
        
        if lsof -ti:$port > /dev/null 2>&1; then
            echo "❌ 无法释放端口 $port，请手动检查并停止占用该端口的进程"
            echo "可以使用: lsof -i:$port 查看占用进程"
            exit 1
        else
            echo "✅ 端口 $port 已释放"
        fi
    fi
}

# 1. 检查环境变量
echo "🔧 检查环境变量..."
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env文件，请先运行 ./setup_environment.sh 配置环境变量"
    exit 1
fi

# 加载环境变量
set -a  # 自动导出变量
source .env
set +a

# 检查必要的环境变量
if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ GEMINI_API_KEY未设置，请检查.env文件"
    exit 1
fi

if [ -z "$TUSHARE_TOKEN" ]; then
    echo "⚠️  TUSHARE_TOKEN未设置，数据功能可能受限"
fi

echo "✅ 环境变量检查完成"

# 2. 检查Python虚拟环境
echo "🐍 检查Python虚拟环境..."
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
echo "✅ Python虚拟环境已激活"

# 3. 安装Python依赖
echo "📦 安装Python依赖..."
pip install -q -r requirements.txt
echo "✅ Python依赖安装完成"

# 4. 检查Node.js依赖（如果有前端）
if [ -d "frontend" ]; then
    echo "📦 检查前端依赖..."
    cd frontend
    
    # 确保依赖是最新的
    echo "安装/更新前端依赖..."
    npm ci
    
    cd ..
    echo "✅ 前端依赖检查完成"
fi

# 5. 检查并释放端口
echo "🔍 检查端口占用情况..."
check_port 8000 "后端"
if [ -d "frontend" ]; then
    check_port 3000 "前端"
fi

# 6. 启动后端服务（后台运行）
echo "🎯 启动AI集成后端服务..."

# 停止可能已运行的服务
echo "🧹 清理可能存在的后端进程..."
pkill -f "uvicorn.*backend.server:app" 2>/dev/null || true
pkill -f "python.*backend/server.py" 2>/dev/null || true
pkill -f "uvicorn.*server:app" 2>/dev/null || true
sleep 2

# 确保在项目根目录启动
cd "$(dirname "$0")"

# 使用正确的uvicorn命令启动后端服务
echo "启动后端服务..."
nohup uvicorn backend.server:app --host 0.0.0.0 --port 8000 --reload > backend.log 2>&1 &
BACKEND_PID=$!

# 保存后端PID
echo "$BACKEND_PID" > .backend.pid

# 等待后端启动
echo "⏳ 等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        echo "查看后端日志:"
        tail -20 backend.log
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    
    sleep 1
done

# 7. 启动前端服务（如果存在）
if [ -d "frontend" ]; then
    echo "🌐 启动前端服务..."
    cd frontend
    
    # 停止可能已运行的前端服务
    echo "🧹 清理可能存在的前端进程..."
    pkill -f "npm.*run.*dev" 2>/dev/null || true
    pkill -f "next.*dev" 2>/dev/null || true
    pkill -f "node.*next.*dev" 2>/dev/null || true
    sleep 2
    
    # 启动前端服务
    echo "启动前端服务..."
    nohup npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    cd ..
    
    # 保存前端PID
    echo "$FRONTEND_PID" > .frontend.pid
    
    # 等待前端启动
    echo "⏳ 等待前端服务启动..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
            break
        fi
        
        if [ $i -eq 60 ]; then
            echo "⚠️  前端服务启动可能有问题，请检查 frontend.log"
            echo "查看前端日志:"
            tail -20 frontend.log
            break
        fi
        
        sleep 2
    done
fi

# 8. 显示启动信息
echo ""
echo "🎉 系统启动完成！"
echo "================================"
echo "📊 后端服务:"
echo "   - API文档: http://localhost:8000/docs"
echo "   - AI聊天: http://localhost:8000/chat/stream"
echo "   - 健康检查: http://localhost:8000/health"
echo "   - 进程ID: $BACKEND_PID"

if [ -d "frontend" ]; then
    echo ""
    echo "🌐 前端服务:"
    echo "   - 用户界面: http://localhost:3000"
    echo "   - 进程ID: $FRONTEND_PID"
fi

echo ""
echo "🛑 停止服务: ./stop_full_system.sh"
echo "🔄 重启服务: ./restart_full_system.sh"
echo ""

# 9. 自动开始监控后端日志
echo "📊 自动开始监控后端日志..."
echo "💡 提示：按 Ctrl+C 可以停止日志监控并返回终端"
echo "💡 服务将继续在后台运行，使用 ./stop_full_system.sh 停止"
echo "================================"
sleep 2

# 开始监控日志
tail -f backend.log 